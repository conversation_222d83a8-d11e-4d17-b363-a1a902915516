describe('CRUD Contrato - Processo Novo - Licitação', () => {
  beforeEach(() => {
    cy.login(99999999998);
    cy.selectEntity();
    cy.closeAlerts();

    cy.waitExistsAndClick('[data-cy="cadastros e consultas"]');
    cy.waitExistsAndClick('[data-cy="contrato/aditivo"]');
  });

  it('Create - Tipo Contrato - Licitação', () => {
    cy.waitExistsAndClickText('Novo');

    //Filtra por uma licitacao especifica
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '1111234567');
    cy.get('[id="search-btn-advanced"]').click();
    cy.waitIncludes('1 de 1 registros');
    cy.get('[data-cy="4277016757-selecionar"]').click();

    cy.get('[aria-label="Selecione o contratado(a)"]').click();
    cy.get('[class="p-dropdown-item"]').first().click();

    //Adicionando informações ao componente de vencedores
    cy.get('[class="p-picklist-item"]').first().click();
    cy.get('[class="p-button-icon p-c pi pi-angle-double-right"]').click({ force: true });

    // Editando quantidade do item para 10
    cy.get('[class="p-inputtext p-component p-filled"]').clear().type('10');

    cy.get('[data-cy="4277016757-pi pi-check"]').click();

    cy.waitExistsAndClickText('Avançar');

    //Preenchendo informações básicas
    cy.waitExistsAndType('[id="numero"]', '201');
    cy.get('[id="anoContrato"]').type('2023');
    cy.waitExistsAndType('[id="dataPublicacao"]', '01/01/2023');
    cy.get('[class="p-radiobutton p-component"]').eq(3).click();
    cy.waitExistsAndType('[id="numeroDoe"]', '1022023');
    cy.waitExistsAndType('[id="dataVigenciaFinal"]', '01/12/2050');
    cy.waitExistsAndType('[id="numeroContratoSei"]', '1002024');

    cy.get('[class="p-radiobutton p-component"]').eq(0).click();
    cy.waitExistsAndType('[id="dataVigenciaInicial"]', '01/01/2023');

    //Seleciona autoridade contratante
    cy.waitExistsAndClick('[id="autoridadeContratante"]');
    cy.get('[class="p-dropdown-item"]').first().click();

    //Selecionando fontes de recurso
    cy.get('[id="fontesDeRecurso"]').click();
    cy.get('[class="p-multiselect-item"]').first().click();
    cy.get('[class="p-multiselect-item"]').first().click();

    //Seleciona gestor
    cy.get('[id="search-btn"]').eq(2).click();
    cy.waitExistsAndClick('[class="p-selectable-row"]');
    cy.get('[data-cy="4277016757-selecionar"]').click();

    cy.get('[id="search-btn"]').first().click();
    cy.waitExistsAndClick('[class="p-selectable-row"]');
    cy.get('[data-cy="4277016757-selecionar"]').click();

    const fileTypes = [
      'Certidão negativa de débitos trabalhistas',
      'Certidão negativa de impedimento (CNEP)',
      'Certidão negativa de inidoneidade (CEIS)',
      'Comprovante da divulgação do contrato no Portal Nacional de Contratações Públicas (PNCP)',
      'Comprovante da regularidade fiscal do contratado',
      'Contrato',
      'Designação do gestor e fiscal',
    ];

    //Adicionando pdfs e os tipos aos arquivo
    for (let i = 0; i < 7; i++) {
      cy.get('input[type=file]').selectFile('cypress/fixtures/arquivo.pdf', { force: true });
      cy.waitExists('[aria-label="Selecione um tipo"]');
      cy.get('[aria-label="Selecione um tipo"]').eq(i).click();
      cy.contains('[class="p-dropdown-item"]', fileTypes[i]).click();
    }

    //Avançar e salvar
    cy.waitExistsAndClick('[aria-label="Avançar"]');
    cy.waitExistsAndClick('[aria-label="Salvar"]');

    //Verifica se o registro foi salvo
    cy.waitIncludes('Registro salvo com sucesso!');
  });

  it('Create - Tipo Empenho - Licitação', () => {
    cy.waitExistsAndClickText('Novo');

    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '1111234567');
    cy.get('[id="search-btn-advanced"]').click();
    cy.waitIncludes('1 de 1 registros');
    cy.get('[data-cy="4277016757-selecionar"]').click();

    cy.get('[class="p-radiobutton-box"]').first().click();

    cy.get('[aria-label="Selecione o contratado(a)"]').click();
    cy.get('[class="p-dropdown-item"]').first().click();

    //Adicionando informações ao componente de vencedores
    cy.get('[class="p-picklist-item"]').first().click();
    cy.get('[class="p-button-icon p-c pi pi-angle-double-right"]').click({ force: true });

    // Editando quantidade do item para 10
    cy.get('[class="p-inputtext p-component p-filled"]').clear().type('10');

    cy.waitExistsAndClick('[data-cy="4277016757-pi pi-check"]');

    cy.waitExistsAndClickText('Avançar');

    //Preenchendo informações básicas
    cy.waitExistsAndType('[id="numero"]', '2052023');
    cy.get('[id="dataEmpenho"]').type('01/01/2023');
    cy.get('[aria-label="Ordinário"]').click();
    cy.get('[class="p-radiobutton p-component"]').eq(1).click();
    cy.waitExistsAndType('[id="numeroContratoSei"]', '3062024');

    //Seleciona gestor
    cy.get('[id="search-btn"]').eq(2).click();
    cy.waitExistsAndClick('[class="p-selectable-row"]');
    cy.get('[data-cy="4277016757-selecionar"]').click();

    cy.get('[id="search-btn"]').first().click();
    cy.waitExistsAndClick('[class="p-selectable-row"]');
    cy.get('[data-cy="4277016757-selecionar"]').click();

    //Seleciona autoridade contratante
    cy.waitExistsAndClick('[id="autoridadeContratante"]');
    cy.get('[class="p-dropdown-item"]').first().click();

    //Selecionando fontes de recurso
    cy.get('[id="fontesDeRecurso"]').click();
    cy.get('[class="p-multiselect-item"]').first().click();
    cy.get('[class="p-multiselect-item"]').first().click();

    const fileTypes = [
      'Certidão negativa de débitos trabalhistas',
      'Certidão negativa de impedimento (CNEP)',
      'Certidão negativa de inidoneidade (CEIS)',
      'Comprovante da regularidade fiscal do contratado',
      'Designação do gestor e fiscal',
      'Equivalente de contrato: Empenho',
    ];

    //Adicionando pdfs e os tipos aos arquivo
    for (let i = 0; i < 6; i++) {
      cy.get('input[type=file]').selectFile('cypress/fixtures/arquivo.pdf', { force: true });
      cy.waitExists('[aria-label="Selecione um tipo"]');
      cy.get('[aria-label="Selecione um tipo"]').eq(i).click();
      cy.contains('[class="p-dropdown-item"]', fileTypes[i]).click();
    }

    //Avançar e salvar
    cy.waitExistsAndClick('[aria-label="Avançar"]');
    cy.waitExistsAndClick('[aria-label="Salvar"]');

    //Verifica se o registro foi salvo
    cy.waitIncludes('Registro salvo com sucesso!');
  });

  it('Create - Ordem de Serviço - Licitação', () => {
    cy.waitExistsAndClickText('Novo');

    //Filtra por uma licitacao especifica
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '1111234567');
    cy.waitExistsAndClick('[id="search-btn-advanced"]');
    cy.waitIncludes('1 de 1 registros');
    cy.get('[data-cy="4277016757-selecionar"]').click();

    cy.get('[class="p-radiobutton-box"]').eq(1).click();

    cy.get('[aria-label="Selecione o contratado(a)"]').click();
    cy.get('[class="p-dropdown-item"]').first().click();

    //Adicionando informações ao componente de vencedores
    cy.get('[class="p-picklist-item"]').first().click();
    cy.get('[class="p-button-icon p-c pi pi-angle-double-right"]').click({ force: true });

    // Editando quantidade do item para 10
    cy.get('[class="p-inputtext p-component p-filled"]').clear().type('10');

    cy.waitExistsAndClick('[data-cy="4277016757-pi pi-check"]');

    cy.waitExistsAndClickText('Avançar');

    //Preenchendo informações básicas
    cy.waitExistsAndType('[id="numero"]', '2092023');
    cy.waitExistsAndType('[id="numeroContratoSei"]', '2052023');
    cy.get('[class="p-radiobutton p-component"]').eq(1).click();

    //Seleciona gestor
    cy.get('[id="search-btn"]').eq(2).click();
    cy.waitExistsAndClick('[class="p-selectable-row"]');
    cy.get('[data-cy="4277016757-selecionar"]').click();

    cy.get('[id="search-btn"]').first().click();
    cy.waitExistsAndClick('[class="p-selectable-row"]');
    cy.get('[data-cy="4277016757-selecionar"]').click();

    //Seleciona autoridade contratante
    cy.waitExistsAndClick('[id="autoridadeContratante"]');
    cy.get('[class="p-dropdown-item"]').first().click();

    //Selecionando fontes de recurso
    cy.get('[id="fontesDeRecurso"]').click();
    cy.get('[class="p-multiselect-item"]').first().click();
    cy.get('[class="p-multiselect-item"]').first().click();

    const fileTypes = [
      'Certidão negativa de débitos trabalhistas',
      'Certidão negativa de impedimento (CNEP)',
      'Certidão negativa de inidoneidade (CEIS)',
      'Comprovante da regularidade fiscal do contratado',
      'Designação do gestor e fiscal',
      'Equivalente de contrato: Ordem de Serviço/Entrega',
    ];

    //Adicionando pdfs e os tipos aos arquivo
    for (let i = 0; i < 6; i++) {
      cy.get('input[type=file]').selectFile('cypress/fixtures/arquivo.pdf', { force: true });
      cy.waitExists('[aria-label="Selecione um tipo"]');
      cy.get('[aria-label="Selecione um tipo"]').eq(i).click();
      cy.contains('[class="p-dropdown-item"]', fileTypes[i]).click();
    }

    //Avançar e salvar
    cy.waitExistsAndClick('[aria-label="Avançar"]');
    cy.waitExistsAndClick('[aria-label="Salvar"]');

    //Verifica se o registro foi salvo
    cy.waitIncludes('Registro salvo com sucesso!');
  });

  it('Create - Carta-contrato - Licitação', () => {
    cy.waitExistsAndClickText('Novo');

    //Filtra por uma licitacao especifica
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '1111234567');
    cy.waitExistsAndClick('[id="search-btn-advanced"]');
    cy.waitIncludes('1 de 1 registros');
    cy.get('[data-cy="4277016757-selecionar"]').click();

    cy.get('[class="p-radiobutton-box"]').eq(2).click();

    cy.get('[aria-label="Selecione o contratado(a)"]').click();
    cy.get('[class="p-dropdown-item"]').first().click();

    //Adicionando informações ao componente de vencedores
    cy.get('[class="p-picklist-item"]').first().click();
    cy.get('[class="p-button-icon p-c pi pi-angle-double-right"]').click({ force: true });

    // Editando quantidade do item para 10
    cy.get('[class="p-inputtext p-component p-filled"]').clear().type('10');

    cy.waitExistsAndClick('[data-cy="4277016757-pi pi-check"]');

    cy.waitExistsAndClickText('Avançar');

    //Preenchendo informações básicas
    cy.waitExistsAndType('[id="numero"]', '1072024');
    cy.waitExistsAndType('[id="numeroContratoSei"]', '2012023');
    cy.get('[class="p-radiobutton p-component"]').eq(1).click();
    cy.waitExistsAndType('[id="dataVigenciaFinal"]', '01/12/2050');
    cy.waitExistsAndType('[id="dataVigenciaInicial"]', '01/01/2023');

    //Seleciona gestor
    cy.get('[id="search-btn"]').eq(2).click();
    cy.waitExistsAndClick('[class="p-selectable-row"]');
    cy.get('[data-cy="4277016757-selecionar"]').click();

    cy.get('[id="search-btn"]').first().click();
    cy.waitExistsAndClick('[class="p-selectable-row"]');
    cy.get('[data-cy="4277016757-selecionar"]').click();

    //Seleciona autoridade contratante
    cy.waitExistsAndClick('[id="autoridadeContratante"]');
    cy.get('[class="p-dropdown-item"]').first().click();

    //Selecionando fontes de recurso
    cy.get('[id="fontesDeRecurso"]').click();
    cy.get('[class="p-multiselect-item"]').first().click();
    cy.get('[class="p-multiselect-item"]').first().click();

    const fileTypes = [
      'Certidão negativa de débitos trabalhistas',
      'Certidão negativa de impedimento (CNEP)',
      'Certidão negativa de inidoneidade (CEIS)',
      'Comprovante da regularidade fiscal do contratado',
      'Designação do gestor e fiscal',
      'Equivalente de contrato: Carta-contrato',
    ];

    //Adicionando pdfs e os tipos aos arquivo
    for (let i = 0; i < 6; i++) {
      cy.get('input[type=file]').selectFile('cypress/fixtures/arquivo.pdf', { force: true });
      cy.waitExists('[aria-label="Selecione um tipo"]');
      cy.get('[aria-label="Selecione um tipo"]').eq(i).click();
      cy.contains('[class="p-dropdown-item"]', fileTypes[i]).click();
    }

    //Avançar e salvar
    cy.waitExistsAndClick('[aria-label="Avançar"]');
    cy.waitExistsAndClick('[aria-label="Salvar"]');

    //Verifica se o registro foi salvo
    cy.waitIncludes('Registro salvo com sucesso!');
  });

  it('Read', () => {
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '201/2023');
    cy.get('[id="search-btn-advanced"]').click();
    cy.waitIncludes('201/2023');

    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '2052023');
    cy.get('[id="search-btn-advanced"]').click();
    cy.waitIncludes('2052023');

    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '2092023');
    cy.get('[id="search-btn-advanced"]').click();
    cy.waitIncludes('2092023');

    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '1072024');
    cy.get('[id="search-btn-advanced"]').click();
    cy.waitIncludes('1072024');
  });

  it('Update - Tipo Contrato - Licitação', () => {
    //Filtra por uma licitacao especifica
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '201/2023');
    cy.get('[id="search-btn-advanced"]').click();
    cy.waitIncludes('201/2023');

    cy.waitExistsAndClick('[class="p-button-icon p-c pi pi-list"]');
    cy.get('[class="p-menuitem"]').eq(3).click();

    //Preenchendo informações básicas
    cy.waitExistsAndType('[id="numeroContratoSei"]', '2012023');
    cy.waitExistsAndType('[id="numeroDoe"]', '2012023');

    //Seleciona autoridade contratante
    cy.waitExistsAndClick('[id="autoridadeContratante"]');
    cy.get('[class="p-dropdown-item"]').first().click();

    //Avançar e salvar
    cy.waitExistsAndClick('[aria-label="Avançar"]');
    cy.waitExistsAndClick('[aria-label="Salvar"]');

    //Verifica se o registro foi salvo
    cy.waitIncludes('Registro salvo com sucesso!');
  });

  it('Update - Tipo Empenho - Licitação', () => {
    //Filtra por uma licitacao especifica
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '2052023');
    cy.get('[id="search-btn-advanced"]').click();
    cy.waitIncludes('2052023');

    cy.waitExistsAndClick('[class="p-button-icon p-c pi pi-list"]');
    cy.get('[class="p-menuitem"]').eq(3).click();

    //Preenchendo informações básicas
    cy.waitExistsAndType('[id="numeroContratoSei"]', '2052023');

    //Seleciona autoridade contratante
    cy.waitExistsAndClick('[id="autoridadeContratante"]');
    cy.get('[class="p-dropdown-item"]').first().click();

    //Avançar e salvar
    cy.waitExistsAndClick('[aria-label="Avançar"]');
    cy.waitExistsAndClick('[aria-label="Salvar"]');

    //Verifica se o registro foi salvo
    cy.waitIncludes('Registro salvo com sucesso!');
  });

  it('Update - Tipo Ordem de Serviço - Licitação', () => {
    //Filtra por uma licitacao especifica
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '2092023');
    cy.get('[id="search-btn-advanced"]').click();
    cy.waitIncludes('2092023');

    cy.waitExistsAndClick('[class="p-button-icon p-c pi pi-list"]');
    cy.get('[class="p-menuitem"]').eq(3).click();

    //Preenchendo informações básicas
    cy.waitExistsAndType('[id="numeroContratoSei"]', '2092023');

    //Seleciona autoridade contratante
    cy.waitExistsAndClick('[id="autoridadeContratante"]');
    cy.get('[class="p-dropdown-item"]').first().click();

    //Avançar e salvar
    cy.waitExistsAndClick('[aria-label="Avançar"]');
    cy.waitExistsAndClick('[aria-label="Salvar"]');

    //Verifica se o registro foi salvo
    cy.waitIncludes('Registro salvo com sucesso!');
  });

  it('Update - Carta-contrato - Licitação', () => {
    //Filtra por uma licitacao especifica
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '1072024');
    cy.get('[id="search-btn-advanced"]').click();
    cy.waitIncludes('1072024');

    cy.waitExistsAndClick('[class="p-button-icon p-c pi pi-list"]');
    cy.get('[class="p-menuitem"]').eq(3).click();

    //Preenchendo informações básicas
    cy.waitExistsAndType('[id="numeroContratoSei"]', '1072024');

    //Seleciona autoridade contratante
    cy.waitExistsAndClick('[id="autoridadeContratante"]');
    cy.get('[class="p-dropdown-item"]').first().click();

    //Avançar e salvar
    cy.waitExistsAndClick('[aria-label="Avançar"]');
    cy.waitExistsAndClick('[aria-label="Salvar"]');

    //Verifica se o registro foi salvo
    cy.waitIncludes('Registro salvo com sucesso!');
  });

  it('Cria Empenho - Tipo Contrato - Licitação', () => {
    //Filtra por uma licitacao especifica
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '201/2023');
    cy.get('[id="search-btn-advanced"]').click();
    cy.waitIncludes('201/2023');

    cy.waitExistsAndClick('[class="p-button-icon p-c pi pi-list"]');
    cy.get('[class="p-menuitem"]').eq(1).click();

    cy.waitExistsAndClickText('Novo');

    //Preenchendo informações básicas
    cy.waitExistsAndType('[id="numeroEmpenho"]', '2012023');
    cy.waitExistsAndType('[id="dataEmpenho"]', '09/08/2023');
    cy.waitExistsAndType('[id="valorEmpenho"]', '100,00');

    //Adicionando arquivo pdf
    cy.get('input[type=file]').selectFile('cypress/fixtures/arquivo.pdf', { force: true });
    cy.waitExists('[aria-label="Selecione um tipo"]');
    cy.get('[aria-label="Selecione um tipo"]').first().click();
    cy.contains('[class="p-dropdown-item"]', 'Nota de empenho').click();

    cy.waitExistsAndClick('[aria-label="Salvar"]');

    //Verifica se o registro foi salvo
    cy.waitIncludes('Registro salvo com sucesso!');
  });

  it('Cria Rescisão Contratual - Tipo Contrato - Licitação', () => {
    //Filtra por uma licitacao especifica
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '201/2023');
    cy.get('[id="search-btn-advanced"]').click();
    cy.waitIncludes('201/2023');

    cy.waitExistsAndClick('[class="p-button-icon p-c pi pi-list"]');
    cy.get('[class="p-menuitem"]').eq(4).click();

    cy.waitExistsAndClickText('Sim');
    
    //Preenchendo informações básicas
    cy.waitExistsAndType('[id="dataAvisoRescisao"]', '29/07/2023');
    cy.waitExistsAndClick('[id="motivoRescisao"]');
    cy.waitExistsAndClick('[aria-label="Rescisão"]');
    cy.waitExistsAndType('[id="descricaoRescisao"]', 'teste cypress');

    cy.waitExistsAndClick('[aria-label="Enviar"]');

    //Verifica se o registro foi salvo
    cy.waitIncludes('Registro salvo com sucesso!');
  });

  it('Delete - Licitação', () => {
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '2052023');
    cy.get('[id="search-btn-advanced"]').click();
    cy.waitIncludes('2052023');

    cy.waitExistsAndClick('[class="p-button-icon p-c pi pi-list"]');
    cy.get('[class="p-menuitem"]').eq(5).click();

    cy.waitExistsAndType('[id="justificativa"]', 'teste');
    cy.waitExistsAndClick('[aria-label="Enviar"]');

    //Verifica se o registro foi salvo
    cy.waitIncludes('Requisição salva com sucesso!');
  });
});
