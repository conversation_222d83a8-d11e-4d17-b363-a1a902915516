describe('CRUD Dispensa', () => {
  beforeEach(() => {
    cy.login(99999999998);
    cy.selectEntity();
    cy.closeAlerts();

    cy.get('[data-cy="cadastros e consultas"]').click();
    cy.waitExistsAndClick('[data-cy="dispensa"]');
  });

  it('Create - Dispensa N° 8.666', () => {
    // Acessa tela de dispensa
    cy.get('[data-cy="cadastros e consultas"]').click();
    cy.waitExistsAndClick('[data-cy="dispensa"]');

    //Clica para criar um novo registro
    cy.waitExistsAndClickText('Novo');
    // Aguardar e clicar no botão "Sim" do diálogo de confirmação
    cy.get('.p-confirm-dialog-accept').click();

    //Preenchendo dados basicos
    cy.findByRole('button', { name: 'Lei Nº 8.666' }).click();
    cy.get('[class="p-radiobutton p-component"]').eq(0).click();
    cy.waitExistsAndType('[id="numeroProcessoSEI"]', '12345');
    cy.get('[id="anoDispensa"]').type('2024');
    cy.waitExistsAndType('[id="numeroProcesso"]', '12345');
    cy.waitExistsAndType('[id="dataPedido"]', '01/01/2023');
    cy.waitExistsAndClick('[id="responsavelDispensa"]');
    cy.get('[class="p-dropdown-item"]').first().click();
    cy.waitExistsAndType('[id="objeto"]', 'Teste cypress tela de dispensa');

    //Seleciona a fundamentação legal
    cy.waitExistsAndClick('[aria-label="Selecione a fundamentação legal"]');
    cy.waitExistsAndClick('[aria-label="Art. 24, inciso I, da Lei nº 8.666/1993"]');

    //Seleciona as naturezas do objeto
    cy.waitExistsAndClick('[id="naturezasDoObjeto"]');
    cy.get('[class="p-multiselect-item"]').first().click();

    //Seleciona fontes de recurso
    cy.waitExistsAndClick('[id="fontesDeRecurso"]');
    cy.get('[class="p-multiselect-item"]').first().click();

    const fileTypes = [
      'Documentos de habilitação e qualificação do fornecedor ou executante',
      'Justificativa da necessidade do objeto',
      'Justificativa da situação de dispensa com os elementos necessários à sua caracterização',
      'Justificativa do preço do fornecedor ou executante, incluindo pesquisa de preço de mercado, pesquisa de preço praticado por Órgãos e Entidades da Administração Pública e Mapa Comparativo de Preços',
      'Parecer jurídico demonstrando o atendimento dos requisitos exigidos para a dispensa de licitação',
      'Proposta formal oferecida pelo contratado',
      'Publicação do termo ou ato de ratificação da dispensa de licitação',
      'Razões da escolha do fornecedor ou executante',
      'Termo de Referência ou Projeto Básico',
    ];

    //Adicionando pdfs e os tipos aos arquivo
    for (let i = 0; i < 9; i++) {
      cy.get('input[type=file]').selectFile('cypress/fixtures/arquivo.pdf', { force: true });
      cy.waitExists('[aria-label="Selecione um tipo"]');
      cy.get('[aria-label="Selecione um tipo"]').eq(i).click();
      cy.contains('[class="p-dropdown-item"]', fileTypes[i]).click();
    }

    cy.waitExistsAndClick('[aria-label="Avançar"]');

    //Adicionando fornecedores
    cy.waitExistsAndClick('[aria-label="Adicionar"]');
    cy.get('[class="p-selection-column"]').first().click();
    cy.waitExistsAndClickText('Selecionar');

    //Clica e escolhe fornecedores participantes da fase
    cy.findByRole('button', { name: 'Adicionar Fornecedor' }).click();
    cy.get('[aria-label="Selecione um fornecedor"]').click();
    cy.get('[class="p-dropdown-item"]').first().click();

    cy.get('[id="valor"]').type('222');
    cy.get('[class="p-button p-component"]').eq(2).click({ force: true });

    cy.findByRole('button', { name: 'Avançar' }).click();
    cy.waitExistsAndClick('[aria-label="Salvar"]');

    cy.get('[class="data-card p-4"]').first().should('contain', '12345');
  });

  it('Read', () => {
    //Acessa uma dispensa
    cy.waitExists('[class="data-card p-4"]');
    cy.get('[data-cy="2643273457-mais opcoes"]').first().click();
    cy.get('[class="p-menuitem-icon pi pi-eye"]').click();

    //Volta para a listagem e acessa novamente a mesma dispensa
    cy.waitExistsAndClick('[class="pi pi-lock tab-icon-lock"]');
    cy.get('[data-cy="2643273457-mais opcoes"]').first().click();
    cy.waitExistsAndClick('[class="p-menuitem-icon pi pi-eye"]');

    //Apenas um icone de fechar deve ser encontrado, se der erro é porque abriu a visualização da dispensa duplicada
    cy.waitExistsAndClick('[class="pi pi-times tab-icon-times"]');

    //Observa o valor do processo SEI da segunda dispensa no cardList e verifica se é igual nos detalhes, garantindo que o processo certo é aberto
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '1469/2023');
    cy.get('[id="search-btn-advanced"]').click();

    cy.waitExists('[class="data-card p-4"]');
    cy.get('[class="data-card p-4"]').first().should('contain', '1469/2023');
    cy.get('[data-cy="2643273457-mais opcoes"]').first().click();
    cy.get('[class="p-menuitem-icon pi pi-eye"]').click();

    cy.waitExists('[class="data-card p-4"]');
    cy.get('body').should('contain', '1469/2023');

    //Garante que mesmo com um processo aberto, quando eu tentar abrir outro processo o redirecionamento vai ser certo
    cy.get('[class="pi pi-lock tab-icon-lock"]').click();

    //Limpa filtro
    cy.get('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]').clear();
    cy.get('[id="search-btn-advanced"]').click();

    //Busca pelo processo
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '12345');
    cy.get('[id="search-btn-advanced"]').click();

    cy.waitExists('[class="data-card p-4"]');
    cy.get('[class="data-card p-4"]').first().should('contain', '12345');
    cy.get('[data-cy="2643273457-mais opcoes"]').first().click();
    cy.get('[class="p-menuitem-icon pi pi-eye"]').click();
    cy.waitExists('[class="data-card p-4"]');
    cy.get('body').should('contain', '12345');

    cy.get('[class="pi pi-times tab-icon-times"]').first().click();
    cy.get('[class="pi pi-times tab-icon-times"]').click();
  });

  it('Update - Dispensa N° 8.666', () => {
    //Busca pela dispensa
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', 'Teste cypress tela de dispensa');
    cy.get('[id="search-btn-advanced"]').click();
    cy.waitExists('[class="data-card p-4"]');

    //edição simples
    cy.get('[data-cy="2643273457-mais opcoes"]').first().click();
    cy.get('[class="p-menuitem-icon pi pi-pencil"]').click();

    //Edita objeto da dispensa
    cy.waitExistsAndType('[id="objeto"]', 'Teste cypress tela de dispensa alteracao');

    //Avança as demais etapas e salva a edição
    cy.waitExistsAndClick('[aria-label="Avançar"]');
    cy.waitExistsAndClick('[aria-label="Avançar"]');
    cy.waitExistsAndClick('[aria-label="Salvar"]');

    //Verica se a alteracao foi persistida
    cy.waitExists('[class="data-card p-4"]');
    cy.get('body').should('contain', 'Dispensa editada com sucesso!');

    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '1469/2023');
    cy.get('[id="search-btn-advanced"]').click();
    cy.waitExists('[class="data-card p-4"]');

    //Edição via Requsição de modificação
    cy.get('[data-cy="2643273457-mais opcoes"]').first().click();
    cy.get('[class="p-menuitem-icon pi pi-pencil"]').click();

    const fileTypes = [
      'Documentos de habilitação e qualificação do fornecedor ou executante',
      'Justificativa da necessidade do objeto',
      'Justificativa da situação de dispensa com os elementos necessários à sua caracterização',
      'Justificativa do preço do fornecedor ou executante, incluindo pesquisa de preço de mercado, pesquisa de preço praticado por Órgãos e Entidades da Administração Pública e Mapa Comparativo de Preços',
      'Parecer jurídico demonstrando o atendimento dos requisitos exigidos para a dispensa de licitação',
      'Proposta formal oferecida pelo contratado',
      'Publicação do termo ou ato de ratificação da dispensa de licitação',
      'Razões da escolha do fornecedor ou executante',
      'Termo de Referência ou Projeto Básico',
    ];

    //Adicionando pdfs e os tipos aos arquivo
    cy.get('input[type=file]').selectFile('cypress/fixtures/arquivo.pdf', { force: true });
    cy.waitExists('[aria-label="Selecione um tipo"]');
    cy.get('[aria-label="Selecione um tipo"]').eq(9).click();
    cy.contains('[class="p-dropdown-item"]', fileTypes[0]).click();

    //Avança as demais etapas e salva a edição
    cy.waitExistsAndClick('[aria-label="Avançar"]');
    cy.waitExistsAndClick('[aria-label="Avançar"]');

    cy.waitExistsAndClick('[data-cy="202843497-enviar requisicao"]');
    cy.waitExistsAndType('[id="justificativa"]', 'teste');
    cy.get('[data-cy="202843497-enviar requisicao"]').eq(1).click();

    //Acessando a requisição de modificação oq garante que ela foi criada
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '1469/2023');
    cy.get('[id="search-btn-advanced"]').click();
    cy.waitExists('[class="data-card p-4"]');

    cy.get('[data-cy="2643273457-mais opcoes"]').first().click();
    cy.get('[class="p-menuitem-icon pi pi-exclamation-triangle"]').click();
  });

  it('Create - Dispensa N° 14.133', () => {
    //Clica para criar um novo registro
    cy.waitExistsAndClickText('Novo');
    // Aguardar e clicar no botão "Sim" do diálogo de confirmação
    cy.get('.p-confirm-dialog-accept').click();

    //Preenchendo dados basicos
    cy.get('[class="p-radiobutton p-component"]').eq(0).click();
    cy.get('[id="numeroProcessoSEI"]').type('54321');
    cy.get('[id="anoDispensa"]').type('2024');
    cy.get('[id="numeroProcesso"]').type('54321');
    cy.get('[id="dataPedido"]').type('01/01/2023');
    cy.waitExistsAndClick('[id="responsavelDispensa"]');
    cy.get('[class="p-dropdown-item"]').first().click();
    cy.get('[id="objeto"]').type('Teste cypress tela de dispensa lei 14333');

    //Seleciona o termo
    cy.get('[data-cy="1220839576-pi pi-search"]').click();
    cy.get('[class="p-radiobutton p-component"]').eq(2).first().click();
    cy.waitExistsAndClickText('Confirmar');

    //Seleciona a fundamentação legal
    cy.waitExistsAndClick('[aria-label="Selecione a fundamentação legal"]');
    cy.waitExistsAndClick('[aria-label="Art. 75, inciso I, da Lei nº 14.133/2021"]');

    //Seleciona as naturezas do objeto
    cy.waitExistsAndClick('[id="naturezasDoObjeto"]');
    cy.get('[class="p-multiselect-item"]').first().click();

    //Seleciona fontes de recurso
    cy.waitExistsAndClick('[id="fontesDeRecurso"]');
    cy.get('[class="p-multiselect-item"]').first().click();

    const fileTypes = [
      'Autorização da autoridade competente',
      'Comprovante da divulgação, em sítio eletrônico oficial, do ato que autoriza a contratação direta ou o extrato decorrente do contrato',
      'Demonstração da compatibilidade da previsão de recursos orçamentários com o compromisso a ser assumido',
      'Documento de formalização de demanda',
      'Documentos de habilitação e qualificação do contratado',
      'Justificativa do preço do contratado',
      'Mapa Comparativo de Preços',
      'Parecer jurídico demonstrando o atendimento dos requisitos exigidos para a dispensa de licitação',
      'Pesquisa de preço de mercado',
      'Pesquisa de preço praticado por Órgãos e Entidades da Administração Pública',
      'Proposta formal oferecida pelo contratado',
      'Razões da escolha do contratado',
    ];
    //Adicionando pdfs e os tipos aos arquivo
    for (let i = 0; i < 12; i++) {
      cy.get('input[type=file]').selectFile('cypress/fixtures/arquivo.pdf', { force: true });
      cy.waitExists('[aria-label="Selecione um tipo"]');
      cy.get('[aria-label="Selecione um tipo"]').eq(i).click();
      cy.contains('[class="p-dropdown-item"]', fileTypes[i]).click();
    }

    cy.waitExistsAndClickText('Avançar');

    //Adicionando fornecedores
    cy.waitExistsAndClick('[aria-label="Adicionar"]');
    cy.get('[class="p-selection-column"]').first().click();
    cy.waitExistsAndClickText('Selecionar');

    //Adicionando informação ao componente de vencedores
    cy.get('[aria-label="Selecione um Licitante"]').click();
    cy.get('[class="p-dropdown-item"]').first().click();

    cy.get('[class="p-picklist-item"]').first().click();
    cy.get('[class="p-button-icon p-c pi pi-angle-double-right"]').click({ force: true });

    cy.get('[id="dropdown-lote"]').click();
    cy.contains('[class="p-dropdown-item"]', 'LOTE PL 1.1').click();

    cy.get('[class="p-picklist-item"]').first().click();
    cy.get('[class="p-button-icon p-c pi pi-angle-double-right"]').click({ force: true });

    cy.get('[placeholder="Informe a Marca/Modelo"]').first().type('cypress teste');

    cy.get('[data-cy="1220839576-pi pi-check"]').first().click();

    cy.get('[placeholder="Informe a Marca/Modelo"]').type('cypress teste');

    cy.get('[data-cy="1220839576-pi pi-check"]').first().click();

    cy.waitExistsAndClick('[aria-label="Avançar"]');
    cy.waitExistsAndClick('[aria-label="Salvar"]');
    cy.waitExists('[class="data-card p-4"]');

    //Verifica se a carona foi criada, se foi ela corresponde ao primeiro card e não da erro
    cy.get('[class="data-card p-4"]').first().should('contain', '54321');
  });

  it('Update - Dispensa N° 14.133', () => {
    //Busca pela dispensa
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', 'Teste cypress tela de dispensa lei 14333');
    cy.get('[id="search-btn-advanced"]').click();
    cy.waitExists('[class="data-card p-4"]');

    //edição simples
    cy.get('[data-cy="2643273457-mais opcoes"]').first().click();
    cy.get('[class="p-menuitem-icon pi pi-pencil"]').click();

    //Edita objeto da dispensa
    cy.waitExistsAndType('[id="objeto"]', 'Teste cypress tela de dispensa lei 14333 alteracao');

    //Avança as demais etapas e salva a edição
    cy.waitExistsAndClick('[aria-label="Avançar"]');
    cy.waitExistsAndClick('[aria-label="Avançar"]');
    cy.waitExistsAndClick('[aria-label="Salvar"]');

    //Verica se a alteracao foi persistida
    cy.waitExists('[class="data-card p-4"]');
    cy.get('body').should('contain', 'Dispensa editada com sucesso!');

    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '1469/2023');
    cy.get('[id="search-btn-advanced"]').click();
    cy.waitExists('[class="data-card p-4"]');
  });

  it('Create - Dispensa LEI OUTRA', () => {
    //Clica para criar um novo registro
    cy.waitExistsAndClickText('Novo');
    // Aguardar e clicar no botão "Sim" do diálogo de confirmação
    cy.get('.p-confirm-dialog-accept').click();

    //Preenchendo dados basicos
    cy.findByRole('button', { name: 'Outra' }).click();
    cy.get('[id="legislacaoOutros"]').type('666');
    cy.get('[class="p-radiobutton p-component"]').eq(0).click();
    cy.get('[id="numeroProcessoSEI"]').type('98765');
    cy.get('[id="anoDispensa"]').type('2024');
    cy.get('[id="numeroProcesso"]').type('98765');
    cy.get('[id="dataPedido"]').type('01/01/2023');
    cy.waitExistsAndClick('[id="responsavelDispensa"]');
    cy.get('[class="p-dropdown-item"]').first().click();
    cy.get('[id="objeto"]').type('Teste cypress tela de dispensa lei OUTRA');

    //Seleciona o termo
    cy.get('[data-cy="1220839576-pi pi-search"]').click();
    cy.get('[class="p-radiobutton p-component"]').eq(2).first().click();
    cy.waitExistsAndClickText('Confirmar');

    //Seleciona a fundamentação legal
    cy.waitExistsAndClick('[aria-label="Selecione a fundamentação legal"]');
    cy.waitExistsAndClick('[aria-label="Art. 1°"]');

    //Seleciona as naturezas do objeto
    cy.waitExistsAndClick('[id="naturezasDoObjeto"]');
    cy.get('[class="p-multiselect-item"]').first().click();

    //Seleciona fontes de recurso
    cy.waitExistsAndClick('[id="fontesDeRecurso"]');
    cy.get('[class="p-multiselect-item"]').first().click();

    const fileTypes = [
      'Documentos de habilitação e qualificação do fornecedor ou executante',
      'Justificativa da necessidade do objeto',
      'Justificativa da situação de dispensa com os elementos necessários à sua caracterização',
      'Justificativa do preço do fornecedor ou executante',
      'Mapa Comparativo de Preços',
      'Parecer jurídico demonstrando o atendimento dos requisitos exigidos para a dispensa de licitação',
      'Pesquisa de preço de mercado',
      'Pesquisa de preço praticado por Órgãos e Entidades da Administração Pública',
      'Proposta formal oferecida pelo contratado',
      'Publicação do termo ou ato de ratificação da dispensa de licitação',
      'Razões da escolha do fornecedor ou executante',
    ];
    //Adicionando pdfs e os tipos aos arquivo
    for (let i = 0; i < 11; i++) {
      cy.get('input[type=file]').selectFile('cypress/fixtures/arquivo.pdf', { force: true });
      cy.waitExists('[aria-label="Selecione um tipo"]');
      cy.get('[aria-label="Selecione um tipo"]').eq(i).click();
      cy.contains('[class="p-dropdown-item"]', fileTypes[i]).click();
    }

    cy.waitExistsAndClickText('Avançar');

    //Adicionando fornecedores
    cy.waitExistsAndClick('[aria-label="Adicionar"]');
    cy.get('[class="p-selection-column"]').first().click();
    cy.waitExistsAndClickText('Selecionar');

    //Adicionando informação ao componente de vencedores
    cy.get('[aria-label="Selecione um Licitante"]').click();
    cy.get('[class="p-dropdown-item"]').first().click();

    cy.get('[class="p-picklist-item"]').first().click();
    cy.get('[class="p-button-icon p-c pi pi-angle-double-right"]').click({ force: true });

    cy.get('[id="dropdown-lote"]').click();
    cy.contains('[class="p-dropdown-item"]', 'LOTE PL 1.1').click();

    cy.get('[class="p-picklist-item"]').first().click();
    cy.get('[class="p-button-icon p-c pi pi-angle-double-right"]').click({ force: true });

    cy.get('[placeholder="Informe a Marca/Modelo"]').first().type('cypress teste');

    cy.get('[data-cy="1220839576-pi pi-check"]').first().click();

    cy.get('[placeholder="Informe a Marca/Modelo"]').type('cypress teste');

    cy.get('[data-cy="1220839576-pi pi-check"]').first().click();

    cy.waitExistsAndClick('[aria-label="Avançar"]');
    cy.waitExistsAndClick('[aria-label="Salvar"]');
    cy.waitExists('[class="data-card p-4"]');

    //Verifica se a carona foi criada, se foi ela corresponde ao primeiro card e não da erro
    cy.get('[class="data-card p-4"]').first().should('contain', '98765');
  });

  it('Delete - Dispensa N° 8.666', () => {
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '12345');
    cy.get('[id="search-btn-advanced"]').click();
    cy.waitExists('[class="data-card p-4"]');

    //Selecionando a Dispensa a ser removida
    cy.get('[data-cy="2643273457-mais opcoes"]').first().click();
    cy.get('[class="p-menuitem-icon pi pi-trash"]').click();

    //Adicionando uma justificativa a requisição de remoção e clicando em enviar
    cy.waitExistsAndType('[id="justificativa"]', 'teste');
    cy.waitExistsAndClickText('Enviar');
    cy.waitExists('[class="data-card p-4"]');

    //Acessando a requisição de remoção oq garante que ela foi criada
    cy.get('[data-cy="2643273457-mais opcoes"]').first().click();
    cy.get('[class="p-menuitem-icon pi pi-exclamation-triangle"]').click();
  });

  it('Delete - Dispensa N° 14.133', () => {
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '54321');
    cy.get('[id="search-btn-advanced"]').click();
    cy.waitExists('[class="data-card p-4"]');

    //Selecionando a Dispensa a ser removida
    cy.get('[data-cy="2643273457-mais opcoes"]').first().click();
    cy.get('[class="p-menuitem-icon pi pi-trash"]').click();

    //Adicionando uma justificativa a requisição de remoção e clicando em enviar
    cy.waitExistsAndType('[id="justificativa"]', 'teste');
    cy.waitExistsAndClickText('Enviar');
    cy.waitExists('[class="data-card p-4"]');

    //Acessando a requisição de remoção oq garante que ela foi criada
    cy.get('[data-cy="2643273457-mais opcoes"]').first().click();
    cy.get('[class="p-menuitem-icon pi pi-exclamation-triangle"]').click();
  });

  it('Delete - Dispensa Lei Outra', () => {
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '98765');
    cy.get('[id="search-btn-advanced"]').click();
    cy.waitExists('[class="data-card p-4"]');

    //Selecionando a Dispensa a ser removida
    cy.get('[data-cy="2643273457-mais opcoes"]').first().click();
    cy.get('[class="p-menuitem-icon pi pi-trash"]').click();

    //Adicionando uma justificativa a requisição de remoção e clicando em enviar
    cy.waitExistsAndType('[id="justificativa"]', 'teste');
    cy.waitExistsAndClickText('Enviar');
    cy.waitExists('[class="data-card p-4"]');

    //Acessando a requisição de remoção oq garante que ela foi criada
    cy.get('[data-cy="2643273457-mais opcoes"]').first().click();
    cy.get('[class="p-menuitem-icon pi pi-exclamation-triangle"]').click();
  });
});
