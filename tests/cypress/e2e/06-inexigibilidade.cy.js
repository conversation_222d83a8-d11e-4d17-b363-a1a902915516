describe('CRUD Inexigibilidade', () => {
  beforeEach(() => {
    cy.login(99999999998);
    cy.selectEntity();
    cy.closeAlerts();

    cy.get('[data-cy="cadastros e consultas"]').click();
    cy.waitExistsAndClick('[data-cy="inexigibilidade"]');
  });

  it('Create - Inexigibilidade Lei Nova N° 14.133', () => {
    //Clica para criar um novo registro
    cy.waitExistsAndClickText('Novo');
    // Aguardar e clicar no botão "Sim" do diálogo de confirmação
    cy.get('.p-confirm-dialog-accept').click();

    //Preenchendo dados basicos
    cy.get('[class="p-radiobutton p-component"]').eq(0).click();
    cy.waitExistsAndType('[id="numeroSei"]', '14133');
    cy.waitExistsAndType('[id="numeroProcesso"]', '14133');
    cy.get('[id="anoInexigibilidade"]').type('2024');
    cy.waitExistsAndType('[id="dataPedido"]', '06/03/2024');
    cy.waitExistsAndClick('[id="responsavelInexigibilidade"]');
    cy.get('[class="p-dropdown-item"]').first().click();

    //Seleciona o termo
    cy.waitExistsAndClick('[data-cy="4125573996-pi pi-search"]');
    cy.get('[class="p-radiobutton p-component"]').eq(2).first().click();
    cy.waitExistsAndClickText('Confirmar');

    //Seleciona a fundamentação legal
    cy.waitExistsAndClick('[id="fundamentacaoLegalEntidade"]');
    cy.get('[aria-label="Art. 74, inciso I, da Lei nº 14.133/2021"]').click();

    //Seleciona Naturezas do objeto
    cy.get('[id="naturezasDoObjeto"]').click();
    cy.get('[class="p-multiselect-item"]').first().click();

    //Seleciona fontes de recurso
    cy.get('[id="fontesDeRecurso"]').click();
    cy.get('[class="p-multiselect-item"]').first().click();
    cy.get('[class="p-multiselect-item"]').first().click();

    cy.waitExistsAndType('[id="objeto"]', 'Teste cypress tela de inexigibilidade lei nova');

    const fileTypes = [
      'Autorização da autoridade competente',
      'Comprovante da divulgação, em sítio eletrônico oficial, do ato que autoriza a contratação direta ou extrato decorrente do contrato',
      'Demonstração da compatibilidade da previsão de recursos orçamentários com o compromisso a ser assumido',
      'Documento de formalização de demanda',
      'Documentos de habilitação e qualificação do contratado',
      'Justificativa do preço do contratado',
      'Mapa Comparativo de Preços',
      'Parecer jurídico demonstrando o atendimento dos requisitos exigidos para a inexigibilidade de licitação',
      'Pesquisa de preço de mercado',
      'Pesquisa de preço praticado por Órgãos e Entidades da Administração Pública',
      'Proposta formal oferecida pelo contratado',
      'Razões da escolha do contratado',
    ];

    //Adicionando pdfs e os tipos aos arquivo
    for (let i = 0; i < 12; i++) {
      cy.get('input[type=file]').selectFile('cypress/fixtures/arquivo.pdf', { force: true });
      cy.waitExists('[aria-label="Selecione um tipo"]');
      cy.get('[aria-label="Selecione um tipo"]').eq(i).click();
      cy.contains('[class="p-dropdown-item"]', fileTypes[i]).click();
    }

    cy.waitExistsAndClickText('Avançar');

    //Adicionando fornecedores e Vencedores
    cy.waitExistsAndClick('[aria-label="Adicionar"]');
    cy.get('[class="p-selection-column"]').first().click();
    cy.waitExistsAndClickText('Selecionar');

    //Adicionando informação ao componente de vencedores
    cy.get('[aria-label="Selecione um Licitante"]').click();
    cy.get('[class="p-dropdown-item"]').first().click();

    cy.get('[class="p-picklist-item"]').first().click();
    cy.get('[class="p-button-icon p-c pi pi-angle-double-right"]').click({ force: true });

    cy.get('[placeholder="Informe a Marca/Modelo"]').first().type('cypress teste');

    cy.get('[data-cy="4125573996-pi pi-check"]').first().click();

    cy.waitExistsAndClick('[aria-label="Avançar"]');
    cy.waitExistsAndClick('[aria-label="Salvar"]');
    cy.waitExists('[class="data-card p-4"]');

    //Verifica se a inexigibilidade foi criada, se foi ela corresponde ao primeiro card e não da erro
    cy.get('[class="data-card p-4"]').first().should('contain', '14133');
  });

  it('Read', () => {
    //Acessa uma inexigibilidade
    cy.waitExists('[class="data-card p-4"]');
    cy.get('[data-cy="3253598109-mais opcoes"]').first().click();
    cy.get('[class="p-menuitem-icon pi pi-eye"]').click();

    //Volta para a listagem e acessa novamente a mesma inexigibilidade
    cy.waitExistsAndClick('[class="pi pi-lock tab-icon-lock"]');
    cy.get('[data-cy="3253598109-mais opcoes"]').first().click();
    cy.get('[class="p-menuitem-icon pi pi-eye"]').click();

    //Apenas um icone de fechar deve ser encontrado, se der erro é porque abriu a visualização da inexigibilidade duplicada
    cy.waitExistsAndClick('[class="pi pi-times tab-icon-times"]');

    //Observa o valor do processo SEI da segunda inexigibilidade no cardList e verifica se é igual nos detalhes, garantindo que o processo certo é aberto
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '1357/2023');
    cy.get('[id="search-btn-advanced"]').click();
    cy.waitExists('[class="data-card p-4"]');

    cy.get('[class="data-card p-4"]').first().should('contain', '1357/2023');
    cy.get('[data-cy="3253598109-mais opcoes"]').first().click();
    cy.get('[class="p-menuitem-icon pi pi-eye"]').click();
    cy.waitExists('[class="data-card p-4"]');
    cy.get('body').should('contain', '1357/2023');

    //Volta pra listgem, verifica o valor do provesso SEI do primeiro cardList e verifica se há o valor quando os detalhes são abertos.
    //Garante que mesmo com um processo aberto, quando eu tentar abrir outro processo o redirecionamento vai ser certo
    cy.get('[class="pi pi-lock tab-icon-lock"]').click();

    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '14133');
    cy.get('[id="search-btn-advanced"]').click();
    cy.waitExists('[class="data-card p-4"]');

    cy.get('[class="data-card p-4"]').first().should('contain', '14133');
    cy.get('[data-cy="3253598109-mais opcoes"]').first().click();
    cy.get('[class="p-menuitem-icon pi pi-eye"]').click();
    cy.waitExists('[class="data-card p-4"]');
    cy.get('body').should('contain', '14133');
  });

  it('Update Inexigibilidade Lei Nova N° 14.133', () => {
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', 'Teste cypress tela de inexigibilidade lei nova');
    cy.get('[id="search-btn-advanced"]').click();
    cy.waitExists('[class="data-card p-4"]');

    //edição simples
    cy.get('[data-cy="3253598109-mais opcoes"]').first().click();
    cy.get('[class="p-menuitem-icon pi pi-pencil"]').click();

    //Edita objeto da inexigibilidade
    cy.waitExistsAndType('[id="objeto"]', 'Teste cypress tela de inexigibilidade alteracao');

    //Avança as demais etapas e salva a edição
    cy.waitExistsAndClick('[aria-label="Avançar"]');
    cy.waitExistsAndClick('[aria-label="Avançar"]');
    cy.waitExistsAndClick('[aria-label="Salvar"]');

    //Verica se a alteracao foi persistida
    cy.get('body').should('contain', 'Inexigibilidade criada com sucesso!');

    //Edição via Requisição de modificação
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '1699/2022');
    cy.get('[id="search-btn-advanced"]').click();
    cy.waitExists('[class="data-card p-4"]');

    cy.get('[data-cy="3253598109-mais opcoes"]').first().click();
    cy.get('[class="p-menuitem-icon pi pi-pencil"]').click();

    const fileType = 'Termo de Referência ou Projeto Básico';

    cy.get('input[type=file]').selectFile('cypress/fixtures/arquivo.pdf', { force: true });
    cy.waitIncludes('arqivo.pdf');
    cy.get('[aria-label="Selecione um tipo"]').last().click();
    cy.contains('li.p-dropdown-item', fileType).last().click();

    //Avança as demais etapas e salva a edição
    cy.waitExistsAndClick('[aria-label="Avançar"]');
    cy.waitExistsAndClick('[aria-label="Avançar"]');

    cy.get('[data-cy="3458550077-enviar requisicao"]').click();
    cy.get('[id="justificativa"]').type('teste');
    cy.get('[data-cy="3458550077-enviar requisicao"]').eq(1).click();

    //Acessando a requisição de modificação oq garante que ela foi criada
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '1699/2022');
    cy.get('[id="search-btn-advanced"]').click();
    cy.waitExists('[class="data-card p-4"]');

    cy.get('[data-cy="3253598109-mais opcoes"]').first().click();
    cy.get('[class="p-menuitem-icon pi pi-exclamation-triangle"]').click();
  });

  it('Create - Inexigibilidade Lei Antiga N° 8.666', () => {
    //Clica para criar um novo registro
    cy.waitExistsAndClickText('Novo');
    // Aguardar e clicar no botão "Sim" do diálogo de confirmação
    cy.get('.p-confirm-dialog-accept').click();

    //Preenchendo dados basicos
    cy.findByRole('button', { name: 'Lei Nº 8.666' }).click();
    cy.get('[class="p-radiobutton p-component"]').eq(0).click();
    cy.waitExistsAndType('[id="numeroSei"]', '8666');
    cy.waitExistsAndType('[id="numeroProcesso"]', '8666');
    cy.get('[id="anoInexigibilidade"]').type('2024');
    cy.waitExistsAndType('[id="dataPedido"]', '06/03/2024');
    cy.waitExistsAndClick('[id="responsavelInexigibilidade"]');
    cy.get('[class="p-dropdown-item"]').first().click();

    //Seleciona a fundamentação legal
    cy.waitExistsAndClick('[id="fundamentacaoLegalEntidade"]');
    cy.get('[aria-label="Art. 13, inciso I, da Lei nº 8.666/1993"]').click();

    //Seleciona Naturezas do objeto
    cy.get('[id="naturezasDoObjeto"]').click();
    cy.get('[class="p-multiselect-item"]').first().click();

    //Seleciona fontes de recurso
    cy.get('[id="fontesDeRecurso"]').click();
    cy.get('[class="p-multiselect-item"]').first().click();
    cy.get('[class="p-multiselect-item"]').first().click();

    cy.waitExistsAndType('[id="objeto"]', 'Teste cypress tela de inexigibilidade');

    const fileTypes = [
      'Documentos de habilitação e qualificação do fornecedor ou executante',
      'Justificativa da necessidade do objeto',
      'Justificativa da situação de inexigibilidade com os elementos necessários à sua caracterização',
      'Justificativa do preço do fornecedor ou executante',
      'Parecer jurídico demonstrando o atendimento dos requisitos exigidos para a inexigibilidade de licitação',
      'Proposta formal oferecida pelo contratado',
      'Publicação do termo ou ato de ratificação da inexigibilidade de licitação',
      'Razões da escolha do fornecedor ou executante',
      'Termo de Referência ou Projeto Básico',
    ];

    //Adicionando pdfs e os tipos aos arquivo
    for (let i = 0; i < 9; i++) {
      cy.get('input[type=file]').selectFile('cypress/fixtures/arquivo.pdf', { force: true });
      cy.waitExists('[aria-label="Selecione um tipo"]');
      cy.get('[aria-label="Selecione um tipo"]').eq(i).click();
      cy.contains('[class="p-dropdown-item"]', fileTypes[i]).click();
    }

    cy.waitExistsAndClickText('Avançar');

    //Adicionando fornecedores e Vencedores
    cy.waitExistsAndClick('[aria-label="Adicionar"]');
    cy.get('[class="p-selection-column"]').first().click();
    cy.waitExistsAndClickText('Selecionar');

    //Adicionando Fornecedor
    cy.waitExistsAndClick('[aria-label="Adicionar Fornecedor"]');
    cy.waitExistsAndClick('[aria-label="Selecione um fornecedor"]');
    cy.get('[class="p-dropdown-item"]').click();
    cy.get('[id="valor"]').type('222');
    cy.get('[class="p-button p-component"]').eq(2).click({ force: true });
    cy.waitExistsAndClickText('Adicionar');

    cy.waitExistsAndClick('[aria-label="Avançar"]');
    cy.waitExistsAndClick('[aria-label="Salvar"]');
    cy.waitExists('[class="data-card p-4"]');

    //Verifica se a inexigibilidade foi criada, se foi ela corresponde ao primeiro card e não da erro
    cy.get('[class="data-card p-4"]').first().should('contain', '8666');
  });

  it('Update Inexigibilidade Lei Antiga N° 8666', () => {
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', 'Teste cypress tela de inexigibilidade');
    cy.get('[id="search-btn-advanced"]').click();
    cy.waitExists('[class="data-card p-4"]');

    //edição simples
    cy.get('[data-cy="3253598109-mais opcoes"]').first().click();
    cy.get('[class="p-menuitem-icon pi pi-pencil"]').click();

    //Edita objeto da inexigibilidade
    cy.waitExistsAndType('[id="objeto"]', 'Teste cypress tela de inexigibilidade alteracao');

    //Avança as demais etapas e salva a edição
    cy.waitExistsAndClick('[aria-label="Avançar"]');
    cy.waitExistsAndClick('[aria-label="Avançar"]');
    cy.waitExistsAndClick('[aria-label="Salvar"]');

    //Verica se a alteracao foi persistida
    cy.get('body').should('contain', 'Inexigibilidade criada com sucesso!');
  });

  it('Delete antiga', () => {
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '8666');
    cy.get('[id="search-btn-advanced"]').click();
    cy.waitExists('[class="data-card p-4"]');

    //Selecionando a inexigibilidade a ser removida
    cy.get('[data-cy="3253598109-mais opcoes"]').first().click();
    cy.get('[class="p-menuitem-icon pi pi-trash"]').click();

    //Adicionando uma justificativa a requisição de remoção e clicando em enviar
    cy.waitExistsAndType('[id="justificativa"]', 'teste');
    cy.waitExistsAndClickText('Enviar');
    cy.waitExists('[class="data-card p-4"]');

    //Acessando a requisição de remoção oq garante que ela foi criada
    cy.get('[data-cy="3253598109-mais opcoes"]').first().click();
    cy.get('[class="p-menuitem-icon pi pi-exclamation-triangle"]').click();
  });

  it('Delete nova', () => {
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '14133');
    cy.get('[id="search-btn-advanced"]').click();
    cy.waitExists('[class="data-card p-4"]');

    //Selecionando a inexigibilidade a ser removida
    cy.get('[data-cy="3253598109-mais opcoes"]').first().click();
    cy.get('[class="p-menuitem-icon pi pi-trash"]').click();

    //Adicionando uma justificativa a requisição de remoção e clicando em enviar
    cy.waitExistsAndType('[id="justificativa"]', 'teste');
    cy.waitExistsAndClickText('Enviar');
    cy.waitExists('[class="data-card p-4"]');

    //Acessando a requisição de remoção oq garante que ela foi criada
    cy.get('[data-cy="3253598109-mais opcoes"]').first().click();
    cy.get('[class="p-menuitem-icon pi pi-exclamation-triangle"]').click();
  });
});
