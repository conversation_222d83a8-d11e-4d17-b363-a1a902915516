describe('Analise Processos Testes', () => {
  before(() => {
    cy.login('admin.sistemas');
    cy.selectEntity();
    cy.closeAlerts();

    //Acesso a tela de Grupos para adicionar um Auditor Inspetor
    cy.waitExistsAndClick('[data-cy="seguranca"]');
    cy.waitExistsAndClick('[data-cy="grupos"]');
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', 'Inspetor');
    cy.get('[id="search-btn-advanced"]').click();
    cy.waitIncludes('Inspetor');
    cy.waitIncludes('Composto por todos os auditores inspetores');
    cy.get('[class="p-button p-component p-button-sm p-button-success p-mr-2 p-button-icon-only"]').eq(1).click();
    
    cy.get('[class="p-picklist-list-wrapper p-picklist-source-wrapper"]').eq(1).find('[class="p-picklist-item"]').first().click();
    cy.get('[class="p-button-icon p-c pi pi-angle-right"]').eq(1).click();

    cy.waitExistsAndClickText('Salvar');
    cy.contains('Registro salvo com sucesso!');
    
  });

  beforeEach(() => {
    cy.intercept('POST', '*/analise-processo-view/advanced-search').as('getRegistros');

    //Login Auditor
    cy.login('suporte.sistemas');
    cy.closeAlerts();
    cy.waitExistsAndClick('[data-cy="auditoria"]');
    cy.waitExistsAndClick('[data-cy="analisar processos"]');

    cy.wait('@getRegistros');
    cy.wait('@getRegistros');
    cy.wait('@getRegistros');
    cy.wait('@getRegistros');
    cy.wait('@getRegistros');
  });

  it('Mover para análise', () => {
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '043/2023');
    cy.waitExistsAndClick('[id="search-btn-advanced"]');
    cy.waitIncludes('SECRETARIA MUNICIPAL DE EDUCAÇÃO - SEME - PMRB');
    cy.contains('div', 'SECRETARIA MUNICIPAL DE EDUCAÇÃO - SEME - PMRB').click();

    //Move para Análise
    cy.waitExistsAndClick('[data-cy="3240378834-mover para analise"]');

    //Verifica se o registro foi movido
    cy.waitIncludes('Processo movido para análise com sucesso!');
  });

  it('Criar TDA', () => {
    //Seleciona o registro
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '043/2023');
    cy.waitExistsAndClick('[id="search-btn-advanced"]');
    cy.waitIncludes('SECRETARIA MUNICIPAL DE EDUCAÇÃO - SEME - PMRB');
    cy.contains('div', 'SECRETARIA MUNICIPAL DE EDUCAÇÃO - SEME - PMRB').click();

    //Cria TDA
    cy.waitExistsAndClick('[data-cy="3240378834-criar tda"]');
    cy.waitExistsAndClick('[id="responsavel"]');

    cy.waitExistsAndClick('[class="p-dropdown-items"]');

    cy.get('[id="dataInicio"]').type('30/06/2023');
    cy.get('[id="dataFinal"]').type('01/12/2026');
    cy.get('[id="Suspensão"]').type('30/06/2023');
    cy.get('[id="dataProrrogacao"]').type('30/12/2023');
    cy.get('[id="observacao"]').type('Cypress teste cria TDA', { force: true });
    cy.clickText('Salvar');

    //Verifica se o registro foi salvo
    cy.waitIncludes('Registro salvo com sucesso!');
  });

  it('Aplicar Checklist', () => {
    // Seleciona o registro
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '043/2023');
    cy.get('[id="search-btn-advanced"]').click();
    cy.waitIncludes('SECRETARIA MUNICIPAL DE EDUCAÇÃO - SEME - PMRB');
    cy.contains('div', 'SECRETARIA MUNICIPAL DE EDUCAÇÃO - SEME - PMRB').click();

    cy.waitExistsAndClick('[data-cy="3240378834-aplicar checklist"]');

    //Aplica checklist
    cy.get('[data-cy="3240378834-gerar relatorio"]').first().click();

    //Adicionando pdf
    cy.get('input[type=file]').last().selectFile('cypress/fixtures/arquivo.pdf', { force: true });

    cy.waitExistsAndClick('[aria-label="Concluir"]');

    cy.waitIncludes('Checklist concluído com sucesso!');
  });

  it('Emitir Alerta', () => {
    //Seleciona o processo
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '043/2023');
    cy.get('[id="search-btn-advanced"]').click();
    cy.waitIncludes('SECRETARIA MUNICIPAL DE EDUCAÇÃO - SEME - PMRB');
    cy.contains('div', 'SECRETARIA MUNICIPAL DE EDUCAÇÃO - SEME - PMRB').click();

    // Emite Alerta
    cy.waitExistsAndClick('[data-cy="3240378834-emitir alerta"]');

    cy.waitExistsAndType('[aria-label="Editor de Formatação, main"]', 'Teste cypress tela de Análise de Processos');
    cy.waitExistsAndType('[id="prazoResposta"]', '10/07/2030');
    cy.waitExistsAndClickText('Emitir');

    //Verifica se o alerta foi salvo
    cy.waitIncludes('Registro salvo com sucesso!');
  });

  it('Arquivar Processo', () => {
    //Seleciona o processo
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '043/2023');
    cy.get('[id="search-btn-advanced"]').click();
    cy.waitIncludes('SECRETARIA MUNICIPAL DA CIDADE- SMC');
    cy.contains('div', 'SECRETARIA MUNICIPAL DA CIDADE- SMC').click();

    // Arquiva o processo
    cy.waitExistsAndClick('[data-cy="3240378834-arquivar"]');
    cy.waitExistsAndClick('[aria-label="Selecione um status"]');
    cy.waitExistsAndClick('[aria-label="Consistente"]');
    cy.waitExistsAndClick('[aria-label="Arquivar"]');

    cy.waitIncludes('Processo arquivado com sucesso');
  });
});
