describe('CRUD Licitação', () => {
  beforeEach(() => {
    cy.login(99999999998);
    cy.selectEntity();
    cy.closeAlerts();

    cy.get('[data-cy="cadastros e consultas"]').click();
    cy.waitExistsAndClick('[data-cy="licitacao"]');
  });

  it('Create - fase preparatória', () => {
    //Clica para criar uma nova licitação
    cy.waitExistsAndClickText('Nova Licitação');
    // Aguardar e clicar no botão "Sim" do diálogo de confirmação
    cy.get('.p-confirm-dialog-accept').click();

    //Responde: Destinado exclusivamente à participação de microempresas e empresas de pequeno porte?
    cy.get('[class="p-radiobutton p-component"]').eq(0).click();

    //Selecionando termo de referência
    cy.waitExistsAndClick('[id="search-btn"]');
    cy.get('[class="p-radiobutton p-component"]').eq(2).first().click();
    cy.waitExistsAndClickText('Confirmar');

    //Selecionando modalidade da licitacao
    cy.get('[id="modalidadeLicitacaoNova"]').click();
    cy.get('[class="p-dropdown-item"]').first().click();

    //Preenchendo informações básicas
    cy.get('[id="numero"]').type('12345');
    cy.get('[id="ano"]').type('2023');
    cy.get('[id="dataAbertura"]').type('02/01/2023 14:00');
    cy.get('[id="numeroProcessoAdm"]').type('12345');
    cy.get('[id="objeto"]').type('Teste cypress tela de licitacao');

    //Selecionando natureza do objeto
    cy.get('[id="naturezasDoObjeto"]').click();
    cy.get('[class="p-multiselect-item"]').first().click();

    //Selecionando fontes de recurso
    cy.get('[id="fontesDeRecurso"]').click();
    cy.get('[class="p-multiselect-item"]').first().click();

    //Cadastrando Comissão
    cy.get('[id="search-btn"]').eq(1).click({ force: true });
    cy.waitExistsAndClickText('Cadastrar');
    cy.waitExists('[id="numero"]');
    cy.get('[id="numero"]').eq(1).type('123456');
    cy.get('[id="dataVigenciaInicial"]').type('01/02/2023');
    cy.get('[id="dataVigenciaFinal"]').type('01/12/2026');
    cy.get('[class="p-picklist-item"]').first().click();
    cy.get('[class="p-button-icon p-c pi pi-angle-right"]').click();
    cy.get('[class="p-picklist-item"]').first().click();
    cy.get('[class="p-button-icon p-c pi pi-angle-right"]').click();
    cy.get('[class="p-picklist-item"]').first().click();
    cy.get('[class="p-button-icon p-c pi pi-angle-right"]').click();

    //Adicionando arquivo pdf
    cy.get('input[type=file]').eq(1).selectFile('cypress/fixtures/arquivo.pdf', { force: true });
    cy.waitExists('[aria-label="Selecione um tipo"]');
    cy.get('[aria-label="Selecione um tipo"]').first().click();
    cy.contains('[class="p-dropdown-item"]', 'Documento de designação da comissão').click();
    cy.get('[data-cy="98475936-salvar"]').eq(1).click();

    cy.waitExistsAndClick('[class="p-selectable-row"]');

    //Selecionando pregoeiro
    cy.get('[id="pregoeiro"]').click();
    cy.get('[class="p-dropdown-item"]').first().click();

    //Selecionando tipos de licitação
    cy.get('[id="tiposLicitacao"]').click();
    cy.get('[class="p-multiselect-item"]').first().click();

    const fileTypes = [
      'Análise dos riscos que possam comprometer o sucesso da licitação e a boa execução contratual',
      'Edital e Anexos',
      'Pesquisa de preço praticado por Órgãos e Entidades da Administração Pública',
      'Pesquisa de preço de mercado',
      'Mapa Comparativo de Preços (Planilha Excel/PDF)',
      'Documento de solicitação formal de cotação de preços',
      'Documento de Formalização de Demanda, com a descrição da necessidade da contratação',
      'Justificativa da escolha dos fornecedores constantes na cotação de preço de mercado',
      'Estudo Técnico Preliminar',
      'Parecer jurídico da Administração',
    ];

    //Adicionando pdfs e os tipos aos arquivo
    for (let i = 0; i < 10; i++) {
      cy.get('input[type=file]').selectFile('cypress/fixtures/arquivo.pdf', { force: true });
      cy.waitExists('[aria-label="Selecione um tipo"]');
      cy.get('[aria-label="Selecione um tipo"]').eq(i).click();
      cy.contains('[class="p-dropdown-item"]', fileTypes[i]).click();
    }

    cy.waitExistsAndClickText('Salvar');
    cy.waitExists('[class="p-panel p-component lici-ray ray-PREPARATORIA"]');

    // Testa se a licitação está na raia certa, já que avançou a fase
    cy.get('[class="p-panel p-component lici-ray ray-PREPARATORIA"]').first().should('contain', 'Teste cypress tela de licitacao');
  });

  it('Create - fase divulgação', () => {
    //Busca a licitacao para criar proxima fase
    cy.waitExistsAndClickText('Concorrência Eletrônica - 12345 / 2023');
    cy.waitExistsAndClick('[data-cy="1555221225-proxima fase"]');

    // Aguardar e clicar no botão "Sim" do diálogo de confirmação
    cy.get('.p-confirm-dialog-accept').click();

    //Adiciona forma de publicação internet
    cy.waitExistsAndClickText('Adicionar Publicação');
    cy.get('[id="tpFormaPublicacao"]').click();
    cy.get('[class="p-dropdown-item"]').eq(3).click();

    // Adiciona data de publicação
    cy.waitExistsAndType('[id="dataPublicacao"]', '01/06/2023');

    //Adiciona a descrição da publicação
    cy.get('[id="descricao"]').type('123123', { force: true });
    cy.get('[id="pagina"]').type('15', { force: true });
    cy.waitExistsAndClickText('Adicionar');

    const fileTypes = [
      'Comprovante da publicação do aviso de licitação',
      'Comprovante de divulgação e manutenção do inteiro teor do ato convocatório e de seus anexos no Portal Nacional de Contratações Públicas (PNCP)',
      'Comprovante da publicação do aviso de licitação em jornal diário de grande circulação',
    ];

    //Adicionando pdfs e os tipos aos arquivo
    for (let i = 0; i < 3; i++) {
      cy.get('input[type=file]').selectFile('cypress/fixtures/arquivo.pdf', { force: true });
      cy.waitExists('[aria-label="Selecione um tipo"]');
      cy.get('[aria-label="Selecione um tipo"]').eq(i).click();
      cy.contains('[class="p-dropdown-item"]', fileTypes[i]).click();
    }

    //Salva as alterações feitas
    cy.waitExistsAndClickText('Publicar');
    cy.waitExists('[class="p-panel p-component lici-ray ray-DIVULGACAO_PUBLICACAO_LICITACAO"]');

    //Testa se a licitação foi movida de raia por que agora ela esta na segunda fase
    cy.get('[class="p-panel p-component lici-ray ray-DIVULGACAO_PUBLICACAO_LICITACAO"]').first().should('contain', 'Teste cypress tela de licitacao');
  });

  it('Create - fase propostas e lances', () => {
    //Busca a licitacao para criar proxima fase
    cy.waitExistsAndClickText('Concorrência Eletrônica - 12345 / 2023');
    cy.waitExistsAndClick('[data-cy="1555221225-proxima fase"]');

    // //Clica em Sinalizar itens fracassados
    cy.waitExists('[data-cy="1555221225-sinalizar itens fracassados"]');
    cy.get('[data-cy="1555221225-sinalizar itens fracassados"]').click();

    // //Escolha de item fracassado
    cy.get('[class="p-picklist-item"]').eq(2).click();
    cy.get('[class="p-button p-component p-button-icon-only"]').eq(6).click({ force: true });
    cy.get('[aria-label="Confirmar"]').click();
    cy.get('[aria-label="Sim"]').click();

    //Clica e escolhe licitantes participantes da fase
    cy.waitExistsAndClickText('Adicionar licitantes');
    cy.get('[class="p-selectable-row"]').first().click();
    cy.waitExistsAndClickText('Selecionar');

    //Preenche o componente de vencedores
    cy.get('[aria-label="Selecione um Licitante"]').click();
    cy.get('[class="p-dropdown-item"]').first().click();

    cy.get('[class="p-picklist-item"]').first().click();
    cy.get('[class="p-button p-component p-button-icon-only"]').eq(5).click({ force: true });

    // //Escolha de item
    cy.get('[placeholder="Informe a Marca/Modelo"]').type('cypress teste publicacao');
    cy.get('[data-cy="1555221225-pi pi-check"]').click();

    const fileTypes = ['Ata da sessão da licitação', 'Mapa de lances (Planilha Excel/PDF)', 'Propostas vencedoras'];

    //Adicionando pdfs e os tipos aos arquivo
    for (let i = 0; i < 3; i++) {
      cy.get('input[type=file]').selectFile('cypress/fixtures/arquivo.pdf', { force: true });
      cy.waitExists('[aria-label="Selecione um tipo"]');
      cy.get('[aria-label="Selecione um tipo"]').eq(i).click();
      cy.contains('[class="p-dropdown-item"]', fileTypes[i]).click();
    }

    //Salva alterações
    cy.waitExistsAndClickText('Salvar');
    cy.waitExists('[class="p-panel p-component lici-ray ray-APRESENTACAO_PROPOSTAS_LANCES"]');

    //Testa se a licitação esta na raia certa já que avançou a fase
    cy.get('[class="p-panel p-component lici-ray ray-APRESENTACAO_PROPOSTAS_LANCES"]').first().should('contain', 'Teste cypress tela de licitacao');
  });

  it('Create - fase final', () => {
    //Busca a licitacao para criar proxima fase
    cy.waitExistsAndClickText('Concorrência Eletrônica - 12345 / 2023');
    cy.waitExistsAndClick('[data-cy="1555221225-proxima fase"]');
    cy.waitExists('input[type=file]');

    const fileTypes = ['Termo de Adjudicação', 'Termo de Homologação'];

    //Adicionando pdfs e os tipos aos arquivo
    for (let i = 0; i < 2; i++) {
      cy.get('input[type=file]').selectFile('cypress/fixtures/arquivo.pdf', { force: true });
      cy.waitExists('[aria-label="Selecione um tipo"]');
      cy.get('[aria-label="Selecione um tipo"]').eq(i).click();
      cy.contains('[class="p-dropdown-item"]', fileTypes[i]).click();
    }

    //Salva alterações feitas
    cy.waitExistsAndClickText('Finalizar');
    cy.waitExists('[class="p-panel p-component lici-ray ray-FINALIZACAO"]');

    //Testa se a licitação esta na raia certa
    cy.get('[class="p-panel p-component lici-ray ray-FINALIZACAO"]').first().should('contain', 'Teste cypress tela de licitacao');
  });

  it('Read', () => {
    //Abre duas vezes a mesma licitação so deve haver um botão de fechar
    cy.waitExistsAndClickText('Concorrência Eletrônica - 12345 / 2023');
    cy.waitExistsAndClick('[class="pi pi-lock tab-icon-lock"]');
    cy.contains('div', 'Concorrência Eletrônica - 12345 / 2023').click();

    //Apenas um botão de fechar deve está na tela, se ocorrer um erro é porque está abrindo licitações duplicadas
    cy.waitExistsAndClick('[class="pi pi-times tab-icon-times"]');

    //Clica novamente na licitação
    cy.contains('div', 'Concorrência Eletrônica - 12345 / 2023').click();

    //Verifica se header e corpo correspondem a licitação clicada
    cy.get('[class="tab-header"]').eq(1).should('contain', '2023');
    cy.get('[class="p-fluid p-form"]').should('contain', 'PREFEITURA MUNICIPAL DE TARAUACÁ');

    //Fecha para abrir uma nova licitação
    cy.get('[class="pi pi-lock tab-icon-lock"]').click();


    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '044');
    cy.waitExistsAndClick('[id="search-btn-advanced"]');

    cy.contains('div', 'Pregão Presencial - 044 / 2023').click();
    cy.waitExists('[class="tab-header"]');
    cy.get('[class="tab-header"]').eq(2).should('contain', '2023');
  });

  it('Update Preparatória', () => {
    cy.waitIncludes('Concorrência Eletrônica - 12345 / 2023');

    // Seleciona licitação que foi criada recentemente
    cy.contains('div', 'Concorrência Eletrônica - 12345 / 2023').click();
    //Clica para editar a fase preparatória
    cy.waitExistsAndClick('[data-cy="1555221225-preparatoria"]');

    //Edita o objeto da licitacao e a modalidade
    cy.waitExistsAndType('[id="objeto"]', 'Teste cypress licitacao editada');

    //Salva alterações
    cy.waitExistsAndClickText('Enviar Requisição');
    cy.get('[id="justificativa"]').type('teste');
    cy.get('[aria-label="Enviar Requisição"]').eq(1).click();

    //Fecha a licitação corrente para abrir outra
    cy.waitExistsAndClick('[class="pi pi-times tab-icon-times"]');

    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '044');
    cy.waitExistsAndClick('[id="search-btn-advanced"]');
    cy.contains('div', 'Pregão Presencial - 044 / 2023').click();

    //Clica para editar a fase
    cy.waitExistsAndClick('[data-cy="1555221225-preparatoria"]');

    //Edita o objeto
    cy.waitExistsAndType('[id="objeto"]', 'Teste cypress licitacao editada');

    //Adiciona os arquivos e associa os tipos aos arquivos adicionados
    cy.get('input[type=file]').selectFile('cypress/fixtures/arquivo.pdf', { force: true });
    cy.waitExists('[aria-label="Selecione um tipo"]');
    cy.get('[aria-label="Selecione um tipo"]').eq(6).click();
    cy.contains('[class="p-dropdown-item"]', 'Parecer jurídico aprovando as minutas do edital e do contrato').click();

    cy.get('input[type=file]').selectFile('cypress/fixtures/arquivo.pdf', { force: true });
    cy.waitExists('[aria-label="Selecione um tipo"]');
    cy.get('[aria-label="Selecione um tipo"]').eq(7).click();
    cy.contains('[class="p-dropdown-item"]', 'Termo de Referência ou Projeto básico').click();

    //Clica para salvar a modificação e adiciona um justificativa
    cy.waitExistsAndClickText('Enviar Requisição');
    cy.get('[id="justificativa"]').type('teste');
    cy.get('[aria-label="Enviar Requisição"]').eq(1).click();
  });

  //Testa Requisição modificação da Fase de Publicação
  it('Update ReqModificação', () => {
    //Busca licitação para editar
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '042');
    cy.waitExistsAndClick('[id="search-btn-advanced"]');
    cy.waitExistsAndClickText('Pregão Presencial - 042 / 2023');
    cy.waitExistsAndClick('[data-cy="1555221225-divulgacao e publicacao da licitacao"]');

    //Adiciona uma Publicação
    cy.waitExistsAndClickText('Adicionar Publicação');
    cy.get('[id="tpFormaPublicacao"]').click();
    cy.get('[class="p-dropdown-item"]').eq(3).click();

    //Adiciona data de publicação
    cy.waitExistsAndType('[id="dataPublicacao"]', '01/06/2023');

    //Adiciona a descrição da publicação
    cy.get('[id="descricao"]').type('145899', { force: true });
    cy.get('[id="pagina"]').type('15', { force: true });
    cy.waitExistsAndClickText('Adicionar');

    //Clica para salvar a modificação e adiciona um justificativa
    cy.waitExistsAndClickText('Enviar Requisição');
    cy.get('[id="justificativa"]').type('teste');
    cy.get('[aria-label="Enviar Requisição"]').eq(1).click();
  });

  it('Delete', () => {
    //Escolhe uma licitação para ser removida e clica em excluir
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '041');
    cy.waitExistsAndClick('[id="search-btn-advanced"]');
    cy.waitExistsAndClickText('Pregão Presencial - 041 / 2023');
    cy.waitExistsAndClick('[data-cy="1555221225-excluir"]');
    cy.waitExistsAndType('[id="justificativa"]', 'teste');
    cy.waitExistsAndClickText('Enviar');

    //Se não achar esse botão é porque a requisição de remocao não foi criada
    cy.waitExists('[data-cy="1555221225-requisicao de modificacao pendente"]');
  });
});
