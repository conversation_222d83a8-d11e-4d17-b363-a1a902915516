describe('CRUD Comissão', () => {
  beforeEach(() => {
    cy.login(99999999998);
    cy.selectEntity();
    cy.closeAlerts();

    cy.get('[data-cy="administracao"]').click();
    cy.waitExistsAndClick('[data-cy="comissao"]');
  });

  it('Create', () => {
    //Clica para criar um novo registro
    cy.waitExistsAndClickText('Novo');

    //Preenchendo dados basicos
    cy.waitExistsAndType('[id="numero"]', '84894');
    cy.waitExistsAndType('[id="dataVigenciaInicial"]', '10/06/2023');
    cy.waitExistsAndType('[id="dataVigenciaFinal"]', '01/12/2026');

    //Adicionando pdf
    cy.get('input[type=file]').selectFile('cypress/fixtures/arquivo.pdf', { force: true });
    cy.waitExists('[aria-label="Selecione um tipo"]');
    cy.get('[aria-label="Selecione um tipo"]').first().click();
    cy.contains('[class="p-dropdown-item"]', 'Documento de designação da comissão').click();

    cy.waitExists('[class="p-picklist-item"]');

    cy.get('[class="p-picklist-item"]').first().click();
    cy.get('[class="p-button-icon p-c pi pi-angle-right"]').click();

    cy.get('[class="p-picklist-item"]').first().click();
    cy.get('[class="p-button-icon p-c pi pi-angle-right"]').click();

    cy.get('[class="p-picklist-item"]').first().click();
    cy.get('[class="p-button-icon p-c pi pi-angle-right"]').click();

    cy.waitExistsAndClickText('Salvar');
    cy.waitExists('[role="row"]');

    cy.get('body').should('contain', 'Registro salvo com sucesso!');
  });

  it('Read', () => {
    //Filtra por uma licitacao especifica
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '84894');
    cy.get('[id="search-btn-advanced"]').click();
    cy.waitExists('[role="row"]');

    cy.get('[role="row"]').should('contain', '84894');
    cy.get('[role="row"]').should('contain', '10/06/2023');
    cy.get('[role="row"]').should('contain', '01/12/2026');
    cy.get('[role="row"]').should('contain', 'Permanente');
  });

  it('Update', () => {
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '84894');
    cy.get('[id="search-btn-advanced"]').click();
    cy.waitExists('[role="row"]');
    //edição uma comissão

    cy.get('[data-cy="4021383245-pi pi-pencil"]').first().click();

    //Edita objeto da Comissao
    cy.waitExistsAndType('[id="numero"]', '5555555');
    cy.waitExistsAndClickText('Salvar');
    cy.waitExists('[role="row"]');
    cy.get('body').should('contain', 'Registro salvo com sucesso!');

    //Verica se a alteracao foi persistida
    cy.get('body').first().should('contain', '5555555');
  });

  it('Delete', () => {
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '5555555');
    cy.get('[id="search-btn-advanced"]').click();
    cy.waitExists('[role="row"]');

    //Selecionando a Comissao a ser removida
    cy.waitExistsAndClick('[data-cy="4021383245-pi pi-trash"]');
    cy.waitExistsAndClickText('Sim');

    cy.get('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]').clear();
    cy.get('[id="search-btn-advanced"]').click();

    cy.get('body').should('not.contain', '5555555');
  });
});
