describe('CRUD Termo de Referência', () => {
  beforeEach(() => {
    cy.login(99999999998);
    cy.selectEntity();
    cy.closeAlerts();

    //Acesso a tela de termo de referencia
    cy.get('[data-cy="termo de referencia"]').click();
    cy.waitExistsAndClick('[data-cy="gerenciamento de termos"]');
  });

  it('Create', () => {
    // Clica no botão de criar um novo termo
    cy.waitExistsAndClickText('Novo');

    //Preenche campo identificador do processo
    cy.get('[id="identificadorProcesso"]').type('Teste cypress termo não modelo');

    //Clica no botão avançar
    cy.waitExistsAndClickText('Avançar');

    //Adiciona arquivo
    cy.get('input[type=file]').selectFile('cypress/fixtures/arquivo-teste.docx', { force: true });
    cy.waitIncludes('Arquivo do Termo de Referência');

    //Clica no botão avançar
    cy.waitExistsAndClickText('Avançar');

    //Escolhe o primeiro item visível e clica no botão para adicionar o item
    cy.waitExistsAndClick('[aria-label="Adicionar"]');

    //Preenche as informações necessarias
    cy.waitExistsAndType('[id="Quantidade"]', '1');
    cy.get('[id="valorUnitarioEstimado"]').type('1,00');

    cy.get('[id="unidadeMedida"]').click();
    cy.get('[class="p-dropdown-item"]').first().click();

    //Clica no botão adicionar dentro de adicionar item
    cy.get('[class="p-button-icon p-c p-button-icon-left pi pi-shopping-cart"]').click();

    //Clica para salvar o termo de referencia
    cy.waitExistsAndClickText('Salvar');

    //Verifica se o termo foi salvo corretamente
    cy.waitIncludes('Teste cypress termo não modelo');

    //Cria outro termo igual e espera receber mensagem de erro
    cy.waitExists('[aria-label= "Novo"]');
    cy.get('[aria-label= "Novo"]').first().click();

    cy.waitExistsAndType('[id="identificadorProcesso"]', 'Teste cypress termo não modelo');

    cy.waitExistsAndClickText('Avançar');
    cy.waitExists('input[type=file]');

    cy.get('input[type=file]').selectFile('cypress/fixtures/arquivo-teste.docx', { force: true });

    cy.get('[aria-label = "Avançar"]').click();

    cy.waitExistsAndClick('[aria-label="Adicionar"]');
    cy.waitExistsAndType('[placeholder="Informe a quantidade"]', '3');
    cy.get('[placeholder="R$"]').type('1,00');

    cy.get('[id="unidadeMedida"]').click();
    cy.get('[class="p-dropdown-item"]').first().click();

    //Clica no botão adicionar dentro de adicionar item
    cy.get('[class="p-button-icon p-c p-button-icon-left pi pi-shopping-cart"]').click();

    cy.waitExistsAndClickText('Salvar');

    cy.waitIncludes('Termo de Referência não pôde ser salvo. Já existe um Termo de Referência na entidade com o mesmo identificador.');

    //Cria termo de referencia modelo

    cy.waitExistsAndClickText('Voltar');
    cy.waitExistsAndClickText('Voltar');

    cy.get('[id="identificadorProcesso"]').clear();
    cy.get('[id="identificadorProcesso"]').type('Teste cypress termo modelo');

    //Clica no botão "Sim" referente ao modelo

    cy.findByRole('button', { name: 'Sim' }).click();

    cy.waitExistsAndClickText('Avançar');

    cy.waitExistsAndClickText('Avançar');

    cy.waitExistsAndClickText('Salvar');
  });

  it('Read', () => {
    //Verifica se os registos criados anteriormente estão listados
    cy.waitIncludes('Teste cypress termo não modelo');
    cy.waitIncludes('Teste cypress termo modelo');

    //Verifica se na listagem dos disponiveis nao contem o modelo
    cy.get('#root > div > div.layout-main > div > div.card.page.index-table > div.tab-content > div.tabs > div > div > div:nth-child(2) > div').click();
    cy.get('#root > div > div.layout-main > div > div.card.page.index-table > div.tab-content > div:nth-child(4) > div > div.p-datatable-wrapper > table > tbody').should('not.contain', 'Teste cypress termo modelo');

    //Verifica se na listagem do modelo nao contem os disponiveis
    cy.get('#root > div > div.layout-main > div > div.card.page.index-table > div.tab-content > div.tabs > div > div > div:nth-child(4) > div').click();
    cy.get('#root > div > div.layout-main > div > div.card.page.index-table > div.tab-content > div:nth-child(6) > div > div.p-datatable-wrapper > table > tbody').should('not.contain', 'Teste cypress termo não modelo');

    //Verifica se não abre registro repetido
    cy.get('#root > div > div.layout-main > div > div.card.page.index-table > div.tab-content > div.tabs > div > div > div:nth-child(1)').click();
    cy.get('[data-cy="1834015054-detalhes"]').first().click();
    cy.waitExistsAndClick('#root > div > div.layout-main > div > div.card.page.index-table > div.tabs > div > div > div.tab.false.tab-fixed > div');
    cy.get('[data-cy="1834015054-detalhes"]').first().click();
    // se o registro tiver aberto duplicado esse comando da erro pq ele so deve encontrar apenas um botao de fechar
    cy.get('[class="pi pi-times tab-icon-times"]').click();

    //Verifica os detalhes do registro
    cy.get('[data-cy="1834015054-detalhes"]').first().click();
    cy.get('body').should('contain', 'arquivo-teste.docx');
    cy.get('[class="pi pi-times tab-icon-times"]').click();
  });

  it('Update', () => {
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', 'Teste cypress termo não modelo');
    cy.get('[id="search-btn-advanced"]').click();
    cy.waitExistsAndClick('[data-cy="1834015054-editar"]');

    //Verifica se o termo aberto corresponde ao termo clicado
    // cy.get('body').should('contain', 'Teste cypress termo não modelo');

    //Tenta salvar com um nome existente, deve lannçar uma mensagem de erro
    cy.get('[id="identificadorProcesso"]').clear();
    cy.get('[id="identificadorProcesso"]').type('Teste cypress termo modificado');

    cy.waitExistsAndClickText('Avançar');
    cy.waitExistsAndClickText('Avançar');
    cy.waitExistsAndClickText('Salvar');

    //Verifica se a edição foi persistida e está listada
    cy.waitIncludes('Teste cypress termo modificado');
  });

  it('Delete', () => {
    //Adiciona filtro para buscar o registro para deletar
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', 'Teste cypress termo modificado');
    cy.get('[id="search-btn-advanced"]').click();

    //Clica em deletar o filtro
    cy.waitExistsAndClick('[data-cy="1834015054-remover"]');
    cy.waitExistsAndClickText('Sim');

    //Limpa a filtragem para voltar a listagem total
    cy.get('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]').clear();
    cy.get('[id="search-btn-advanced"]').click();

    //Verifica se o registro sumiu
    cy.get('body').should('not.contain', 'Teste cypress termo modificado');
  });
});
