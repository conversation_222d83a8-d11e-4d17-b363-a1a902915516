describe('CRUD Contrato - Processo Antigo - Carona', () => {
  beforeEach(() => {
    cy.login(99999999998);
    cy.selectEntity();
    cy.closeAlerts();

    cy.waitExistsAndClick('[data-cy="cadastros e consultas"]');
    cy.waitExistsAndClick('[data-cy="contrato/aditivo"]');
  });

  it('Create - Tipo Contrato - Carona', () => {
    cy.waitExistsAndClickText('Novo');

    //Filtra por uma carona especifica
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '15/2015');
    cy.waitExistsAndClick('[id="search-btn-advanced"]');
    cy.waitIncludes('1 de 1 registros');
    cy.get('[data-cy="4277016757-selecionar"]').click();

    cy.get('[aria-label="Selecione o contratado(a)"]').click();
    cy.get('[class="p-dropdown-item"]').first().click();
    cy.waitExistsAndClick('[id="valorFornecedor"]');
    cy.get('[id="valorFornecedor"]').type('1000');

    // cy.intercept('POST', '*/materiais/elastic-search').as('getRegistros');
    // cy.waitExistsAndClickText('Criar Item');
    // cy.wait('@getRegistros');

    // cy.waitExistsAndClick('[aria-label="Adicionar"]');
    // cy.get('input[value="0"]').first().type('1', { force: true });
    // cy.waitExists('input[decimalplaces="2"]');
    // cy.get('input[placeholder="R$"]').type('1,00');

    // cy.waitExistsAndClickText('Ok');
    // cy.get('[data-cy="4277016757-pi pi-check"]').click();

    cy.waitExistsAndClickText('Avançar');

    //Preenchendo informações básicas
    cy.waitExistsAndType('[id="numero"]', '102');
    cy.get('[id="anoContrato"]').type('2023');
    cy.waitExistsAndType('[id="dataPublicacao"]', '01/01/2023');
    cy.get('[class="p-radiobutton p-component"]').eq(3).click();
    cy.waitExistsAndType('[id="dataVigenciaFinal"]', '01/12/2050');

    cy.get('[class="p-radiobutton p-component"]').eq(0).click();
    cy.waitExistsAndType('[id="dataVigenciaInicial"]', '01/01/2023');

    //Seleciona autoridade contratante
    cy.waitExistsAndClick('[id="autoridadeContratante"]');
    cy.get('[class="p-dropdown-item"]').first().click();

    //Selecionando fontes de recurso
    cy.get('[id="fontesDeRecurso"]').click();
    cy.get('[class="p-multiselect-item"]').first().click();
    cy.get('[class="p-multiselect-item"]').first().click();

    //Seleciona gestor
    cy.get('[id="search-btn"]').eq(2).click();
    cy.waitExistsAndClick('[class="p-selectable-row"]');
    cy.get('[data-cy="4277016757-selecionar"]').click();

    cy.get('[id="search-btn"]').first().click();
    cy.waitExistsAndClick('[class="p-selectable-row"]');
    cy.get('[data-cy="4277016757-selecionar"]').click();

    const fileTypes = ['Contrato', 'Designação do gestor e fiscal', 'Publicação do resumo do contrato'];

    //Adicionando pdfs e os tipos aos arquivo
    for (let i = 0; i < 3; i++) {
      cy.get('input[type=file]').selectFile('cypress/fixtures/arquivo.pdf', { force: true });
      cy.waitExists('[aria-label="Selecione um tipo"]');
      cy.get('[aria-label="Selecione um tipo"]').eq(i).click();
      cy.contains('[class="p-dropdown-item"]', fileTypes[i]).click();
    }

    //Avançar e salvar
    cy.waitExistsAndClick('[aria-label="Avançar"]');
    cy.waitExistsAndClick('[aria-label="Salvar"]');

    //Verifica se o registro foi salvo
    cy.waitIncludes('Registro salvo com sucesso!');
  });

  it('Create - Tipo Empenho - Carona', () => {
    cy.waitExistsAndClickText('Novo');

    //Filtra por uma carona especifica
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '15/2015');
    cy.waitExistsAndClick('[id="search-btn-advanced"]');
    cy.waitIncludes('1 de 1 registros');
    cy.get('[data-cy="4277016757-selecionar"]').click();

    cy.get('[class="p-radiobutton-box"]').first().click();

    cy.get('[aria-label="Selecione o contratado(a)"]').click();
    cy.get('[class="p-dropdown-item"]').first().click();
    cy.waitExistsAndClick('[id="valorFornecedor"]');
    cy.get('[id="valorFornecedor"]').type('1000');

    // cy.intercept('POST', '*/materiais/elastic-search').as('getRegistros');
    // cy.waitExistsAndClickText('Criar Item');
    // cy.wait('@getRegistros');

    // cy.waitExistsAndClick('[aria-label="Adicionar"]');
    // cy.get('input[value="0"]').first().type('1', { force: true });
    // cy.waitExists('input[decimalplaces="2"]');
    // cy.get('input[placeholder="R$"]').type('1,00');

    // cy.waitExistsAndClickText('Ok');
    // cy.waitExistsAndClick('[data-cy="4277016757-pi pi-check"]');
    cy.waitExistsAndClickText('Avançar');

    //Preenchendo informações básicas
    cy.waitExistsAndType('[id="numero"]', '1062023');
    cy.get('[id="dataEmpenho"]').type('01/01/2023');
    cy.get('[aria-label="Ordinário"]').click();
    cy.get('[class="p-radiobutton p-component"]').eq(1).click();

    //Seleciona gestor
    cy.get('[id="search-btn"]').eq(2).click();
    cy.waitExistsAndClick('[class="p-selectable-row"]');
    cy.get('[data-cy="4277016757-selecionar"]').click();

    cy.get('[id="search-btn"]').first().click();
    cy.waitExistsAndClick('[class="p-selectable-row"]');
    cy.get('[data-cy="4277016757-selecionar"]').click();

    //Seleciona autoridade contratante
    cy.waitExistsAndClick('[id="autoridadeContratante"]');
    cy.get('[class="p-dropdown-item"]').first().click();

    //Selecionando fontes de recurso
    cy.get('[id="fontesDeRecurso"]').click();
    cy.get('[class="p-multiselect-item"]').first().click();
    cy.get('[class="p-multiselect-item"]').first().click();

    const fileTypes = ['Designação do gestor e fiscal', 'Equivalente de contrato: Empenho'];

    //Adicionando pdfs e os tipos aos arquivo
    for (let i = 0; i < 2; i++) {
      cy.get('input[type=file]').selectFile('cypress/fixtures/arquivo.pdf', { force: true });
      cy.waitExists('[aria-label="Selecione um tipo"]');
      cy.get('[aria-label="Selecione um tipo"]').eq(i).click();
      cy.contains('[class="p-dropdown-item"]', fileTypes[i]).click();
    }

    //Avançar e salvar
    cy.waitExistsAndClick('[aria-label="Avançar"]');
    cy.waitExistsAndClick('[aria-label="Salvar"]');

    //Verifica se o registro foi salvo
    cy.waitIncludes('Registro salvo com sucesso!');
  });

  it('Create - Ordem de Serviço - Carona', () => {
    cy.waitExistsAndClickText('Novo');

    //Filtra por uma carona especifica
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '15/2015');
    cy.waitExistsAndClick('[id="search-btn-advanced"]');
    cy.waitIncludes('1 de 1 registros');
    cy.get('[data-cy="4277016757-selecionar"]').click();

    cy.get('[class="p-radiobutton-box"]').eq(1).click();

    cy.get('[aria-label="Selecione o contratado(a)"]').click();
    cy.get('[class="p-dropdown-item"]').first().click();
    cy.waitExistsAndClick('[id="valorFornecedor"]');
    cy.get('[id="valorFornecedor"]').type('1000');

    // cy.intercept('POST', '*/materiais/elastic-search').as('getRegistros');
    // cy.waitExistsAndClickText('Criar Item');
    // cy.wait('@getRegistros');

    // cy.waitExistsAndClick('[aria-label="Adicionar"]');
    // cy.get('input[value="0"]').first().type('1', { force: true });
    // cy.waitExists('input[decimalplaces="2"]');
    // cy.get('input[placeholder="R$"]').type('1,00');

    // cy.waitExistsAndClickText('Ok');
    // cy.waitExistsAndClick('[data-cy="4277016757-pi pi-check"]');
    cy.waitExistsAndClickText('Avançar');

    //Preenchendo informações básicas
    cy.waitExistsAndType('[id="numero"]', '1102023');
    cy.get('[class="p-radiobutton p-component"]').eq(1).click();

    //Seleciona gestor
    cy.get('[id="search-btn"]').eq(2).click();
    cy.waitExistsAndClick('[class="p-selectable-row"]');
    cy.get('[data-cy="4277016757-selecionar"]').click();

    cy.get('[id="search-btn"]').first().click();
    cy.waitExistsAndClick('[class="p-selectable-row"]');
    cy.get('[data-cy="4277016757-selecionar"]').click();

    //Seleciona autoridade contratante
    cy.waitExistsAndClick('[id="autoridadeContratante"]');
    cy.get('[class="p-dropdown-item"]').first().click();

    //Selecionando fontes de recurso
    cy.get('[id="fontesDeRecurso"]').click();
    cy.get('[class="p-multiselect-item"]').first().click();
    cy.get('[class="p-multiselect-item"]').first().click();

    const fileTypes = ['Designação do gestor e fiscal', 'Equivalente de contrato: Ordem de Serviço/Entrega'];

    //Adicionando pdfs e os tipos aos arquivo
    for (let i = 0; i < 2; i++) {
      cy.get('input[type=file]').selectFile('cypress/fixtures/arquivo.pdf', { force: true });
      cy.waitExists('[aria-label="Selecione um tipo"]');
      cy.get('[aria-label="Selecione um tipo"]').eq(i).click();
      cy.contains('[class="p-dropdown-item"]', fileTypes[i]).click();
    }

    //Avançar e salvar
    cy.waitExistsAndClick('[aria-label="Avançar"]');
    cy.waitExistsAndClick('[aria-label="Salvar"]');

    //Verifica se o registro foi salvo
    cy.waitIncludes('Registro salvo com sucesso!');
  });

  it('Create - Carta-contrato - Carona', () => {
    cy.waitExistsAndClickText('Novo');

    //Filtra por uma carona especifica
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '15/2015');
    cy.waitExistsAndClick('[id="search-btn-advanced"]');
    cy.waitIncludes('1 de 1 registros');
    cy.get('[data-cy="4277016757-selecionar"]').click();

    cy.get('[class="p-radiobutton-box"]').eq(2).click();

    cy.get('[aria-label="Selecione o contratado(a)"]').click();
    cy.get('[class="p-dropdown-item"]').first().click();
    cy.waitExistsAndClick('[id="valorFornecedor"]');
    cy.get('[id="valorFornecedor"]').type('1000');

    // cy.intercept('POST', '*/materiais/elastic-search').as('getRegistros');
    // cy.waitExistsAndClickText('Criar Item');
    // cy.wait('@getRegistros');

    // cy.waitExistsAndClick('[aria-label="Adicionar"]');
    // cy.get('input[value="0"]').first().type('1', { force: true });
    // cy.waitExists('input[decimalplaces="2"]');
    // cy.get('input[placeholder="R$"]').type('1,00');

    // cy.waitExistsAndClickText('Ok');
    // cy.waitExistsAndClick('[data-cy="4277016757-pi pi-check"]');
    cy.waitExistsAndClickText('Avançar');

    //Preenchendo informações básicas
    cy.waitExistsAndType('[id="numero"]', '1042024');
    cy.get('[class="p-radiobutton p-component"]').eq(1).click();
    cy.waitExistsAndType('[id="dataVigenciaFinal"]', '01/12/2050');
    cy.waitExistsAndType('[id="dataVigenciaInicial"]', '01/01/2023');

    //Seleciona gestor
    cy.get('[id="search-btn"]').eq(2).click();
    cy.waitExistsAndClick('[class="p-selectable-row"]');
    cy.get('[data-cy="4277016757-selecionar"]').click();

    cy.get('[id="search-btn"]').first().click();
    cy.waitExistsAndClick('[class="p-selectable-row"]');
    cy.get('[data-cy="4277016757-selecionar"]').click();

    //Seleciona autoridade contratante
    cy.waitExistsAndClick('[id="autoridadeContratante"]');
    cy.get('[class="p-dropdown-item"]').first().click();

    //Selecionando fontes de recurso
    cy.get('[id="fontesDeRecurso"]').click();
    cy.get('[class="p-multiselect-item"]').first().click();
    cy.get('[class="p-multiselect-item"]').first().click();

    const fileTypes = ['Designação do gestor e fiscal', 'Equivalente de contrato: Carta-contrato'];

    //Adicionando pdfs e os tipos aos arquivo
    for (let i = 0; i < 2; i++) {
      cy.get('input[type=file]').selectFile('cypress/fixtures/arquivo.pdf', { force: true });
      cy.waitExists('[aria-label="Selecione um tipo"]');
      cy.get('[aria-label="Selecione um tipo"]').eq(i).click();
      cy.contains('[class="p-dropdown-item"]', fileTypes[i]).click();
    }

    //Avançar e salvar
    cy.waitExistsAndClick('[aria-label="Avançar"]');
    cy.waitExistsAndClick('[aria-label="Salvar"]');

    //Verifica se o registro foi salvo
    cy.waitIncludes('Registro salvo com sucesso!');
  });

  it('Read', () => {
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '102/2023');
    cy.get('[id="search-btn-advanced"]').click();
    cy.waitIncludes('102/2023');

    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '1062023');
    cy.get('[id="search-btn-advanced"]').click();
    cy.waitIncludes('1062023');

    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '1102023');
    cy.get('[id="search-btn-advanced"]').click();
    cy.waitIncludes('1102023');

    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '1042024');
    cy.get('[id="search-btn-advanced"]').click();
    cy.waitIncludes('1042024');
  });

  it('Update - Tipo Contrato - Carona', () => {
    //Filtra por um contrato especifico
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '102/2023');
    cy.get('[id="search-btn-advanced"]').click();
    cy.waitIncludes('102/2023');

    cy.waitExistsAndClick('[class="p-button-icon p-c pi pi-list"]');
    cy.get('[class="p-menuitem"]').eq(3).click();

    //Preenchendo informações básicas
    cy.waitExistsAndType('[id="numeroContratoSei"]', '1022023');
    cy.waitExistsAndType('[id="numeroDoe"]', '1022023');

    //Seleciona autoridade contratante
    cy.waitExistsAndClick('[id="autoridadeContratante"]');
    cy.get('[class="p-dropdown-item"]').first().click();

    //Avançar e salvar
    cy.waitExistsAndClick('[aria-label="Avançar"]');
    cy.waitExistsAndClick('[aria-label="Salvar"]');

    //Verifica se o registro foi salvo
    cy.waitIncludes('Registro salvo com sucesso!');
  });

  it('Update - Tipo Empenho - Carona', () => {
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '1062023');
    cy.get('[id="search-btn-advanced"]').click();
    cy.waitIncludes('1062023');

    cy.waitExistsAndClick('[class="p-button-icon p-c pi pi-list"]');
    cy.get('[class="p-menuitem"]').eq(3).click();

    //Preenchendo informações básicas
    cy.waitExistsAndType('[id="numeroContratoSei"]', '1062023');

    //Seleciona autoridade contratante
    cy.waitExistsAndClick('[id="autoridadeContratante"]');
    cy.get('[class="p-dropdown-item"]').first().click();

    //Avançar e salvar
    cy.waitExistsAndClick('[aria-label="Avançar"]');
    cy.waitExistsAndClick('[aria-label="Salvar"]');

    //Verifica se o registro foi salvo
    cy.waitIncludes('Registro salvo com sucesso!');
  });

  it('Update - Tipo Ordem de Serviço - Carona', () => {
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '1102023');
    cy.get('[id="search-btn-advanced"]').click();
    cy.waitIncludes('1102023');

    cy.waitExistsAndClick('[class="p-button-icon p-c pi pi-list"]');
    cy.get('[class="p-menuitem"]').eq(3).click();

    //Preenchendo informações básicas
    cy.waitExistsAndType('[id="numeroContratoSei"]', '1102023');

    //Seleciona autoridade contratante
    cy.waitExistsAndClick('[id="autoridadeContratante"]');
    cy.get('[class="p-dropdown-item"]').first().click();

    //Avançar e salvar
    cy.waitExistsAndClick('[aria-label="Avançar"]');
    cy.waitExistsAndClick('[aria-label="Salvar"]');

    //Verifica se o registro foi salvo
    cy.waitIncludes('Registro salvo com sucesso!');
  });

  it('Update - Carta-contrato - Carona', () => {
    //Filtra por uma licitacao especifica
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '1042024');
    cy.get('[id="search-btn-advanced"]').click();
    cy.waitIncludes('1042024');

    cy.waitExistsAndClick('[class="p-button-icon p-c pi pi-list"]');
    cy.get('[class="p-menuitem"]').eq(3).click();

    //Preenchendo informações básicas
    cy.waitExistsAndType('[id="numeroContratoSei"]', '1042024');

    //Seleciona autoridade contratante
    cy.waitExistsAndClick('[id="autoridadeContratante"]');
    cy.get('[class="p-dropdown-item"]').first().click();

    //Avançar e salvar
    cy.waitExistsAndClick('[aria-label="Avançar"]');
    cy.waitExistsAndClick('[aria-label="Salvar"]');

    //Verifica se o registro foi salvo
    cy.waitIncludes('Registro salvo com sucesso!');
  });

  it('Cria Empenho - Tipo Empenho - Carona', () => {
    //Filtra por uma carona especifica
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '1062023');
    cy.get('[id="search-btn-advanced"]').click();
    cy.waitIncludes('1062023');

    cy.waitExistsAndClick('[class="p-button-icon p-c pi pi-list"]');
    cy.get('[class="p-menuitem"]').eq(1).click();

    cy.waitExistsAndClickText('Novo');

    //Preenchendo informações básicas
    cy.waitExistsAndType('[id="numeroEmpenho"]', '1062023');
    cy.waitExistsAndType('[id="dataEmpenho"]', '09/08/2023');
    cy.waitExistsAndType('[id="valorEmpenho"]', '100,00');

    //Adicionando arquivo pdf
    cy.get('input[type=file]').selectFile('cypress/fixtures/arquivo.pdf', { force: true });
    cy.waitExists('[aria-label="Selecione um tipo"]');
    cy.get('[aria-label="Selecione um tipo"]').first().click();
    cy.contains('[class="p-dropdown-item"]', 'Nota de empenho').click();

    cy.waitExistsAndClick('[aria-label="Salvar"]');

    //Verifica se o registro foi salvo
    cy.waitIncludes('Registro salvo com sucesso!');
  });

  it('Delete - Carona', () => {
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '1062023');
    cy.get('[id="search-btn-advanced"]').click();
    cy.waitIncludes('1062023');

    cy.waitExistsAndClick('[class="p-button-icon p-c pi pi-list"]');
    cy.get('[class="p-menuitem"]').eq(5).click();

    cy.waitExistsAndType('[id="justificativa"]', 'teste');
    cy.waitExistsAndClick('[aria-label="Enviar"]');

    //Verifica se o registro foi salvo
    cy.waitIncludes('Requisição salva com sucesso!');
  });
});
