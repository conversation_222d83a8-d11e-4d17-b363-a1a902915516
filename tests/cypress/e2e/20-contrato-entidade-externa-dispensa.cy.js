describe('CRUD Contrato - Entidade Externa - Dispensa', () => {
  beforeEach(() => {
    cy.login(99999999998);
    cy.selectEntity();
    cy.closeAlerts();

    cy.waitExistsAndClick('[data-cy="cadastros e consultas"]');
    cy.waitExistsAndClick('[data-cy="contrato/aditivo"]');
  });

  it('Create - Tipo Contrato - Dispensa', () => {
    cy.waitExistsAndClick('[aria-label="Novo"]');
    cy.waitExistsAndClickText('Processo de Entidade Externa');

    //Escolhe o tipo do processo
    cy.waitExistsAndClick('[id="tipo"]');
    cy.get('[aria-label="Dispensa"]').click();

    //Preenche dados basicos do processo
    cy.waitExistsAndType('[id="numeroDispensa"]', '1201');
    cy.waitExistsAndType('[id="numeroProcessoAdmSei"]', '2023');
    cy.get('[id="anoProcesso"]').type('2024');
    cy.waitExistsAndType('[id="valorProcessoEntidadeExterna"]', '150,00');

    //Seleciona entidade externa
    cy.get('[id="search-btn"]').first().click();
    cy.waitExistsAndClick('[class="p-selectable-row"]');

    //Seleciona a fundamentação legal
    cy.waitExistsAndClick('[id="fundamentacaoLegalEntidade"]');
    cy.get('[aria-label="Art. 75, inciso I, da Lei nº 14.133/2021"]').click();

    //Seleciona fornecedor
    cy.get('[id="search-btn"]').eq(1).click();
    cy.waitExistsAndClick('[class="p-selectable-row"]');

    //Escolha de itens, intercept garante que nao vai quebrar a sync
    cy.intercept('POST', '*/materiais/elastic-search').as('getRegistros');
    cy.waitExistsAndClickText('Criar Item');
    cy.wait('@getRegistros');

    cy.waitExistsAndClick('[aria-label="Adicionar"]');
    cy.get('input[value="0"]').first().type('1', { force: true });
    cy.waitExists('input[decimalplaces="2"]');
    cy.get('input[value="0,00"]').first().type('1,00', { force: true });
    cy.waitExistsAndClick('[class="p-dropdown p-component p-inputwrapper"]');
    cy.get('[class="p-dropdown-item"]').first().click();
    cy.waitExistsAndClickText('Ok');
    cy.get('[data-cy="4277016757-pi pi-check"]').click();

    cy.waitExistsAndClickText('Avançar');

    //Preenchendo informações básicas
    cy.waitExistsAndType('[id="numero"]', '303');
    cy.get('[id="anoContrato"]').type('2023');
    cy.waitExistsAndType('[id="dataPublicacao"]', '01/01/2023');
    cy.get('[class="p-radiobutton p-component"]').eq(3).click();
    cy.waitExistsAndType('[id="dataVigenciaFinal"]', '01/12/2050');
    cy.waitExistsAndType('[id="numeroContratoSei"]', '8522024');

    cy.get('[class="p-radiobutton p-component"]').eq(0).click();
    cy.waitExistsAndType('[id="dataVigenciaInicial"]', '01/01/2023');

    //Seleciona autoridade contratante
    cy.waitExistsAndClick('[id="autoridadeContratante"]');
    cy.get('[class="p-dropdown-item"]').first().click();

    //Selecionando fontes de recurso
    cy.get('[id="fontesDeRecurso"]').click();
    cy.get('[class="p-multiselect-item"]').first().click();
    cy.get('[class="p-multiselect-item"]').first().click();

    //Seleciona gestor
    cy.get('[id="search-btn"]').eq(2).click();
    cy.waitExistsAndClick('[class="p-selectable-row"]');
    cy.get('[data-cy="4277016757-selecionar"]').click();

    cy.get('[id="search-btn"]').first().click();
    cy.waitExistsAndClick('[class="p-selectable-row"]');
    cy.get('[data-cy="4277016757-selecionar"]').click();

    const fileTypes = [
      'Aceite de participação, pelo Órgão/Entidade gerenciador',
      'Análise dos riscos que possam comprometer o sucesso da licitação e a boa execução contratual, feita pelo Órgão/Entidade participante',
      'Ata da sessão de licitação',
      'Ata de Registro de Preços',
      'Certidão negativa de débitos trabalhistas',
      'Certidão negativa de impedimento (CNEP)',
      'Certidão negativa de inidoneidade (CEIS)',
      'Comprovante da divulgação do contrato no Portal Nacional de Contratações Públicas (PNCP)',
      'Comprovante da regularidade fiscal do contratado',
      'Contrato',
      'Designação do gestor e fiscal',
      'Documento de Formalização de Demanda, com a descrição da necessidade da contratação, do Órgão/Entidade participante',
      'Documento de solicitação formal de cotação de preços, do Órgão/Entidade participante',
      'Edital e Anexos',
      'Estudo Técnico Preliminar do Órgão/Entidade participante',
      'Justificativa da escolha dos fornecedores constantes na cotação de preço de mercado feita pelo Órgão/Entidade participante',
      'Mapa Comparativo de Preços, do Órgão/Entidade gerenciador',
      'Ofício enviado ao Órgão/Entidade gerenciador, com a manifestação de interesse',
      'Parecer Jurídico do Órgão/Entidade participante',
      'Pesquisa de preço de mercado feita pelo Órgão/Entidade participante',
      'Pesquisa de preço praticado por Órgãos e Entidades da Administração Pública, feita pelo Órgão/Entidade participante',
      'Propostas vencedoras',
      'Termo de Adjudicação',
      'Termo de Homologação',
    ];

    //Adicionando pdfs e os tipos aos arquivo
    for (let i = 0; i < 24; i++) {
      cy.get('input[type=file]').selectFile('cypress/fixtures/arquivo.pdf', { force: true });
      cy.waitExists('[aria-label="Selecione um tipo"]');
      i === 20 && cy.waitExistsAndClick('[aria-label="Next Page"]');
      cy.get('[aria-label="Selecione um tipo"]')
        .eq(i % 20)
        .click();
      cy.contains('[class="p-dropdown-item"]', fileTypes[i]).click();
    }

    //Avançar e salvar
    cy.waitExistsAndClick('[aria-label="Avançar"]');
    cy.waitExistsAndClick('[aria-label="Salvar"]');

    //Verifica se o registro foi salvo
    cy.waitIncludes('Registro salvo com sucesso!');
  });

  it('Create - Tipo Empenho - Dispensa', () => {
    cy.waitExistsAndClick('[aria-label="Novo"]');
    cy.waitExistsAndClickText('Processo de Entidade Externa');

    //Escolhe o tipo do processo
    cy.waitExistsAndClick('[id="tipo"]');
    cy.get('[aria-label="Dispensa"]').click();

    //Preenche dados basicos do processo
    cy.waitExistsAndType('[id="numeroDispensa"]', '1201');
    cy.waitExistsAndType('[id="numeroProcessoAdmSei"]', '2023');
    cy.get('[id="anoProcesso"]').type('2024');
    cy.waitExistsAndType('[id="valorProcessoEntidadeExterna"]', '150,00');

    //Seleciona entidade externa
    cy.get('[id="search-btn"]').first().click();
    cy.waitExistsAndClick('[class="p-selectable-row"]');

    //Seleciona a fundamentação legal
    cy.waitExistsAndClick('[id="fundamentacaoLegalEntidade"]');
    cy.get('[aria-label="Art. 75, inciso I, da Lei nº 14.133/2021"]').click();

    //Seleciona tipo empenho
    cy.get('[class="p-radiobutton-box"]').first().click();

    //Seleciona fornecedor
    cy.get('[id="search-btn"]').eq(1).click();
    cy.waitExistsAndClick('[class="p-selectable-row"]');

    //Escolha de itens, intercept garante que nao vai quebrar a sync
    cy.intercept('POST', '*/materiais/elastic-search').as('getRegistros');
    cy.waitExistsAndClickText('Criar Item');
    cy.wait('@getRegistros');

    cy.waitExistsAndClick('[aria-label="Adicionar"]');
    cy.get('input[value="0"]').first().type('1', { force: true });
    cy.waitExists('input[decimalplaces="2"]');
    cy.get('input[value="0,00"]').first().type('1,00', { force: true });
    cy.waitExistsAndClick('[class="p-dropdown p-component p-inputwrapper"]');
    cy.get('[class="p-dropdown-item"]').first().click();
    cy.waitExistsAndClickText('Ok');
    cy.get('[data-cy="4277016757-pi pi-check"]').click();

    cy.waitExistsAndClickText('Avançar');

    //Preenchendo informações básicas do contrato
    cy.waitExistsAndType('[id="numero"]', '3072023');
    cy.get('[id="objeto"]').type('Histórico do empenho');
    cy.get('[id="dataEmpenho"]').type('01/01/2023');
    cy.get('[aria-label="Ordinário"]').click();
    cy.get('[class="p-radiobutton p-component"]').eq(1).click();
    cy.waitExistsAndType('[id="numeroContratoSei"]', '9352024');

    //Seleciona gestor
    cy.get('[id="search-btn"]').eq(2).click();
    cy.waitExistsAndClick('[class="p-selectable-row"]');
    cy.get('[data-cy="4277016757-selecionar"]').click();

    cy.get('[id="search-btn"]').first().click();
    cy.waitExistsAndClick('[class="p-selectable-row"]');
    cy.get('[data-cy="4277016757-selecionar"]').click();

    //Seleciona autoridade contratante
    cy.waitExistsAndClick('[id="autoridadeContratante"]');
    cy.get('[class="p-dropdown-item"]').first().click();

    //Selecionando fontes de recurso
    cy.get('[id="fontesDeRecurso"]').click();
    cy.get('[class="p-multiselect-item"]').first().click();
    cy.get('[class="p-multiselect-item"]').first().click();

    const fileTypes = [
      'Aceite de participação, pelo Órgão/Entidade gerenciador',
      'Análise dos riscos que possam comprometer o sucesso da licitação e a boa execução contratual, feita pelo Órgão/Entidade participante',
      'Ata da sessão de licitação',
      'Ata de Registro de Preços',
      'Certidão negativa de débitos trabalhistas',
      'Certidão negativa de impedimento (CNEP)',
      'Certidão negativa de inidoneidade (CEIS)',
      'Comprovante da regularidade fiscal do contratado',
      'Equivalente de contrato: Empenho',
      'Designação do gestor e fiscal',
      'Documento de Formalização de Demanda, com a descrição da necessidade da contratação, do Órgão/Entidade participante',
      'Documento de solicitação formal de cotação de preços, do Órgão/Entidade participante',
      'Edital e Anexos',
      'Estudo Técnico Preliminar do Órgão/Entidade participante',
      'Justificativa da escolha dos fornecedores constantes na cotação de preço de mercado feita pelo Órgão/Entidade participante',
      'Mapa Comparativo de Preços, do Órgão/Entidade gerenciador',
      'Ofício enviado ao Órgão/Entidade gerenciador, com a manifestação de interesse',
      'Parecer Jurídico do Órgão/Entidade participante',
      'Pesquisa de preço de mercado feita pelo Órgão/Entidade participante',
      'Pesquisa de preço praticado por Órgãos e Entidades da Administração Pública, feita pelo Órgão/Entidade participante',
      'Propostas vencedoras',
      'Termo de Adjudicação',
      'Termo de Homologação',
    ];

    //Adicionando pdfs e os tipos aos arquivo
    for (let i = 0; i < 23; i++) {
      cy.get('input[type=file]').selectFile('cypress/fixtures/arquivo.pdf', { force: true });
      cy.waitExists('[aria-label="Selecione um tipo"]');
      i === 20 && cy.waitExistsAndClick('[aria-label="Next Page"]');
      cy.get('[aria-label="Selecione um tipo"]')
        .eq(i % 20)
        .click();
      cy.contains('[class="p-dropdown-item"]', fileTypes[i]).click();
    }

    //Avançar e salvar
    cy.waitExistsAndClick('[aria-label="Avançar"]');
    cy.waitExistsAndClick('[aria-label="Salvar"]');

    //Verifica se o registro foi salvo
    cy.waitIncludes('Registro salvo com sucesso!');
  });

  it('Create - Tipo Ordem de Serviço - Dispensa', () => {
    cy.waitExistsAndClick('[aria-label="Novo"]');
    cy.waitExistsAndClickText('Processo de Entidade Externa');

    //Escolhe o tipo do processo
    cy.waitExistsAndClick('[id="tipo"]');
    cy.get('[aria-label="Dispensa"]').click();

    //Preenche dados basicos do processo
    cy.waitExistsAndType('[id="numeroDispensa"]', '1201');
    cy.waitExistsAndType('[id="numeroProcessoAdmSei"]', '2023');
    cy.get('[id="anoProcesso"]').type('2024');
    cy.waitExistsAndType('[id="valorProcessoEntidadeExterna"]', '150,00');

    //Seleciona entidade externa
    cy.get('[id="search-btn"]').first().click();
    cy.waitExistsAndClick('[class="p-selectable-row"]');

    //Seleciona a fundamentação legal
    cy.waitExistsAndClick('[id="fundamentacaoLegalEntidade"]');
    cy.get('[aria-label="Art. 75, inciso I, da Lei nº 14.133/2021"]').click();

    //Seleciona ordem de serviço
    cy.get('[class="p-radiobutton-box"]').eq(1).click();

    //Seleciona fornecedor
    cy.get('[id="search-btn"]').eq(1).click();
    cy.waitExistsAndClick('[class="p-selectable-row"]');

    //Escolha de itens, intercept garante que nao vai quebrar a sync
    cy.intercept('POST', '*/materiais/elastic-search').as('getRegistros');
    cy.waitExistsAndClickText('Criar Item');
    cy.wait('@getRegistros');

    cy.waitExistsAndClick('[aria-label="Adicionar"]');
    cy.get('input[value="0"]').first().type('1', { force: true });
    cy.waitExists('input[decimalplaces="2"]');
    cy.get('input[value="0,00"]').first().type('1,00', { force: true });
    cy.waitExistsAndClick('[class="p-dropdown p-component p-inputwrapper"]');
    cy.get('[class="p-dropdown-item"]').first().click();
    cy.waitExistsAndClickText('Ok');
    cy.get('[data-cy="4277016757-pi pi-check"]').click();

    cy.waitExistsAndClickText('Avançar');

    //Preenchendo informações básicas do contrato
    cy.waitExistsAndType('[id="numero"]', '3112023');
    cy.waitExistsAndType('[id="numeroContratoSei"]', '3112023');
    cy.get('[class="p-radiobutton p-component"]').eq(1).click();
    //Seleciona gestor
    cy.get('[id="search-btn"]').eq(2).click();
    cy.waitExistsAndClick('[class="p-selectable-row"]');
    cy.get('[data-cy="4277016757-selecionar"]').click();

    cy.get('[id="search-btn"]').first().click();
    cy.waitExistsAndClick('[class="p-selectable-row"]');
    cy.get('[data-cy="4277016757-selecionar"]').click();

    //Seleciona autoridade contratante
    cy.waitExistsAndClick('[id="autoridadeContratante"]');
    cy.get('[class="p-dropdown-item"]').first().click();

    //Selecionando fontes de recurso
    cy.get('[id="fontesDeRecurso"]').click();
    cy.get('[class="p-multiselect-item"]').first().click();
    cy.get('[class="p-multiselect-item"]').first().click();

    const fileTypes = [
      'Aceite de participação, pelo Órgão/Entidade gerenciador',
      'Análise dos riscos que possam comprometer o sucesso da licitação e a boa execução contratual, feita pelo Órgão/Entidade participante',
      'Ata da sessão de licitação',
      'Ata de Registro de Preços',
      'Certidão negativa de débitos trabalhistas',
      'Certidão negativa de impedimento (CNEP)',
      'Certidão negativa de inidoneidade (CEIS)',
      'Comprovante da regularidade fiscal do contratado',
      'Equivalente de contrato: Ordem de Serviço/Entrega',
      'Designação do gestor e fiscal',
      'Documento de Formalização de Demanda, com a descrição da necessidade da contratação, do Órgão/Entidade participante',
      'Documento de solicitação formal de cotação de preços, do Órgão/Entidade participante',
      'Edital e Anexos',
      'Estudo Técnico Preliminar do Órgão/Entidade participante',
      'Justificativa da escolha dos fornecedores constantes na cotação de preço de mercado feita pelo Órgão/Entidade participante',
      'Mapa Comparativo de Preços, do Órgão/Entidade gerenciador',
      'Ofício enviado ao Órgão/Entidade gerenciador, com a manifestação de interesse',
      'Parecer Jurídico do Órgão/Entidade participante',
      'Pesquisa de preço de mercado feita pelo Órgão/Entidade participante',
      'Pesquisa de preço praticado por Órgãos e Entidades da Administração Pública, feita pelo Órgão/Entidade participante',
      'Propostas vencedoras',
      'Termo de Adjudicação',
      'Termo de Homologação',
    ];

    //Adicionando pdfs e os tipos aos arquivo
    for (let i = 0; i < 23; i++) {
      cy.get('input[type=file]').selectFile('cypress/fixtures/arquivo.pdf', { force: true });
      cy.waitExists('[aria-label="Selecione um tipo"]');
      i === 20 && cy.waitExistsAndClick('[aria-label="Next Page"]');
      cy.get('[aria-label="Selecione um tipo"]')
        .eq(i % 20)
        .click();
      cy.contains('[class="p-dropdown-item"]', fileTypes[i]).click();
    }

    //Avançar e salvar
    cy.waitExistsAndClick('[aria-label="Avançar"]');
    cy.waitExistsAndClick('[aria-label="Salvar"]');

    //Verifica se o registro foi salvo
    cy.waitIncludes('Registro salvo com sucesso!');
  });

  it('Create - Tipo Carta-contrato - Dispensa', () => {
    cy.waitExistsAndClick('[aria-label="Novo"]');
    cy.waitExistsAndClickText('Processo de Entidade Externa');

    //Escolhe o tipo do processo
    cy.waitExistsAndClick('[id="tipo"]');
    cy.get('[aria-label="Dispensa"]').click();

    //Preenche dados basicos do processo
    cy.waitExistsAndType('[id="numeroDispensa"]', '1201');
    cy.waitExistsAndType('[id="numeroProcessoAdmSei"]', '2023');
    cy.get('[id="anoProcesso"]').type('2024');
    cy.waitExistsAndType('[id="valorProcessoEntidadeExterna"]', '150,00');

    //Seleciona entidade externa
    cy.get('[id="search-btn"]').first().click();
    cy.waitExistsAndClick('[class="p-selectable-row"]');

    //Seleciona a fundamentação legal
    cy.waitExistsAndClick('[id="fundamentacaoLegalEntidade"]');
    cy.get('[aria-label="Art. 75, inciso I, da Lei nº 14.133/2021"]').click();

    // seleciona carta-contrato
    cy.get('[class="p-radiobutton-box"]').eq(2).click();

    //Seleciona fornecedor
    cy.get('[id="search-btn"]').eq(1).click();
    cy.waitExistsAndClick('[class="p-selectable-row"]');

    //Escolha de itens, intercept garante que nao vai quebrar a sync
    cy.intercept('POST', '*/materiais/elastic-search').as('getRegistros');
    cy.waitExistsAndClickText('Criar Item');
    cy.wait('@getRegistros');

    cy.waitExistsAndClick('[aria-label="Adicionar"]');
    cy.get('input[value="0"]').first().type('1', { force: true });
    cy.waitExists('input[decimalplaces="2"]');
    cy.get('input[value="0,00"]').first().type('1,00', { force: true });
    cy.waitExistsAndClick('[class="p-dropdown p-component p-inputwrapper"]');
    cy.get('[class="p-dropdown-item"]').first().click();
    cy.waitExistsAndClickText('Ok');
    cy.get('[data-cy="4277016757-pi pi-check"]').click();

    cy.waitExistsAndClickText('Avançar');

    //Preenchendo informações básicas
    cy.waitExistsAndType('[id="numero"]', '10132024');
    cy.waitExistsAndType('[id="numeroContratoSei"]', '2012023');
    cy.get('[class="p-radiobutton p-component"]').eq(1).click();
    cy.waitExistsAndType('[id="dataVigenciaFinal"]', '01/12/2050');
    cy.waitExistsAndType('[id="dataVigenciaInicial"]', '01/01/2023');

    //Seleciona autoridade contratante
    cy.waitExistsAndClick('[id="autoridadeContratante"]');
    cy.get('[class="p-dropdown-item"]').first().click();

    //Selecionando fontes de recurso
    cy.get('[id="fontesDeRecurso"]').click();
    cy.get('[class="p-multiselect-item"]').first().click();
    cy.get('[class="p-multiselect-item"]').first().click();

    //Seleciona gestor
    cy.get('[id="search-btn"]').eq(2).click();
    cy.waitExistsAndClick('[class="p-selectable-row"]');
    cy.get('[data-cy="4277016757-selecionar"]').click();

    cy.get('[id="search-btn"]').first().click();
    cy.waitExistsAndClick('[class="p-selectable-row"]');
    cy.get('[data-cy="4277016757-selecionar"]').click();

    const fileTypes = [
      'Aceite de participação, pelo Órgão/Entidade gerenciador',
      'Análise dos riscos que possam comprometer o sucesso da licitação e a boa execução contratual, feita pelo Órgão/Entidade participante',
      'Ata da sessão de licitação',
      'Ata de Registro de Preços',
      'Certidão negativa de débitos trabalhistas',
      'Certidão negativa de impedimento (CNEP)',
      'Certidão negativa de inidoneidade (CEIS)',
      'Comprovante da regularidade fiscal do contratado',
      'Equivalente de contrato: Carta-contrato',
      'Designação do gestor e fiscal',
      'Documento de Formalização de Demanda, com a descrição da necessidade da contratação, do Órgão/Entidade participante',
      'Documento de solicitação formal de cotação de preços, do Órgão/Entidade participante',
      'Edital e Anexos',
      'Estudo Técnico Preliminar do Órgão/Entidade participante',
      'Justificativa da escolha dos fornecedores constantes na cotação de preço de mercado feita pelo Órgão/Entidade participante',
      'Mapa Comparativo de Preços, do Órgão/Entidade gerenciador',
      'Ofício enviado ao Órgão/Entidade gerenciador, com a manifestação de interesse',
      'Parecer Jurídico do Órgão/Entidade participante',
      'Pesquisa de preço de mercado feita pelo Órgão/Entidade participante',
      'Pesquisa de preço praticado por Órgãos e Entidades da Administração Pública, feita pelo Órgão/Entidade participante',
      'Propostas vencedoras',
      'Termo de Adjudicação',
      'Termo de Homologação',
    ];

    //Adicionando pdfs e os tipos aos arquivo
    for (let i = 0; i < 23; i++) {
      cy.get('input[type=file]').selectFile('cypress/fixtures/arquivo.pdf', { force: true });
      cy.waitExists('[aria-label="Selecione um tipo"]');
      i === 20 && cy.waitExistsAndClick('[aria-label="Next Page"]');
      cy.get('[aria-label="Selecione um tipo"]')
        .eq(i % 20)
        .click();
      cy.contains('[class="p-dropdown-item"]', fileTypes[i]).click();
    }

    //Avançar e salvar
    cy.waitExistsAndClick('[aria-label="Avançar"]');
    cy.waitExistsAndClick('[aria-label="Salvar"]');

    //Verifica se o registro foi salvo
    cy.waitIncludes('Registro salvo com sucesso!');
  });

  it('Read', () => {
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '303/2023');
    cy.get('[id="search-btn-advanced"]').click();
    cy.waitIncludes('303/2023');

    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '3072023');
    cy.get('[id="search-btn-advanced"]').click();
    cy.waitIncludes('3072023');

    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '3112023');
    cy.get('[id="search-btn-advanced"]').click();
    cy.waitIncludes('3112023');

    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '10132024');
    cy.get('[id="search-btn-advanced"]').click();
    cy.waitIncludes('10132024');
  });

  it('Update - Tipo Contrato - Dispensa', () => {
    //Filtra por uma licitacao especifica
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '3072023');
    cy.get('[id="search-btn-advanced"]').click();
    cy.waitIncludes('3072023');

    cy.waitExistsAndClick('[class="p-button-icon p-c pi pi-list"]');
    cy.get('[class="p-menuitem"]').eq(3).click();

    //Preenchendo informações básicas
    cy.waitExistsAndType('[id="numeroContratoSei"]', '3072023');

    //Seleciona autoridade contratante
    cy.waitExistsAndClick('[id="autoridadeContratante"]');
    cy.get('[class="p-dropdown-item"]').first().click();

    //Avançar e salvar
    cy.waitExistsAndClick('[aria-label="Avançar"]');
    cy.waitExistsAndClick('[aria-label="Salvar"]');

    //Verifica se o registro foi salvo
    cy.waitIncludes('Registro salvo com sucesso!');
  });

  it('Update - Tipo Empenho - Dispensa', () => {
    //Filtra por uma licitacao especifica
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '3072023');
    cy.get('[id="search-btn-advanced"]').click();
    cy.waitIncludes('3072023');

    cy.waitExistsAndClick('[class="p-button-icon p-c pi pi-list"]');
    cy.get('[class="p-menuitem"]').eq(3).click();

    //Preenchendo informações básicas
    cy.waitExistsAndType('[id="numeroContratoSei"]', '3072023');

    //Seleciona autoridade contratante
    cy.waitExistsAndClick('[id="autoridadeContratante"]');
    cy.get('[class="p-dropdown-item"]').first().click();

    //Avançar e salvar
    cy.waitExistsAndClick('[aria-label="Avançar"]');
    cy.waitExistsAndClick('[aria-label="Salvar"]');

    //Verifica se o registro foi salvo
    cy.waitIncludes('Registro salvo com sucesso!');
  });

  it('Update - Tipo Ordem de Serviço - Dispensa', () => {
    //Filtra por uma licitacao especifica
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '3112023');
    cy.get('[id="search-btn-advanced"]').click();
    cy.waitIncludes('3112023');

    cy.waitExistsAndClick('[class="p-button-icon p-c pi pi-list"]');
    cy.get('[class="p-menuitem"]').eq(3).click();

    //Preenchendo informações básicas
    cy.waitExistsAndType('[id="numeroContratoSei"]', '3112023');

    //Seleciona autoridade contratante
    cy.waitExistsAndClick('[id="autoridadeContratante"]');
    cy.get('[class="p-dropdown-item"]').first().click();

    //Avançar e salvar
    cy.waitExistsAndClick('[aria-label="Avançar"]');
    cy.waitExistsAndClick('[aria-label="Salvar"]');

    //Verifica se o registro foi salvo
    cy.waitIncludes('Registro salvo com sucesso!');
  });

  it('Update - Carta-contrato - Dispensa', () => {
    //Filtra por uma licitacao especifica
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '10132024');
    cy.get('[id="search-btn-advanced"]').click();
    cy.waitIncludes('10132024');

    cy.waitExistsAndClick('[class="p-button-icon p-c pi pi-list"]');
    cy.get('[class="p-menuitem"]').eq(3).click();

    //Preenchendo informações básicas
    cy.waitExistsAndType('[id="numeroContratoSei"]', '2012024');

    //Seleciona autoridade contratante
    cy.waitExistsAndClick('[id="autoridadeContratante"]');
    cy.get('[class="p-dropdown-item"]').first().click();

    //Avançar e salvar
    cy.waitExistsAndClick('[aria-label="Avançar"]');
    cy.waitExistsAndClick('[aria-label="Salvar"]');

    //Verifica se o registro foi salvo
    cy.waitIncludes('Registro salvo com sucesso!');
  });

  it('Cria Empenho - Tipo Ordem de Serviço - Dispensa', () => {
    //Filtra por uma licitacao especifica
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '3112023');
    cy.get('[id="search-btn-advanced"]').click();
    cy.waitIncludes('3112023');

    cy.waitExistsAndClick('[class="p-button-icon p-c pi pi-list"]');
    cy.get('[class="p-menuitem"]').eq(1).click();

    cy.waitExistsAndClickText('Novo');

    //Preenchendo informações básicas
    cy.waitExistsAndType('[id="numeroEmpenho"]', '3112023');
    cy.waitExistsAndType('[id="dataEmpenho"]', '09/08/2023');
    cy.waitExistsAndType('[id="valorEmpenho"]', '100,00');

    const fileTypes = ['Nota de empenho'];

    //Adicionando pdfs e os tipos aos arquivo
    for (let i = 0; i < 1; i++) {
      cy.get('input[type=file]').selectFile('cypress/fixtures/arquivo.pdf', { force: true });
      cy.waitExists('[aria-label="Selecione um tipo"]');
      cy.get('[aria-label="Selecione um tipo"]').eq(i).click();
      cy.contains('[class="p-dropdown-item"]', fileTypes[i]).click();
    }

    cy.waitExistsAndClick('[aria-label="Salvar"]');

    //Verifica se o registro foi salvo
    cy.waitIncludes('Registro salvo com sucesso!');
  });

  it('Cria Rescisão Contratual - Tipo Empenho - Dispensa', () => {
    //Filtra por uma licitacao especifica
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '3072023');
    cy.get('[id="search-btn-advanced"]').click();
    cy.waitIncludes('3072023');

    cy.waitExistsAndClick('[class="p-button-icon p-c pi pi-list"]');
    cy.get('[class="p-menuitem"]').eq(4).click();

    cy.waitExistsAndClickText('Sim');
    
    //Preenchendo informações básicas
    cy.waitExistsAndType('[id="dataAvisoRescisao"]', '29/07/2023');
    cy.waitExistsAndClick('[id="motivoRescisao"]');
    cy.waitExistsAndClick('[aria-label="Rescisão"]');
    cy.waitExistsAndType('[id="descricaoRescisao"]', 'teste cypress');

    cy.waitExistsAndClick('[aria-label="Enviar"]');

    //Verifica se o registro foi salvo
    cy.waitIncludes('Registro salvo com sucesso!');
  });

  it('Delete - Dispensa', () => {
    //Filtra por uma licitacao especifica
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '3072023');
    cy.get('[id="search-btn-advanced"]').click();
    cy.waitIncludes('3072023');

    cy.waitExistsAndClick('[class="p-button-icon p-c pi pi-list"]');
    cy.get('[class="p-menuitem"]').eq(5).click();

    cy.waitExistsAndType('[id="justificativa"]', 'teste');
    cy.waitExistsAndClick('[aria-label="Enviar"]');

    //Verifica se o registro foi salvo
    cy.waitIncludes('Requisição salva com sucesso!');
  });
});
