describe('Julgar Requisição de moficação', () => {
  before(() => {
    //Cria requisições
    cy.login(99999999998);
    cy.selectEntity();
    cy.closeAlerts();

    //Licitação
    cy.waitExistsAndClick('[data-cy="cadastros e consultas"]');
    cy.waitExistsAndClick('[data-cy="licitacao"]');

    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '017/2020');
    cy.waitExistsAndClick('[id="search-btn-advanced"]');
    cy.waitExistsAndClickText('Pregão Presencial - 017 / 2020');

    cy.waitExistsAndClick('[data-cy="1555221225-excluir"]');
    cy.waitExistsAndType('[id="justificativa"]', 'teste');
    cy.waitExistsAndClickText('Enviar');

    cy.waitExistsAndClick('[class="pi pi-lock tab-icon-lock"]');
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '017/2021');
    cy.waitExistsAndClick('[id="search-btn-advanced"]');
    cy.waitExists('[class="p-card-body"]');

    cy.get('[class="p-card-body"]').first().click();
    cy.get('[data-cy="1555221225-excluir"]').click();
    cy.waitExistsAndType('[id="justificativa"]', 'teste');
    cy.waitExistsAndClickText('Enviar');

    //Carona
    // cy.waitExistsAndClick('[data-cy="adesao/carona"]');

    // cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '005/2022');
    // cy.waitExistsAndClick('[id="search-btn-advanced"]');
    // cy.waitExists('[class="data-card p-4"]');

    // cy.get('[data-cy="1678432678-pi pi-ellipsis-v"]').first().click();
    // cy.get('[class="p-menuitem-icon pi pi-trash"]').click();
    // cy.waitExistsAndType('[id="justificativa"]', 'teste');
    // cy.waitExistsAndClickText('Enviar');

    //Dispensa
    cy.waitExistsAndClick('[data-cy="dispensa"]');

    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '1907');
    cy.waitExistsAndClick('[id="search-btn-advanced"]');
    cy.waitExists('[class="data-card p-4"]');

    cy.get('[data-cy="2643273457-mais opcoes"]').first().click();
    cy.get('[class="p-menuitem-icon pi pi-trash"]').click();
    cy.waitExistsAndType('[id="justificativa"]', 'teste');
    cy.waitExistsAndClickText('Enviar');

    //Limpa filtro
    cy.get('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]').clear();
    cy.get('[id="search-btn-advanced"]').click();
    cy.waitExists('[class="data-card p-4"]');

    //Escolhe outra dispensa para ser removida e clica em excluir
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '4342');
    cy.get('[id="search-btn-advanced"]').click();
    cy.waitExists('[class="data-card p-4"]');

    cy.get('[data-cy="2643273457-mais opcoes"]').first().click();
    cy.get('[class="p-menuitem-icon pi pi-trash"]').click();
    cy.waitExistsAndType('[id="justificativa"]', 'teste');
    cy.waitExistsAndClickText('Enviar');

    //Inexigibilidade
    cy.waitExistsAndClick('[data-cy="inexigibilidade"]');

    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '088');
    cy.get('[id="search-btn-advanced"]').click();
    cy.waitExists('[class="data-card p-4"]');

    cy.get('[data-cy="3253598109-mais opcoes"]').first().click();
    cy.get('[class="p-menuitem-icon pi pi-trash"]').click();
    cy.waitExistsAndType('[id="justificativa"]', 'teste');
    cy.waitExistsAndClickText('Enviar');

    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '089');
    cy.get('[id="search-btn-advanced"]').click();
    cy.waitExists('[class="data-card p-4"]');

    cy.get('[data-cy="3253598109-mais opcoes"]').first().click();
    cy.get('[class="p-menuitem-icon pi pi-trash"]').click();
    cy.waitExistsAndType('[id="justificativa"]', 'teste');
    cy.waitExistsAndClickText('Enviar');
  });

  beforeEach(() => {
    //Login Auditor
    cy.login('suporte.sistemas');
    cy.closeAlerts();
    cy.waitExistsAndClick('[data-cy="auditoria"]');
    cy.waitExistsAndClick('[data-cy="requisicoes de modificacao"]');
  });

  it('Julgar Requisição - Licitação - Rejeitar', () => {
    //Seleciona a Licitação
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '017/2020');
    cy.waitExistsAndClick('[id="search-btn-advanced"]');
    cy.waitExistsAndClick('[title="Julgar Requisição"]');
    cy.waitExists('[class="tab-header"]');

    cy.get('[class="tab-header"]').eq(7).click();
    cy.waitExistsAndType('[placeholder="Informe uma justificativa"]', 'Teste cypress tela de Julgamento de Requisição de Modificação.');
    cy.waitExistsAndClickText('Indeferir');

    //Verifica se o julgamento foi salvo
    cy.waitIncludes('Julgamento salvo com sucesso!');
  });

  it('Julgar Requisição - Licitação - Aceitar', () => {
    //Seleciona a Licitação
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '017/2021');
    cy.waitExistsAndClick('[id="search-btn-advanced"]');
    cy.waitExistsAndClick('[title="Julgar Requisição"]');
    cy.waitExists('[class="tab-header"]');

    cy.get('[class="tab-header"]').eq(7).click();
    cy.waitExistsAndType('[placeholder="Informe uma justificativa"]', 'Teste cypress tela de Julgamento de Requisição de Modificação.');
    cy.waitExistsAndClickText('Deferir');

    //Verifica se o julgamento foi salvo
    cy.waitIncludes('Julgamento salvo com sucesso!');
  });

  it.skip('Julgar Requisição - Carona - Rejeitar', () => {
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '005/2022');
    cy.waitExistsAndClick('[id="search-btn-advanced"]');
    cy.waitExistsAndClick('[title="Julgar Requisição"]');
    cy.waitExists('[class="tab-header"]');

    cy.get('[class="tab-header"]').eq(4).click();
    cy.waitExistsAndType('[placeholder="Informe uma justificativa"]', 'Teste cypress tela de Julgamento de Requisição de Modificação.');
    cy.waitExistsAndClickText('Indeferir');

    //Verifica se o julgamento foi salvo
    cy.waitIncludes('Julgamento salvo com sucesso!');
  });

  // it('Julgar Requisição - Dispensa - Rejeitar', () => {
  //   //Seleciona a dispensa 1907 processo
  //   cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '1907');
  //   cy.waitExistsAndClick('[id="search-btn-advanced"]');
  //   cy.waitExistsAndClick('[title="Julgar Requisição"]');
  //   cy.waitExists('[class="tab-header"]');

  //   cy.get('[class="tab-header"]').eq(4).click();
  //   cy.waitExistsAndType('[placeholder="Informe uma justificativa"]', 'Teste cypress tela de Julgamento de Requisição de Modificação.');
  //   cy.waitExistsAndClickText('Indeferir');

  //   //Verifica se o julgamento foi salvo
  //   cy.waitIncludes('Julgamento salvo com sucesso!');
  // });

  it('Julgar Requisição - Dispensa - Aceitar', () => {
    //Seleciona a dispensa 1907 processo
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '4342');
    cy.waitExistsAndClick('[id="search-btn-advanced"]');
    cy.waitExistsAndClick('[title="Julgar Requisição"]');
    cy.waitExists('[class="tab-header"]');

    cy.get('[class="tab-header"]').eq(4).click();
    cy.waitExistsAndType('[placeholder="Informe uma justificativa"]', 'Teste cypress tela de Julgamento de Requisição de Modificação.');
    cy.waitExistsAndClickText('Deferir');

    //Verifica se o julgamento foi salvo
    cy.waitIncludes('Julgamento salvo com sucesso!');
  });

  it('Julgar Requisição - Inexigibilidade - Rejeitar', () => {
    //Seleciona a Inexigibilidade processo
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '088');
    cy.waitExistsAndClick('[id="search-btn-advanced"]');
    cy.waitExistsAndClick('[title="Julgar Requisição"]');
    cy.waitExists('[class="tab-header"]');

    cy.get('[class="tab-header"]').eq(4).click();
    cy.waitExistsAndType('[placeholder="Informe uma justificativa"]', 'Teste cypress tela de Julgamento de Requisição de Modificação.');
    cy.waitExistsAndClickText('Indeferir');

    //Verifica se o julgamento foi salvo
    cy.waitIncludes('Julgamento salvo com sucesso!');
  });

  it('Julgar Requisição - Inexigibilidade - Aceitar', () => {
    //Seleciona a Inexigibilidade processo
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '089');
    cy.waitExistsAndClick('[id="search-btn-advanced"]');
    cy.waitExistsAndClick('[title="Julgar Requisição"]');
    cy.waitExists('[class="tab-header"]');

    cy.get('[class="tab-header"]').eq(4).click();
    cy.waitExistsAndType('[placeholder="Informe uma justificativa"]', 'Teste cypress tela de Julgamento de Requisição de Modificação.');
    cy.waitExistsAndClickText('Deferir');

    //Verifica se o julgamento foi salvo
    cy.waitIncludes('Julgamento salvo com sucesso!');
  });
});
