describe('CRUD Carona', () => {
  beforeEach(() => {
    cy.login(99999999998);
    cy.selectEntity();
    cy.closeAlerts();

    cy.get('[data-cy="cadastros e consultas"]').click();
    cy.waitExistsAndClick('[data-cy="adesao/carona"]');
  });

  it('Create', () => {
    //Clica para criar um novo registro
    cy.waitExistsAndClickText('Novo');
    // Aguardar e clicar no botão "Sim" do diálogo de confirmação
    cy.get('.p-confirm-dialog-accept').click();

    //Seleciona orgão gerenciador da Ata
    cy.get('[data-cy="4225974019-pi pi-search"]').first().click();
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', 'CÂMARA MUNICIPAL DE RIO BRANCO ');
    cy.get('[data-cy="4225974019-pi pi-search"]').eq(2).click();
    cy.get('[class="p-selectable-row"]').first().click();

    //Seleciona uma licitação
    cy.get('[data-cy="4225974019-pi pi-search"]').eq(1).click();
    cy.get('[class="p-selectable-row"]').first().click();

    //Preenchendo dados basicos
    cy.get('[id="numeroProcessoGerenciadorAta"]').type('1328/2022');
    cy.get('[id="anoCarona"]').type('2024');
    cy.get('[id="numeroProcessoAdministrativo"]').type('1234444');
    cy.get('[id="dataAdesao"]').type('01/01/2023');
    cy.get('[id="dataValidadeAta"]').type('01/01/2026');
    cy.waitExistsAndClick('[id="responsavelAdesaoCarona"]');
    cy.get('[class="p-dropdown-item"]').first().click();

    //Selecionando fontes de recurso
    cy.get('[id="fontesDeRecurso"]').click();
    cy.get('[class="p-multiselect-item"]').first().click();
    cy.get('[id="fontesDeRecurso"]').click();

    //Preenchendo fundamentação legal
    cy.get('[id="fundamentacaoLegal"]').type('Teste da fundamentação legal da carona');

    const fileTypes = [
      'Documento de Formalização de Demanda, com a descrição da necessidade da contratação',
      'Documento de solicitação formal de cotação de preços do Órgão/Entidade aderente (carona)',
      'Estudo Técnico Preliminar do Órgão/Entidade aderente (carona)',
      'Justificativa da escolha dos fornecedores constantes na cotação de preço de mercado feita pelo Órgão/Entidade aderente (carona)',
      'Justificativa da vantagem da adesão',
      'Mapa Comparativo de Preços do Órgão/Entidade aderente (carona)',
      'Ofício ao fornecedor/prestador da ata, consultando a disponibilidade em fornecer produtos/prestar serviços',
      'Ofício ao Órgão/Entidade gerenciador da ata, pedindo autorização para adesão',
      'Ofício com a resposta do fornecedor/prestador da ata de registro de preços',
      'Ofício com a resposta do Órgão/Entidade gerenciador da ata de registro de preços',
      'Parecer jurídico do Órgão/Entidade aderente (carona)',
      'Pesquisa de preço de mercado feita pelo Órgão/Entidade aderente (carona)',
      'Pesquisa de preço praticado por Órgãos e Entidades da Administração Pública feita pelo Órgão/Entidade aderente (carona)',
      'Publicação do extrato de adesão à ata de registro de preços',
      'Autorização da autoridade competente',
    ];

    //Adicionando pdfs e os tipos aos arquivo
    for (let i = 0; i < 15; i++) {
      cy.get('input[type=file]').selectFile('cypress/fixtures/arquivo.pdf', { force: true });
      cy.waitExists('[aria-label="Selecione um tipo"]');
      cy.get('[aria-label="Selecione um tipo"]').eq(i).click();
      cy.contains('[class="p-dropdown-item"]', fileTypes[i]).click();
    }

    cy.waitExistsAndClickText('Avançar');

    //Adicionando detentores
    cy.waitExistsAndClick('[aria-label="Adicionar"]');
    cy.get('[class="p-selection-column"]').first().click();
    cy.waitExistsAndClickText('Selecionar');
    cy.waitExistsAndClick('[aria-label="Avançar"]');

    //Adicionando informações ao componente de vencedores
    cy.get('[class="p-picklist-item"]').first().click();
    cy.get('[class="p-button-icon p-c pi pi-angle-double-right"]').click({ force: true });

    cy.get('[class="p-picklist-item"]').first().click();
    cy.get('[class="p-button-icon p-c pi pi-angle-double-right"]').click({ force: true });

    cy.get('[data-cy="4225974019-pi pi-check"]').first().click();

    cy.get('[aria-label="Avançar"]').click();
    cy.waitExistsAndClick('[aria-label="Salvar"]');

    cy.waitIncludes('Registro salvo com sucesso!');
  });

  it('Read', () => {
    //Acessa uma carona
    cy.get('[data-cy="1678432678-mais opcoes"]').first().click();
    cy.get('[class="p-menuitem-icon pi pi-eye"]').click();
    cy.waitExists('[class="pi pi-lock tab-icon-lock"]');

    //Volta para a listagem e acessa novamente a mesma carona
    cy.get('[class="pi pi-lock tab-icon-lock"]').click();
    cy.get('[data-cy="1678432678-mais opcoes"]').first().click();
    cy.get('[class="p-menuitem-icon pi pi-eye"]').click();
    cy.waitExists('[class="pi pi-times tab-icon-times"]');

    //Apenas um icone de fechar deve ser encontrado, se der erro é porque abriu a visualização da carona duplicada
    cy.get('[class="pi pi-times tab-icon-times"]').click();

    //Observa o valor do processo SEI da carona no cardList e verifica se é igual nos detalhes, garantindo que o processo certo é aberto
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '13282022');
    cy.get('[id="search-btn-advanced"]').click();
    cy.waitExists('[class="data-card p-4"]');

    cy.get('[class="data-card p-4"]').first().should('contain', '13282022');
    cy.get('[data-cy="1678432678-mais opcoes"]').first().click();
    cy.get('[class="p-menuitem-icon pi pi-eye"]').click();
    cy.waitExists('[class="data-card p-4"]');
    cy.get('body').should('contain', '2022');

    //Limpa filtros
    cy.get('[class="pi pi-lock tab-icon-lock"]').click();
    cy.get('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]').clear();
    cy.get('[id="search-btn-advanced"]').click();
    cy.waitExists('[class="data-card p-4"]');

    //Volta pra listgem, verifica o valor do provesso SEI do cardList e verifica se há o valor quando os detalhes são abertos.
    //Garante que mesmo com um processo aberto, quando eu tentar abrir outro processo o redirecionamento vai ser certo
    cy.get('[class="pi pi-lock tab-icon-lock"]').click();
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '13282022');
    cy.get('[id="search-btn-advanced"]').click();
    cy.waitExists('[class="data-card p-4"]');
    cy.get('[class="data-card p-4"]').first().should('contain', '13282022');
    cy.get('[data-cy="1678432678-mais opcoes"]').first().click();
    cy.get('[class="p-menuitem-icon pi pi-eye"]').click();
    cy.waitExists('[class="data-card p-4"]');
    cy.get('body').should('contain', '1234444');

    cy.get('[class="pi pi-times tab-icon-times"]').first().click();
  });

  it('Update', () => {
    //edição simples
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '13282022');
    cy.waitExistsAndClick('[id="search-btn-advanced"]');
    cy.waitExists('[class="data-card p-4"]');

    cy.get('[data-cy="1678432678-mais opcoes"]').first().click();
    cy.get('[class="p-menuitem-icon pi pi-pencil"]').click();

    cy.waitExistsAndType('[id="numeroProcessoGerenciadorAta"]', '13282023');

    //Avança as demais etapas e salva a edição
    cy.waitExistsAndClick('[aria-label="Avançar"]');
    cy.waitExistsAndClick('[aria-label="Avançar"]');
    cy.waitExistsAndClick('[aria-label="Avançar"]');
    cy.waitExistsAndClick('[aria-label="Salvar"]');

    //Verica se a alteracao feita foi persistida e está listada
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '13282023');
    cy.waitExistsAndClick('[id="search-btn-advanced"]');
    cy.waitExists('[class="data-card p-4"]');
    cy.get('[class="data-card p-4"]').first().should('contain', '13282023');
  });

  it('Delete', () => {
    //Selecionando a Carona a ser removida
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '13282023');
    cy.waitExistsAndClick('[id="search-btn-advanced"]');

    cy.waitExists('[data-cy="1678432678-mais opcoes"]');
    cy.get('[data-cy="1678432678-mais opcoes"]').first().click();
    cy.get('[class="p-menuitem-icon pi pi-trash"]').click();

    //Adicionando uma justificativa a requisição de remoção e clicando em enviar
    cy.waitExistsAndType('[id="justificativa"]', 'teste');
    cy.waitExistsAndClickText('Enviar');

    //Acessando a requisição de remoção o que garante que ela foi criada
    cy.waitExistsAndType('[placeholder="Digite um texto ou use os filtros para uma pesquisa avançada"]', '13282023');
    cy.waitExistsAndClick('[id="search-btn-advanced"]');

    cy.waitExists('[data-cy="1678432678-mais opcoes"]');
    cy.get('[data-cy="1678432678-mais opcoes"]').first().click();
    cy.waitExistsAndClick('[class="p-menuitem-icon pi pi-exclamation-triangle"]');
  });
});
