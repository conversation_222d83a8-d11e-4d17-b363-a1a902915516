## Testes de aceitação Cypress

### Pipeline

Os testes de aceitação cypress executam no pipeline sempre que um novo merge-request está aberto e na branch main. Para isso ele sobe o sqlserver, backend e frontend em um único container utilizando a imagem `registry.gitlab.com/lsi-ufcg/tce-ac/novo-licon/licon:init-novo-licon-dev` e contendo a versão do sistema naquela branch. A intenção é verificar principalmente se as funcionalidades de CRUD do sistema continuam funcionando.

### Execução local para alteração dos testes

Para alterar um teste cypress localmente e verificar sua execução, é necessário executar no diretório `tests`:
- O comando `docker-compose up --force-recreate` que executa o sistema em um só container contendo banco, frontend e backend.
- O comando `yarn cypress` que disponibiliza uma plataforma para visualização da execução da suíte de testes.


**O docker-compose vai subir o novo-licon completo em um único container e expor o acesso das portas 1433 (sqlserver), 8080 (backend) e 3000 (frontend) ao localhost. Assim, é necessário estar com essas portas livres no momentos da execução.**