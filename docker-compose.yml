version: '3.7'

services:
  licon-be:
    container_name: licon-be
    build:
      context: backend
      dockerfile: Dockerfile-dev
    ports:
      - '8080:8080'
    depends_on:
      - db
    environment:
      SPRING_DATASOURCE_URL: ***********************************************************************
      SPRING_DATASOURCE_USERNAME: sa
      SPRING_DATASOURCE_PASSWORD: Oil2005iGIS
    restart: always
    networks:
      - backend
      - frontend
  licon-fe:
    container_name: licon-fe
    build:
      context: frontend
      dockerfile: Dockerfile-dev
    ports:
      - '3000:3000'
    restart: always
    depends_on:
      - licon-be
    networks:
      - frontend
  db:
    image: registry.gitlab.com/lsi-ufcg/tce-ac/glassfish/modelolicon:licon_db_01122021
    container_name: licon_db
    environment:
      ACCEPT_EULA: Y
      MSSQL_SA_PASSWORD: Oil2005iGIS
    ports:
      - '1433:1433'
    expose:
      - 1433
    volumes:
      - sql_licondata:/var/opt/mssql
    networks:
      backend:
        ipv4_address: **********

networks:
  frontend:
  backend:
    ipam:
      config:
        - subnet: **********/24

volumes:
  sql_licondata:
    external: true


