import { observable } from 'mobx';
import DomainBase from 'fc/domains/DomainBase';

class LicitacaoSrpView extends DomainBase {
  @observable id;
  @observable numero;
  @observable termoReferencia;
  @observable entidade;
  @observable lei;
  @observable ano;
  @observable valorEstimado;
  @observable dataCadastroPreparatoria;
  @observable dataAbertura;
  @observable valorAdjudicado;
  @observable objeto;
  @observable naturezasDoObjeto;
  @observable vencedores;

  static getDomainAttributes() {
    return [
      'id',
      'numero',
      'termoReferencia',
      'entidade',
      'lei',
      'ano',
      'valorEstimado',
      'dataCadastroPreparatoria',
      'dataAbertura',
      'valorAdjudicado',
      'objeto',
      'naturezasDoObjeto',
      'vencedores',
    ];
  }
}

export default LicitacaoSrpView;
