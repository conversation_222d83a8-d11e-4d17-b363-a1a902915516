import { observable } from 'mobx';
import DomainBase from 'fc/domains/DomainBase';

class Acompanhamento extends DomainBase {
  @observable numero;
  @observable descricao;
  @observable valor;
  @observable status;
  @observable percentualConclusao;
  @observable ultimaMedicao;
  @observable dataInicio;
  @observable dataFim;
  @observable fase;

  static getDomainAttributes() {
    return [
      'numero',
      'descricao',
      'valor',
      'status',
      'percentualConclusao',
      'ultimaMedicao',
      'dataInicio',
      'dataFim',
      'fase',
    ];
  }
}

export default Acompanhamento;
