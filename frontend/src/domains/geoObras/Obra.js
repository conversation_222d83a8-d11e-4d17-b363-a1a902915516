import { observable } from 'mobx';
import DomainBase from 'fc/domains/DomainBase';

class GeoObra extends DomainBase {
  @observable id;
  @observable numero;
  @observable contrato;
  @observable tipo;
  @observable endereco;
  @observable cep;
  @observable dimensoes;
  @observable valor;
  @observable descricao;
  @observable incorporavelPatrimonio;
  @observable tipoObjeto;
  @observable subtipoObjeto;
  @observable dataPrevistaInicio;
  @observable dataPrevistaConclusao;
  @observable status;
  @observable fase;
  @observable possuiConveniosAssociados;
  @observable convenios;
  @observable possuiRecursosProprios;
  @observable dataInicio;
  @observable dataFim;
  @observable dataConclusao;
  @observable dataRecebimento;
  @observable tipoEncerramento;
  @observable recursosProprios;
  @observable municipio;

  static getDomainAttributes() {
    return [
      'id',
      'numero',
      'contrato',
      'tipo',
      'endereco',
      'cep',
      'dimensoes',
      'valor',
      'descricao',
      'incorporavelPatrimonio',
      'tipoObjeto',
      'subtipoObjeto',
      'dataPrevistaInicio',
      'dataPrevistaConclusao',
      'status',
      'possuiConveniosAssociados',
      'possuiRecursosProprios',
      'convenios',
      'dataInicio',
      'dataFim',
      'dataConclusao',
      'dataRecebimento',
      'tipoEncerramento',
      'possuiRecursosProprios',
      'recursosProprios',
      'municipio',
    ];
  }
}

export default GeoObra;
