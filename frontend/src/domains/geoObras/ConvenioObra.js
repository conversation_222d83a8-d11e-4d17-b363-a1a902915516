import DomainBase from 'fc/domains/DomainBase';
import { observable } from 'mobx';

class ConvenioObra extends DomainBase {
  @observable id;
  @observable numero;
  @observable convenente;
  @observable cnpjConvenente;
  @observable dataAssinatura;
  @observable dataTermino;
  @observable valor;

  static getDomainAttributes() {
    return ['id', 'numero', 'convenente', 'cnpjConvenente', 'dataAssinatura', 'dataTermino', 'valor'];
  }
}

export default ConvenioObra;
