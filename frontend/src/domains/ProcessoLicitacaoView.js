import { observable } from 'mobx';
import DomainBase from 'fc/domains/DomainBase';

class ProcessoLicitacaoView extends DomainBase {
  @observable id;
  @observable numero;
  @observable numeroAnoLicitacao;
  @observable ano;
  @observable objeto;
  @observable entidade;
  @observable fase;
  @observable orgao;
  @observable dataCadastro;
  @observable valorEstimado;
  @observable dataAbertura;
  @observable usuario;

  static getDomainAttributes() {
    return [
      'id',
      'numero',
      'numeroAnoLicitacao',
      'ano',
      'objeto',
      'entidade',
      'fase',
      'orgao',
      'dataCadastro',
      'valorEstimado',
      'dataAbertura',
      'usuario',
    ];
  }
}

export default ProcessoLicitacaoView;
