import SearchTypes from 'fc/components/AdvancedSearch/SearchTypes';
import ItemChecklistService from '~/services/ItemChecklistService';
import IndexBase from 'fc/stores/IndexBase';
import ItemChecklist from '~/domains/ItemChecklist';
import AsyncDropDownStore from 'fc/stores/AsyncDropdownStore';
import SecaoChecklist from '~/domains/SecaoChecklist';
import SecaoChecklistService from '~/services/SecaoChecklistService';
import DadosEstaticosService from '~/services/DadosEstaticosService';

class ItemChecklistIndexStore extends IndexBase {
  constructor() {
    super(ItemChecklistService, ItemChecklist, 'titulo', 'asc');
  }

  getAdvancedSearchParams() {
    return [
      {
        field: 'titulo',
        label: 'Título',
        type: SearchTypes.TEXT,
      },
      {
        field: 'legislacao',
        label: 'Legislação',
        type: SearchTypes.TEXT,
      },
      {
        field: 'suspenso',
        label: 'Suspenso',
        type: SearchTypes.ENUM,
        options: DadosEstaticosService.getSimNao(),
      },
      {
        field: 'ordem',
        label: 'Ordem',
        type: SearchTypes.NUMBER,
      },
      {
        field: 'secao',
        label: 'Seção',
        type: SearchTypes.ASYNC_QUERY,
        store: new AsyncDropDownStore(SecaoChecklist, SecaoChecklistService, 'titulo', 'id'),
      },
    ];
  }
}

export default ItemChecklistIndexStore;
