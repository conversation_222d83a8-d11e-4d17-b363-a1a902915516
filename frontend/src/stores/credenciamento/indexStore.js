import IndexBase from 'fc/stores/IndexBase';
import Credenciamento from '~/domains/Credenciamento';
import FonteRecurso from '~/domains/FonteRecurso';
import SearchTypes from 'fc/components/AdvancedSearch/SearchTypes';
import AsyncDropDownStore from 'fc/stores/AsyncDropdownStore';
import AsyncMultiselectStore from 'fc/stores/AsyncMultiselectStore';
import { checkUserGroup, showErrorNotification } from 'fc/utils/utils';
import CredenciamentoService from '~/services/CredenciamentoService';
import FonteRecursoService from '~/services/FonteRecursoService';
import EntidadeService from '~/services/EntidadeService';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import AppStore from 'fc/stores/AppStore';
import Entidade from '~/domains/Entidade';
import moment from 'moment';
import { runInAction } from 'mobx';

class CredenciamentoIndexStore extends IndexBase {
  constructor() {
    super(CredenciamentoService, Credenciamento, 'updatedAt', 'desc');
  }

  getFilterSuggest() {
    const entidade = AppStore.getContextEntity();
    const filterSuggest = [
      {
        id: '',
        field: 'status',
        operator: 'NOT_EQUAL_TO',
        value: 'REMOVIDA',
        formatted: '',
        fixed: true,
        invisible: true,
        completeParam: {
          field: 'status',
          label: 'Status',
          type: SearchTypes.ENUM,
          options: DadosEstaticosService.getStatusContrato(),
        },
      },
    ];

    if (entidade) {
      filterSuggest.push({
        id: '',
        field: 'entidade',
        operator: 'EQUAL_TO',
        value: entidade,
        formatted: '',
        fixed: true,
        invisible: true,
        completeParam: {
          field: 'entidade',
          label: 'Órgão/Entidade',
          type: SearchTypes.ASYNC_QUERY,
          store: new AsyncDropDownStore(Entidade, EntidadeService, 'nome', 'id'),
        },
      });
    }

    return filterSuggest;
  }

  getAdvancedSearchParams() {
    const isAuditor = checkUserGroup(['Auditor', 'Auditor - Demais Inspetorias']);
    let searchParams = [];

    if (isAuditor) {
      searchParams = [
        {
          field: 'entidade',
          label: 'Órgão/Entidade',
          type: SearchTypes.ASYNC_QUERY,
          store: new AsyncDropDownStore(Entidade, EntidadeService, 'nome', 'id'),
        },
      ];
    }
    searchParams.push(
      {
        field: 'numeroProcesso',
        label: 'Número do Processo Administrativo',
        type: SearchTypes.TEXT,
      },
      {
        field: 'numero',
        label: 'Número do Credenciamento',
        type: SearchTypes.TEXT,
      },
      {
        field: 'objeto',
        label: 'Objeto',
        type: SearchTypes.TEXT,
      },
      {
        field: 'fontesDeRecurso',
        label: 'Fontes de Recurso',
        type: SearchTypes.ASYNC_QUERY,
        store: new AsyncMultiselectStore(FonteRecurso, FonteRecursoService, 'nome', 'id'),
      },
      {
        field: 'status',
        label: 'Status do Processo',
        type: SearchTypes.ENUM,
        options: DadosEstaticosService.getStatusLicitacao(),
      },
      {
        field: 'observacoes',
        label: 'Observações',
        type: SearchTypes.TEXT,
      }
    );
    return searchParams;
  }

  isRequisicaoModificacao(dataCadastro) {
    let result = false;

    if (dataCadastro) {
      const data = moment(dataCadastro);
      const current = moment();
      const diff = moment.duration(current.diff(data));

      if (diff.asHours() > 24) {
        result = true;
      }
    }

    return result;
  }

  getById(id, callback) {
    if (id) {
      this.service
        .getById(id)
        .then((response) => {
          return callback && runInAction(() => callback(response.data));
        })
        .catch((e) => {
          showErrorNotification(e);
        });
    }
  }
}

export default CredenciamentoIndexStore;
