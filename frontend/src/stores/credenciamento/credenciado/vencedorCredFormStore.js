import { action, computed, makeObservable, observable } from 'mobx';
import { extractRules } from 'fc/utils/formRules';

class VencedorCredenciadoFormStore {
  @observable termoReferencia;
  @observable licitantes;
  @observable lotes = [];
  @observable vencedores = [];
  @observable lotesFracassados = [];
  @observable itensDesertos = [];
  @observable mapItensLote = observable.map();
  @observable mapLotes = observable.map();
  @observable selectedLote;

  //LEI ANTIGA
  @observable vencedor = {};
  @observable edit = false;
  @observable lastVencedor;
  //LEI ANTIGA

  onChangeVencedores;
  onChangeLotesFracassados;
  onChangeItensDesertos;
  onChangeTipoAdjudicacao;
  tipoAdjudicacao;
  modifyVencedor = false;
  oldLotesFracassados = [];
  oldItensDesertos = [];
  processoMigrado;

  constructor() {
    makeObservable(this);
  }

  @action
  initialize(
    termoReferencia,
    licitantes = [],
    vencedores = [],
    lotesFracassados = [],
    itensDesertos = [],
    tipoAdjudicacao = 'ITEM',
    onChangeVencedores,
    onChangeLotesFracassados,
    onChangeItensDesertos,
    onChangeTipoAdjudicacao,
    processoMigrado = false
  ) {
    this.termoReferencia = termoReferencia;
    this.lotes = termoReferencia?.lotes;
    this.licitantes = licitantes;
    this.vencedores = vencedores;
    this.lotesFracassados = lotesFracassados;
    this.itensDesertos = itensDesertos;
    this.tipoAdjudicacao = tipoAdjudicacao;
    this.onChangeVencedores = onChangeVencedores;
    this.onChangeLotesFracassados = onChangeLotesFracassados;
    this.onChangeItensDesertos = onChangeItensDesertos;
    this.onChangeTipoAdjudicacao = onChangeTipoAdjudicacao;
    this.processoMigrado = processoMigrado;
    this.selectedLote = this.lotes && this.lotes?.length && this.lotes[0];
    (this.tipoAdjudicacao == 'LOTE' || this.tipoAdjudicacao == 'GLOBAL') && this.initializeLotesLicitante();
  }

  @action
  initializeLotesLicitante() {
    this.lotes?.forEach((l) => {
      l.fracassado = l.itens.some((i) => this.lotesFracassados?.map((lf) => lf?.itemLote?.id).includes(i.id));

      l.deserto = l.itens.some((i) => this.itensDesertos?.map((lf) => lf?.itemLote?.id).includes(i.id));

      l.itens.forEach((i) => {
        const indexvencedorItem = this.vencedores.findIndex((v) => v.itemLote.id == i.id);
        if (indexvencedorItem >= 0) {
          l.licitante = this.vencedores[indexvencedorItem].licitante;
          this.addItem(i.id, this.vencedores[indexvencedorItem]);
        } else {
          const itemVencedor = {
            loteItem: '',
            motivoAlteracao: '',
            obsMotivoAlteracao: '',
            licitante: undefined,
            itemLote: i,
            valor: 0,
            quantidade: i.quantidade,
            valorUnitario: i.valorUnitarioEstimado,
            valor: parseFloat(i.quantidade) * parseFloat(i.valorUnitarioEstimado),
            marcaModelo: '',
            especificacao: '',
            observacao: '',
            desconto: 0,
            preenchido: false,
            fracassado: false,
            descricaoComplementar: i.descricaoComplementar ?? '',
          };
          this.addItem(i.id, itemVencedor);
        }
      });

      this.addLote(l.id, l);
    });
  }

  @computed
  get rules() {
    const definition = this.rulesDefinition();
    const result = extractRules(definition, this.vencedor);
    Object.keys(result).forEach((key) => {
      const error = result[key].error;
      if (error) {
        result.hasError = true;
      }
    });
    return result;
  }

  @computed
  get itensLotes() {
    const itens = [];
    this.lotes?.forEach((l) => {
      itens.push(...l.itens);
    });
    return itens;
  }

  @computed
  get hasItensNotFilleds() {
    return this.vencedores.some((v) => !v.preenchido || this.itensDesertos.some((i) => !i.preenchido));
  }

  @computed
  get hasQuantityNotAssign() {
    return (
      !this.itensLotes ||
      this.itensLotes?.some((item) => {
        return (
          !this.lotesFracassados?.some((i) => i.itemLote.id === item.id) &&
          this.getQuantidadeItemDisponivel(item.id) !== 0
        );
      })
    );
  }

  @computed
  get allItensFracassadosOrDesertos() {
    let count = 0;
    this.itensLotes?.forEach((item) => {
      if (this.lotesFracassados?.some((i) => i.itemLote.id === item.id)) {
        count += 1;
      } else if (this.itensDesertos?.some((i) => i.itemLote.id === item.id)) {
        count += 1;
      }
    });
    return count == this.itensLotes?.length ?? 0;
  }

  @computed
  get hasLicitantesNotAssign() {
    return !this.licitantes || this.licitantes.some((l) => !this.vencedores?.some((i) => i.licitante.id === l.id));
  }

  @computed
  get itensDisponiveisParaFracasso() {
    const itensLoteUnico = this.itensLotes;
    return itensLoteUnico.filter(
      (item) =>
        !this.vencedores.some((v) => v.itemLote.id == item.id) &&
        !this.lotesFracassados.some((itemFracassado) => itemFracassado.itemLote.id == item.id) &&
        !this.itensDesertos.some((itemFracassado) => itemFracassado.itemLote.id == item.id)
    );
  }

  @computed
  get itensFracassados() {
    const itensLoteUnico = this.itensLotes;
    return itensLoteUnico.filter((item) =>
      this.lotesFracassados.some((itemFracassado) => itemFracassado.itemLote.id == item.id)
    );
  }

  @computed
  get itensDisponiveisParaDesertar() {
    const itensLoteUnico = this.itensLotes;
    return itensLoteUnico.filter(
      (item) =>
        this.getQuantidadeItemDisponivel(item.id) !== 0 &&
        !this.lotesFracassados.some((itemFracassado) => itemFracassado.itemLote.id == item.id) &&
        !this.itensDesertos.some((itemDeserto) => itemDeserto.itemLote.id == item.id)
    );
  }

  @computed
  get itensDesertosList() {
    const itensLoteUnico = this.itensLotes;
    return itensLoteUnico.filter((item) =>
      this.itensDesertos.some((itemDeserto) => itemDeserto.itemLote.id == item.id)
    );
  }

  @computed
  get itensGroupByVencedor() {
    const vencedoresDistinct = [];
    this.vencedores.map((v) => {
      if (vencedoresDistinct.find((vencedor) => vencedor.licitante.id == v.licitante.id)) {
        const vencedorPersitedIndex = vencedoresDistinct.findIndex(
          (vencedor) => vencedor.licitante.id == v.licitante.id
        );

        const vencedorSelected = vencedoresDistinct[vencedorPersitedIndex];
        vencedorSelected.itens.push({ ...v });
        vencedoresDistinct[vencedorPersitedIndex] = vencedorSelected;
      } else {
        const vencedor = { licitante: v.licitante, itens: [{ ...v }] };
        vencedoresDistinct.push(vencedor);
      }
    });
    return vencedoresDistinct;
  }

  rulesDefinition() {
    let rules = {};
    if (this.processoMigrado) {
      rules = {
        licitante: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      };

      if (this.modifyVencedor) {
        rules = {
          licitante: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
          valor: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
          motivoAlteracao: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
          obsMotivoAlteracao: [
            { rule: 'required', message: 'Por favor, preencha o campo' },
            { rule: 'isMaxLength', maxLength: 4000, message: 'Por favor, diminua o tamanho do campo' },
          ],
        };
      }
    } else {
      rules = {
        licitante: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
        valor: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      };

      if (this.modifyVencedor) {
        rules = {
          licitante: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
          valor: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
          motivoAlteracao: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
          obsMotivoAlteracao: [
            { rule: 'required', message: 'Por favor, preencha o campo' },
            { rule: 'isMaxLength', maxLength: 4000, message: 'Por favor, diminua o tamanho do campo' },
          ],
        };
      }
    }

    return rules;
  }

  getRule(field) {
    return this.rules[field] ?? {};
  }

  getValorTotal(legislacaoAntiga = false) {
    const valorTotal = (this.vencedores || []).reduce((acc, vencedor) => {
      const valor = legislacaoAntiga ? vencedor.valor : vencedor.valor;
      return acc + parseFloat(valor);
    }, 0);
    return Number.isNaN(valorTotal) ? 0 : valorTotal;
  }

  @action
  resetAll() {
    this.onChangeItensDesertos([]);
    this.onChangeLotesFracassados([]);
    this.onChangeVencedores([]);
    this.vencedores = [];
    this.lotesFracassados = [];
    this.itensDesertos = [];
    this.vencedor = {};
    this.mapItensLote.clear();
    this.mapLotes.clear();
    this.selectedLote = undefined;
  }

  @action
  editVencedor(vencedor, callback) {
    this.vencedor = { ...vencedor };
    this.edit = true;
    this.backupVencedor();
    callback && callback();
  }

  @action
  removeVencedor(vencedor) {
    this.vencedores = this.vencedores.filter((v) => !(v.licitante?.id === vencedor.licitante?.id));
    this.updateVencedores();
  }

  @action
  adicionarVencedor(callback) {
    if (!this.rules.hasError) {
      if (this.vencedores?.length) {
        this.vencedores = [
          ...this.vencedores.filter(
            (vencedor) =>
              vencedor.licitante?.id !== this.vencedor.licitante?.id && vencedor.licitante?.id !== this.lastVencedor?.id
          ),
          this.vencedor,
        ];
      } else {
        this.vencedores = [this.vencedor];
      }
      this.updateVencedores();
      showNotification('success', null, `Vencedor ${this.edit ? 'Editado' : 'Adicionado'}.`);
      this.vencedor = {};
      callback && callback();
    }
  }

  @action
  changeModifyVencedor() {
    this.modifyVencedor = !this.modifyVencedor;
    this.vencedor = {
      ...this.vencedor,
      motivoAlteracao: '',
      obsMotivoAlteracao: '',
    };
  }

  @action
  backupVencedor() {
    this.lastVencedor = { ...this.vencedor };
  }

  @action
  resetVencedor() {
    this.vencedor = { ...this.lastVencedor };
  }

  @action
  updateLicitante(licitante) {
    if (!this.modifyVencedor) {
      const vencedorAdicionado = this.vencedores?.find((v) => v.licitante?.id === licitante.id);
      if (vencedorAdicionado) {
        this.vencedor = { ...vencedorAdicionado, licitante: licitante };
      } else {
        this.vencedor = { ...this.vencedor, licitante: licitante, preenchido: true };
      }
    } else {
      this.vencedor = { ...this.vencedor, licitante: licitante };
    }
  }

  @action
  updateAttributeLicitante(attribute, event) {
    let value = event;
    if (event && event.value) {
      value = event.value;
    }
    if (event && event.target) {
      value = event.target.value;
    }
    this.vencedor[attribute] = value;
  }

  @action
  updateTipoAdjudicacao(value) {
    this.tipoAdjudicacao = value;
    this.onChangeTipoAdjudicacao(value);
  }

  @action
  updateSelectedLote(value) {
    this.selectedLote = value;
  }

  @action
  updateVencedores() {
    this.onChangeVencedores && this.onChangeVencedores(this.vencedores);
  }

  @action
  updateAttributeItemDeserto(idItemLote, attribute, event) {
    let value = event;
    if (event && event.value) {
      value = event.value;
    }
    if (event && event.target) {
      value = event.target.value;
    }

    this.itensDesertos = this.itensDesertos.map((i) => {
      if (i.itemLote.id == idItemLote) {
        i[attribute] = value;
      }
      return { ...i };
    });

    this.updateItensDesertos();
  }

  @action
  updateAttributeItem(idItemLote, idLicitante, attribute, event, callback) {
    let value = event;
    if (event && event.value) {
      value = event.value;
    }
    if (event && event.target) {
      value = event.target.value;
    }

    const indexVencedor = this.vencedores.findIndex(
      (v) => v.itemLote.id === idItemLote && v.licitante.id == idLicitante
    );

    if (attribute == 'quantidade' || attribute == 'valorUnitario' || attribute == 'desconto') {
      let qtdUsada = this.vencedores.reduce((accumulator, currentValue) => {
        if (currentValue.itemLote.id === idItemLote) {
          return accumulator + currentValue.quantidade;
        }
        return accumulator;
      }, 0);

      qtdUsada = parseFloat(qtdUsada);
      const qtdTotal = parseFloat(this.vencedores.find((v) => v.itemLote.id === idItemLote)?.itemLote.quantidade);
      const qtdRestante = parseFloat(qtdTotal - qtdUsada);

      const qtdVencedor = parseFloat(
        this.vencedores.find((v) => v.itemLote.id === idItemLote && v.licitante.id == idLicitante).quantidade
      );

      if (attribute == 'quantidade') {
        if (parseFloat(value) <= parseFloat(qtdRestante + qtdVencedor)) {
          this.vencedores[indexVencedor][attribute] = value;
        } else {
          this.vencedores[indexVencedor][attribute] = qtdRestante + qtdVencedor;
        }
      } else if (attribute == 'valorUnitario' || attribute == 'desconto') {
        this.vencedores[indexVencedor][attribute] = value;
      }

      const valor = parseFloat(
        (
          this.vencedores[indexVencedor].valorUnitario * this.vencedores[indexVencedor].quantidade -
          (this.vencedores[indexVencedor].valorUnitario *
            this.vencedores[indexVencedor].quantidade *
            this.vencedores[indexVencedor].desconto) /
            100
        ).toFixed(3)
      );

      this.updateAttributeItem(idItemLote, idLicitante, 'valor', valor);
    } else {
      this.vencedores[indexVencedor][attribute] = value;
    }

    this.updateVencedores();
    callback && callback();
  }

  @action
  updateAttributeItemLote(idItemLote, attribute, event, callback) {
    let value = event;
    if (event && event.value) {
      value = event.value;
    }
    if (event && event.target) {
      value = event.target.value;
    }

    const currentItem = this.getItem(idItemLote);

    currentItem[attribute] = value;

    if (attribute == 'quantidade' || attribute == 'valorUnitario' || attribute == 'desconto') {
      currentItem['valor'] = parseFloat(
        (
          currentItem['valorUnitario'] * currentItem['quantidade'] -
          (currentItem['valorUnitario'] * currentItem['quantidade'] * currentItem['desconto']) / 100
        ).toFixed(3)
      );
    }

    this.addItem(idItemLote, currentItem);

    this.updateVencedoresLotes();
    callback && callback();
  }

  @action
  updateLotesFracassados() {
    this.onChangeLotesFracassados && this.onChangeLotesFracassados(this.lotesFracassados);
  }

  @action
  updateItensDesertos() {
    this.onChangeItensDesertos && this.onChangeItensDesertos(this.itensDesertos);
  }

  @action
  updateVencedoresLotes() {
    this.vencedores = Array.from(this.mapItensLote.values())
      .map((v) => v.get())
      .filter((v) => !v.fracassado && v.licitante);

    this.onChangeVencedores && this.onChangeVencedores(this.vencedores);
  }

  @action
  setLicitantes(licitantes) {
    this.licitantes = licitantes;
  }

  @action
  createOrUpdateVencedorLoteUnico(itens, licitante) {
    let itensVencedor = [];

    itens.forEach((item) => {
      if (!this.vencedores.some((v) => v.licitante.id == licitante.id && item.id == v.itemLote.id)) {
        const itemVencedor = {
          loteItem: '',
          motivoAlteracao: '',
          obsMotivoAlteracao: '',
          licitante: this.licitantes.find((l) => l.id == licitante.id),
          itemLote: item,
          valor: 0,
          quantidade: this.getQuantidadeItemDisponivel(item.id),
          valorUnitario: item.valorUnitarioEstimado,
          valor: parseFloat(this.getQuantidadeItemDisponivel(item.id)) * parseFloat(item.valorUnitarioEstimado),
          marcaModelo: '',
          especificacao: '',
          observacao: '',
          desconto: 0,
          preenchido: false,
          descricaoComplementar: item.descricaoComplementar ?? '',
        };
        itensVencedor.push(itemVencedor);
      }
    });

    this.vencedores = [...this.vencedores, ...itensVencedor].filter(
      (v) => v.licitante.id != licitante.id || itens.some((item) => item.id == v.itemLote.id)
    );

    this.updateVencedores();
  }

  @action
  removeItemVencedor(item) {
    this.vencedores = this.vencedores.filter((v) => v.itemLote.id != item.itemLote.id);
    this.updateVencedores();
  }

  @action
  createOrUpdateLoteUnicoFracassado(itens) {
    this.lotesFracassados = [...itens.map((i) => ({ itemLote: i }))];
  }

  @action
  createOrUpdateLoteFracassado(itens) {
    this.lotesFracassados = [...this.lotesFracassados, ...itens.map((i) => ({ itemLote: i }))];
  }

  @action
  createOrUpdateItensDesertos(itens) {
    this.itensDesertos = itens.map((i) => {
      if (!this.itensDesertos.some((itemDeserto) => i.id == itemDeserto.itemLote.id)) {
        return { itemLote: i, quantidade: this.getQuantidadeItemDisponivel(i.id), preenchido: false };
      } else {
        const index = this.itensDesertos.findIndex((itemDeserto) => itemDeserto.itemLote.id == i.id);
        return { ...this.itensDesertos[index] };
      }
    });
  }

  @action
  createOrUpdateVencedorLote(lote, licitanteId) {
    const currentLote = this.getLote(lote.id);
    const licitante = this.licitantes.find((l) => l.id == licitanteId);
    currentLote.licitante = licitante;
    this.addLote(lote.id, currentLote);

    currentLote.itens.forEach((i) => {
      const currentItem = this.getItem(i.id);
      if (currentItem.licitante !== licitante) {
        currentItem.licitante = licitante;
        this.addItem(i.id, currentItem);
      }
    });

    this.updateVencedoresLotes();
  }

  @action
  createOrUpdateLoteItensDesertos(itens) {
    this.itensDesertos = [
      ...this.itensDesertos,
      ...itens.map((i) => ({ itemLote: i, quantidade: i.quantidade, preenchido: true })),
    ];
  }

  @action
  resetLotesFracassados() {
    this.lotesFracassados = [...this.oldLotesFracassados];
  }

  @action
  resetItensDesertos() {
    this.itensDesertos = [...this.oldItensDesertos];
  }

  @action
  setOldLotesItensFracassados() {
    this.oldLotesFracassados = [...this.lotesFracassados];
  }

  @action
  setOldLotesItensDesertos() {
    this.oldItensDesertos = [...this.itensDesertos];
  }

  @action
  setDescontoItem(idItemLote, idLicitante, desconto, attr = 'desconto') {
    if (desconto > 100) {
      desconto = 100;
    }
    this.updateAttributeItem(idItemLote, idLicitante, attr, desconto);
    this.updateVencedores();
  }

  @action
  setDescontoLote(idItemLote, desconto) {
    if (desconto > 100) {
      desconto = 100;
    }
    this.updateAttributeItemLote(idItemLote, 'desconto', desconto);
    this.updateVencedores();
  }

  @action
  setLoteItemFracassado(lote) {
    lote.itens.forEach((i) => {
      const currentItem = this.getItem(i.id);
      currentItem.fracassado = true;
      currentItem.quantidade = i.quantidade;
      currentItem.valorUnitario = i.valorUnitarioEstimado;
      currentItem.valor = parseFloat(i.quantidade) * parseFloat(i.valorUnitarioEstimado);
      currentItem.marcaModelo = '';
      currentItem.especificacao = '';
      currentItem.observacao = '';
      currentItem.desconto = 0;
      this.addItem(i.id, currentItem);
    });

    const currentLote = this.getLote(lote.id);
    currentLote.fracassado = true;
    currentLote.licitante = undefined;
    this.addLote(lote.id, currentLote);

    this.createOrUpdateLoteFracassado(lote.itens);
    this.updateLotesFracassados();
    this.updateVencedoresLotes();
  }

  @action
  setLoteItemNaoFracassado(lote) {
    const loteCurrent = this.getLote(lote.id);
    loteCurrent.fracassado = false;
    this.addLote(lote.id, loteCurrent);

    loteCurrent.itens.forEach((i) => {
      this.lotesFracassados = this.lotesFracassados.filter((lote) => lote.itemLote.id != i.id);
      const currentItem = this.getItem(i.id);
      currentItem.fracassado = false;
      currentItem.preenchido = false;
      currentItem.quantidade = i.quantidade;
      currentItem.valorUnitario = i.valorUnitarioEstimado;
      currentItem.valor = parseFloat(i.quantidade) * parseFloat(i.valorUnitarioEstimado);
      currentItem.marcaModelo = '';
      currentItem.especificacao = '';
      currentItem.observacao = '';
      currentItem.desconto = 0;
      this.addItem(i.id, currentItem);
    });

    this.updateVencedoresLotes();
    this.updateLotesFracassados();
  }

  @action
  setLoteItemDeserto(lote) {
    lote.itens.forEach((i) => {
      const currentItem = this.getItem(i.id);
      currentItem.deserto = true;
      currentItem.quantidade = i.quantidade;
      currentItem.valorUnitario = i.valorUnitarioEstimado;
      currentItem.valor = i.quantidade * i.valorUnitarioEstimado;
      currentItem.marcaModelo = '';
      currentItem.especificacao = '';
      currentItem.observacao = '';
      currentItem.desconto = 0;
      currentItem.preenchido = true;
      this.addItem(i.id, currentItem);
    });

    const currentLote = this.getLote(lote.id);
    currentLote.deserto = true;
    currentLote.licitante = undefined;
    this.addLote(lote.id, currentLote);

    this.createOrUpdateLoteItensDesertos(lote.itens);
    this.updateLotesFracassados();
    this.updateVencedoresLotes();
    this.updateItensDesertos();
  }

  @action
  setLoteItemNaoDeserto(lote) {
    const loteCurrent = this.getLote(lote.id);
    loteCurrent.deserto = false;
    this.addLote(lote.id, loteCurrent);

    loteCurrent.itens.forEach((i) => {
      this.itensDesertos = this.itensDesertos.filter((item) => item.itemLote.id != i.id);
      const currentItem = this.getItem(i.id);
      currentItem.deserto = false;
      currentItem.quantidade = i.quantidade;
      currentItem.valorUnitario = i.valorUnitarioEstimado;
      currentItem.valor = i.quantidade * i.valorUnitarioEstimado;
      currentItem.marcaModelo = '';
      currentItem.especificacao = '';
      currentItem.observacao = '';
      currentItem.desconto = 0;
      currentItem.preenchido = false;
      this.addItem(i.id, currentItem);
    });

    this.updateVencedoresLotes();
    this.updateLotesFracassados();
    this.updateItensDesertos();
  }

  getNomeLoteByitem(item) {
    const lote = this.lotes.find((l) => {
      if (l.itens.some((i) => i.id == item?.id || i.id == item?.itemLote?.id)) {
        return l;
      }
    });
    return lote?.nome;
  }

  getItensDisponiveisLoteUnico(vencedor) {
    const itensLoteUnico = this.itensLotes;
    let itensDisponiveis = [];
    itensLoteUnico?.forEach((item) => {
      const checkExistItemInVencedor = this.vencedores.some(
        (v) => v.itemLote.id == item.id && v.licitante.id == vencedor.id
      );
      if (!checkExistItemInVencedor && !this.lotesFracassados.some((l) => l.itemLote.id == item.id)) {
        if (this.getQuantidadeItemDisponivel(item.id) > 0) {
          if (this.selectedLote) {
            const itemExistInLoteSelected = this.selectedLote && this.selectedLote.itens.some((i) => i.id == item.id);
            itemExistInLoteSelected && itensDisponiveis.push(item);
          } else {
            itensDisponiveis.push(item);
          }
        }
      }
    });
    return itensDisponiveis;
  }

  getItemAssociadoByItemLoteVencedor(itemLote, vencedor) {
    return this.vencedores.find((v) => v.itemLote.id == itemLote.id && v.licitante.id == vencedor.id);
  }

  getItemAssociadoByItemLoteDeserto(itemLote) {
    return this.itensDesertos.find((i) => i.itemLote.id == itemLote.id);
  }

  getItensAssociadosLoteUnico(vencedor) {
    const itensLoteUnico = this.itensLotes;
    let itensAssociados = [];
    itensLoteUnico?.forEach((item) => {
      if (this.vencedores.find((v) => v.itemLote.id == item.id && v.licitante.id == vencedor.id)) {
        itensAssociados.push(item);
      }
    });
    return itensAssociados;
  }

  getQuantidadeItemDisponivel(idItemLote, additionalValue = 0) {
    let qtdUsadaVencedores = this.vencedores.reduce((accumulator, currentValue) => {
      if (currentValue.itemLote.id === idItemLote) {
        return accumulator + currentValue.quantidade;
      }
      return accumulator;
    }, 0);

    qtdUsadaVencedores = qtdUsadaVencedores;

    let qtdUsadaDesertos = this.itensDesertos.reduce((accumulator, currentValue) => {
      if (currentValue.itemLote.id === idItemLote) {
        return accumulator + currentValue.quantidade;
      }
      return accumulator;
    }, 0);

    qtdUsadaDesertos = qtdUsadaDesertos;

    const qtdTotal = this.itensLotes.find((i) => i.id === idItemLote)?.quantidade;

    return qtdTotal - qtdUsadaVencedores - qtdUsadaDesertos + additionalValue;
  }

  getItensByLote(lote) {
    const result = [...this.mapItensLote.values()]
      .filter((item) => lote.itens.some((i) => i.id == item.get().itemLote.id))
      .map((i) => i.get());
    return result;
  }

  getStatusLote(lote) {
    let status = 'error';
    const haveUnfilledItems = lote.itens.map((i) => this.getItem(i.id)).some((i) => !i?.preenchido);

    if (lote.licitante && !haveUnfilledItems) {
      status = 'check';
    } else if (lote.licitante && haveUnfilledItems) {
      status = 'warning';
    }
    return status;
  }

  addItem(key, value) {
    this.mapItensLote.set(key, observable.box(value));
  }

  removeItem(key) {
    this.mapItensLote.delete(key);
  }

  getItem(key) {
    const item = this.mapItensLote.get(key);
    return item ? item.get() : undefined;
  }

  addLote(key, value) {
    this.mapLotes.set(key, observable.box(value));
  }

  removeLote(key) {
    this.mapLotes.delete(key);
  }

  getLote(key) {
    const lote = this.mapLotes.get(key);
    return lote ? lote.get() : undefined;
  }
}

export default VencedorCredenciadoFormStore;
