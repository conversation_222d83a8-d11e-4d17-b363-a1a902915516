import { observable, action, runInAction, override } from 'mobx';
import Credenciado from '~/domains/Credenciado';
import CredenciamentoService from '~/services/CredenciamentoService';
import CredenciadoService from '~/services/CredenciadoService';
import FormBase from 'fc/stores/FormBase';
import { showErrorNotification, showNotification } from 'fc/utils/utils';
import VencedorCredenciadoFormStore from './vencedorCredFormStore';
import LicitanteIndexStore from '~/stores/licitante/indexStore';
import moment from 'moment';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import MultipleFileUploaderStore from 'fc/stores/MultipleFileUploaderStore';
import ObrigatoriedadeArquivoService from '~/services/ObrigatoriedadeArquivoService';

class CredenciadoFormStore extends FormBase {
  @observable idCredenciado;
  @observable credenciamento;
  @observable vencedorCredStore;
  @observable arquivos = [];
  @observable loadedArquivos = [];
  @observable fileStore;
  @observable loadingContratosCredenciado = false;
  @observable contratosCredenciado = [];
  @observable vigencias = [];
  @observable credenciadosCadastrados = [];
  @observable canSave = true;

  constructor() {
    super(CredenciadoService, Credenciado);
    this.fornecedoresIndexStore = new LicitanteIndexStore();

    this.setCredenciado = this.setCredenciado.bind(this);

    this.vencedorCredStore = new VencedorCredenciadoFormStore();

    this.fileStore = new MultipleFileUploaderStore(
      [],
      (file) => this.service.upload(file),
      (fileDTO, countDownload) => this.service.download(fileDTO, countDownload),
      (idArquivo) => this.removerArquivoCredencido(idArquivo),
      (idArquivo, arquivoCredenciadoDTO) => this.atualizarArquivoCredenciado(idArquivo, arquivoCredenciadoDTO)
    );

    this.removerArquivoCredenciado = this.removerArquivoCredenciado.bind(this);
    this.atualizarArquivoCredenciado = this.atualizarArquivoCredenciado.bind(this);

    this.loadTipos();
  }

  @override
  initialize(id, defaultValues = {}, callback) {
    if (!id) {
      this.object = Object.assign({}, defaultValues);
      callback && callback();
    } else {
      this.loading = true;
      this.service
        .getById(id)
        .then((response) =>
          runInAction(() => {
            this.object = response.data;
            this.credenciamento = response.data?.credenciamento;
            this.loadedObject = response.data;
            callback && callback();
          })
        )
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        )
        .finally(() =>
          runInAction(() => {
            this.loading = false;
          })
        );
    }
  }

  @action
  initializeVencedorStore() {
    this.vencedorCredStore.initialize(
      this.credenciamento.termoReferencia,
      this.object.licitante ? [this.object.licitante] : [],
      this.object.credenciadoItems ?? [],
      [],
      [],
      this.object.tipoAdjudicacao,
      (credenciadosItems) => {
        this.updateAttribute('credenciadoItems', credenciadosItems);
        this.setValor(this.vencedorCredStore.getValorTotal());
      },
      () => {},
      () => {},
      (tipoAdjudicacao) => {
        this.updateAttribute('tipoAdjudicacao', tipoAdjudicacao);
      }
    );

    !this.object.valor && this.setValor(this.vencedorCredStore.getValorTotal());
  }

  @action
  initializeArquivos(id, callback) {
    this.idCredenciado = id;

    if (id) {
      this.service
        .recuperarArquivos(id)
        .then((response) =>
          runInAction(() => {
            const arquivosRecuperados = response.data;
            this.fileStore.initialize(arquivosRecuperados);
            this.arquivos = arquivosRecuperados;
            this.loadedArquivos = arquivosRecuperados;
          })
        )
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        )
        .finally(() =>
          runInAction(() => {
            callback && callback();
          })
        );
    }
  }

  @action
  setArquivos(arquivos) {
    this.arquivos = arquivos;
  }

  removerArquivoCredenciado(idArquivo) {
    return this.service.removerArquivo(this.idCredenciado, idArquivo);
  }

  atualizarArquivoCredenciado(idArquivo, arquivoCredenciadoDTO) {
    return this.service.atualizarArquivo(this.idCredenciado, idArquivo, arquivoCredenciadoDTO);
  }

  @action
  validaArquivos(callback) {
    this.loading = true;
    this.service
      .validaArquivos({ arquivos: this.arquivos })
      .then(() =>
        runInAction(() => {
          callback && callback();
        })
      )
      .catch((error) => showErrorNotification(error))
      .finally(() =>
        runInAction(() => {
          this.loading = false;
        })
      );
  }

  @action
  loadTipos() {
    ObrigatoriedadeArquivoService.getArquivosObrigatorios({
      tipoProcesso: 'CREDENCIADO',
      filtros: ['CREDENCIADO'],
    })
      .then((response) =>
        runInAction(() => {
          const arquivosObrigatorios = response?.data ?? [];
          this.fileStore.tipoArquivoEnum = arquivosObrigatorios.map((arqObg) => {
            const arq = DadosEstaticosService.getTipoArquivoCredenciado().find(
              (arq) => arq.value === arqObg.arquivoEnum
            );
            return { ...arqObg, text: arqObg.obrigatorio ? '* ' + arqObg.arquivo : arqObg.arquivo, value: arq.value };
          });
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      );
  }

  @override
  save(callback, type = 'edit') {
    this.loading = true;
    const saveObject = this.getObjectToSave(type);
    const credenciadoDTO = {
      id: this.object.id,
      credenciado: { ...this.object },
      arquivos: this.arquivos,
    };

    if (!Object.keys(saveObject).length) {
      showNotification('warn', null, 'Nenhuma alteração realizada.');
      this.loading = false;
    } else {
      this.service
        .salvar(credenciadoDTO)
        .then(() =>
          runInAction(() => {
            callback && callback();
            showNotification('success', null, 'Registro salvo com sucesso!');
          })
        )
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        )
        .finally(() =>
          runInAction(() => {
            this.loading = false;
          })
        );
    }
  }

  async checkFornecedor() {
    await this.findCredenciadosCadastrados();

    this.canSave = false;
    if (this.credenciadosCadastrados.some((cred) => cred?.situacao === 'VIGENTE')) {
      showNotification('warn', null, 'Já existe credenciado vigente deste licitante');
    } else {
      const credAntigo = this.credenciadosCadastrados.pop();
      if (credAntigo !== undefined && credAntigo.status !== 'REMOVIDA') {
        const novaVigenciaInicial = moment(credAntigo.vigenciaFinal).add(1, 'days');
        if (novaVigenciaInicial.isSameOrAfter(this.credenciamento?.fimVigencia)) {
          showNotification('warn', null, 'Não é possível recadastrar credenciado pois vigência já foi atingida');
        } else {
          this.updateAttributeDate('vigenciaInicial', novaVigenciaInicial);
          showNotification(
            'info',
            null,
            'Início da Vigência foi alterado para um dia após o fim da vigência do último cadastro de credenciado do licitante selecionado'
          );
          this.canSave = true;
        }
      } else {
        const vigenciaInicialCredenciamento = moment(this.credenciamento?.inicioVigencia);
        !vigenciaInicialCredenciamento.isSame(this.object?.vigenciaInicial) &&
          this.updateAttributeDate('vigenciaInicial', vigenciaInicialCredenciamento);
        this.canSave = true;
      }
    }
  }

  @action
  async findCredenciadosCadastrados() {
    this.loading = true;
    await this.service
      .findAllByCredenciamentoAndLicitante(this.credenciamento?.id, this.object.licitante?.id)
      .then((response) => {
        this.credenciadosCadastrados = response.data;
      })
      .catch((error) => showErrorNotification(error))
      .finally(() => {
        this.loading = false;
      });
  }

  validateVigenciaFinal() {
    this.canSave = false;
    this.object.vigenciaFinal && this.vigencias.includes(this.object.vigenciaFinal)
      ? showNotification('warn', null, 'Já existe credenciado com esta Data de Vigência Final')
      : (this.canSave = true);
  }

  rulesDefinition() {
    let rules = {
      licitante: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      tipoAdjudicacao: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      credenciadoItems: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
    };

    return rules;
  }

  validaDadosBasicos() {
    const rules = this.rulesDefinition();
    const required = Object.keys(rules).filter((k) => rules[k].find((r) => r.rule === 'required'));
    const campos = required.filter(
      (k) =>
        this.object[k] === undefined ||
        this.object[k] === null ||
        this.object[k] === '' ||
        (Array.isArray(this.object[k]) && this.object[k].length === 0)
    );

    return campos.length === 0;
  }

  @action
  setValor(valor) {
    this.object.valor = valor;
  }

  @action
  setCredenciado(credenciado, callback) {
    this.object = credenciado;
    callback && callback();
  }

  @action
  carregaCredenciamento(idCredenciamento, callback) {
    this.loading = true;
    CredenciamentoService.getById(idCredenciamento)
      .then((response) => {
        this.credenciamento = response.data;
        this.object.vigenciaInicial = this.credenciamento.inicioVigencia;
        this.object.vigenciaFinal = this.credenciamento.fimVigencia;
        this.object.credenciamento = this.credenciamento;
      })
      .catch((error) => showErrorNotification(error))
      .finally(() => {
        this.loading = false;
        callback && callback();
      });
  }

  @action
  getVigenciasCredenciados(idCredenciamento, callback) {
    this.loading = true;
    this.service
      .getVigenciasCredenciados(idCredenciamento)
      .then((response) => {
        this.vigencias = response.data;
      })
      .catch((error) => showErrorNotification(error))
      .finally(() => {
        this.loading = false;
        callback && callback();
      });
  }
}

export default CredenciadoFormStore;
