import FormBase from 'fc/stores/FormBase';
import { action, makeObservable, observable, override, runInAction } from 'mobx';
import MultipleFileUploaderStore from 'fc/stores/MultipleFileUploaderStore';
import { showErrorNotification, showNotification } from 'fc/utils/utils';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import CredenciamentoService from '~/services/CredenciamentoService';
import CredenciadoService from '~/services/CredenciadoService';
import Credenciamento from '~/domains/Credenciamento';
import SearchTypes from 'fc/components/AdvancedSearch/SearchTypes';
import AppStore from 'fc/stores/AppStore';
import AsyncDropDownStore from 'fc/stores/AsyncDropdownStore';
import Entidade from '~/domains/Entidade';
import EntidadeService from '~/services/EntidadeService';
import UsuarioService from '~/services/UsuarioService';
import ComissaoService from '~/services/ComissaoService';
import FonteRecursoService from '~/services/FonteRecursoService';
import LicitacaoService from '~/services/LicitacaoService';
import ObraTipoService from '~/services/ObraTipoService';
import ObraCategoriaService from '~/services/ObraCategoriaService';
import ObrigatoriedadeArquivoService from '~/services/ObrigatoriedadeArquivoService';
import TermoIndexStore from '../licitacao/termoIndex';
import Usuario from '~/domains/Usuario';
import Comissao from '~/domains/Comissao';
import AsyncMultiselectStore from 'fc/stores/AsyncMultiselectStore';
import ObraTipo from '~/domains/ObraTipo';
import ObraCategoria from '~/domains/ObraCategoria';
import moment from 'moment';
import EdificacaoService from '~/services/EdificacaoService';
import SearchOperators from 'fc/components/AdvancedSearch/SearchOperators';
import TdaCredenciamentoService from '~/services/TdaCredenciamentoService';
import AccessPermission from '~/constants/AccessPermission';

class CredenciamentoFormStore extends FormBase {
  @observable idCredenciamento;
  @observable arquivos;
  @observable loadedArquivos;
  @observable fontesRecursos = [];
  @observable anos = [];
  @observable obraObject = {};
  @observable edificacao;
  @observable stateObra = {};
  @observable enableReqMod = false;
  @observable credenciados = [];
  @observable idAlerta = null;

  constructor() {
    super(CredenciamentoService, Credenciamento);
    makeObservable(this);

    this.termoIndexStore = new TermoIndexStore();
    this.responsavelStore = new AsyncDropDownStore(Usuario, UsuarioService, 'nome', 'id', {
      sort: {
        by: 'nome',
        order: 'asc',
      },
      andParameters: [{ field: 'entidadesTituladas', operator: 'INCLUDES', value: AppStore.getContextEntity()?.id }],
    });
    this.tipoCredenciamentoStore = new AsyncDropDownStore(Comissao, ComissaoService, 'numero', 'id', {
      sort: {
        by: 'numero',
        order: 'asc',
      },
    });
    this.orgaosParticipantesStore = new AsyncMultiselectStore(Entidade, EntidadeService, 'nome', 'id', {
      sort: {
        by: 'nome',
        order: 'asc',
      },
    });
    this.obraTipoStore = new AsyncDropDownStore(ObraTipo, ObraTipoService, 'nome', 'id', {
      sort: {
        by: 'nome',
        order: 'asc',
      },
    });
    this.obraCategoriaStore = new AsyncDropDownStore(ObraCategoria, ObraCategoriaService, 'nome', 'id', {
      sort: {
        by: 'nome',
        order: 'asc',
      },
    });

    this.arquivos = [];
    this.loadedArquivos = [];
    this.fileStore = new MultipleFileUploaderStore(
      [],
      (file) => this.service.upload(file),
      (fileDTO, countDownload) => this.service.download(fileDTO, countDownload),
      (idArquivo) => this.removerArquivoCredenciamento(idArquivo),
      (idArquivo, arquivoCredenciamentoDTO) => this.atualizarArquivoCredenciamento(idArquivo, arquivoCredenciamentoDTO)
    );
    this.fileStoreTda = new MultipleFileUploaderStore(
      [],
      (file) => TdaCredenciamentoService.upload(file),
      (fileDTO) => TdaCredenciamentoService.download(fileDTO)
    );
    this.removerArquivoCredenciamento = this.removerArquivoCredenciamento.bind(this);
    this.atualizarArquivoCredenciamento = this.removerArquivoCredenciamento.bind(this);
  }

  rulesDefinition() {
    const isObra = ['OBRAS', 'SERVICOS_DE_ENGENHARIA'].some((natureza) =>
      this.object?.naturezasDoObjeto?.includes(natureza)
    );

    const rules = {
      lei: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      numeroProcesso: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      numero: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      ano: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      inicioVigencia: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      fimVigencia: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      termoReferencia: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      responsavel: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      tipoContratacao: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      sitioDivulgacao: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      comissaoContratacao: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      presidenteComissao: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      naturezasDoObjeto: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      fontesDeRecurso: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      objeto: [
        { rule: 'required', message: 'Por favor, preencha o campo' },
        { rule: 'isMaxLength', maxLength: 4000, message: 'Por favor, diminua o tamanho do campo' },
      ],
      observacoes: [{ rule: 'isMaxLength', maxLength: 4000, message: 'Por favor, diminua o tamanho do campo' }],
      justificativa: [{ rule: 'isMaxLength', maxLength: 4000, message: 'Por favor, diminua o tamanho do campo' }],
      tipoObra: [isObra && { rule: 'required', message: 'Por favor, preencha o campo' }],
      categoria: [isObra && { rule: 'required', message: 'Por favor, preencha o campo' }],
      coordenadas: [isObra && { rule: 'required', message: 'Por favor, preencha o campo' }],
      participacaoExclusiva: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
    };

    return rules;
  }

  validaDadosBasicos() {
    const rules = this.rulesDefinition();
    const required = Object.keys(rules).filter((k) => rules[k].find((r) => r.rule === 'required'));
    const campos = required.filter(
      (k) =>
        this.object[k] === undefined ||
        this.object[k] === null ||
        this.object[k] === '' ||
        (Array.isArray(this.object[k]) && this.object[k].length === 0)
    );

    return campos.length === 0;
  }

  @action
  initializeTdaCredenciamento(idCredenciamento) {
    if (idCredenciamento && this.hasPermissionTda()) {
      TdaCredenciamentoService.tdaCredenciamentoByIdCredenciamento(idCredenciamento)
        .then((response) =>
          runInAction(() => {
            if (response && response.data) {
              const tdaCredenciamento = response.data;
              this.carregaArquivosTda(tdaCredenciamento);
            }
          })
        )
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        );
    }
  }

  hasPermissionTda() {
    return AppStore.hasPermission([AccessPermission.cadastrarTda.readPermission]);
  }

  @action
  carregaArquivosTda(tdaCredenciamento) {
    if (tdaCredenciamento) {
      TdaCredenciamentoService.recuperarArquivos(tdaCredenciamento.id)
        .then((response) =>
          runInAction(() => {
            if (response && response.data) {
              const arquivosRecuperados = response.data.map((arq) => {
                return {
                  ...arq,
                  analista: tdaCredenciamento.analista.nome,
                };
              });
              this.fileStoreTda.initialize(arquivosRecuperados);
              this.tdaCredenciamento = arquivosRecuperados;
            }
          })
        )
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        );
    }
  }

  getFilterSuggestTermo() {
    const filterSuggest = [
      {
        id: '',
        field: 'disponivel',
        operator: 'EQUAL_TO',
        value: true,
        formatted: '',
        fixed: true,
        invisible: true,
        completeParam: {
          field: 'disponivel',
          label: 'Disponível',
          type: SearchTypes.ENUM,
          options: DadosEstaticosService.getSimNao(),
        },
      },
      {
        id: '',
        field: 'modelo',
        operator: 'EQUAL_TO',
        value: false,
        formatted: '',
        fixed: true,
        invisible: true,
        completeParam: {
          field: 'modelo',
          label: 'Modelo',
          type: SearchTypes.ENUM,
          options: DadosEstaticosService.getSimNao(),
        },
      },
    ];

    const entidade = AppStore.getContextEntity();
    if (entidade) {
      filterSuggest.push({
        id: '',
        field: 'entidade',
        operator: 'EQUAL_TO',
        value: entidade,
        formatted: '',
        fixed: true,
        invisible: true,
        completeParam: {
          field: 'entidade',
          label: 'Órgão/Entidade',
          type: SearchTypes.ASYNC_QUERY,
          store: new AsyncDropDownStore(Entidade, EntidadeService, 'nome', 'id'),
        },
      });
    }
    return filterSuggest;
  }

  checkDataCadastro() {
    let data = this.object?.dataCadastro;
    if (data) {
      const current = moment();
      const diff = moment.duration(current.diff(data));
      this.enableReqMod = diff.asHours() > 24;
    }
  }

  comissaoFilterSuggest() {
    const filterSuggest = [];

    const entidade = AppStore.getContextEntity();
    if (entidade) {
      filterSuggest.push({
        id: '',
        field: 'entidade',
        operator: 'EQUAL_TO',
        value: entidade,
        formatted: '',
        fixed: true,
        invisible: true,
        completeParam: {
          field: 'entidade',
          label: 'Entidade',
          type: SearchTypes.ASYNC_QUERY,
          store: new AsyncDropDownStore(Entidade, EntidadeService, 'nome', 'id'),
        },
      });
    }

    return filterSuggest;
  }

  @action
  setCredenciamento(credenciamento, callback) {
    this.object = credenciamento;
    callback && callback();
  }

  @action
  initializeTabDadosBasicos(callback) {
    this.loadingTabDadosBasicos = true;
    const filtro = {
      tipoProcesso: 'CREDENCIAMENTO',
      filtros:
        this.object?.naturezasDoObjeto && this.object?.naturezasDoObjeto.includes('OBRAS')
          ? ['CREDENCIAMENTO', 'NATUREZAOBJ_OBRA']
          : ['CREDENCIAMENTO'],
    };

    Promise.all([
      FonteRecursoService.getAll(),
      LicitacaoService.getAnosLicitacao(),
      ObrigatoriedadeArquivoService.getArquivosObrigatorios(filtro),
    ])
      .then((response) =>
        runInAction(() => {
          this.fontesRecursos = response[0].data;
          this.anos = response[1].data.map((ano) => ({ value: ano, text: ano }));
          const arquivosObrigatorios = response[2]?.data ?? [];
          this.fileStore.tipoArquivoEnum = arquivosObrigatorios.map((arqObg) => {
            const arq = DadosEstaticosService.getTipoArquivoCredenciamento().find(
              (arq) => arq.value === arqObg.arquivoEnum
            );
            return { ...arqObg, text: arqObg.obrigatorio ? '* ' + arqObg.arquivo : arqObg.arquivo, value: arq.value };
          });
        })
      )
      .catch((error) => showErrorNotification(error))
      .finally(() => {
        this.loadingTabDadosBasicos = false;
        callback && callback();
      });
  }

  @action
  carregarEdificacaoObra() {
    this.loading = true;
    const filtro = {
      page: { index: 1, size: 100 },
      sort: {},
      andParameters: [{ field: 'obra', operator: SearchOperators.EQUAL_TO.value, value: this.object?.obra?.id }],
    };
    EdificacaoService.advancedSearch(filtro)
      .then((response) =>
        runInAction(() => {
          const edificacao = response.data?.items[0];
          this.object.obra.edificacao = edificacao;
          this.edificacao = edificacao;
          this.initializeObra();
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      )
      .finally(() =>
        runInAction(() => {
          this.loading = false;
        })
      );
  }

  initializeObra() {
    const localizacao = this.object?.obra?.edificacao?.localizacao;
    const coordenadas = [];
    this.object.categoria = this.object?.obra?.categoria;
    this.object.tipoObra = this.object?.obra?.tipo;

    if (localizacao) {
      switch (localizacao.type) {
        case 'Polygon':
          localizacao.coordinates[0].forEach((element) => {
            coordenadas.push(element.join(' '));
          });
          break;
        case 'LineString':
          localizacao.coordinates.forEach((element) => {
            coordenadas.push(element.join(' '));
          });
          break;
        default:
          coordenadas.push(localizacao.coordinates.join(' '));
      }
    }

    this.object.coordenadas = coordenadas.join(', ');
    this.object.tipoSelecao = this.getTipoSelecaoByTipoEdificacao(localizacao?.type);
  }

  getTipoSelecaoByTipoEdificacao(tipo) {
    return { Point: 'PONTO', LineString: 'LINHA', Polygon: 'POLIGONO' }[tipo];
  }

  @action
  updateObraAttribute(att, value) {
    this.object.obra[att] = value;
  }

  @action
  updateObraDTOAtt(att, value) {
    this.obraObject[att] = value;
  }

  @action
  initializeObraDTO() {
    this.obraObject = {};
    this.edificacao = undefined;
  }

  @action
  resetStateObra() {
    this.stateObra = {};
  }

  @action
  updateStateObraAttribute(att, value) {
    this.stateObra[att] = value;
  }

  @action
  updateAttributePresidenteComissao(attribute, event, list) {
    let value = event;
    if (event && event.value) {
      value = event.value;
    }
    if (event && event.target) {
      value = event.target.value;
    }

    list.forEach((user) => {
      if (user.id == value) this.object[attribute] = user;
    });
  }

  @action
  initializeArquivos(id, callback) {
    this.idCredenciamento = id;

    if (id) {
      this.service
        .recuperarArquivos(id)
        .then((response) =>
          runInAction(() => {
            const arquivosRecuperados = response.data;
            this.fileStore.initialize(arquivosRecuperados);
            this.arquivos = arquivosRecuperados;
            this.loadedArquivos = arquivosRecuperados;
          })
        )
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        )
        .finally(() =>
          runInAction(() => {
            callback && callback();
          })
        );
    }
  }

  @action
  setArquivos(arquivos) {
    this.arquivos = arquivos;
  }

  @action
  onChangeStep(index) {
    this.currentStep = index;
  }

  removerArquivoCredenciamento(idArquivo) {
    return this.service.removerArquivo(this.idCredenciamento, idArquivo);
  }

  atualizarArquivoCredenciamento(idArquivo, arquivoCredenciamentoDTO) {
    return this.service.atualizarArquivo(this.idCredenciamento, idArquivo, arquivoCredenciamentoDTO);
  }

  @action
  validaArquivos(callback) {
    this.loading = true;
    this.service
      .validaArquivos({ arquivos: this.arquivos })
      .then(() =>
        runInAction(() => {
          callback && callback();
        })
      )
      .catch((error) => showErrorNotification(error))
      .finally(() =>
        runInAction(() => {
          this.loading = false;
        })
      );
  }

  @action
  loadTipos() {
    const filtro = {
      tipoProcesso: 'CREDENCIAMENTO',
      filtros:
        this.object?.naturezasDoObjeto && this.object?.naturezasDoObjeto.includes('OBRAS')
          ? ['CREDENCIAMENTO', 'NATUREZAOBJ_OBRA']
          : ['CREDENCIAMENTO'],
    };
    ObrigatoriedadeArquivoService.getArquivosObrigatorios(filtro)
      .then((response) =>
        runInAction(() => {
          const arquivosObrigatorios = response?.data ?? [];
          this.fileStore.tipoArquivoEnum = arquivosObrigatorios.map((arqObg) => {
            const arq = DadosEstaticosService.getTipoArquivoCredenciamento().find(
              (arq) => arq.value === arqObg.arquivoEnum
            );
            return { ...arqObg, text: arqObg.obrigatorio ? '* ' + arqObg.arquivo : arqObg.arquivo, value: arq.value };
          });
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      );
  }

  @action
  getCredenciados(callback) {
    this.loading = true;
    CredenciadoService.getAllByCredenciamento(this.idCredenciamento)
      .then((response) =>
        runInAction(() => {
          this.credenciados = response.data;
          callback && callback();
        })
      )
      .catch((error) => showErrorNotification(error))
      .finally(() =>
        runInAction(() => {
          this.loading = false;
        })
      );
  }

  @override
  save(callback, type = 'edit') {
    this.loading = true;
    const saveObject = this.getObjectToSave(type);
    const credenciamentoDTO = {
      id: this.object.id,
      credenciamento: { ...this.object, entidade: AppStore.getContextEntity() },
      arquivos: this.arquivos,
      obra: this.obraObject && Object.keys(this.obraObject).length > 0 ? this.obraObject : null,
      edificacao: this.edificacao,
    };

    if (!Object.keys(saveObject).length) {
      showNotification('warn', null, 'Nenhuma alteração realizada.');
      this.loading = false;
    } else {
      this.service
        .salvar(credenciamentoDTO)
        .then(() =>
          runInAction(() => {
            callback && callback();
            showNotification('success', null, 'Registro salvo com sucesso!');
          })
        )
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        )
        .finally(() =>
          runInAction(() => {
            this.loading = false;
          })
        );
    }
  }

  hasPermissionAlerta() {
    return AppStore.hasPermission([AccessPermission.alerta.readPermission]);
  }

  @action
  hasAlert(idCredenciamento) {
    this.service
      .getAlertaCredenciamento(idCredenciamento)
      .then((alerta) => {
        if (alerta?.data) {
          runInAction(() => {
            this.idAlerta = alerta.data;
          });
        }
      })
      .catch(() => {
        runInAction(() => {
          showErrorNotification('Ocorreu um erro ao buscar o id do alerta.');
        });
      });
  }
}

export default CredenciamentoFormStore;
