import { action, computed, makeObservable, observable } from 'mobx';
import { showNotification } from 'fc/utils/utils';
import { extractRules } from 'fc/utils/formRules';

class VencedorFormStore {
  @observable termoReferencia;
  @observable licitantes;
  @observable lotes = [];
  @observable vencedores = [];
  @observable lotesFracassados = [];

  @observable vencedor = {};
  @observable showDialogVencedores = false;

  @observable edit = false;
  @observable modifyVencedor = false;
  @observable lastVencedor;

  onChange;
  onChangeLotesFracassados;
  propItens;

  constructor(propItens) {
    makeObservable(this);
    this.propItens = propItens;
  }

  @action
  initialize(licitantes, vencedores, termoReferencia, lotesFracassados, onChange, onChangeLotesFracassados) {
    this.licitantes = licitantes;
    this.vencedores = vencedores;
    this.termoReferencia = termoReferencia;
    this.lotesFracassados = lotesFracassados;
    this.lotes = termoReferencia?.lotes?.map((l) => {
      return {
        ...l,
        itens: l.itens.map((i) => {
          return {
            ...i,
            quantidadeDisponivel: i.quantidade,
            valorUnitario: i.valorUnitarioEstimado,
            desconto: 0,
            valorTotal: i.valorUnitarioEstimado * i.quantidade,
            itemCatalogo: i,
            preenchido: false,
          };
        }),
      };
    });
    this.onChange = onChange;
    this.onChangeLotesFracassados = onChangeLotesFracassados;
    this.lotes && this.atualizaLotes();
  }

  @action
  setValorVencedor(valor) {
    this.vencedor.valor = valor;
  }

  @action
  adicionarVencedor(callback) {
    if (!this.rules.hasError) {
      if (this.vencedores?.length > 0) {
        this.vencedores = [
          ...this.vencedores.filter(
            (vencedor) =>
              vencedor.licitante?.id !== this.vencedor.licitante?.id && vencedor.licitante?.id !== this.lastVencedor?.id
          ),
          this.vencedor,
        ];
      } else {
        this.vencedores = [this.vencedor];
      }
      this.onChange(this.vencedores);
      showNotification('success', null, `Vencedor ${this.edit ? 'Editado' : 'Adicionado'}.`);
      this.vencedor = {};
      this.showDialogVencedores = false;
      callback && callback();
    }
  }

  @action
  updateAttributeLote(lote, attribute, value) {
    this.lotes = this.lotes?.map((l) => {
      if (l.id === lote.id) {
        l[attribute] = value;
      }
      return l;
    });
  }

  @action
  hasValorVencedor() {
    return this.vencedores.some((vencedor) => vencedor.valor > 0);
  }

  @action
  atualizaLotes() {
    this.vencedores.forEach((v) => {
      const itens = v[this.propItens];
      const lote = v.lote;
      if (lote) {
        this.setLicitanteLote(lote, this.getLicitanteById(v.licitante?.id) ?? v.licitante);
        this.updateAttributeLote(lote, 'modificado', v.modificado);
        v.newLicitante && this.setLicitanteLote(lote, v.newLicitante, 'newLicitante');
        itens?.forEach((i) => {
          const item = i.itemCatalogo;
          if (item) {
            item.quantidadeDisponivel = item.quantidade;
            item.valorUnitario = i.valorUnitario;
            item.quantidade = i.quantidade;
            this.updateAttributeItem(lote, item, 'idItem', i.id);
            this.updateAttributeItem(lote, item, 'valorUnitario', i.valorUnitario);
            this.updateAttributeItem(lote, item, 'newValorUnitario', i.newValorUnitario);
            this.updateAttributeItem(lote, item, 'especificacao', i.especificacao);
            this.updateAttributeItem(lote, item, 'newEspecificacao', i.newEspecificacao);
            this.updateAttributeItem(lote, item, 'observacao', i.observacao);
            this.updateAttributeItem(lote, item, 'newObservacao', i.newObservacao);
            this.updateAttributeItem(lote, item, 'marcaModelo', i.marcaModelo);
            this.updateAttributeItem(lote, item, 'newMarcaModelo', i.newMarcaModelo);
            this.updateAttributeItem(lote, item, 'modificado', i.modificado);
            this.updateAttributeItem(lote, item, 'preenchido', i.preenchido);
            this.setQuantidade(lote, item, i.quantidade);
            this.setQuantidade(lote, item, i.newQuantidade, 'newQuantidade');
            this.setDesconto(lote, item, i.desconto);
            this.updateAttributeItem(lote, item, 'newDesconto', i.newDesconto, () => this.setValorTotal(lote, item));
            this.updateAttributeLote(lote, 'idVencedor', v.id);
          }
        });
      }
    });
    this.updateVencedores();
  }

  getLicitanteById(id) {
    const licitantes = this.licitantes.filter((l) => l.id === id);
    return licitantes?.length ? licitantes[0] : null;
  }

  @action
  editVencedor(vencedor) {
    this.toggleDialogVencedores(true);
    this.updateLicitante(vencedor.licitante);
  }

  @action
  detailVencedor(vencedor) {
    this.toggleDialogVencedores(true);
    if (vencedor) {
      this.vencedor = { ...vencedor };
    } else {
      this.updateLicitante(vencedor.licitante);
    }
  }

  @action
  removeVencedor(vencedor) {
    this.vencedores = this.vencedores.filter((v) => !(v.licitante?.id === vencedor.licitante?.id));
    this.lotes = this.lotes?.map((lote) => {
      if (lote.vencedor?.licitante.id === vencedor.licitante?.id) {
        lote.vencedor = null;
      }
      return lote;
    });
    this.onChange(this.vencedores);
  }

  @action
  toggleDialogVencedores(edit = false, modifyVencedor = false) {
    this.showDialogVencedores = !this.showDialogVencedores;
    this.vencedor = {};
    this.edit = edit;
    this.modifyVencedor = modifyVencedor;
    this.backupVencedor();
  }

  @action
  updateLicitante(licitante) {
    if (!this.modifyVencedor) {
      const vencedorAdicionado = this.vencedores?.find((v) => v.licitante?.id === licitante.id);
      if (vencedorAdicionado) {
        this.vencedor = { ...vencedorAdicionado, licitante: licitante };
      } else {
        this.vencedor = { ...this.vencedor, licitante: licitante };
      }
    } else {
      this.vencedor = { ...this.vencedor, licitante: licitante };
    }
  }

  @action
  setLicitanteLote(lote, licitante, attr = 'licitante') {
    if (lote && licitante) {
      this.lotes = this.lotes.map((l) => {
        if (l.id === lote.id) {
          l[attr] = licitante;
        }
        return l;
      });
      this.updateVencedores();
    }
  }

  @action
  removeLicitanteLote(lote) {
    if (lote) {
      this.lotes = this.lotes.map((l) => {
        if (l.id === lote.id) {
          l.licitante = null;
        }
        return l;
      });
      this.updateVencedores();
    }
  }

  @action
  updateVencedores() {
    const vencedores = this.lotes
      ?.map((l) => {
        const valorTotal = l.itens?.map((i) => i.valorTotal).reduce((a, b) => a + b, 0);
        const vencedor = { id: l.idVencedor, lote: l, valor: valorTotal, licitante: l.licitante };
        vencedor[this.propItens] = l.itens?.map((i) => {
          return {
            ...i,
            id: i.idItem,
          };
        });
        return vencedor;
      })
      ?.filter((v) => v.licitante);
    this.vencedores = vencedores;
    this.onChange && this.onChange(vencedores);
  }

  @action
  setQuantidade(lote, item, quantidade, attr = 'quantidade') {
    if (lote && item) {
      if (!quantidade) {
        quantidade = 0;
      }
      quantidade = parseFloat(quantidade);
      if (quantidade > item.quantidadeDisponivel) {
        quantidade = item.quantidadeDisponivel;
      }
      this.updateAttributeItem(lote, item, attr, quantidade, () => this.setValorTotal(lote, item));
      this.updateVencedores();
    }
  }

  @action
  setDesconto(lote, item, desconto, attr = 'desconto') {
    if (lote && item) {
      if (desconto > 100) {
        desconto = 100;
      }
      this.updateAttributeItem(lote, item, attr, desconto, () => this.setValorTotal(lote, item));
      this.updateVencedores();
    }
  }

  @action
  setValorTotal(lote, item) {
    if (lote && item) {
      this.lotes = this.lotes?.map((l) => {
        if (l.id === lote.id) {
          l.itens = l.itens?.map((i) => {
            if (i.id === item.id) {
              const desconto = i.desconto || 0;
              const valor = parseFloat(
                (i.valorUnitario * i.quantidade - (i.valorUnitario * i.quantidade * desconto) / 100).toFixed(3)
              );
              i.valorTotal = valor;
            }
            return i;
          });
        }
        return l;
      });
      this.updateVencedores();
    }
  }

  @action
  setLicitantes(licitantes) {
    this.licitantes = licitantes;
  }

  @action
  setLoteItemFracassado(lote, loteUnico = true) {
    if (loteUnico) {
      this.lotesFracassados = lote?.map((l) => ({ lote: { ...l } }));
    } else {
      this.lotesFracassados.push({ lote: this.termoReferencia?.lotes?.find((l) => l.id === lote.id) });
      this.lotes = this.lotes?.map((l) => {
        if (l.id == lote.id) {
          return {
            ...l,
            licitante: undefined,
            itens: l.itens.map((i) => {
              return {
                ...i,
                quantidadeDisponivel: i.quantidade,
                valorUnitario: i.valorUnitarioEstimado,
                desconto: 0,
                valorTotal: i.valorUnitarioEstimado * i.quantidade,
                itemCatalogo: i,
                preenchido: false,
              };
            }),
          };
        } else {
          return l;
        }
      });
    }
    this.onChangeLotesFracassados(this.lotesFracassados);
    this.updateVencedores();
  }

  @action
  setLoteItemNaoFracassado(lote) {
    this.lotesFracassados = this.lotesFracassados.filter((l) => l.lote.id !== lote.id);
    this.onChangeLotesFracassados(this.lotesFracassados);
  }

  getValorTotal() {
    const valorTotal = this.vencedores?.map((vencedor) => vencedor.valor).reduce((a, b) => a + b, 0);
    return Number.isNaN(valorTotal) ? 0 : valorTotal;
  }

  calculaValorTotalVencedor(vencedor = this.vencedor) {
    const itens = vencedor[this.propItens];
    return itens?.map((item) => (item.quantidade || 0) * (item.valorUnitario || 0)).reduce((a, b) => a + b, 0) || 0;
  }

  @action
  changeModifyVencedor() {
    this.modifyVencedor = !this.modifyVencedor;
    this.vencedor = {
      ...this.vencedor,
      motivoAlteracao: '',
      obsMotivoAlteracao: '',
    };
    this.backupVencedor();
  }

  backupVencedor() {
    if (this.modifyVencedor) {
      this.lastVencedor = this.vencedor.licitante;
    } else {
      this.lastVencedor = undefined;
    }
  }

  @action
  updateAttributeLicitante(attribute, event) {
    let value = event;
    if (event && event.value) {
      value = event.value;
    }
    if (event && event.target) {
      value = event.target.value;
    }
    this.vencedor[attribute] = value;
  }

  getVencedoresAgrupados() {
    const groupByLicitante = (vencedores) => {
      return vencedores.reduce((vencedoresAgrupados, vencedor) => {
        (vencedoresAgrupados[vencedor.licitante.id] = vencedoresAgrupados[vencedor.licitante.id] || []).push(vencedor);
        return vencedoresAgrupados;
      }, {});
    };

    const vencedoresAgrupados = groupByLicitante(this.vencedores);
    const vencedores = Object.values(vencedoresAgrupados).map((vencedores) => {
      const vencedor = {};
      vencedor.licitante = vencedores[0].licitante;
      vencedor.lotes = vencedores.map((venc) => {
        return { ...venc.lote, valor: venc.valor };
      });
      vencedor.valor = vencedores.map((venc) => venc.valor).reduce((a, b) => a + b, 0);
      return vencedor;
    });
    return vencedores.sort((a, b) => {
      return (b.valor || 0) - (a.valor || 0);
    });
  }

  getLotesFracassadosAgrupados() {
    return [];
    // const groupByLotesFracassados = (lotesFracassados) => {
    //   return lotesFracassados.reduce((lotesFracassadosAgrupados, loteFracassado) => {
    //     (lotesFracassadosAgrupados[loteFracassado.lote.id] =
    //       lotesFracassadosAgrupados[loteFracassado.lote.id] || []).push(loteFracassado);
    //     return lotesFracassadosAgrupados;
    //   }, {});
    // };
    // const lotesFracassadosAgrupados = groupByLotesFracassados(this.lotesFracassados);
    // const lotesFracassados = Object.values(lotesFracassadosAgrupados).map((lotesFracassados) => {
    //   const loteFracassado = {};
    //   loteFracassado.lotes = lotesFracassados.map((lotesFrac) => {
    //     return { ...lotesFrac.lote };
    //   });
    //   loteFracassado.valor = lotesFracassados.map((lotesFrac) => lotesFrac.valor).reduce((a, b) => a + b, 0);
    //   return loteFracassado;
    // });
    // return lotesFracassados.sort((a, b) => {
    //   return (b.valor || 0) - (a.valor || 0);
    // });
  }

  getLoteUnico() {
    let loteUnico = {};
    if (this.lotes?.length > 0) {
      const lotesGerados = this.lotes.filter((lote) => lote.gerado);
      if (lotesGerados?.length > 0) {
        const itensLoteUnico = lotesGerados.map((lote) => {
          let item = null;
          if (lote.itens?.length > 0) {
            const loteFracassado = this.lotesFracassados?.findIndex((l) => lote.id === l.lote.id) >= 0;
            item = { ...lote.itens[0], lote: lote, fracassado: loteFracassado };
          }
          return item;
        });
        loteUnico = { nome: 'Lote Único', gerado: true, itens: itensLoteUnico.filter((item) => item) };
      }
    }
    return loteUnico;
  }

  @action
  updateAttributeItem(lote, item, attribute, event, callback) {
    if (lote && item) {
      let value = event;
      if (event && event.value) {
        value = event.value;
      }
      if (event && event.target) {
        value = event.target.value;
      }

      this.lotes = this.lotes?.map((l) => {
        if (l.id === lote.id) {
          l.itens = l.itens?.map((i) => {
            if (i.id === item.id) {
              i[attribute] = value;
            }
            return i;
          });
        }
        return l;
      });
      callback && callback();
    }
  }

  checkLoteFracassado() {
    // return this.lotesFracassados.find((v) => v.lote.id === lote.id) ? true : false;
    return false;
  }

  statusLote(lote) {
    let status = 'error';
    const itensNaoPreenchidos = lote.itens?.filter((item) => !item.preenchido)?.length;
    if (this.isLoteUnico) {
      if (this.lotes?.find((l) => l.licitante) || itensNaoPreenchidos < lote.itens?.length) {
        status = 'warning';
      }
      if (this.lotes?.filter((l) => !l.licitante)?.length === 0 && itensNaoPreenchidos === 0) {
        status = 'check';
      }
    } else {
      if (lote.licitante && itensNaoPreenchidos === 0) {
        status = 'check';
      } else if (lote.licitante || itensNaoPreenchidos < lote.itens?.length) {
        status = 'warning';
      } else if (this.checkLoteFracassado(lote)) {
        status = 'failed';
      }
    }

    return status;
  }

  rulesDefinition() {
    let rules = {
      valorUnitario: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      quantidade: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      observacao: [{ rule: 'isMaxLength', maxLength: 4000, message: 'Por favor, diminua o tamanho do campo' }],
    };
    if (this.showDialogVencedores) {
      rules = {};
    }
    if (this.modifyVencedor) {
      rules = {
        ...rules,
        motivoAlteracao: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
        obsMotivoAlteracao: [
          { rule: 'required', message: 'Por favor, preencha o campo' },
          { rule: 'isMaxLength', maxLength: 4000, message: 'Por favor, diminua o tamanho do campo' },
        ],
      };
    }
    return rules;
  }

  @computed
  get rules() {
    const definition = this.rulesDefinition();
    const result = extractRules(definition, this.vencedor);
    Object.keys(result).forEach((key) => {
      const error = result[key].error;
      if (error) {
        result.hasError = true;
      }
    });
    return result;
  }

  @computed
  get isLoteUnico() {
    return this.lotes?.find((lote) => lote.gerado) ? true : false;
  }

  getRule(field) {
    return this.rules[field] ?? {};
  }

  @computed
  get validaItensPreenchidos() {
    return (this.lotes ?? []).every(
      (lote) =>
        lote.itens?.every((item) => item.preenchido) ||
        this.lotesFracassados.findIndex((v) => v.lote.id == lote.id) != -1
    );
  }
}

export default VencedorFormStore;
