import Entidade from '~/domains/Entidade';
import EntradaService from '~/services/EntradaService';
import EntidadeService from '~/services/EntidadeService';
import IndexBase from 'fc/stores/IndexBase';
import AppStore from 'fc/stores/AppStore';
import { checkUserGroup, showErrorNotification } from 'fc/utils/utils';
import { action, observable, runInAction } from 'mobx';
import AsyncDropDownStore from 'fc/stores/AsyncDropdownStore';

class PaginaInicialIndexStore extends IndexBase {
  @observable resumoAlertas;

  constructor() {
    super(EntidadeService, Entidade, 'id');

    this.getPrecosQuantidades = this.getPrecosQuantidades.bind(this);
    this.getAlertas = this.getAlertas.bind(this);
  }

  async getPrecosQuantidades() {
    try {
      const value = await EntradaService.getPrecosQuantidades(AppStore.getContextEntity()?.id);
      return value.data;
    } catch (error) {
      showErrorNotification(error);
    }
  }

  @action
  getAlertas() {
    const isAuditor = checkUserGroup('Auditor');
    const isDafo = checkUserGroup('DAFO');
    const entity = isAuditor || isDafo ? null : AppStore.getData('selectedContextEntity');

    if (isAuditor || isDafo || entity) {
      EntradaService.getAlertas(entity?.id ?? entity)
        .then((response) =>
          runInAction(() => {
            this.resumoAlertas = response.data;
          })
        )
        .catch((e) => {
          showErrorNotification(e);
        });
    } else {
      setTimeout(() => this.getAlertas(), 150);
    }
  }

  getAdvancedSearchParams() {
    return [
      {
        field: 'entidade',
        label: 'Órgão/Entidade',
        type: SearchTypes.ASYNC_QUERY,
        store: new AsyncDropDownStore(Entidade, EntidadeService, 'nome', 'id'),
      },
      {
        field: 'dataCadastro',
        label: 'Data de Cadastro',
        type: SearchTypes.DATE,
      },
      {
        field: 'tipoProcesso',
        label: 'Tipo do Processo',
        type: SearchTypes.ENUM,
        options: DadosEstaticosService.getTipoProcesso(),
      },
      {
        field: 'valor',
        label: 'Valor',
        type: SearchTypes.NUMBER,
      },
    ];
  }
}

export default PaginaInicialIndexStore;
