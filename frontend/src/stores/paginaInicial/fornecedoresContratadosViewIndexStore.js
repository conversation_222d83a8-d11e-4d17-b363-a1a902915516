import SearchTypes from 'fc/components/AdvancedSearch/SearchTypes';
import FornecedoresContratadosView from '~/domains/FornecedoresContratadosView';
import FornecedoresContratadosViewService from '~/services/FornecedoresContratadosViewService';
import EntidadeService from '~/services/EntidadeService';
import LicitanteService from '~/services/LicitanteService';
import IndexBase from 'fc/stores/IndexBase';
import Licitante from '~/domains/Licitante';
import Entidade from '~/domains/Entidade';
import AsyncDropDownStore from 'fc/stores/AsyncDropdownStore';
import AppStore from 'fc/stores/AppStore';
import SearchOperators from 'fc/components/AdvancedSearch/SearchOperators';
import { showErrorNotification } from 'fc/utils/utils';
import { override, runInAction } from 'mobx';

class FornecedoresContratadosViewIndexStore extends IndexBase {
  constructor() {
    super(FornecedoresContratadosViewService, FornecedoresContratadosView, 'data', 'desc');
  }

  getFilterSuggest() {
    const entidade = AppStore.getContextEntity();
    const filterSuggest = [];
    if (entidade) {
      filterSuggest.push({
        id: '',
        field: 'entidade',
        operator: 'EQUAL_TO',
        value: entidade,
        formatted: '',
        fixed: true,
        invisible: true,
        completeParam: {
          field: 'entidade',
          label: 'Órgão/Entidade',
          type: SearchTypes.ASYNC_QUERY,
          store: new AsyncDropDownStore(Entidade, EntidadeService, 'nome', 'id'),
        },
      });
    }

    return filterSuggest;
  }

  getAdvancedSearchParams() {
    return [
      {
        field: 'licitante',
        label: 'Licitante',
        type: SearchTypes.ASYNC_QUERY,
        store: new AsyncDropDownStore(Licitante, LicitanteService, 'nome', 'id'),
      },
      {
        field: 'entidade',
        label: 'Órgão/Entidade',
        type: SearchTypes.ASYNC_QUERY,
        store: new AsyncDropDownStore(Entidade, EntidadeService, 'nome', 'id'),
      },
      {
        field: 'valor',
        label: 'Valor',
        type: SearchTypes.NUMBER,
      },
    ];
  }

  @override
  load(options = {}, callback, increment = false) {
    this.setLoading(true, increment);
    const entidade = AppStore.getContextEntity();

    const parameters = Object.assign(this.advancedSearchParams, options);

    if (options && options.page && options.page.index && options.page.size) {
      parameters.page = { index: options.page.index, size: options.page.size };
    } else {
      parameters.page = { index: 1, size: 10 };
    }

    if (options.andParameters && options.andParameters.length > 0) {
      parameters.andParameters = options.andParameters;
    }

    if (options.orParameters && options.orParameters.length > 0) {
      parameters.orParameters = options.orParameters;
    }

    if (options && options['sort'] && options['sort'].by && options['sort'].order) {
      parameters.sort = { by: options['sort'].by, order: options['sort'].order };
    } else {
      parameters.sort = { by: this.defaultSortBy, order: this.defaultSortOrder };
    }

    const parametersAndFilters = JSON.parse(JSON.stringify(parameters));
    if (entidade) {
      parametersAndFilters.andParameters.push({
        field: 'entidade',
        operator: SearchOperators.EQUAL_TO.value,
        value: entidade?.id ?? entidade,
      });
    }

    this.pagination = parametersAndFilters;
    FornecedoresContratadosViewService.advancedSearch(parametersAndFilters)
      .then((response) =>
        runInAction(() => {
          if (increment) {
            this.pagination.total += response.data.total;
            this.list = this.initializeLoadedList([...this.list, ...response.data.items]);
          } else {
            this.pagination.total = response.data.total;
            this.list = this.initializeLoadedList(response.data.items ?? []);
          }
          callback && callback(response);
        })
      )
      .catch((error) => showErrorNotification(error))
      .finally(() =>
        runInAction(() => {
          this.setLoading(false, increment);
        })
      );
  }
}

export default FornecedoresContratadosViewIndexStore;
