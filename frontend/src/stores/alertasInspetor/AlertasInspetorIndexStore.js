import SearchTypes from 'fc/components/AdvancedSearch/SearchTypes';
import AlertaAnaliseEntidadeViewService from '~/services/AlertaAnaliseEntidadeViewService';
import IndexBase from 'fc/stores/IndexBase';
import AlertaAnaliseEntidadeView from '~/domains/AlertaAnaliseEntidadeView';
import LicitacaoService from '~/services/LicitacaoService';
import CaronaService from '~/services/CaronaService';
import DispensaService from '~/services/DispensaService';
import InexigibilidadeService from '~/services/InexigibilidadeService';
import { action, computed, observable, runInAction } from 'mobx';
import { getValueByKey, isValueValid, showErrorNotification, showNotification } from 'fc/utils/utils';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import TdaDispensaService from '~/services/TdaDispensaService';
import TdaLicitacaoService from '~/services/TdaLicitacaoService';
import TdaCaronaService from '~/services/TdaCaronaService';
import TdaInexigibilidadeService from '~/services/TdaInexigibilidadeService';
import AlertaMensagemService from '~/services/AlertaMensagemService';
import UsuarioAuditorView from '~/domains/UsuarioAuditorView';
import UsuarioAuditorServiceView from '~/services/UsuarioAuditorViewService';
import Entidade from '~/domains/Entidade';
import EntidadeService from '~/services/EntidadeService';
import AsyncDropDownStore from 'fc/stores/AsyncDropdownStore';
import CredenciamentoService from '~/services/CredenciamentoService';
import TdaCredenciamentoService from '~/services/TdaCredenciamentoService';
import AlertaAnaliseService from '~/services/AlertaAnaliseService';
import { extractRules } from 'fc/utils/formRules';

class AlertasInspetorIndexStore extends IndexBase {
  @observable processo;
  @observable tipoProcesso;
  @observable loadingProcesso = false;
  @observable object;
  @observable mensagem = null;
  @observable tda;
  @observable statusArquivamento;

  constructor() {
    const domainsAndServices = {
      licitacao: {
        serviceProcesso: LicitacaoService,
        serviceTda: TdaLicitacaoService,
        tda: 'tdaLicitacao',
        idProcesso: 'idLicitacao',
      },
      carona: {
        serviceProcesso: CaronaService,
        serviceTda: TdaCaronaService,
        tda: 'tdaCarona',
        idProcesso: 'idCarona',
      },
      dispensa: {
        serviceProcesso: DispensaService,
        serviceTda: TdaDispensaService,
        tda: 'tdaDispensa',
        idProcesso: 'idDispensa',
      },
      inexigibilidade: {
        serviceProcesso: InexigibilidadeService,
        serviceTda: TdaInexigibilidadeService,
        tda: 'tdaInexigibilidade',
        idProcesso: 'idInexigibilidade',
      },
      credenciamento: {
        serviceProcesso: CredenciamentoService,
        serviceTda: TdaCredenciamentoService,
        tda: 'tdaCredenciamento',
        idProcesso: 'idCredenciamento',
      },
    };
    super(AlertaAnaliseEntidadeViewService, AlertaAnaliseEntidadeView, 'data', 'desc');
    this.domainsAndServices = domainsAndServices;

    this.updateMensagem = this.updateMensagem.bind(this);
    this.updateRespostaRejeicao = this.updateRespostaRejeicao.bind(this);
    this.getRule = this.getRule.bind(this);
  }

  getAdvancedSearchParams() {
    return [
      {
        field: 'data',
        label: 'Data Emissão',
        type: SearchTypes.DATE,
      },
      {
        field: 'tipo',
        label: 'Tipo do Processo',
        type: SearchTypes.ENUM,
        options: DadosEstaticosService.getTipoProcesso(),
      },
      {
        field: 'usuarioResponsavel',
        label: 'Auditor Responsável',
        type: SearchTypes.ASYNC_QUERY,
        store: new AsyncDropDownStore(UsuarioAuditorView, UsuarioAuditorServiceView, 'nome', 'id'),
      },
      {
        field: 'entidade',
        label: 'Órgão/Entidade',
        type: SearchTypes.ASYNC_QUERY,
        store: new AsyncDropDownStore(Entidade, EntidadeService, 'nome', 'id'),
      },
    ];
  }

  @action
  loadProcesso(alerta, callback) {
    this.mensagem = alerta.mensagens[alerta.mensagens.length - 1]?.mensagemAuditor;
    this.loadingProcesso = true;
    this.object = alerta;
    this.tipoProcesso = getValueByKey(alerta.tipo, DadosEstaticosService.getTiposProcesso(), 'key', 'lowerText');
    const processo = this.domainsAndServices[this.tipoProcesso];
    this.tda = alerta[processo.tda];
    processo.serviceProcesso
      .getById(this.tda[processo.idProcesso])
      .then((response) =>
        runInAction(() => {
          this.processo = response.data;
          this.loadingProcesso = false;
          callback && callback();
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      )
      .finally(() =>
        runInAction(() => {
          this.loadingProcesso = false;
        })
      );
  }

  rulesDefinition() {
    return {
      prazoResposta: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
    };
  }

  getRule(field) {
    return this.rules[field] ? this.rules[field] : {};
  }

  @computed
  get rules() {
    const definition = this.rulesDefinition();
    const result = extractRules(definition, this.object);
    Object.keys(result).forEach((key) => {
      const error = result[key].error;
      if (error) {
        result.hasError = true;
      }
    });
    return result;
  }

  @action
  updateMensagem(event) {
    let value = event;
    if (event) {
      if (isValueValid(event.target)) {
        value = event.target.value;
      } else if (isValueValid(event.value)) {
        value = event.value;
      } else if (typeof event.getData === 'function') {
        value = event.getData();
      } else {
        value = undefined;
      }
    }
    this.mensagem = value;
  }

  @action
  updateRespostaRejeicao(event) {
    let value = event;
    if (event) {
      if (isValueValid(event.target)) {
        value = event.target.value;
      } else if (isValueValid(event.value)) {
        value = event.value;
      } else {
        value = undefined;
      }
    }
    this.object.respostaRejeicao = value;
  }

  getFilterSuggest() {
    const filterSuggest = [];
    filterSuggest.push({
      id: '',
      field: 'status',
      operator: 'EQUAL_TO',
      value: 'INSPETOR',
      formatted: '',
      fixed: true,
      invisible: true,
      completeParam: {
        field: 'status',
        label: 'Status',
        type: SearchTypes.ENUM,
        options: DadosEstaticosService.getStatusAlertaAnalise(),
      },
    });
    return filterSuggest;
  }

  @action
  rejeitarAlerta(alerta, callback) {
    if (!alerta?.respostaRejeicao) {
      showNotification('error', null, 'Por favor, informe um motivo para rejeição!');
    } else {
      AlertaMensagemService.rejeitarMensagem(alerta.id, alerta.respostaRejeicao)
        .then(() => {
          runInAction(() => {
            showNotification('success', null, 'Alerta rejeitado com sucesso!');
          });
        })
        .catch((error) => {
          runInAction(() => {
            showErrorNotification(error);
          });
        })
        .finally(() => {
          callback && callback();
        });
    }
  }

  @action
  aprovarAlerta(prazoResposta, callback) {
    const idMensagem = this.object.mensagens[this.object.mensagens.length - 1]?.id;
    AlertaMensagemService.aprovarMensagem(idMensagem, this.mensagem, prazoResposta)
      .then(() => {
        runInAction(() => {
          showNotification('success', null, 'Alerta enviado com sucesso!');
        });
      })
      .catch((error) => {
        runInAction(() => {
          showErrorNotification(error);
        });
      })
      .finally(() => {
        callback && callback();
      });
  }

  @action
  aprovarEArquivarAlerta(idAlertaAnalise, callback) {
    if (!this.statusArquivamento) {
      showNotification('error', null, 'Por favor, selecione um status');
      return;
    }

    AlertaAnaliseService.arquivarAlerta(idAlertaAnalise, this.statusArquivamento)
      .then(() => {
        runInAction(() => {
          showNotification('success', null, 'Alerta arquivado com sucesso!');
          callback && callback();
        });
      })
      .catch((error) => {
        runInAction(() => {
          showErrorNotification(error);
        });
      });
  }

  @action
  updateStatusArquivamento(value) {
    this.statusArquivamento = value;
  }
}

export default AlertasInspetorIndexStore;
