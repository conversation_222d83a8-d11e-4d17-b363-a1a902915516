import FormBase from 'fc/stores/FormBase';
import LicitacaoService from '~/services/LicitacaoService';
import CaronaService from '~/services/CaronaService';
import DispensaService from '~/services/DispensaService';
import InexigibilidadeService from '~/services/InexigibilidadeService';
import TdaLicitacaoService from '~/services/TdaLicitacaoService';
import TdaCaronaService from '~/services/TdaCaronaService';
import TdaDispensaService from '~/services/TdaDispensaService';
import TdaInexigibilidadeService from '~/services/TdaInexigibilidadeService';
import Licitacao from '~/domains/Licitacao';
import Carona from '~/domains/Carona';
import Dispensa from '~/domains/Dispensa';
import Inexigibilidade from '~/domains/Inexigibilidade';
import AlertaAnalise from '~/domains/AlertaAnalise';
import AlertaAnaliseService from '~/services/AlertaAnaliseService';
import { action, observable, override, runInAction } from 'mobx';
import { isValueValid, showErrorNotification, showNotification } from 'fc/utils/utils';
import AsyncPicklistStore from 'fc/stores/AsyncPicklistStore';
import Usuario from '~/domains/Usuario';
import UsuarioService from '~/services/UsuarioService';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import SearchOperators from 'fc/components/AdvancedSearch/SearchOperators';
import GrupoUsuarioService from '~/services/GrupoUsuarioService';
import AppStore from 'fc/stores/AppStore';
import TdaService from '~/services/TdaService';
import AnaliseProcessoViewService from '~/services/AnaliseProcessoViewService';
import CredenciamentoService from '~/services/CredenciamentoService';
import TdaCredenciamentoService from '~/services/TdaCredenciamentoService';
import Credenciamento from '~/domains/Credenciamento';

class EmitirAlertaFormStore extends FormBase {
  @observable destinatariosStore;
  @observable diretoresDafo = [];
  @observable comissao;
  @observable motivoChecklistRejeitado;

  constructor(idProcesso, tipoProcesso) {
    const domainsAndServices = {
      licitacao: {
        serviceProcesso: LicitacaoService,
        serviceTdaProcesso: TdaLicitacaoService,
        domaiProcesso: Licitacao,
      },
      carona: {
        serviceProcesso: CaronaService,
        serviceTdaProcesso: TdaCaronaService,
        domaiProcesso: Carona,
      },
      dispensa: {
        serviceProcesso: DispensaService,
        serviceTdaProcesso: TdaDispensaService,
        domaiProcesso: Dispensa,
      },
      inexigibilidade: {
        serviceProcesso: InexigibilidadeService,
        serviceTdaProcesso: TdaInexigibilidadeService,
        domaiProcesso: Inexigibilidade,
      },
      credenciamento: {
        serviceProcesso: CredenciamentoService,
        serviceTdaProcesso: TdaCredenciamentoService,
        domaiProcesso: Credenciamento,
      },
    };
    super(AlertaAnaliseService, AlertaAnalise);
    this.domainsAndServices = domainsAndServices;
    this.tipoProcesso = tipoProcesso;
    this.idProcesso = idProcesso;
    this.updateMensagem = this.updateMensagem.bind(this);
    this.updateRespostaRejeicaoInspetor = this.updateRespostaRejeicaoInspetor.bind(this);
  }

  rulesDefinition() {
    return {
      prazoResposta: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      mensagem: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      diretorDafo: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
    };
  }

  @override
  save(callback, type = 'edit') {
    const saveObject = this.getObjectToSave(type);
    if (Object.keys(saveObject).length === 0) {
      showNotification('warn', null, 'Nenhuma alteração realizada.');
    } else {
      const promises = [];
      const column =
        'tda' + DadosEstaticosService.getTiposProcesso().find((v) => v.lowerText === this.tipoProcesso).textCamelCase;
      this.object.respostaRejeicaoInspetor = undefined;
      this.object.dataRejeicaoInspetor = undefined;
      if (type === 'edit') {
        this.object.respostaRejeicao = undefined;
        this.object.dataRejeicao = undefined;
        this.object.passouPorDafo = false;
        this.object.status = 'ENCAMINHADO';
        this.object.usuarioRejeicao = undefined;
        this.processo.tda.statusProcessoArquivado = undefined;
        promises.push(
          this.domainsAndServices[this.tipoProcesso].serviceTdaProcesso.update(this.processo.tda, this.processo.tda.id)
        );
      }
      this.object[column] = this.processo.tda;
      if (!this.object?.usuarioAtual?.entidades) this.object.usuarioAtual.entidades = [];
      promises.push(this.service.save(this.object, type, this.object.id));
      this.loading = true;
      Promise.all(promises)
        .then(() => {
          callback && callback();
          showNotification('success', null, 'Registro salvo com sucesso!');
        })
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        )
        .finally(() =>
          runInAction(() => {
            this.loading = false;
          })
        );
    }
  }

  @action
  updateMensagem(event) {
    let value = event;
    if (event) {
      if (isValueValid(event.target)) {
        value = event.target.value;
      } else if (isValueValid(event.value)) {
        value = event.value;
      } else if (typeof event.getData === 'function') {
        value = event.getData();
      } else {
        value = undefined;
      }
    }
    this.object.mensagem = value;
  }

  @action
  updateRespostaRejeicaoInspetor(event) {
    let value = event;
    if (event) {
      if (isValueValid(event.target)) {
        value = event.target.value;
      } else if (isValueValid(event.value)) {
        value = event.value;
      } else {
        value = undefined;
      }
    }
    this.motivoChecklistRejeitado = value;
  }

  @action
  rejeitarCheckInspetor(loadingCallback, callback) {
    if (!this.motivoChecklistRejeitado) {
      showNotification('error', null, 'Por favor, informe um motivo para rejeição!');
      loadingCallback && loadingCallback();
    } else {
      const rejeicaoChecklistRequest = {
        idTda: this.tda.id,
        motivoChecklistRejeitado: this.motivoChecklistRejeitado,
        tipoProcesso: this.object.tipo,
      };

      TdaService.rejeitarChecklist(rejeicaoChecklistRequest)
        .then(() => {
          runInAction(() => {
            showNotification('success', null, 'Alerta rejeitado com sucesso!');
          });
        })
        .catch((error) => {
          runInAction(() => {
            showErrorNotification(error);
          });
        })
        .finally(() => {
          callback && callback();
          loadingCallback && loadingCallback();
        });
    }
  }

  @action
  initializeStoreUsers() {
    this.destinatariosStore = new AsyncPicklistStore(
      Usuario,
      UsuarioService,
      'nome',
      'id',
      {
        andParameters: [
          { field: 'entidades', operator: SearchOperators.EQUAL_TO.value, value: this.processo.entidade.id },
        ],
      },
      {
        by: 'nome',
        order: 'asc',
      },
      {
        filter: true,
        filterBy: 'nome',
      }
    );
  }

  @action
  initializeProcesso(callback) {
    this.loading = true;
    const promises = [this.domainsAndServices[this.tipoProcesso].serviceProcesso.getById(this.idProcesso)];
    promises.push(UsuarioService.getById(AppStore.getData('userDetails').id));
    promises.push(
      AnaliseProcessoViewService.getProcesso(
        this.idProcesso,
        DadosEstaticosService.getTiposProcesso().find((v) => v.lowerText === this.tipoProcesso).key
      )
    );
    Promise.all(promises)
      .then((response) => {
        runInAction(() => {
          this.processo = response[0]?.data;
          this.object.usuarioAtual = response[1]?.data;
          this.object.usuarioResponsavel = this.processo.tda.responsavel;
          if (this.tipoProcesso === 'licitacao' && this.processo?.comissao?.membros) {
            this.comissao = this.processo.comissao.membros;
          }
          this.alertaRejeitado = response[2]?.data?.labels?.includes('REJEITADO_DIRETOR_DAFO');
          this.initializeStoreUsers();
          this.initializeTda(callback);
        });
      })
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      )
      .finally(() =>
        runInAction(() => {
          callback && callback();
          this.loading = false;
        })
      );
  }

  @action
  initializeTda(callback) {
    this.domainsAndServices[this.tipoProcesso].serviceTdaProcesso
      .getById(this.processo?.tda.id)
      .then((response) =>
        runInAction(() => {
          this.tda = response.data;
          this.object.mensagem = response.data.relatorioChecklist ?? this.object.mensagem;
          callback && callback();
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      );
  }

  @action
  getDiretoresDafo() {
    const filtro = {
      page: { index: 1, size: 100 },
      sort: {},
      andParameters: [{ field: 'nome', operator: SearchOperators.EQUAL_TO.value, value: 'DAFO' }],
    };
    GrupoUsuarioService.advancedSearch(filtro)
      .then((response) => {
        runInAction(() => {
          const usuarios = response?.data?.items && response.data.items[0]?.usuarios;
          this.diretoresDafo = usuarios;
        });
      })
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      );
  }
}

export default EmitirAlertaFormStore;
