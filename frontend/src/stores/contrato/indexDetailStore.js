import SearchTypes from 'fc/components/AdvancedSearch/SearchTypes';
import Contrato from '~/domains/Contrato';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import ContratoService from '~/services/ContratoService';
import LicitanteService from '~/services/LicitanteService';
import FonteRecursoService from '~/services/FonteRecursoService';
import EntidadeService from '~/services/EntidadeService';
import IndexBase from 'fc/stores/IndexBase';
import Licitante from '~/domains/Licitante';
import AsyncDropDownStore from 'fc/stores/AsyncDropdownStore';
import { checkUserGroup } from 'fc/utils/utils';
import Entidade from '~/domains/Entidade';
import AppStore from 'fc/stores/AppStore';
import FonteRecurso from '~/domains/FonteRecurso';
import AsyncMultiselectStore from 'fc/stores/AsyncMultiselectStore';
import EntidadeExterna from '~/domains/EntidadeExterna';
import EntidadeExternaService from '~/services/EntidadeExternaService';

class ContratoIndexDetailStore extends IndexBase {
  constructor() {
    super(ContratoService, Contrato, 'dataCadastro', 'desc');
  }

  getFilterSuggest(idLicitacao = undefined) {
    const filterSuggest = [];

    if (idLicitacao) {
      filterSuggest.push({
        id: '',
        field: 'idLicitacao',
        operator: 'EQUAL_TO',
        value: idLicitacao,
        formatted: '',
        fixed: true,
        invisible: true,
        completeParam: {
          field: 'idLicitacao',
          label: 'Id Licitação',
          type: SearchTypes.NUMBER,
        },
      });
      filterSuggest.push({
        id: '',
        field: 'status',
        operator: 'NOT_EQUAL_TO',
        value: 'REMOVIDA',
        formatted: '',
        fixed: true,
        invisible: true,
        completeParam: {
          field: 'status',
          label: 'Status',
          type: SearchTypes.ENUM,
          options: DadosEstaticosService.getStatusContrato(),
        },
      });
    }

    const entidade = AppStore.getContextEntity();
    if (entidade && !idLicitacao) {
      filterSuggest.push({
        id: '',
        field: 'entidade',
        operator: 'EQUAL_TO',
        value: entidade,
        formatted: '',
        fixed: true,
        invisible: true,
        completeParam: {
          field: 'entidade',
          label: 'Órgão/Entidade',
          type: SearchTypes.ASYNC_QUERY,
          store: new AsyncDropDownStore(Entidade, EntidadeService, 'nome', 'id'),
        },
      });
    }

    return filterSuggest;
  }

  getAdvancedSearchParams() {
    const isAuditor = checkUserGroup(['Auditor', 'Auditor - Demais Inspetorias']);
    let searchParams = [];

    if (isAuditor) {
      searchParams = [
        {
          field: 'entidade',
          label: 'Órgão/Entidade',
          type: SearchTypes.ASYNC_QUERY,
          store: new AsyncDropDownStore(Entidade, EntidadeService, 'nome', 'id'),
        },
      ];
    }
    searchParams.push(
      {
        field: 'entidadeExterna',
        label: 'Entidade Externa',
        type: SearchTypes.ASYNC_QUERY,
        store: new AsyncDropDownStore(EntidadeExterna, EntidadeExternaService, 'nomeEntidadeExterna', 'id'),
      },
      {
        field: 'tipo',
        label: 'Origem',
        type: SearchTypes.ENUM,
        options: DadosEstaticosService.getTipoProcesso(),
      },
      {
        field: 'numero',
        label: 'Número',
        type: SearchTypes.TEXT,
      },
      {
        field: 'formaContrato',
        label: 'Forma de Contrato',
        type: SearchTypes.ENUM,
        options: DadosEstaticosService.getFormaContrato(),
      },
      {
        field: 'valorGlobal',
        label: 'Valor',
        type: SearchTypes.NUMBER,
      },
      {
        field: 'objeto',
        label: 'Objeto',
        type: SearchTypes.TEXT,
      },
      {
        field: 'licitante',
        label: 'Contratado(a)',
        type: SearchTypes.ASYNC_QUERY,
        store: new AsyncDropDownStore(Licitante, LicitanteService, 'nome', 'id'),
      },
      {
        field: 'fontesDeRecurso',
        label: 'Fontes de Recurso',
        type: SearchTypes.ASYNC_QUERY,
        store: new AsyncMultiselectStore(FonteRecurso, FonteRecursoService, 'nome', 'id'),
      }
    );
    return searchParams;
  }
}

export default ContratoIndexDetailStore;
