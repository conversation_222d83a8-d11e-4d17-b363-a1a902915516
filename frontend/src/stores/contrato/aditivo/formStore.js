import FormBase from 'fc/stores/FormBase';
import AditivoContrato from '~/domains/AditivoContrato';
import AditivoContratoService from '~/services/AditivoContratoService';
import ContratoService from '~/services/ContratoService';
import ObrigatoriedadeArquivoService from '~/services/ObrigatoriedadeArquivoService';
import { action, makeObservable, observable, runInAction } from 'mobx';
import { getValueDate, getValueMoney, showErrorNotification, showNotification } from 'fc/utils/utils';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import moment from 'moment';
import MultipleFileUploaderStore from 'fc/stores/MultipleFileUploaderStore';

class AditivoContratoFormStore extends FormBase {
  @observable arquivoAditivoContratoList = [];
  @observable fileStore;
  @observable idAditivoContrato;
  @observable contrato;
  @observable loadingDadosComplementares = false;

  constructor() {
    super(AditivoContratoService, AditivoContrato);
    this.fileStore = new MultipleFileUploaderStore(
      [],
      (file) => AditivoContratoService.upload(file),
      (fileDTO) => AditivoContratoService.download(fileDTO),
      (idArquivo) => this.removerArquivoAditivoContrato(idArquivo),
      (idArquivo, arquivoAditivoContratoDTO) =>
        this.atualizarArquivoAditivoContrato(idArquivo, arquivoAditivoContratoDTO)
    );

    this.getProximoNumeroAditivo = this.getProximoNumeroAditivo.bind(this);
    this.carregaDadosComplementaresAditivo = this.carregaDadosComplementaresAditivo.bind(this);
    this.removerArquivoAditivoContrato = this.removerArquivoAditivoContrato.bind(this);
    this.atualizarArquivoAditivoContrato = this.atualizarArquivoAditivoContrato.bind(this);
    this.setArquivoAditivoContratoList = this.setArquivoAditivoContratoList.bind(this);
    this.carregaContrato = this.carregaContrato.bind(this);
    this.preencheResponsaveis = this.preencheResponsaveis.bind(this);
    this.isAdiamentoVigencia = this.isAdiamentoVigencia.bind(this);
    this.isAdiamentoValor = this.isAdiamentoValor.bind(this);
    this.checkDataCadastro = this.checkDataCadastro.bind(this);

    this.loadTipos();

    makeObservable(this);
  }

  @action
  loadTipos() {
    ObrigatoriedadeArquivoService.getArquivosObrigatorios({ tipoProcesso: 'ADITIVO', filtros: ['ADITIVO'] })
      .then((response) =>
        runInAction(() => {
          const arquivosObrigatorios = response?.data ?? [];
          this.fileStore.tipoArquivoEnum = arquivosObrigatorios.map((arqObg) => {
            const arq = DadosEstaticosService.getTipoArquivoAditivo().find((arq) => arq.value === arqObg.arquivoEnum);
            return { ...arqObg, text: arqObg.obrigatorio ? '* ' + arqObg.arquivo : arqObg.arquivo, value: arq.value };
          });
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      );
  }

  initialize(id, defaultValues = {}, callback) {
    if (!id) {
      this.object = Object.assign({}, defaultValues);
      this.getProximoNumeroAditivo(defaultValues.idContrato);
      callback && callback();
    } else {
      this.loading = true;
      AditivoContratoService.getById(id)
        .then((response) =>
          runInAction(() => {
            this.object = response.data;
            this.loadedObject = response.data;
            callback && callback();
          })
        )
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        )
        .finally(() =>
          runInAction(() => {
            this.loading = false;
          })
        );
    }
  }

  @action
  getProximoNumeroAditivo(idContrato) {
    this.loading = true;
    AditivoContratoService.getProximoNumero(idContrato)
      .then((response) =>
        runInAction(() => {
          this.object.numero = response.data;
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      )
      .finally(() =>
        runInAction(() => {
          this.loading = false;
        })
      );
  }

  @action
  carregaDadosComplementaresAditivo(idAditivoContrato) {
    if (idAditivoContrato) {
      this.loadingDadosComplementares = true;
      this.idAditivoContrato = idAditivoContrato;
      let promisses = [];
      promisses.push(AditivoContratoService.recuperarArquivos(idAditivoContrato));

      Promise.all(promisses)
        .then((response) => {
          runInAction(() => {
            const arquivosRecuperados = response[0].data;
            this.fileStore.initialize(arquivosRecuperados);
            this.arquivoAditivoContratoList = arquivosRecuperados;

            this.loadingDadosComplementares = false;
          });
        })
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
            this.loadingDadosComplementares = false;
          })
        );
    }
  }

  @action
  preencheResponsaveis() {
    if (this.contrato && this.object) {
      this.object.gestorTitular = this.contrato.gestor;
      this.object.gestorSuplente = this.contrato.gestorSubstituto;
      this.object.fiscalAditivo = this.contrato.fiscal;
      this.object.fiscalSuplente = this.contrato.fiscalSubstituto;
    }
  }

  rulesDefinition() {
    let rules = {
      tipoAlteracao: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      dataPublicacao: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      justificativa: [{ rule: 'isMaxLength', maxLength: 4000, message: 'Por favor, diminua o tamanho do campo' }],
      dataVigenciaFinal: [this.isAdiamentoVigencia() && { rule: 'required', message: 'Por favor, preencha o campo' }],
      valor: [this.isAdiamentoValor() && { rule: 'required', message: 'Por favor, preencha o campo' }],
    };
    if (!this.contrato?.processoMigrado) {
      rules = this.mergeRules(rules, {
        justificativa: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      });
    }
    return rules;
  }

  @action
  carregaContrato(idContrato, callback) {
    this.loading = true;
    ContratoService.getById(idContrato)
      .then((response) =>
        runInAction(() => {
          this.contrato = response.data;
          callback && callback();
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      )
      .finally(() =>
        runInAction(() => {
          this.loading = false;
        })
      );
  }

  @action
  setTipoAlteracao(e) {
    this.updateAttribute('tipoAlteracao', e);
    if (this.isAdiamentoVigencia()) {
      this.updateAttribute('dataVigenciaInicial', this.contrato?.dataVigenciaInicial);
      this.updateAttribute('dataVigenciaFinal', this.contrato?.dataVigenciaFinal);
    } else if (!this.isAdiamentoVigencia()) {
      this.updateAttribute('dataVigenciaFinal', null);
    }
    if (!this.isAdiamentoValor()) {
      this.updateAttribute('valor', null);
    }
  }

  @action
  isAdiamentoVigencia() {
    return ['ALTERACAO_PRAZO', 'REDUCAO_VIGENCIA'].includes(this.object?.tipoAlteracao);
  }

  @action
  isAdiamentoValor() {
    return ['ALTERACAO_VALOR', 'SUPRESSAO_VALOR'].includes(this.object?.tipoAlteracao);
  }

  @action
  setArquivoAditivoContratoList(arquivoAditivoContratoList) {
    this.arquivoAditivoContratoList = arquivoAditivoContratoList;
  }

  @action
  validateValuesAlteracaoContratual(tipo, valor, data) {
    const dataContratoVigenciaFinal = this.contrato?.dataVigenciaFinal;
    const alteracoesContratuais = {
      ALTERACAO_VALOR: {
        invalidValue: valor <= this.contrato?.valorGlobal + this.contrato?.aditivado - this.contrato?.suprimido,
        errorMessage: `O valor preenchido deve ser maior do que o valor vigente do contrato (${getValueMoney(
          this.contrato?.valorGlobal + this.contrato?.aditivado - this.contrato?.suprimido
        )}) para alteração do tipo aditivo de valor`,
      },
      SUPRESSAO_VALOR: {
        invalidValue: valor >= this.contrato?.valorGlobal + this.contrato?.aditivado - this.contrato?.suprimido,
        errorMessage: `O valor preenchido deve ser menor do que o valor vigente do contrato (${getValueMoney(
          this.contrato?.valorGlobal + this.contrato?.aditivado - this.contrato?.suprimido
        )}) para alteração do tipo supressão de valor`,
      },
      ALTERACAO_PRAZO: {
        invalidValue: moment(data).isSameOrBefore(dataContratoVigenciaFinal),
        errorMessage: `Para alteração do tipo prorrogação da vigência, a data final deve ser posterior a vigência final presente no contrato (${getValueDate(
          dataContratoVigenciaFinal
        )})`,
      },
      REDUCAO_VIGENCIA: {
        invalidValue: moment(data).isSameOrAfter(dataContratoVigenciaFinal),
        errorMessage: `Para alteração do tipo redução da vigência, a data final deve ser anterior a vigência final presente no contrato (${getValueDate(
          dataContratoVigenciaFinal
        )})`,
      },
    };
    return alteracoesContratuais[tipo];
  }

  save(callback, type = 'edit') {
    const saveObject = this.getObjectToSave(type);
    const dataAditivoVigenciaInicial = this.object.dataVigenciaInicial ? moment(this.object.dataVigenciaInicial) : null;
    const dataAditivoVigenciaFinal = this.object?.dataVigenciaFinal ? moment(this.object.dataVigenciaFinal) : null;
    const alteracaoContratual = this.validateValuesAlteracaoContratual(
      this.object?.tipoAlteracao,
      this.object?.valor,
      dataAditivoVigenciaFinal
    );

    if (Object.keys(saveObject).length === 0) {
      showNotification('warn', null, 'Nenhuma alteração realizada.');
    } else if (alteracaoContratual && alteracaoContratual.invalidValue) {
      showNotification('warn', null, alteracaoContratual.errorMessage);
    } else if (
      dataAditivoVigenciaInicial &&
      dataAditivoVigenciaFinal &&
      dataAditivoVigenciaFinal.isBefore(dataAditivoVigenciaInicial)
    ) {
      showNotification('warn', null, 'Fim da Vigência da Alteração Contratual é inferior ao Início da Vigência.');
    } else {
      if (this.object && this.contrato) {
        this.object.contrato = this.contrato;
        this.object.tipo = this.contrato.tipo;
      }
      const aditivoContratoDTO = {
        id: this.object.id,
        aditivoContrato: this.object,
        arquivosAditivo: this.arquivoAditivoContratoList,
      };

      this.loading = true;
      this.service
        .createAditivoContrato(aditivoContratoDTO)
        .then(() =>
          runInAction(() => {
            callback && callback();
            showNotification('success', null, 'Registro salvo com sucesso!');
          })
        )
        .catch((error) => showErrorNotification(error))
        .finally(() =>
          runInAction(() => {
            this.loading = false;
          })
        );
    }
  }

  removerArquivoAditivoContrato(idArquivo) {
    return AditivoContratoService.removerArquivo(this.idAditivoContrato, idArquivo);
  }

  atualizarArquivoAditivoContrato(idArquivo, arquivoAditivoContratoDTO) {
    return AditivoContratoService.atualizarArquivo(this.idAditivoContrato, idArquivo, arquivoAditivoContratoDTO);
  }

  checkDataCadastro(dataCadastro) {
    let data = this.object?.dataCadastro;
    if (dataCadastro) data = dataCadastro;
    if (data) {
      const dataCadastro = moment(data);
      const current = moment();
      const diff = moment.duration(current.diff(dataCadastro));

      this.enableReqMod = diff.asHours() > 24;
    }
  }
}

export default AditivoContratoFormStore;
