import IndexBase from 'fc/stores/IndexBase';
import SearchTypes from 'fc/components/AdvancedSearch/SearchTypes';
import AditivoContrato from '~/domains/AditivoContrato';
import ContratoAditivoService from '~/services/AditivoContratoService';
import ContratoService from '~/services/ContratoService';
import { action, observable, runInAction } from 'mobx';
import { getValueDate, showErrorNotification } from 'fc/utils/utils';
import moment from 'moment';

class AditivoContratoIndexStore extends IndexBase {
  @observable idContrato;
  @observable contrato;

  constructor() {
    super(ContratoAditivoService, AditivoContrato);
    this.setIdContrato = this.setIdContrato.bind(this);
    this.setContrato = this.setContrato.bind(this);
    this.carregaContrato = this.carregaContrato.bind(this);
  }

  @action
  setIdContrato(id) {
    this.idContrato = id;
  }

  @action
  setContrato(contrato) {
    this.contrato = contrato;
  }

  getAdvancedSearchParams() {
    return [
      {
        field: 'numero',
        label: 'Número',
        type: SearchTypes.TEXT,
      },
      {
        field: 'valor',
        label: 'Valor',
        type: SearchTypes.NUMBER,
      },
      {
        field: 'dataVigenciaFinal',
        label: 'Fim da Vigência',
        type: SearchTypes.DATE,
      },
    ];
  }

  @action
  carregaContrato(callback) {
    this.loading = true;
    ContratoService.getById(this.idContrato)
      .then((response) =>
        runInAction(() => {
          this.setContrato(response.data);
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      )
      .finally(() =>
        runInAction(() => {
          this.loading = false;
          callback && callback();
        })
      );
  }

  isRequisicaoModificacao(dataCadastro) {
    let result = false;
    if (dataCadastro) {
      const data = moment(dataCadastro);
      const current = moment();
      const diff = moment.duration(current.diff(data));

      if (diff.asHours() > 24) {
        result = true;
      }
    }
    return result;
  }

  getVigenciaFinal(rowData) {
    const dataContratoVigenciaFinal = moment(this.contrato?.dataVigenciaFinal);
    const dataAditivoVigenciaFinal = rowData?.dataVigenciaFinal ? moment(rowData?.dataVigenciaFinal) : null;

    return dataAditivoVigenciaFinal && dataAditivoVigenciaFinal.isSame(dataContratoVigenciaFinal)
      ? '-'
      : getValueDate(dataAditivoVigenciaFinal);
  }
}

export default AditivoContratoIndexStore;
