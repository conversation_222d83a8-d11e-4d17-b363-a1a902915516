import FormBase from 'fc/stores/FormBase';
import CryptoJS from 'crypto-js';
import <PERSON><PERSON><PERSON> from '~/domains/Contrato';
import MultipleFileUploaderStore from 'fc/stores/MultipleFileUploaderStore';
import ContratoService from '~/services/ContratoService';
import LicitacaoService from '~/services/LicitacaoService';
import DispensaService from '~/services/DispensaService';
import CaronaService from '~/services/CaronaService';
import InexigibilidadeService from '~/services/InexigibilidadeService';
import ObrigatoriedadeArquivoService from '~/services/ObrigatoriedadeArquivoService';
import EntidadeService from '~/services/EntidadeService';
import FonteRecursoService from '~/services/FonteRecursoService';
import { action, computed, makeObservable, observable, override, runInAction } from 'mobx';
import { DATE_PARSE_FORMAT_WITH_HOURS_AND_SECONDS } from 'fc/utils/date';
import moment from 'moment';
import { showErrorNotification, showNotification } from 'fc/utils/utils';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import AppStore from 'fc/stores/AppStore';
import EntidadeExterna from '~/domains/EntidadeExterna';
import EntidadeExternaService from '~/services/EntidadeExternaService';
import UsuarioService from '~/services/UsuarioService';
import AsyncDropDownStore from 'fc/stores/AsyncDropdownStore';
import LicitanteIndexStore from '../licitante/indexStore';
import SearchTypes from 'fc/components/AdvancedSearch/SearchTypes';
import omit from 'lodash/omit';
import Usuario from '~/domains/Usuario';
import FundamentacaoLegalService from '~/services/FundamentacaoLegalService';
import SearchOperators from 'fc/components/AdvancedSearch/SearchOperators';
import CredenciamentoService from '~/services/CredenciamentoService';
import CredenciadoItemService from '~/services/CredenciadoItemService';

class ContratoFormStore extends FormBase {
  @observable loadingFiles = false;
  @observable arquivoContratoList = [];
  @observable fileStore;
  @observable tipoContratoSelecionado;
  @observable tipoProcesso = '';
  @observable idContrato;
  @observable anos = [];
  @observable enableReqMod = false;
  @observable itensLicitante = [];
  @observable tresCasasDecimais = false;
  @observable entidadesFiltradas;
  @observable modalidades = [];
  @observable lote = {};
  @observable currentStep = 0;
  @observable fontesRecursos = [];
  @observable loadingTabItens = false;
  @observable loadingTabDadosBasicos = false;
  @observable valoresDisponiveis;
  @observable itensDisponiveis = [];
  @observable valorContratado;
  @observable vencedorContratado;
  @observable fundamentacoesLegais = [];
  @observable quantidadeAditivos;
  @observable pessoasResponsaveis;

  autoridadeContratanteStore;

  camposPorFormaContrato = {
    CONTRATO: [
      'numero',
      'anoContrato',
      'dataPublicacao',
      'permiteAditivo',
      'numeroDoe',
      'modalidade',
      'cnpjFornecedorContratado',
      'nomeFornecedorContratado',
      'dataVigenciaInicial',
      'dataVigenciaFinal',
      'valorGlobal',
      'objeto',
      'garantiaContratual',
      'gestor',
      'gestorSubstituto',
      'fiscal',
      'fiscalSubstituto',
      'numeroContratoSei',
      'fontesDeRecurso',
    ],
    EQUIVALENTE_EMPENHO: [
      'numero',
      'valorGlobal',
      'modalidade',
      'dataEmpenho',
      'nomeFornecedorContratado',
      'cnpjFornecedorContratado',
      'objeto',
      'garantiaContratual',
      'numeroContratoSei',
      'gestor',
      'gestorSubstituto',
      'fiscal',
      'fiscalSubstituto',
      'tipoEmpenho',
      'fontesDeRecurso',
      'garantiaContratual',
    ],
    EQUIVALENTE_SERVICO: [
      'numero',
      'valorGlobal',
      'modalidade',
      'nomeFornecedorContratado',
      'cnpjFornecedorContratado',
      'objeto',
      'garantiaContratual',
      'numeroContratoSei',
      'gestor',
      'gestorSubstituto',
      'fiscal',
      'fiscalSubstituto',
      'fontesDeRecurso',
      'dataPublicacao',
      'garantiaContratual',
    ],
    CARTA_CONTRATO: [
      'numero',
      'valorGlobal',
      'cnpjFornecedorContratado',
      'nomeFornecedorContratado',
      'numeroContratoSei',
      'objeto',
      'dataVigenciaInicial',
      'dataVigenciaFinal',
      'gestor',
      'gestorSubstituto',
      'fiscal',
      'fiscalSubstituto',
      'garantiaContratual',
      'fontesDeRecurso',
    ],
  };

  constructor() {
    super(ContratoService, Contrato);

    this.entidadeStore = new AsyncDropDownStore(EntidadeExterna, EntidadeExternaService, 'nomeEntidadeExterna', 'id');
    this.autoridadeContratanteStore = new AsyncDropDownStore(Usuario, UsuarioService, 'nome', 'id', {
      sort: {
        by: 'nome',
        order: 'asc',
      },
      andParameters: [{ field: 'entidadesTituladas', operator: 'INCLUDES', value: AppStore.getContextEntity()?.id }],
    });

    this.fornecedoresIndexStore = new LicitanteIndexStore();
    this.fileStore = new MultipleFileUploaderStore(
      [],
      (file) => ContratoService.upload(file),
      (fileDTO) => ContratoService.download(fileDTO),
      (idArquivo) => this.removerArquivoContrato(idArquivo),
      (idArquivo, arquivoContratoDTO) => this.atualizarArquivoContrato(idArquivo, arquivoContratoDTO)
    );

    this.getQuantidadeAditivosContrato = this.getQuantidadeAditivosContrato.bind(this);
    this.removerArquivoContrato = this.removerArquivoContrato.bind(this);
    this.atualizarArquivoContrato = this.atualizarArquivoContrato.bind(this);
    this.carregarProcesso = this.carregarProcesso.bind(this);
    this.checkDataCadastro = this.checkDataCadastro.bind(this);
    this.carregarTiposArquivo = this.carregarTiposArquivo.bind(this);
    this.updateGarantia = this.updateGarantia.bind(this);
    this.getEntidadesFiltradas = this.getEntidadesFiltradas.bind(this);
    this.setPreenchido = this.setPreenchido.bind(this);
    this.setMarcaModelo = this.setMarcaModelo.bind(this);
    this.setQuantidade = this.setQuantidade.bind(this);
    this.setDescricaoComplementar = this.setDescricaoComplementar.bind(this);
    this.setUnidadeMedida = this.setUnidadeMedida.bind(this);
    this.setValor = this.setValor.bind(this);
    this.handleSelectItens = this.handleSelectItens.bind(this);
    this.valueData = this.valueData.bind(this);
    makeObservable(this);
  }

  @action
  getQuantidadeAditivosContrato(idContrato) {
    ContratoService.getQuantidadeAditivosContrato(idContrato)
      .then((response) =>
        runInAction(() => {
          this.quantidadeAditivos = response.data;
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      );
  }

  @override
  initialize(id, defaultValues = {}, callback) {
    if (!id) {
      this.object = Object.assign({}, defaultValues);
      callback && callback();
    } else {
      this.initializeWithAssociatedProcess(id, defaultValues, callback);
    }
  }

  @action
  initializeWithAssociatedProcess(id, defaultValues = {}, callback) {
    if (!id) {
      this.object = Object.assign({}, defaultValues);
      callback && callback();
    } else {
      this.loading = true;
      this.service
        .getByIdWithAssociatedProcess(id)
        .then((response) =>
          runInAction(() => {
            this.object = response.data;
            callback && callback();
          })
        )
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        )
        .finally(() =>
          runInAction(() => {
            this.loading = false;
          })
        );
    }
  }

  @action
  initializeFiles(idContrato, callback) {
    this.idContrato = idContrato;
    if (idContrato) {
      ContratoService.recuperarArquivos(idContrato)
        .then((response) => {
          const arquivosRecuperados = response.data;
          this.fileStore.initialize(arquivosRecuperados);
          this.arquivoContratoList = arquivosRecuperados;
          callback && callback();
        })
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        )
        .finally(() =>
          runInAction(() => {
            this.loadingFiles = false;
            callback && callback();
          })
        );
    }
  }

  filtraFundamentacaoLegal(lei, processo) {
    return {
      page: { index: 1, size: 100 },
      sort: {
        by: 'fundamentacao',
        order: 'asc',
      },
      andParameters: [
        { field: 'ativo', operator: SearchOperators.EQUAL_TO.value, value: true },
        { field: 'tipoDispensa', operator: SearchOperators.EQUAL_TO.value, value: processo === 'D' ? true : false },
        { field: 'legislacao', operator: SearchOperators.EQUAL_TO.value, value: lei },
      ],
    };
  }

  @action
  carregarFundamentacaoLegal(callback) {
    const legislacao = this.object.lei ?? 'LEI_N_14133';
    const processo = this.object.tipo ?? 'D';
    const fundamentacoesList = this.filtraFundamentacaoLegal(legislacao, processo);

    if (['D', 'I'].includes(this.object.tipo)) {
      this.loading = true;
      FundamentacaoLegalService.advancedSearch(fundamentacoesList)
        .then((response) =>
          runInAction(() => {
            this.fundamentacoesLegais = response.data.items;
            callback && callback();
          })
        )
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        )
        .finally(() =>
          runInAction(() => {
            this.loading = false;
          })
        );
    }
  }

  @action
  initializeTabDadosBasicos() {
    this.loadingTabDadosBasicos = true;
    Promise.all([FonteRecursoService.getAll(), this.service.getAnosContrato()])
      .then((response) =>
        runInAction(() => {
          this.fontesRecursos = response[0].data;
          this.anos = response[1].data.map((ano) => ({ text: String(ano), value: ano }));
        })
      )
      .catch((error) => showErrorNotification(error))
      .finally(() => (this.loadingTabDadosBasicos = false));
  }

  @action
  setContrato(contrato, callback) {
    this.object = contrato;
    callback && callback();
  }

  @action
  updateGarantias(e) {
    this.object.garantias = e.value?.map((g) => g.code);
  }

  filterLegislacao(data) {
    return data.filter((item) => item.legislacao.includes('LEI_N_14133'));
  }

  filtraTipoProcesso(tipoProcesso) {
    return ['L', 'I', 'D'].includes(tipoProcesso.value);
  }

  getEmpenhos() {
    return this.object?.empenhos ?? [];
  }

  getOptionsArquivosContrato() {
    const { processoExterno, formaContrato, tipo, garantiaContratual, entidade } = this.object;
    let legislacao, orgaosParticipantes;
    const isProcessoExternoCarona = tipo === 'C' && this.object.processo?.entidadeOrigemExterna;

    if (tipo === 'C') {
      legislacao = isProcessoExternoCarona ? this.object?.carona?.lei : this.object?.processo?.licitacao?.lei;
    } else {
      legislacao = processoExterno ? this.object?.lei : this.object?.processo?.lei;
      orgaosParticipantes = this.object?.processo?.orgaosParticipantes;
    }

    const tipoOrigemContrato =
      processoExterno || isProcessoExternoCarona
        ? 'CONTRATO_EXTERNO'
        : orgaosParticipantes?.length && orgaosParticipantes?.some((entidadeAtual) => entidadeAtual.id == entidade.id)
        ? 'CONTRATO_INTERNO'
        : 'CONTRATO_PROPRIO';

    if (tipo === 'CR') {
      return {
        tipoOrigemContrato: tipoOrigemContrato,
        formaContrato,
        processo: 'CREDENCIAMENTO',
        ...(tipoOrigemContrato === 'CONTRATO_PROPRIO' && { lei: legislacao ?? 'LEI_N_8666' }),
        garantiaContratual: garantiaContratual ? 'GARANTIA' : null,
      };
    } else {
      return {
        tipoOrigemContrato: tipoOrigemContrato,
        formaContrato,
        lei: legislacao ?? 'LEI_N_8666',
        garantiaContratual: garantiaContratual ? 'GARANTIA' : null,
      };
    }
  }

  @action
  carregarTiposArquivo(callback) {
    this.loading = true;
    const filtros = this.getOptionsArquivosContrato();
    const filtrosList = Object.keys(filtros).reduce((acc, key) => {
      const valor = filtros[key];
      if (valor !== null) {
        acc.push(valor);
      }
      return acc;
    }, []);

    const request = {
      tipoProcesso: 'CONTRATO',
      filtros: filtrosList,
    };
    ObrigatoriedadeArquivoService.getArquivosObrigatorios(request)
      .then((response) =>
        runInAction(() => {
          const arquivosObrigatorios = response?.data ?? [];
          this.fileStore.tipoArquivoEnum = arquivosObrigatorios.map((arqObg) => {
            const arq = DadosEstaticosService.getTipoArquivoContrato().find((arq) => arq.value === arqObg.arquivoEnum);
            return { ...arqObg, text: arqObg.obrigatorio ? '* ' + arqObg.arquivo : arqObg.arquivo, value: arq.value };
          });
          callback && callback();
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      )
      .finally(() =>
        runInAction(() => {
          this.loading = false;
        })
      );
  }

  rulesDefinition() {
    let rules = {};
    if (this.currentStep == 2) {
      rules = {
        numero: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
        valorGlobal: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
        objeto: [{ rule: 'isMaxLength', maxLength: 4000, message: 'Por favor, diminua o tamanho do campo' }],
        idAutoridadeContratante: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
        justificativa: [{ rule: 'isMaxLength', maxLength: 4000, message: 'Por favor, diminua o tamanho do campo' }],
        gestor: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
        fiscal: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
        fontesDeRecurso: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
        numeroContratoSei: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      };
    }

    if (this.object.formaContrato === 'EQUIVALENTE_EMPENHO' && this.currentStep == 2) {
      rules = this.mergeRules(rules, {
        objeto: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
        dataEmpenho: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
        tipoEmpenho: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
        garantiaContratual: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      });
      if (this.object.garantiaContratual) {
        this.mergeRules(rules, {
          garantias: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
        });
      }
    }

    if (this.object.formaContrato === 'EQUIVALENTE_SERVICO' && this.currentStep == 2) {
      rules = this.mergeRules(rules, {
        garantiaContratual: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      });
      if (this.object.garantiaContratual) {
        this.mergeRules(rules, {
          garantias: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
        });
      }
    }

    if (this.object.formaContrato === 'CONTRATO' && this.currentStep == 2) {
      rules = this.mergeRules(rules, {
        garantiaContratual: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
        permiteAditivo: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
        dataVigenciaInicial: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
        dataVigenciaFinal: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
        dataPublicacao: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
        anoContrato: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      });
      if (this.object.garantiaContratual) {
        this.mergeRules(rules, {
          garantias: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
        });
      }
      if (this.isLei14133()) {
        this.mergeRules(rules, {
          numeroDoe: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
        });
      }
    }

    if (this.object.formaContrato === 'CARTA_CONTRATO' && this.currentStep == 2) {
      rules = this.mergeRules(rules, {
        dataVigenciaInicial: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
        dataVigenciaFinal: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
        garantiaContratual: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      });
      if (this.object.garantiaContratual) {
        this.mergeRules(rules, {
          garantias: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
        });
      }
    }

    if (this.object.processoExterno) {
      let rulesExternalProcess = {
        tipo: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
        entidadeExterna: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
        valorProcessoEntidadeExterna: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
        numeroProcessoAdmSei: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
        anoProcesso: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
        lei: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      };

      if (['D', 'I'].includes(this.object.tipo)) {
        rulesExternalProcess.fundamentacaoLegalEntidade = [
          { rule: 'required', message: 'Por favor, preencha o campo' },
          { rule: 'isMaxLength', maxLength: 4000, message: 'Por favor, diminua o tamanho do campo' },
        ];
      }

      if (this.object.lei === 'OUTRA') {
        rulesExternalProcess.legislacaoOutros = [{ rule: 'required', message: 'Por favor, preencha o campo' }];
      }

      if (this.object?.tipo === 'L')
        rulesExternalProcess.numeroLicitacao = [{ rule: 'required', message: 'Por favor, preencha o campo' }];

      if (this.object?.tipo === 'D')
        rulesExternalProcess.numeroDispensa = [{ rule: 'required', message: 'Por favor, preencha o campo' }];

      if (this.object?.tipo === 'I')
        rulesExternalProcess.numeroInexigibilidade = [{ rule: 'required', message: 'Por favor, preencha o campo' }];

      this.mergeRules(rules, rulesExternalProcess);
    }
    return rules;
  }

  @action
  resetFieldsExternalProcess() {
    this.object.numeroProcessoAdmSei = undefined;
    this.object.numeroLicitacao = undefined;
    this.object.numeroDispensa = undefined;
    this.object.numeroInexigibilidade = undefined;
    this.object.anoProcesso = undefined;
  }

  renderFormField(field) {
    if (this.object.formaContrato) {
      return this.camposPorFormaContrato[this.object.formaContrato].includes(field);
    } else {
      return true;
    }
  }

  @action
  setArquivoContratoList(arquivoContratoList) {
    this.arquivoContratoList = arquivoContratoList;
  }

  @action
  initializeExternalProcess() {
    this.object.processoExterno = true;
    this.object.contratoLicitante = observable({ itens: [], licitante: undefined });
    this.object.dataCadastro = moment().format(DATE_PARSE_FORMAT_WITH_HOURS_AND_SECONDS);
    this.object.contratoAntigo = false;
    this.object.status = 'PUBLICADA';
    const entidade = AppStore.getContextEntity();
    if (entidade) {
      this.object.entidade = entidade;
    }
  }

  @action
  clearOldValuesSelectedProcess() {
    this.object.idCarona = undefined;
    this.object.processo = undefined;
    this.object.idDispensa = undefined;
    this.object.processo = undefined;
    this.object.idLicitacao = undefined;
    this.object.processo = undefined;
    this.object.idInexigibilidade = undefined;
    this.object.processo = undefined;
    this.object.idCredenciamento = undefined;
    this.object.processo = undefined;
    this.object.lei = undefined;
  }

  @action
  carregarProcesso(processo, successCallback, warningCallback) {
    const { carona, dispensa, licitacao, inexigibilidade, credenciamento, tipoProcesso } = processo;
    this.object.tipo = processo.tipoProcesso;
    this.object.contratoLicitante = observable({ itens: [], licitante: undefined });
    this.object.formaContrato = 'CONTRATO';
    this.object.garantias = [];
    this.clearOldValuesSelectedProcess();
    if (tipoProcesso === 'C') {
      this.object.idCarona = carona.id;
      this.object.processo = carona;
      this.object.lei = carona.lei;
    } else if (tipoProcesso === 'D') {
      this.object.idDispensa = dispensa.id;
      this.object.processo = dispensa;
      this.object.lei = dispensa.lei;
    } else if (tipoProcesso === 'L') {
      this.object.idLicitacao = licitacao.id;
      this.object.processo = licitacao;
      this.object.lei = licitacao.lei;
    } else if (tipoProcesso === 'I') {
      this.object.idInexigibilidade = inexigibilidade.id;
      this.object.processo = inexigibilidade;
      this.object.lei = inexigibilidade.lei;
    } else if (tipoProcesso === 'CR') {
      this.object.idCredenciamento = credenciamento.id;
      this.object.processo = credenciamento;
      this.object.lei = credenciamento.lei;
    }
    this.object.contratoAntigo = false;
    this.object.processoExterno = false;

    const entidade = AppStore.getContextEntity();
    if (entidade) {
      this.object.entidade = entidade;
    }

    this.carregarDadosProcesso(successCallback, warningCallback);
  }

  @action
  carregarDadosProcesso(successCallback, warningCallback) {
    if (!this.object.processoExterno) {
      this.loading = true;
      const requisicaoProcesso = [];
      const { idLicitacao, idCarona, idDispensa, idInexigibilidade, idCredenciamento } = this.object;
      if (idLicitacao) requisicaoProcesso.push(LicitacaoService.getById(idLicitacao));
      else if (idDispensa) requisicaoProcesso.push(DispensaService.getById(idDispensa));
      else if (idCarona) requisicaoProcesso.push(CaronaService.getById(idCarona));
      else if (idInexigibilidade) requisicaoProcesso.push(InexigibilidadeService.getById(idInexigibilidade));
      else if (idCredenciamento) {
        requisicaoProcesso.push(CredenciamentoService.getById(idCredenciamento));
        requisicaoProcesso.push(CredenciadoItemService.getAllByCredenciamento(idCredenciamento));
      }
      Promise.all(requisicaoProcesso)
        ?.then((responses) =>
          runInAction(() => {
            this.object.objeto = responses[0].data?.objeto;
            this.object.status = responses[0].data?.status;
            this.object.tresCasasDecimaisTermo = responses[0].data?.termoReferencia?.tresCasasDecimais ?? false;

            if (idLicitacao) {
              this.object.numeroContratoSei = responses[0].data?.numeroProcessoAdm;
            } else if (idDispensa)
              this.object.numeroContratoSei = responses[0].data?.numeroProcesso
                ? responses[0].data?.numeroProcesso
                : responses[0].data?.numeroProcessoSEI;
            else if (idCarona)
              this.object.numeroContratoSei = responses[0].data?.numeroProcessoGerenciadorAta
                ? responses[0].data?.numeroProcessoGerenciadorAta
                : responses[0].data?.numeroProcessoAdministrativo;
            else if (idInexigibilidade)
              this.object.numeroContratoSei = responses[0].data?.numeroProcesso
                ? responses[0].data?.numeroProcesso
                : responses[0].data?.numeroSei;
            else if (idCredenciamento) {
              this.object.numeroContratoSei = responses[0].data?.numeroProcesso;
              this.object.processo.credenciados = responses[1].data;
            }

            this.carregarItens(successCallback, warningCallback);
          })
        )
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        )
        .finally(() =>
          runInAction(() => {
            this.loading = false;
          })
        );
    }
  }

  getLicitacao(id, callback) {
    let licitacao;
    LicitacaoService.getById(id)
      .then((response) => {
        licitacao = response.data;
        callback({ licitacao: licitacao, tipoProcesso: 'L' });
      })
      .catch((error) => showErrorNotification(error));
  }

  @action
  itensGroupByVencedor(vencedores) {
    const vencedoresDistinct = [];
    vencedores?.map((v) => {
      if (vencedoresDistinct.find((vencedor) => vencedor.licitante.id == v.licitante.id)) {
        const vencedorPersitedIndex = vencedoresDistinct.findIndex(
          (vencedor) => vencedor.licitante.id == v.licitante.id
        );

        const vencedorSelected = vencedoresDistinct[vencedorPersitedIndex];
        vencedorSelected.itens.push({ ...v });
        vencedorSelected.quantidadeDisponivel += v.quantidadeDisponivel;
        vencedoresDistinct[vencedorPersitedIndex] = vencedorSelected;
      } else {
        const vencedor = { licitante: v.licitante, itens: [{ ...v }], quantidadeDisponivel: v.quantidadeDisponivel };
        vencedoresDistinct.push(vencedor);
      }
    });
    return vencedoresDistinct;
  }

  @action
  setItensLicitante(data) {
    if (data) {
      this.object.tresCasasDecimaisTermo = data.termoReferencia?.tresCasasDecimais;
      this.vencedores = this.itensGroupByVencedor(data);
    }
  }

  @action
  carregarItens = (successCallback, warningCallback) => {
    this.loading = true;
    const { idLicitacao, idCarona, idDispensa, idInexigibilidade, tipo, idCredenciamento } = this.object;
    const requestId = idLicitacao || idDispensa || idCarona || idInexigibilidade || idCredenciamento;

    ContratoService.getItensDisponiveis(requestId, tipo)
      .then((response) => {
        runInAction(() => {
          if (this.object.processo?.lei === 'LEI_N_8666') {
            this.setItensLicitante(this.object.processo[this.getFornecedorField()]);
            this.valoresDisponiveis = response.data;
            this.vencedorContratado = this.object.contratoLicitante?.licitante;
            this.valorContratado = this.object.contratoLicitante?.valor;
            this.warning(response.data, warningCallback, successCallback);
          } else {
            const vencedoresDisponiveis = response.data;
            const vencedoresProcesso = this.object.processo?.[this.getFornecedorField()];

            if (this.object.tipo === 'CR') {
              this.handleVencedoresCredenciamento(vencedoresDisponiveis, vencedoresProcesso);
            } else {
              this.handleVencedoresLeiNova(vencedoresDisponiveis, vencedoresProcesso);
            }

            if (
              this.object.processo?.termoReferencia ||
              (this.object.tipo === 'C' && this.object.lei !== 'LEI_N_8666')
            ) {
              this.warning(
                this.object.tipo === 'CR' ? vencedoresDisponiveis : vencedoresProcesso,
                warningCallback,
                successCallback
              );
            } else {
              successCallback && successCallback();
            }
          }
        });
      })
      .catch((error) => {
        runInAction(() => {
          showErrorNotification(error);
        });
      })
      .finally(() => {
        runInAction(() => {
          this.loading = false;
        });
      });
  };

  @action
  handleVencedoresLeiNova = (vencedoresDisponiveis, vencedoresProcesso) => {
    vencedoresProcesso?.map((vencedorProcesso) => {
      const vencedorDisponivel = vencedoresDisponiveis.find(
        ({ vencedor }) =>
          vencedor.licitante.id === vencedorProcesso.licitante.id &&
          vencedor.itemLote.id === vencedorProcesso.itemLote.id
      );

      const vencedorContratado = this.object.contratoLicitante?.itens?.find(
        (item) =>
          item[this.getVencedorField()].licitante.id == vencedorProcesso.licitante.id &&
          item[this.getVencedorField()].itemLote.id == vencedorProcesso.itemLote.id
      );

      let quantidadeDisponivel = 0;
      if (vencedorContratado) {
        quantidadeDisponivel = vencedorDisponivel
          ? vencedorDisponivel.quantidadeDisponivel + vencedorContratado.quantidade
          : vencedorProcesso.quantidade;
      } else {
        quantidadeDisponivel = vencedorDisponivel
          ? vencedorDisponivel.quantidadeDisponivel
          : vencedorProcesso.quantidade;
      }

      return (vencedorProcesso.quantidadeDisponivel = quantidadeDisponivel);
    });

    this.setItensLicitante(vencedoresProcesso);
  };

  @action
  handleVencedoresCredenciamento = (vencedoresDisponiveis, vencedoresProcesso) => {
    const vencedores = [];
    vencedoresProcesso?.forEach((vencedorProcesso) => {
      const vencedorDisponivel = vencedoresDisponiveis.find(
        ({ vencedor }) =>
          vencedor.licitante.id === vencedorProcesso.licitante.id &&
          vencedor.itemLote.id === vencedorProcesso.itemLote.id &&
          vencedor.id === vencedorProcesso.id
      );

      const vencedorContratado = this.object.contratoLicitante?.itens?.find(
        (item) =>
          item[this.getVencedorField()].licitante.id == vencedorProcesso.licitante.id &&
          item[this.getVencedorField()].itemLote.id == vencedorProcesso.itemLote.id &&
          item[this.getVencedorField()].id === vencedorProcesso.id
      );

      let quantidadeDisponivel = 0;
      if (vencedorContratado) {
        quantidadeDisponivel = vencedorDisponivel
          ? vencedorDisponivel.quantidadeDisponivel + vencedorContratado.quantidade
          : vencedorProcesso.quantidade;
        const result = { ...vencedorProcesso, quantidadeDisponivel };
        vencedores.push(result);
      } else if (vencedorDisponivel) {
        quantidadeDisponivel = vencedorDisponivel
          ? vencedorDisponivel.quantidadeDisponivel
          : vencedorProcesso.quantidade;
        const result = { ...vencedorProcesso, quantidadeDisponivel };
        vencedores.push(result);
      }
    });

    this.setItensLicitante(vencedores);
  };

  getQuantidadeItemAssociado(item) {
    const itemAssociado = this.object?.contratoLicitante?.itens?.find(
      (itemContrato) =>
        itemContrato[this.getVencedorField()].licitante.id == item.licitante.id &&
        itemContrato[this.getVencedorField()].itemLote.id == item.itemLote.id
    );
    return itemAssociado ? itemAssociado.quantidade : item.quantidade;
  }

  warning(data, warningCallback, successCallback) {
    const isLeiAntiga = this.object.processo?.lei === 'LEI_N_8666';
    const shouldWarnSucess = isLeiAntiga
      ? data.some((e) => e.valorDisponivel !== 0)
      : data.some((e) => e.quantidadeDisponivel !== 0);

    if (shouldWarnSucess) {
      successCallback && successCallback();
    } else {
      warningCallback && warningCallback();
    }
  }

  @override
  save(callback, type = 'edit') {
    this.loading = true;
    const saveObject = this.getObjectToSave(type);
    const filtros = this.getOptionsArquivosContrato();

    if (Object.keys(saveObject).length === 0) {
      showNotification('warn', null, 'Nenhuma alteração realizada.');
    } else {
      if (!this.object.id) this.object.dataCadastro = moment().format(DATE_PARSE_FORMAT_WITH_HOURS_AND_SECONDS);
      const contrato = omit(this.object, ['licitacao', 'dispensa', 'carona', 'inexigibilidade', 'credenciamento']);
      const contratoDTO = {
        id: this.object.id,
        contrato: {
          ...contrato,
          lei: filtros.lei,
          contratoLicitante: {
            ...contrato.contratoLicitante,
            itens: contrato.contratoLicitante?.itens?.map((i) => {
              if (i.itemGerado) {
                i.id = undefined;
              }
              return i;
            }),
          },
        },
        arquivosContrato: this.arquivoContratoList,
        filtros: filtros,
      };
      this.service
        .createContrato(contratoDTO)
        .then(() =>
          runInAction(() => {
            callback && callback();
            showNotification('success', null, 'Registro salvo com sucesso!');
          })
        )
        .catch((error) => showErrorNotification(error))
        .finally(() =>
          runInAction(() => {
            this.loading = false;
          })
        );
    }
  }

  @action
  salvarRescisaoContrato(contrato, callback) {
    const contratoDTO = {
      id: this.object.id,
      contrato: {
        ...contrato,
        permiteAditivo: false,
        contratoLicitante: {
          ...contrato.contratoLicitante,
        },
      },
      arquivosContrato: this.arquivoContratoList,
    };
    this.loading = true;
    this.service
      .salvarRescisaoContrato(contratoDTO)
      .then(() =>
        runInAction(() => {
          callback && callback();
          showNotification('success', null, 'Registro salvo com sucesso!');
        })
      )
      .catch((error) => showErrorNotification(error))
      .finally(() =>
        runInAction(() => {
          this.loading = false;
        })
      );
  }

  removerArquivoContrato(idArquivo) {
    return ContratoService.removerArquivo(this.idContrato, idArquivo);
  }

  atualizarArquivoContrato(idArquivo, arquivoContratoDTO) {
    return ContratoService.atualizarArquivo(this.idContrato, idArquivo, arquivoContratoDTO);
  }

  labelNumero() {
    if (this.object?.formaContrato === 'CONTRATO') return 'Número do Contrato';
    if (this.object?.formaContrato === 'EQUIVALENTE_EMPENHO') return 'Número do Empenho';
    if (this.object?.formaContrato === 'EQUIVALENTE_SERVICO') return 'Número da Ordem de Serviço/Entrega';
    if (this.object?.formaContrato === 'CARTA_CONTRATO') return 'Número da Carta-contrato';
    return 'Número';
  }

  labelObjeto() {
    return this.object?.formaContrato === 'EQUIVALENTE_EMPENHO' ? 'Histórico do Empenho' : 'Objeto';
  }

  labelFornecedor() {
    if (this.object?.formaContrato === 'CONTRATO' || this.object?.formaContrato === 'CARTA_CONTRATO')
      return 'Contratado(a)';
    if (this.object?.formaContrato === 'EQUIVALENTE_EMPENHO') return 'Credor(a)';
    if (this.object?.formaContrato === 'EQUIVALENTE_SERVICO') return 'Prestador/Fornecedor(a)';
    return 'Fornecedor(a)';
  }

  labelValor() {
    if (this.object?.formaContrato === 'CONTRATO') return 'Valor do Contrato';
    if (this.object?.formaContrato === 'EQUIVALENTE_EMPENHO') return 'Valor do Empenho';
    if (this.object?.formaContrato === 'EQUIVALENTE_SERVICO') return 'Valor da Ordem de Serviço/Entrega ';
    if (this.object?.formaContrato === 'CARTA_CONTRATO') return 'Valor da Carta-contrato';
    return 'Valor';
  }

  labelData() {
    if (this.object?.formaContrato === 'CONTRATO')
      return this.isLei14133() ? 'Data da divulgação no PNCP' : 'Data da Publicação';
    if (this.object?.formaContrato === 'EQUIVALENTE_EMPENHO') return 'Data do Empenho';
    if (this.object?.formaContrato === 'EQUIVALENTE_SERVICO') return 'Data da Ordem de Serviço/Entrega';
    return 'Data';
  }

  labelNumeroDoe() {
    return this.isLei14133() ? 'ID do Contrato no PNCP' : 'Número do DOE';
  }

  placeholderNumeroDoe() {
    return this.isLei14133() ? 'Informe o id do contrato no PNCP' : 'Informe o número do DOE';
  }

  keyFilterNumeroDoe() {
    return this.isLei14133() ? new RegExp('[^a-zA-Z]') : '';
  }

  isLei14133() {
    const processo = this.getProcesso();
    return this.object?.tipo === 'C'
      ? processo?.licitacao?.lei === 'LEI_N_14133' || processo?.lei === 'LEI_N_14133'
      : processo?.lei === 'LEI_N_14133';
  }

  valueData() {
    if (['CONTRATO', 'EQUIVALENTE_SERVICO'].includes(this.object?.formaContrato)) return this.object.dataPublicacao;
    if (this.object?.formaContrato === 'EQUIVALENTE_EMPENHO') return this.object.dataEmpenho;
    return null;
  }

  checkDataCadastro(dataCadastro) {
    let data = this.object?.dataCadastro;
    if (dataCadastro) data = dataCadastro;
    if (data) {
      const dataCadastro = moment(data);
      const current = moment();
      const diff = moment.duration(current.diff(dataCadastro));

      this.enableReqMod = diff.asHours() > 24;
    }
  }

  limparRescisaoContratual() {
    this.object = Object.assign({}, {});
    this.setArquivoContratoList([]);
  }

  calculaValorContrato(itens = this.object.contratoLicitante?.itens) {
    return itens?.map((item) => item?.valor * item?.quantidade).reduce((a, b) => a + b, 0);
  }

  @action
  clearCheckboxGarantia() {
    this.object.garantias = [];
  }

  @action
  updateGarantia() {
    this.object.garantiaContratual = !!this.object.garantias.length;
  }

  @computed
  get selectedLicitante() {
    return this.object?.contratoLicitante?.licitante;
  }

  getFornecedorField(tipoProcesso = this.object.tipo) {
    if (tipoProcesso === 'L') return 'vencedores';
    if (tipoProcesso === 'C') return 'detentores';
    if (tipoProcesso === 'D') return 'fornecedores';
    if (tipoProcesso === 'I') return 'fornecedores';
    if (tipoProcesso === 'CR') return 'credenciados';
  }

  getProcesso() {
    return this.object.processo;
  }

  @action
  selectLicitante(licitante) {
    const processo = this.getProcesso();
    if (processo?.lei === 'LEI_N_8666') {
      const valorVencedor = this.getValorLicitante();
      this.object.contratoLicitante.licitante = licitante;
      this.setValorLicitante(valorVencedor);
    } else {
      if (this.object.contratoLicitante?.licitante && licitante.id !== this.object.contratoLicitante?.licitante.id) {
        this.object.contratoLicitante.licitante = licitante;
        this.object.contratoLicitante.itens = [];
      } else {
        this.object.contratoLicitante.licitante = licitante;
      }
    }
  }

  getValorLicitante() {
    return this.object.contratoLicitante.licitante?.valor ?? 0;
  }

  getValorMaximoByLicitante() {
    const licitante = this.selectedLicitante;
    return this.vencedorContratado?.id === licitante?.id
      ? this.getValorDisponivelByLicitante(licitante) + this.valorContratado
      : this.getValorDisponivelByLicitante(licitante);
  }

  checkValorMaximoContratado() {
    const valorMaximo = this.selectedLicitante ? this.getValorMaximoByLicitante() : null;
    const valorFinal = valorMaximo ?? this.object?.processo?.valor;
    return valorFinal > 0;
  }

  getValorDisponivelByLicitante(licitante) {
    return this.valoresDisponiveis?.find((v) => v.vencedor.licitante.id === licitante?.id)?.valorDisponivel;
  }

  getQuantidadeDisponivel(itemValue) {
    const itemVencedor = this.vencedores
      .find((v) =>
        itemValue[this.getVencedorField()]
          ? v.licitante.id == itemValue[this.getVencedorField()].licitante.id
          : v.licitante.id == itemValue.licitante.id
      )
      ?.itens?.find((item) =>
        itemValue[this.getVencedorField()]
          ? item.itemLote.id == itemValue[this.getVencedorField()].itemLote.id
          : item.itemLote.id == itemValue.itemLote.id
      );
    return itemVencedor.quantidadeDisponivel;
  }

  getVencedorByLicitante(licitante) {
    if (licitante) {
      const processo = this.getProcesso();
      return processo[this.getFornecedorField()]?.find((v) => v.licitante.id === licitante?.id);
    }
  }

  getItensDisponiveis() {
    const currentLicitante = this.object?.contratoLicitante?.licitante;
    const itens = this.vencedores?.find((v) => v.licitante.id == currentLicitante?.id)?.itens ?? [];
    return itens.filter((item) => !(item.quantidadeDisponivel === 0 || item.valor === 0));
  }

  getVencedorField() {
    if (this.object.tipo == 'L') return 'vencedorLicitacao';
    if (this.object.tipo == 'C') return 'vencedorCarona';
    if (this.object.tipo == 'D') return 'vencedorDispensa';
    if (this.object.tipo == 'I') return 'vencedorInexigibilidade';
    if (this.object.tipo == 'CR') return 'credenciadoItem';
  }

  getValorUnitarioMaximo(item) {
    const vencedor = this.getVencedorField();
    const itemVencedor = this.vencedores
      .find((v) =>
        item[vencedor] ? v.licitante.id === item[vencedor].licitante.id : v.licitante.id === item.licitante.id
      )
      ?.itens?.find((vencedorItem) =>
        item[vencedor]
          ? vencedorItem.itemLote.id === item[vencedor].itemLote.id
          : vencedorItem.itemLote.id === item.itemLote.id
      );

    return itemVencedor?.valorUnitario;
  }

  @action
  addItem(item) {
    if (!item.itemGerado) {
      const itemAdicionado = this.object.contratoLicitante?.itens?.find(
        (i) => i[this.getVencedorField()].id === item.id
      );
      const quantidade = item.quantidadeDisponivel ?? 0;
      if (!itemAdicionado) {
        const newItem = {
          preenchido: false,
          quantidade: quantidade,
          valor: item.valorUnitario,
        };
        newItem[this.getVencedorField()] = { ...item };
        this.object.contratoLicitante.itens.push(newItem);
        this.object.valorGlobal = this.calculaValorContrato();
      }
    } else {
      const itemAdicionado = this.object.contratoLicitante?.itens?.find((i) => i.id === item.id);
      if (!itemAdicionado) {
        this.object.contratoLicitante.itens.push(item);
        this.object.valorGlobal = this.calculaValorContrato();
      } else {
        showNotification('info', null, 'Item já foi adicionado.');
      }
    }
  }

  @action
  removeItem(item) {
    if (!item.itemGerado) {
      const itemAdicionado = this.object.contratoLicitante?.itens?.find(
        (i) => i[this.getVencedorField()].id === item.id
      );
      if (itemAdicionado) {
        this.object.contratoLicitante.itens = this.object.contratoLicitante.itens.filter(
          (i) => i[this.getVencedorField()].id !== item.id
        );
        this.object.valorGlobal = this.calculaValorContrato();
      }
    } else {
      const itemAdicionado = this.object.contratoLicitante?.itens?.find((i) => i.id === item.id);
      if (itemAdicionado) {
        this.object.contratoLicitante.itens = this.object.contratoLicitante.itens.filter((i) => i.id !== item.id);
        this.object.valorGlobal = this.calculaValorContrato();
      }
    }
  }

  @action
  handleSelectItens(source, target) {
    target?.forEach((item) => this.addItem(item));
    source?.forEach((item) => item.id && this.removeItem(item));
  }

  criarItem(material) {
    const idGerado = CryptoJS.SHA1(`${material.codigo} ${material?.pdm?.nome} ${material.dataCadastro}`).toString(
      CryptoJS.enc.Base64
    );
    const item = {
      id: idGerado,
      itemGerado: true,
      materialDetalhamento: material,
      quantidade: 0,
      valor: 0,
      preenchido: false,
      descricaoComplementar: '',
      unidadeMedida: '',
    };
    this.addItem(item, true);
  }

  @action
  addtodos() {
    const itens = this.getItensDisponiveis();
    itens.forEach((i) => this.addItem(i));
  }

  calculaValorItem(valor, quantidade, desconto = 0) {
    let valorCalculado = quantidade * valor;
    if (desconto) {
      valorCalculado -= (valor * desconto) / 100;
    }
    return valorCalculado;
  }

  @action
  setValorLicitante(valor) {
    this.object.contratoLicitante.valor = valor ? parseFloat(valor) : 0;
    this.object.valorGlobal = valor;
  }

  @action
  setValor(item, valor) {
    if (!item.itemGerado) {
      const indexItem = this.object.contratoLicitante.itens.findIndex(
        (i) => i[this.getVencedorField()].id == item[this.getVencedorField()].id
      );
      this.object.contratoLicitante.itens[indexItem].valor = valor ?? 0;
    } else {
      const indexItem = this.object.contratoLicitante.itens.findIndex((i) => i.id == item.id);
      this.object.contratoLicitante.itens[indexItem].valor = valor ?? 0;
    }
    this.object.valorGlobal = this.calculaValorContrato();
  }

  @action
  setPreenchido(item, preenchido) {
    if (!item.itemGerado) {
      const indexItem = this.object.contratoLicitante.itens.findIndex(
        (i) => i[this.getVencedorField()].id == item[this.getVencedorField()].id
      );
      this.object.contratoLicitante.itens[indexItem].preenchido = preenchido;
    } else {
      const indexItem = this.object.contratoLicitante.itens.findIndex((i) => i.id == item.id);
      this.object.contratoLicitante.itens[indexItem].preenchido = preenchido;
    }
  }

  @action
  setMarcaModelo(item, marcaModelo) {
    this.object.contratoLicitante.itens = this.object.contratoLicitante.itens.map((i) => {
      if (i[this.getVencedorField()].id == item[this.getVencedorField()].id) {
        return { ...i, marcaModelo };
      } else {
        return i;
      }
    });
  }

  @action
  setQuantidade(item, quantidade) {
    if (!item.itemGerado) {
      const indexItem = this.object.contratoLicitante.itens.findIndex(
        (i) => i[this.getVencedorField()].id == item[this.getVencedorField()].id
      );
      this.object.contratoLicitante.itens[indexItem].quantidade = quantidade;
    } else {
      const indexItem = this.object.contratoLicitante.itens.findIndex((i) => i.id == item.id);
      this.object.contratoLicitante.itens[indexItem].quantidade = quantidade;
    }

    this.object.valorGlobal = this.calculaValorContrato();
  }

  @action
  setDescricaoComplementar(item, descricao, callback) {
    if (!item.itemGerado) {
      const indexItem = this.object.contratoLicitante.itens.findIndex(
        (i) => i[this.getVencedorField()].id == item[this.getVencedorField()].id
      );
      this.object.contratoLicitante.itens[indexItem].descricaoComplementar = descricao;
    } else {
      const indexItem = this.object.contratoLicitante.itens.findIndex((i) => i.id == item.id);
      this.object.contratoLicitante.itens[indexItem].descricaoComplementar = descricao;
    }
    callback && callback();
  }

  @action
  setUnidadeMedida(item, unidade, callback) {
    if (!item.itemGerado) {
      const indexItem = this.object.contratoLicitante.itens.findIndex(
        (i) => i[this.getVencedorField()].id == item[this.getVencedorField()].id
      );
      this.object.contratoLicitante.itens[indexItem].unidadeMedida = unidade;
    } else {
      const indexItem = this.object.contratoLicitante.itens.findIndex((i) => i.id == item.id);
      this.object.contratoLicitante.itens[indexItem].unidadeMedida = unidade;
    }
    callback && callback();
  }

  @computed.struct
  get decimalPlaces() {
    return this.object?.tresCasasDecimaisTermo ? 3 : 2;
  }

  @action
  getEntidadesFiltradas(event, callback) {
    const query = event.query.trim();

    const queryParams = {
      andParameters: [
        {
          id: '',
          field: 'nome',
          operator: 'CONTAINS',
          value: query,
          formatted: '',
        },
      ],
    };

    EntidadeService.advancedSearch(queryParams)
      .then((response) =>
        runInAction(() => {
          this.entidadesFiltradas = response.data?.items;
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      )
      .finally(() =>
        runInAction(() => {
          callback && callback();
        })
      );
  }

  @action
  carregaDecimalPlaces(callback) {
    if (!this.object.processoExterno) {
      let requisicaoProcesso;
      const { idLicitacao, idCarona, idDispensa, idInexigibilidade } = this.object;
      if (idLicitacao) requisicaoProcesso = LicitacaoService.getTresCasasDecimais(idLicitacao);
      else if (idDispensa) requisicaoProcesso = DispensaService.getTresCasasDecimais(idDispensa);
      else if (idCarona) requisicaoProcesso = CaronaService.getTresCasasDecimais(idCarona);
      else if (idInexigibilidade) requisicaoProcesso = InexigibilidadeService.getTresCasasDecimais(idInexigibilidade);

      requisicaoProcesso &&
        requisicaoProcesso
          .then((response) => callback && callback(response?.data ? 3 : 2))
          .catch((error) => console.error(error));
    }
  }

  getAdvancedSearchParamsAditivo() {
    return [
      {
        field: 'numero',
        label: 'Número',
        type: SearchTypes.TEXT,
      },
      {
        field: 'dataPublicacao',
        label: 'Data da Publicação',
        type: SearchTypes.DATE,
      },
    ];
  }

  @action
  validaArquivos(callback) {
    this.loading = true;
    const contratoDTO = {
      filtros: this.getOptionsArquivosContrato(),
      arquivosContrato: this.arquivoContratoList,
    };
    ContratoService.validaArquivos(contratoDTO)
      .then(() => runInAction(() => callback && callback()))
      .catch((error) => showErrorNotification(error))
      .finally(() =>
        runInAction(() => {
          this.loading = false;
        })
      );
  }

  @action
  getPessoasResponsaveis() {
    const entidade = AppStore.getContextEntity();
    entidade &&
      ContratoService.getPessoasResponsaveis(entidade?.id)
        .then((response) =>
          runInAction(() => {
            this.pessoasResponsaveis = response.data;
            if (
              this.object.idAutoridadeContratante &&
              this.object.nomeAutoridadeContratante &&
              !this.pessoasResponsaveis.some(
                (pessoa) =>
                  pessoa.id == this.object.idAutoridadeContratante &&
                  pessoa.nome == this.object.nomeAutoridadeContratante
              )
            ) {
              this.pessoasResponsaveis?.push({
                nome: this.object.nomeAutoridadeContratante,
                id: this.object.idAutoridadeContratante,
              });
            }
          })
        )
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        );
  }

  @action
  updateAutoridadeContratanteAttribute(attribute, event) {
    let value = event;
    if (event && event.value) {
      value = event.value;
    }
    if (event && event.target) {
      value = event.target.value;
    }
    this.object[attribute] = value;
    const nomePessoaResponsavel = this.pessoasResponsaveis.find((pessoa) => pessoa.id == value)?.nome;
    this.object['nomeAutoridadeContratante'] = nomePessoaResponsavel;
  }
}

export default ContratoFormStore;
