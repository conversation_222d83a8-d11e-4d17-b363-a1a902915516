import SearchTypes from 'fc/components/AdvancedSearch/SearchTypes';
import ContratoView from '~/domains/ContratoView';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import ContratoViewService from '~/services/ContratoViewService';
import ContratoService from '~/services/ContratoService';
import LicitacaoService from '~/services/LicitacaoService';
import LicitanteService from '~/services/LicitanteService';
import FonteRecursoService from '~/services/FonteRecursoService';
import DispensaService from '~/services/DispensaService';
import CaronaService from '~/services/CaronaService';
import InexigibilidadeService from '~/services/InexigibilidadeService';
import EntidadeService from '~/services/EntidadeService';
import IndexBase from 'fc/stores/IndexBase';
import Licitante from '~/domains/Licitante';
import AsyncDropDownStore from 'fc/stores/AsyncDropdownStore';
import moment from 'moment';
import { action, observable, runInAction } from 'mobx';
import { checkUserGroup, showErrorNotification } from 'fc/utils/utils';
import Entidade from '~/domains/Entidade';
import AppStore from 'fc/stores/AppStore';
import FonteRecurso from '~/domains/FonteRecurso';
import AsyncMultiselectStore from 'fc/stores/AsyncMultiselectStore';
import EntidadeExterna from '~/domains/EntidadeExterna';
import EntidadeExternaService from '~/services/EntidadeExternaService';

class ContratoIndexStore extends IndexBase {
  @observable licitacao;
  @observable origem;
  @observable tresCasasDecimais = false;
  @observable loadingItem = false;
  @observable loadingDecimalPlaces = false;

  constructor() {
    super(ContratoViewService, ContratoView, 'dataCadastro', 'desc');
  }

  getFilterSuggest(idLicitacao = undefined) {
    const filterSuggest = [];

    if (idLicitacao) {
      filterSuggest.push({
        id: '',
        field: 'idLicitacao',
        operator: 'EQUAL_TO',
        value: idLicitacao,
        formatted: '',
        fixed: true,
        invisible: true,
        completeParam: {
          field: 'idLicitacao',
          label: 'Id Licitação',
          type: SearchTypes.NUMBER,
        },
      });
    }

    const entidade = AppStore.getContextEntity();
    if (entidade && !idLicitacao) {
      filterSuggest.push({
        id: '',
        field: 'entidade',
        operator: 'EQUAL_TO',
        value: entidade,
        formatted: '',
        fixed: true,
        invisible: true,
        completeParam: {
          field: 'entidade',
          label: 'Órgão/Entidade',
          type: SearchTypes.ASYNC_QUERY,
          store: new AsyncDropDownStore(Entidade, EntidadeService, 'nome', 'id'),
        },
      });
    }

    return filterSuggest;
  }

  getAdvancedSearchParams() {
    const isAuditor = checkUserGroup(['Auditor', 'Auditor - Demais Inspetorias']);
    let searchParams = [];

    if (isAuditor) {
      searchParams = [
        {
          field: 'entidade',
          label: 'Órgão/Entidade',
          type: SearchTypes.ASYNC_QUERY,
          store: new AsyncDropDownStore(Entidade, EntidadeService, 'nome', 'id'),
        },
      ];
    }
    searchParams.push(
      {
        field: 'entidadeExterna',
        label: 'Entidade Externa',
        type: SearchTypes.ASYNC_QUERY,
        store: new AsyncDropDownStore(EntidadeExterna, EntidadeExternaService, 'nomeEntidadeExterna', 'id'),
      },
      {
        field: 'tipo',
        label: 'Origem',
        type: SearchTypes.ENUM,
        options: DadosEstaticosService.getTipoProcesso(),
      },
      {
        field: 'numero',
        label: 'Número',
        type: SearchTypes.TEXT,
      },
      {
        field: 'formaContrato',
        label: 'Forma de Contrato',
        type: SearchTypes.ENUM,
        options: DadosEstaticosService.getFormaContrato(),
      },
      {
        field: 'valorGlobal',
        label: 'Valor',
        type: SearchTypes.NUMBER,
      },
      {
        field: 'objeto',
        label: 'Objeto',
        type: SearchTypes.TEXT,
      },
      {
        field: 'licitante',
        label: 'Contratado(a)',
        type: SearchTypes.ASYNC_QUERY,
        store: new AsyncDropDownStore(Licitante, LicitanteService, 'nome', 'id'),
      },
      {
        field: 'fontesDeRecurso',
        label: 'Fontes de Recurso',
        type: SearchTypes.ASYNC_QUERY,
        store: new AsyncMultiselectStore(FonteRecurso, FonteRecursoService, 'nome', 'id'),
      },
      {
        field: 'dataCadastro',
        label: 'Data de Cadastro',
        type: SearchTypes.DATE,
      },
      {
        field: 'dataVigenciaInicial',
        label: 'Início da Vigência',
        type: SearchTypes.DATE,
      },
      {
        field: 'dataVigenciaFinal',
        label: 'Fim da Vigência',
        type: SearchTypes.DATE,
      },
      {
        field: 'cnpjContratado',
        label: 'CNPJ do Contratado',
        type: SearchTypes.CNPJ,
      }
    );
    return searchParams;
  }

  isRequisicaoModificacao(dataCadastro) {
    let result = false;
    if (dataCadastro) {
      const data = moment(dataCadastro);
      const current = moment();
      const diff = moment.duration(current.diff(data));

      if (diff.asHours() > 24) {
        result = true;
      }
    }
    return result;
  }

  @action
  getTresCasasDecimais(contrato, callback) {
    if (!contrato.processoExterno) {
      this.loadingDecimalPlaces = true;

      let requisicaoProcesso;

      const { idLicitacao, idCarona, idDispensa, idInexigibilidade } = contrato;

      if (idLicitacao) requisicaoProcesso = LicitacaoService.getTresCasasDecimais(idLicitacao);
      else if (idDispensa) requisicaoProcesso = DispensaService.getTresCasasDecimais(idDispensa);
      else if (idCarona) requisicaoProcesso = CaronaService.getTresCasasDecimais(idCarona);
      else if (idInexigibilidade) requisicaoProcesso = InexigibilidadeService.getTresCasasDecimais(idInexigibilidade);

      requisicaoProcesso
        .then((response) =>
          runInAction(() => {
            this.tresCasasDecimais = response?.data;
            this.loadingDecimalPlaces = false;
            callback && callback();
          })
        )
        .catch((error) =>
          runInAction(() => {
            this.loadingDecimalPlaces = false;
            showErrorNotification(error);
          })
        );
    }
  }

  getById(id, callback) {
    if (id) {
      ContratoService.getById(id)
        .then((response) => {
          return callback && runInAction(() => callback(response.data));
        })
        .catch((e) => {
          showErrorNotification(e);
        });
    }
  }
}

export default ContratoIndexStore;
