import EmpenhoContratoService from '~/services/EmpenhoContratoService';
import IndexBase from 'fc/stores/IndexBase';
import SearchTypes from 'fc/components/AdvancedSearch/SearchTypes';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import { action, observable, runInAction } from 'mobx';
import MultipleFileUploaderStore from 'fc/stores/MultipleFileUploaderStore';
import { showErrorNotification } from 'fc/utils/utils';

class EmpenhoContratoIndexStore extends IndexBase {
  @observable arquivos = [];

  constructor() {
    super(EmpenhoContratoService);
    this.fileStore = new MultipleFileUploaderStore(
      [],
      (file) => EmpenhoContratoService.upload(file),
      (fileDTO) => EmpenhoContratoService.download(fileDTO),
      (idArquivo) => this.removerArquivoEmpenhoContrato(idArquivo),
      (idArquivo, arquivoCaronaDTO) => this.atualizarEmpenhoContrato(idArquivo, arquivoCaronaDTO)
    );
  }

  @action
  initializeArquivos(idEmpenhoContrato, callback) {
    if (idEmpenhoContrato) {
      this.service
        .recuperarArquivos(idEmpenhoContrato)
        .then((response) =>
          runInAction(() => {
            const arquivosRecuperados = response.data;
            this.fileStore.initialize(arquivosRecuperados);
            this.arquivos = arquivosRecuperados;
          })
        )
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        )
        .finally(() =>
          runInAction(() => {
            callback && callback();
          })
        );
    }
  }

  getAdvancedSearchParams() {
    return [
      {
        field: 'numeroEmpenho',
        label: 'Número do Empenho',
        type: SearchTypes.NUMBER,
      },
      {
        field: 'valorEmpenho',
        label: 'Valor',
        type: SearchTypes.NUMBER,
      },
      {
        field: 'dataEmpenho',
        label: 'Data',
        type: SearchTypes.DATE,
      },
      {
        field: 'tipoEmpenho',
        label: 'Tipo',
        type: SearchTypes.ENUM,
        options: DadosEstaticosService.getTiposEmpenho(),
      },
    ];
  }
}

export default EmpenhoContratoIndexStore;
