import FormBase from 'fc/stores/FormBase';
import EmpenhoContratoService from '~/services/EmpenhoContratoService';
import ContratoService from '~/services/ContratoService';
import { action, observable, override, runInAction } from 'mobx';
import EmpenhoContrato from '~/domains/EmpenhoContrato';
import MultipleFileUploaderStore from 'fc/stores/MultipleFileUploaderStore';
import { showErrorNotification, showNotification } from 'fc/utils/utils';
import ObrigatoriedadeArquivoService from '~/services/ObrigatoriedadeArquivoService';
import DadosEstaticosService from '~/services/DadosEstaticosService';

class EmpenhoContratoFormStore extends FormBase {
  @observable idEmpenhoContrato;
  @observable contrato;
  @observable arquivos = [];

  constructor() {
    super(EmpenhoContratoService, EmpenhoContrato);
    this.fileStore = new MultipleFileUploaderStore(
      [],
      (file) => EmpenhoContratoService.upload(file),
      (fileDTO) => EmpenhoContratoService.download(fileDTO),
      (idArquivo) => this.removerArquivoEmpenhoContrato(idArquivo),
      (idArquivo, arquivoEmpenhoDTO) => this.atualizarEmpenhoContrato(idArquivo, arquivoEmpenhoDTO)
    );

    this.loadTipos();
  }

  rulesDefinition() {
    let rules = {
      numeroEmpenho: [
        { rule: 'required', message: 'Por favor, preencha o campo' },
        { rule: 'isMaxLength', maxLength: 17, message: 'Por favor, diminua o tamanho do campo' },
      ],
      valorEmpenho: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      dataEmpenho: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      tipoEmpenho: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
    };
    return rules;
  }

  @override
  initialize(id, defaultValues = {}, callback) {
    if (!id) {
      this.object = Object.assign({}, defaultValues);
      callback && callback();
    } else {
      this.loading = true;
      const promises = [];
      promises.push(this.service.getById(id));
      promises.push(this.service.recuperarArquivos(id));

      Promise.all(promises)
        .then((response) =>
          runInAction(() => {
            this.object = response[0]?.data;
            this.loadedObject = response[0]?.data;
            const arquivosRecuperados = response[1]?.data;
            this.arquivos = arquivosRecuperados;
            this.fileStore.initialize(arquivosRecuperados);
            callback && callback();
          })
        )
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        )
        .finally(() =>
          runInAction(() => {
            this.loading = false;
          })
        );
    }
  }

  @override
  save(callback, type = 'edit') {
    const saveObject = this.getObjectToSave(type);
    if (Object.keys(saveObject).length === 0) {
      showNotification('warn', null, 'Nenhuma alteração realizada.');
    } else {
      const empenhoDTO = {
        id: this.object.id,
        empenhoContrato: this.object,
        arquivosEmpenhoContrato: this.arquivos,
      };
      this.loading = true;
      this.service
        .criarEmpenho(empenhoDTO)
        .then(() => {
          callback && callback();
          showNotification('success', null, 'Registro salvo com sucesso!');
        })
        .catch((error) => showErrorNotification(error))
        .finally(() =>
          runInAction(() => {
            this.loading = false;
          })
        );
    }
  }

  @action
  carregaContrato(idContrato, callback) {
    this.loading = true;
    ContratoService.getById(idContrato)
      .then((response) =>
        runInAction(() => {
          this.object.contrato = response.data;
          callback && callback();
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      )
      .finally(() =>
        runInAction(() => {
          this.loading = false;
        })
      );
  }

  @action
  setReadFileStore(list, callback) {
    this.readFileStore.initialize(list);
    callback && callback();
  }

  @action
  setFileList(list) {
    this.arquivos = list;
  }

  removerArquivoEmpenhoContrato(idArquivo) {
    return this.service.removerArquivo(this.idEmpenhoContrato, idArquivo);
  }

  atualizarEmpenhoContrato(idArquivo, arquivoEmpenhoContratoDTO) {
    return this.service.atualizarArquivo(this.idEmpenhoContrato, idArquivo, arquivoEmpenhoContratoDTO);
  }

  @action
  loadTipos() {
    ObrigatoriedadeArquivoService.getArquivosObrigatorios({ tipoProcesso: 'EMPENHO', filtros: ['EMPENHO'] })
      .then((response) =>
        runInAction(() => {
          const arquivosObrigatorios = response?.data ?? [];
          this.fileStore.tipoArquivoEnum = arquivosObrigatorios.map((arqObg) => {
            const arq = DadosEstaticosService.getTipoArquivoEmpenho().find((arq) => arq.value === arqObg.arquivoEnum);
            return { ...arqObg, text: arqObg.obrigatorio ? '* ' + arqObg.arquivo : arqObg.arquivo, value: arq.value };
          });
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      );
  }
}

export default EmpenhoContratoFormStore;
