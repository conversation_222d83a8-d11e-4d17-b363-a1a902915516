import FormBase from 'fc/stores/FormBase';
import { action, override, observable, runInAction } from 'mobx';
import RequisicaoModificacao from '~/domains/RequisicaoModificacao';
import RequisicaoModificacaoService from '~/services/RequisicaoModificacaoService';
import { showErrorNotification, showNotification } from 'fc/utils/utils';
import AppStore from 'fc/stores/AppStore';
import moment from 'moment';

class RequisicaoModificacaoContratoFormStore extends FormBase {
  @observable justificativaJurisdicionado;

  constructor() {
    super(RequisicaoModificacaoService, RequisicaoModificacao);

    this.enviarRequisicaoContrato = this.enviarRequisicaoContrato.bind(this);
  }

  rulesDefinition() {
    return {};
  }

  @override
  updateAttribute(tipo, event) {
    let value = event;
    if (event && event.value) {
      value = event.value;
    }
    if (event && event.target) {
      value = event.target.value;
    }
    if (tipo === 'jurisdicionado') {
      this.justificativaJurisdicionado = value;
    } else if (tipo === 'auditor') {
      this.justificativaAuditor = value;
    }
  }

  enviarRequisicaoContrato(contratoDTO, callback) {
    this.loading = true;
    const dto = {};
    dto.justificativaJurisdicionado = this.justificativaJurisdicionado;
    dto.filtros = contratoDTO.filtros;
    dto.contratoDTO = contratoDTO;
    dto.idEntidade = AppStore.getContextEntity()?.id
      ? AppStore.getContextEntity()?.id
      : contratoDTO.contrato?.entidade?.id;
    RequisicaoModificacaoService.requisicaoModificacaoContrato(contratoDTO.contrato.id, dto)
      .then(() =>
        runInAction(() => {
          callback && callback();
          showNotification('success', null, 'Requisição de Modificação enviada com sucesso!');
          this.justificativaJurisdicionado = undefined;
        })
      )
      .catch((error) =>
        runInAction(() => {
          this.justificativaJurisdicionado = undefined;
          showErrorNotification(error);
        })
      )
      .finally(() =>
        runInAction(() => {
          this.loading = false;
        })
      );
  }

  @action
  enviarRequisicaoRescisao(contratoDTO, callback) {
    this.loading = true;
    const dto = {};
    dto.justificativaJurisdicionado = this.justificativaJurisdicionado;
    dto.contratoDTO = contratoDTO;
    dto.idEntidade = AppStore.getData('selectedContextEntity');
    RequisicaoModificacaoService.requisicaoModificacaoRescisao(contratoDTO.contrato.id, dto)
      .then(() =>
        runInAction(() => {
          callback && callback();
          showNotification('success', null, 'Requisição de Modificação enviada com sucesso!');
          this.justificativaJurisdicionado = undefined;
        })
      )
      .catch((error) =>
        runInAction(() => {
          this.justificativaJurisdicionado = undefined;
          showErrorNotification(error);
        })
      )
      .finally(() =>
        runInAction(() => {
          this.loading = false;
        })
      );
  }

  @action
  enviarRequisicaoAditivoContrato(aditivoDTO, callback) {
    this.loading = true;
    const dto = {};
    dto.justificativaJurisdicionado = this.justificativaJurisdicionado;
    dto.aditivoDTO = aditivoDTO;
    dto.idEntidade = AppStore.getContextEntity()?.id
      ? AppStore.getContextEntity()?.id
      : aditivoDTO.aditivoContrato.contrato.entidade.id;
    RequisicaoModificacaoService.requisicaoModificacaoAditivoContrato(aditivoDTO.aditivoContrato.id, dto)
      .then(() =>
        runInAction(() => {
          callback && callback();
          showNotification('success', null, 'Requisição de Modificação enviada com sucesso!');
          this.justificativaJurisdicionado = undefined;
        })
      )
      .catch((error) =>
        runInAction(() => {
          this.justificativaJurisdicionado = undefined;
          showErrorNotification(error);
        })
      )
      .finally(() =>
        runInAction(() => {
          this.loading = false;
        })
      );
  }

  isRequisicaoModificacao(dataCadastro) {
    let result = false;
    if (dataCadastro) {
      const data = moment(dataCadastro);
      const current = moment();
      const diff = moment.duration(current.diff(data));

      if (diff.asHours() > 24) {
        result = true;
      }
    }
    return result;
  }
}

export default RequisicaoModificacaoContratoFormStore;
