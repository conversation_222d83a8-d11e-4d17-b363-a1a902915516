import SearchTypes from 'fc/components/AdvancedSearch/SearchTypes';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import TdaLicitacaoService from '~/services/TdaLicitacaoService';
import IndexBase from 'fc/stores/IndexBase';
import LicitacaoService from '~/services/LicitacaoService';
import { runInAction, observable, action } from 'mobx';
import { showErrorNotification } from 'fc/utils/utils';
import SearchOperators from 'fc/components/AdvancedSearch/SearchOperators';
import EdificacaoService from '~/services/EdificacaoService';
import AppStore from 'fc/stores/AppStore';
import AccessPermission from '~/constants/AccessPermission';
import MultipleFileUploaderStore from 'fc/stores/MultipleFileUploaderStore';
import ProcessoLicitacaoViewService from '~/services/ProcessoLicitacaoViewService';
import ProcessoLicitacaoView from '~/domains/ProcessoLicitacaoView';

class ConsultaProcessoIndexStore extends IndexBase {
  @observable fileStore;
  @observable arquivoLicitacaoList = [];
  @observable idLicitacao;
  @observable ultimaAlteracao;
  @observable idAlerta = null;

  constructor() {
    super(ProcessoLicitacaoViewService, ProcessoLicitacaoView, 'dataCadastro', 'desc');
    this.licitacaoService = LicitacaoService;
    this.carregaUltimaAlteracao = this.carregaUltimaAlteracao.bind(this);
    this.fileStore = new MultipleFileUploaderStore(
      DadosEstaticosService.getTipoArquivoLicitacao(),
      (file) => LicitacaoService.upload(file),
      (fileDTO) => LicitacaoService.download(fileDTO),
      (idArquivo) => this.removerArquivoLicitacao(idArquivo),
      (idArquivo, arquivoLicitacaoDTO) => this.atualizarArquivoLicitacao(idArquivo, arquivoLicitacaoDTO)
    );
    this.fileStoreTda = new MultipleFileUploaderStore(
      [],
      (file) => TdaLicitacaoService.upload(file),
      (fileDTO) => TdaLicitacaoService.download(fileDTO)
    );
  }

  @action
  initialize(idLicitacao) {
    this.idLicitacao = idLicitacao;
    if (idLicitacao) {
      this.licitacaoService
        .recuperarArquivos(idLicitacao)
        .then((response) =>
          runInAction(() => {
            if (response && response.data) {
              const arquivosRecuperados = response.data;
              this.fileStore.initialize(arquivosRecuperados);
              this.arquivoLicitacaoList = arquivosRecuperados;
            }
          })
        )
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        );
    }
  }

  @action
  carregaUltimaAlteracao(id) {
    if (id) {
      this.licitacaoService
        .getUltimaAlteracao(id)
        .then(({ data }) =>
          runInAction(() => {
            if (data) this.ultimaAlteracao = data;
          })
        )
        .catch((error) => showErrorNotification(error));
    }
  }

  @action
  carregarEdificacaoObra(licitacao, callback) {
    this.loading = true;
    const filtro = {
      page: { index: 1, size: 100 },
      sort: {},
      andParameters: [{ field: 'obra', operator: SearchOperators.EQUAL_TO.value, value: licitacao.obra.id }],
    };
    EdificacaoService.advancedSearch(filtro)
      .then((response) =>
        runInAction(() => {
          const edificacao = response.data?.items[0];
          licitacao.obra.edificacao = edificacao;
          callback && callback(licitacao);
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      )
      .finally(() =>
        runInAction(() => {
          this.loading = false;
        })
      );
  }

  getLicitacaoById(idLicitacao, callback) {
    if (idLicitacao) {
      LicitacaoService.getById(idLicitacao)
        .then((response) => {
          return callback && callback(response.data);
        })
        .catch((e) => {
          showErrorNotification(e);
        });
    }
  }

  hasPermissionTda() {
    return AppStore.hasPermission([AccessPermission.cadastrarTda.readPermission]);
  }

  hasPermissionAlerta() {
    return AppStore.hasPermission([AccessPermission.alerta.readPermission]);
  }

  @action
  hasAlert(idProcessoLicitacao, callback) {
    idProcessoLicitacao &&
      this.service
        .getAlertaProcessoLicitacao(idProcessoLicitacao)
        .then((alerta) => {
          runInAction(() => {
            if (alerta?.data) {
              this.idAlerta = alerta.data;
              callback && callback();
            }
          });
        })
        .catch(() => {
          runInAction(() => {
            showErrorNotification('Ocorreu um erro ao buscar o id do alerta.');
          });
        });
  }

  @action
  initializeTdaLicitacao(idLicitacao) {
    this.idLicitacao = idLicitacao;
    if (idLicitacao && this.hasPermissionTda()) {
      TdaLicitacaoService.tdaLicitacaoByIdLicitacao(idLicitacao)
        .then((response) =>
          runInAction(() => {
            if (response && response.data) {
              const tdaLicitacao = response.data;
              this.carregaArquivosTda(tdaLicitacao);
            }
          })
        )
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        );
    }
  }

  @action
  carregaArquivosTda(tdaLicitacao) {
    if (tdaLicitacao) {
      TdaLicitacaoService.recuperarArquivos(tdaLicitacao.id)
        .then((response) =>
          runInAction(() => {
            if (response && response.data) {
              const arquivosRecuperados = response.data.map((arq) => {
                return {
                  ...arq,
                  analista: tdaLicitacao.analista.nome,
                };
              });
              this.fileStoreTda.initialize(arquivosRecuperados);
              this.arquivosTdaLicitacao = arquivosRecuperados;
            }
          })
        )
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        );
    }
  }

  verificaFormaPublicacao(tpFormaPublicacao, descricao, pagina) {
    let descricaoCampo = descricao ? descricao : '';
    const descPrefix = descricao && /^\d*\.?\d+$/.test(descricao) ? 'Nº ' : '';
    const pagPrefix = pagina && /^\d*\.?\d+$/.test(pagina) ? 'pág. ' : '';
    if (
      (tpFormaPublicacao === 'Diário Oficial da União (DOU)' ||
        tpFormaPublicacao === 'Diário Oficial do Estado (DOE)' ||
        tpFormaPublicacao === 'Diário Oficial de Contas') &&
      (pagina || descricao)
    ) {
      descricaoCampo =
        pagina && descricao
          ? `${descPrefix} ${descricao}, ${pagPrefix} ${pagina}`
          : pagina
          ? `${pagPrefix} ${pagina}`
          : `${descPrefix} ${descricao}`;
    }
    return descricaoCampo;
  }

  getAdvancedSearchParams() {
    return [
      {
        field: 'dataCadastro',
        label: 'Data de Cadastro da Licitação',
        type: SearchTypes.DATE,
      },
      {
        field: 'dataAbertura',
        label: 'Data de Abertura',
        type: SearchTypes.DATE,
      },
      {
        field: 'orgao',
        label: 'Órgão',
        type: SearchTypes.TEXT,
      },
      {
        field: 'numero',
        label: 'Número',
        type: SearchTypes.NUMBER,
      },
      {
        field: 'ano',
        label: 'Ano',
        type: SearchTypes.UNGROUPED_NUMBER,
      },
      {
        field: 'objeto',
        label: 'Objeto',
        type: SearchTypes.TEXT,
      },
      {
        field: 'valorEstimado',
        label: 'Valor Estimado',
        type: SearchTypes.TEXT,
      },
    ];
  }

  getAdvancedSearchParamsContrato() {
    return [
      {
        field: 'numero',
        label: 'Número',
        type: SearchTypes.TEXT,
      },
    ];
  }

  getAdvancedSearchParamsAditivo() {
    return [
      {
        field: 'numero',
        label: 'Número',
        type: SearchTypes.TEXT,
      },
      {
        field: 'dataPublicacao',
        label: 'Data da Publicação',
        type: SearchTypes.DATE,
      },
    ];
  }

  getFilterSuggestContrato() {
    const filterSuggest = [
      {
        id: '',
        field: 'status',
        operator: 'NOT_EQUAL_TO',
        value: 'REMOVIDA',
        formatted: '',
        fixed: true,
        completeParam: {
          field: 'status',
          label: 'Status',
          type: SearchTypes.ENUM,
          options: DadosEstaticosService.getStatusContrato(),
        },
      },
      {
        id: '',
        field: 'idLicitacao',
        operator: 'EQUAL_TO',
        value: this.idLicitacao,
        formatted: '',
        fixed: true,
        completeParam: {
          field: 'id',
          label: 'Id Licitação',
          type: SearchTypes.NUMBER,
        },
      },
    ];

    return filterSuggest;
  }
}

export default ConsultaProcessoIndexStore;
