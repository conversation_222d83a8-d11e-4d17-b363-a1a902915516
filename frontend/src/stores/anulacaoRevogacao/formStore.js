import FormBase from 'fc/stores/FormBase';
import { action, observable, override, runInAction } from 'mobx';
import { showErrorNotification, showNotification } from 'fc/utils/utils';
import AnulacaoRevogacaoService from '~/services/AnulacaoRevogacaoService';
import AnulacaoRevogacao from '~/domains/AnulacaoRevogacao';
import MultipleFileUploaderStore from 'fc/stores/MultipleFileUploaderStore';
import AppStore from 'fc/stores/AppStore';
import ObrigatoriedadeArquivoService from '~/services/ObrigatoriedadeArquivoService';
import DadosEstaticosService from '~/services/DadosEstaticosService';
class AnulacaoRevogacaoFormStore extends FormBase {
  @observable tipoProcessoAssociado;
  @observable fileStore;
  @observable idAnulacaoRevogacao;
  @observable arquivosList = [];

  constructor() {
    super(AnulacaoRevogacaoService, AnulacaoRevogacao);

    this.fileStore = new MultipleFileUploaderStore(
      [],
      (file) => AnulacaoRevogacaoService.upload(file),
      (fileDTO) => AnulacaoRevogacaoService.download(fileDTO),
      (idArquivo) => this.removerArquivo(idArquivo),
      (idArquivo, arquivoAnulacaoRevogacaoDTO) => this.atualizarArquivo(idArquivo, arquivoAnulacaoRevogacaoDTO)
    );
  }

  @override
  initialize(tipoProcessoAssociado, id, defaultValues = {}, callback) {
    this.tipoProcessoAssociado = tipoProcessoAssociado;
    if (!id) {
      this.object = Object.assign({}, defaultValues);
      this.tipoProcessoAssociado != 'CREDENCIAMENTO' && this.loadTipos(tipoProcessoAssociado);
    } else {
      this.loading = true;
      this.service
        .getById(id)
        .then((response) =>
          runInAction(() => {
            this.object = response.data;
            this.loadedObject = response.data;
            callback && callback();
          })
        )
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        )
        .finally(() =>
          runInAction(() => {
            this.loading = false;
            this.loadTipos(tipoProcessoAssociado);
          })
        );
    }
  }

  @action
  initializeArquivos(idAnulacaoRevogacao, callback) {
    this.idAnulacaoRevogacao = idAnulacaoRevogacao;
    if (idAnulacaoRevogacao) {
      AnulacaoRevogacaoService.recuperarArquivos(idAnulacaoRevogacao)
        .then((response) =>
          runInAction(() => {
            const arquivosRecuperados = response.data;
            this.fileStore.initialize(arquivosRecuperados);
            this.arquivosList = arquivosRecuperados;
          })
        )
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        )
        .finally(() =>
          runInAction(() => {
            callback && callback();
          })
        );
    }
  }

  @action
  validaArquivos(callback) {
    this.loading = true;
    AnulacaoRevogacaoService.validaArquivos({
      arquivos: this.arquivosList,
      anulacaoRevogacao: {
        tipoOcorrencia: this.object.tipoOcorrencia,
        tipoProcessoAssociado: this.tipoProcessoAssociado,
      },
    })
      .then(() => runInAction(() => callback && callback()))
      .catch((error) => showErrorNotification(error))
      .finally(() =>
        runInAction(() => {
          this.loading = false;
        })
      );
  }

  @action
  loadTipos(tipoProcessoAssociado, callback) {
    let filtros = [];
    if (tipoProcessoAssociado === 'CREDENCIAMENTO') {
      if (this.object?.tipoOcorrencia === 'ANULAR') {
        filtros = ['CR_ANULACAO'];
      } else if (this.object?.tipoOcorrencia === 'REVOGAR') {
        filtros = ['CR_REVOGACAO'];
      }
    } else {
      filtros = ['OUTROS'];
    }

    ObrigatoriedadeArquivoService.getArquivosObrigatorios({
      tipoProcesso: 'ANULACAO_REVOGACAO',
      filtros: filtros,
    })
      .then((response) =>
        runInAction(() => {
          const obrgArquivo = response?.data;
          const filteredList = DadosEstaticosService.getTipoArquivoAnulacaoRevogacao().filter((aqr) =>
            obrgArquivo.some((obg) => obg.arquivoEnum === aqr.value)
          );
          this.fileStore.tipoArquivoEnum = filteredList.map((aqr) => {
            if (obrgArquivo.find((obg) => obg.arquivo === aqr.text).obrigatorio) {
              aqr.text = '* ' + aqr.text;
              aqr.obrigatorio = true;
            }
            return aqr;
          });
          callback && callback();
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      );
  }

  @action
  setArquivosList(arquivos) {
    this.arquivosList = arquivos;
  }

  @action
  anularRevogarProcesso(idProcesso, numeroProcesso, callback) {
    const anulacaoRevogacao = this.object;
    anulacaoRevogacao.numeroProcesso = numeroProcesso;
    anulacaoRevogacao.tipoProcessoAssociado = this.tipoProcessoAssociado;
    anulacaoRevogacao.entidade = AppStore.getContextEntity();

    const anulacaoRevogacaoDTO = {
      anulacaoRevogacao: anulacaoRevogacao,
      arquivos: this.arquivosList,
    };
    this.service
      .anularRevogarProcesso(Object.assign(anulacaoRevogacaoDTO), idProcesso)
      .then(() =>
        runInAction(() => {
          callback && callback();
          showNotification('success', null, 'Anulação/Revogação concluída com sucesso!');
        })
      )
      .catch((error) => showErrorNotification(error))
      .finally(() => {
        callback && callback();
      });
  }

  rulesDefinition() {
    let rules = {
      tipoOcorrencia: [{ rule: 'required', message: 'Por favor, selecione uma opção' }],
      descricao: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
    };

    if (this.tipoProcessoAssociado === 'CREDENCIAMENTO') {
      rules = this.mergeRules(rules, {
        dataAviso: [{ rule: 'required', message: 'Por favor, selecione uma data' }],
      });
    }

    return rules;
  }

  removerArquivo(idArquivo) {
    return AnulacaoRevogacaoService.removerArquivo(this.object.id, idArquivo);
  }

  atualizarArquivo(idArquivo, arquivoDTO) {
    return AnulacaoRevogacaoService.atualizarArquivo(this.object.id, idArquivo, arquivoDTO);
  }
}

export default AnulacaoRevogacaoFormStore;
