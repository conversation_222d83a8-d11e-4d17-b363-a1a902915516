import FormBase from 'fc/stores/FormBase';
import MultipleFileUploaderStore from 'fc/stores/MultipleFileUploaderStore';
import ObraMedicaoService from '~/services/ObraMedicaoService';
import { action, makeObservable, observable, override, runInAction } from 'mobx';
import ObraMedicao from '~/domains/ObraMedicao';
import { showErrorNotification, showNotification } from 'fc/utils/utils';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import ObrigatoriedadeArquivoService from '~/services/ObrigatoriedadeArquivoService';
import SearchOperators from 'fc/components/AdvancedSearch/SearchOperators';

class ObraMedicaoFormStore extends FormBase {
  @observable arquivos = [];
  @observable medicoesObra;
  @observable readFileStore;
  @observable fileStore;
  @observable maxPercConclusao;

  constructor() {
    super(ObraMedicaoService, ObraMedicao);
    this.fileStore = new MultipleFileUploaderStore(
      [],
      (file) => this.service.upload(file),
      (fileDTO) => this.service.download(fileDTO),
      (idArquivo) => this.service.removerArquivo(idArquivo)
    );
    this.readFileStore = new MultipleFileUploaderStore(
      [],
      (file) => this.service.upload(file),
      (fileDTO) => this.service.download(fileDTO),
      () => {},
      () => {}
    );

    this.setReadFileStore = this.setReadFileStore.bind(this);
    this.loadMedicoes = this.loadMedicoes.bind(this);

    this.loadTipos();

    makeObservable(this);
  }

  rulesDefinition() {
    return {
      percConclusao: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      dataMedicao: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      valorEmpenhado: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      justificativa: [{ rule: 'isMaxLength', maxLength: 4000, message: 'Por favor, diminua o tamanho do campo' }],
    };
  }

  @override
  initialize(id, defaultValues = {}, idObraMedicaoReqMod, callback) {
    this.loading = true;
    this.service
      .listarMedicoesObra(id)
      .then((response) =>
        runInAction(() => {
          let medicoes = response.data;
          if (idObraMedicaoReqMod) {
            const indexMedicao = medicoes.map((m) => m.obraMedicao.id).indexOf(idObraMedicaoReqMod);
            if (indexMedicao === -1) {
              showNotification(
                'error',
                null,
                'Medição solicitada para requisição de modificação não existe para esta obra'
              );
              return;
            }
            const { obraMedicao, arquivosObraMedicao } = medicoes[indexMedicao];
            this.object = obraMedicao;
            this.arquivos = arquivosObraMedicao;
            this.fileStore.initialize(arquivosObraMedicao);
            this.maxPercConclusao = medicoes[indexMedicao + 1]?.obraMedicao?.percConclusao;
            medicoes = medicoes.slice(0, indexMedicao);
          } else {
            this.object = Object.assign({}, defaultValues);
          }
          this.medicoesObra = medicoes;
          callback && callback();
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      )
      .finally(() =>
        runInAction(() => {
          this.loading = false;
        })
      );
  }

  loadTipos() {
    const filtro = {
      page: { index: 1, size: 100 },
      sort: {
        by: 'id',
        order: 'asc',
      },
      andParameters: [{ field: 'objeto', operator: SearchOperators.EQUAL_TO.value, value: 'OBRA_MEDICAO' }],
    };

    ObrigatoriedadeArquivoService.advancedSearch(filtro)
      .then((response) =>
        runInAction(() => {
          const obrgArquivo = response?.data?.items ? [...response.data.items] : [];
          const filteredList = DadosEstaticosService.getTipoArquivoObraMedicao().filter((aqr) =>
            obrgArquivo.some((obg) => obg.arquivo === aqr.text)
          );
          this.fileStore.tipoArquivoEnum = filteredList.map((aqr) => {
            if (obrgArquivo.find((obg) => obg.arquivo === aqr.text).obrigatorio) {
              aqr.text = '* ' + aqr.text;
              aqr.obrigatorio = true;
            }
            return aqr;
          });
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      );
  }

  @action
  loadMedicoes(id) {
    this.loading = true;
    this.service
      .listarMedicoesObra(id)
      .then((response) =>
        runInAction(() => {
          this.medicoesObra = response.data;
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      )
      .finally(() =>
        runInAction(() => {
          this.loading = false;
        })
      );
  }

  @action
  validaArquivos(callback) {
    this.loading = true;
    this.service
      .validaArquivos({ arquivosObraMedicao: this.arquivos })
      .then(() => runInAction(() => callback && callback()))
      .catch((error) => showErrorNotification(error))
      .finally(() =>
        runInAction(() => {
          this.loading = false;
        })
      );
  }

  @override
  save(callback, type = 'new') {
    const saveObject = this.getObjectToSave(type);
    if (Object.keys(saveObject).length === 0) {
      showNotification('warn', null, 'Nenhuma alteração realizada.');
    } else {
      this.loading = true;
      this.service
        .cadastrarMedicaoObra({ obraMedicao: saveObject, arquivosObraMedicao: this.arquivos ?? [] })
        .then(() => {
          callback && callback();
          showNotification('success', null, 'Registro salvo com sucesso!');
        })
        .catch((error) => showErrorNotification(error))
        .finally(() =>
          runInAction(() => {
            this.loading = false;
          })
        );
    }
  }

  @action
  setReadFileStore(list, callback) {
    this.readFileStore.initialize(list);
    callback && callback();
  }

  @action
  setFileList(list) {
    this.arquivos = list;
  }

  getImagesFiles(arquivosObraMedicao, visualizeCallback) {
    const arquivos = arquivosObraMedicao.map((arquivoDTO) => {
      if (arquivoDTO.arquivo && !!arquivoDTO.arquivo.tipoArquivo?.match(/(png|jpg|jpeg)/g)) return arquivoDTO.arquivo;
    });
    const promises = [];
    arquivos.forEach((arquivo) => arquivo && promises.push(this.service.download(arquivo)));
    Promise.all(promises)
      .then((response) =>
        runInAction(() => {
          const arquivosResponse = response;
          const urls = arquivosResponse.map((arquivo) => URL.createObjectURL(arquivo.data));
          visualizeCallback(urls);
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      );
  }
}
export default ObraMedicaoFormStore;
