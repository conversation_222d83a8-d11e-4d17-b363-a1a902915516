import FormBase from 'fc/stores/FormBase';
import ResponsavelContratoService from '~/services/ResponsavelContratoService';
import ResponsavelContrato from '~/domains/ResponsavelContrato';

class ResponsavelContratoFormStore extends FormBase {
  constructor() {
    super(ResponsavelContratoService, ResponsavelContrato);
  }

  rulesDefinition() {
    return {
      nome: [
        { rule: 'required', message: 'Por favor, preencha o campo' },
        { rule: 'isMaxLength', maxLength: 255, message: 'Por favor, diminua o tamanho do campo' },
      ],
      email: [
        { rule: 'required', message: 'Por favor, preencha o campo' },
        { rule: 'isMaxLength', maxLength: 255, message: 'Por favor, diminua o tamanho do campo' },
        { rule: 'isEmail', message: 'Por favor, informe um e-mail válido' },
      ],
      telefone: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      cpf: [
        { rule: 'required', message: 'Por favor, preencha o campo' },
        { rule: 'isCpfCnpj', message: 'Por favor, informe um CPF válido' },
      ],
    };
  }
}

export default ResponsavelContratoFormStore;
