import SearchTypes from 'fc/components/AdvancedSearch/SearchTypes';
import ResponsavelContratoService from '~/services/ResponsavelContratoService';
import IndexBase from 'fc/stores/IndexBase';
import ResponsavelContrato from '~/domains/ResponsavelContrato';
import DadosEstaticosService from '~/services/DadosEstaticosService';

class ResponsavelContratoIndexStore extends IndexBase {
  constructor() {
    super(ResponsavelContratoService, ResponsavelContrato, 'nome', 'asc');
  }

  getAdvancedSearchParams() {
    return [
      {
        field: 'nome',
        label: 'Nome',
        type: SearchTypes.TEXT,
      },
      {
        field: 'email',
        label: 'E-mail',
        type: SearchTypes.TEXT,
      },
      {
        field: 'telefone',
        label: 'Telefone',
        type: SearchTypes.TEXT,
      },
      {
        field: 'cpf',
        label: 'CPF',
        type: SearchTypes.TEXT,
      },
      {
        field: 'efetivo',
        label: 'Efetivo',
        type: SearchTypes.ENUM,
        options: DadosEstaticosService.getSimNao(),
      },
    ];
  }
}

export default ResponsavelContratoIndexStore;
