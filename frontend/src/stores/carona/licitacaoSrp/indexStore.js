import SearchTypes from 'fc/components/AdvancedSearch/SearchTypes';
import Entidade from '~/domains/Entidade';
import EntidadeService from '~/services/EntidadeService';
import LicitacaoSrpViewService from '~/services/LicitacaoSrpViewService';
import AsyncDropDownStore from 'fc/stores/AsyncDropdownStore';
import IndexBase from 'fc/stores/IndexBase';
import AppStore from 'fc/stores/AppStore';
import { override, runInAction } from 'mobx';
import { showErrorNotification } from 'fc/utils/utils';
import LicitacaoSrpView from '~/domains/LicitacaoSrpView';

class LicitacaoSrpIndexStore extends IndexBase {
  constructor() {
    super(LicitacaoSrpViewService, LicitacaoSrpView, 'dataCadastroPreparatoria', 'desc');
  }

  getAdvancedSearchParams() {
    let result = [];

    result.push(
      {
        field: 'dataCadastroPreparatoria',
        label: 'Data de Cadastro',
        type: SearchTypes.DATE,
      },
      {
        field: 'dataAbertura',
        label: 'Data de Abertura',
        type: SearchTypes.DATE,
      },
      {
        field: 'orgao',
        label: 'Órgão',
        type: SearchTypes.TEXT,
      },
      {
        field: 'numero',
        label: 'Número',
        type: SearchTypes.TEXT,
      },
      {
        field: 'ano',
        label: 'Ano',
        type: SearchTypes.UNGROUPED_NUMBER,
      },
      {
        field: 'objeto',
        label: 'Objeto',
        type: SearchTypes.TEXT,
      },
      {
        field: 'valorAdjudicado',
        label: 'Valor Adjudicado',
        type: SearchTypes.NUMBER,
      }
    );

    return result;
  }

  getFilterSuggest() {
    const entidade = AppStore.getContextEntity();
    const filterSuggest = [];
    if (entidade) {
      filterSuggest.push({
        id: '',
        field: 'entidade',
        operator: 'EQUAL_TO',
        value: entidade,
        formatted: '',
        fixed: true,
        invisible: true,
        completeParam: {
          field: 'entidade',
          label: 'Órgão/Entidade',
          type: SearchTypes.ASYNC_QUERY,
          store: new AsyncDropDownStore(Entidade, EntidadeService, 'nome', 'id'),
        },
      });
    }

    return filterSuggest;
  }

  @override
  load(options = {}, callback, increment = false) {
    this.setLoading(true, increment);
    const parameters = Object.assign(this.advancedSearchParams, options);

    if (options && options.page && options.page.index && options.page.size) {
      parameters.page = { index: options.page.index, size: options.page.size };
    } else {
      parameters.page = { index: 1, size: 10 };
    }

    if (options.andParameters && options.andParameters.length > 0) {
      parameters.andParameters = options.andParameters;
    }

    if (options.orParameters && options.orParameters.length > 0) {
      parameters.orParameters = options.orParameters;
    }

    if (options && options['sort'] && options['sort'].by && options['sort'].order) {
      parameters.sort = { by: options['sort'].by, order: options['sort'].order };
    } else {
      parameters.sort = { by: this.defaultSortBy, order: this.defaultSortOrder };
    }

    this.pagination = parameters;

    let parametersCopy = JSON.parse(JSON.stringify(parameters));

    this.service
      .advancedSearch(parametersCopy)
      .then((response) =>
        runInAction(() => {
          if (increment) {
            this.pagination.total += response.data.total;
            this.list = this.initializeLoadedList([...this.list, ...response.data.items]);
          } else {
            this.pagination.total = response.data.total;
            this.list = this.initializeLoadedList(response.data.items);
          }
          callback && callback(response);
        })
      )
      .catch((error) => showErrorNotification(error))
      .finally(() =>
        runInAction(() => {
          this.setLoading(false, increment);
        })
      );
  }
}

export default LicitacaoSrpIndexStore;
