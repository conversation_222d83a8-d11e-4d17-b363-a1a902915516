import FormBase from 'fc/stores/FormBase';
import Carona from '~/domains/Carona';
import CaronaService from '~/services/CaronaService';
import FonteRecursoService from '~/services/FonteRecursoService';
import LicitanteService from '~/services/LicitanteService';
import Entidade from '~/domains/Entidade';
import EntidadeService from '~/services/EntidadeService';
import AsyncDropDownStore from 'fc/stores/AsyncDropdownStore';
import AsyncMultiselectStore from 'fc/stores/AsyncMultiselectStore';
import Licitante from '~/domains/Licitante';
import { showErrorNotification, showNotification } from 'fc/utils/utils';
import { action, observable, runInAction, computed } from 'mobx';
import TermoIndexStore from '../licitacao/termoIndex';
import CaronaLicitanteFormStore from '../caronaLicitante/formStore';
import AppStore from 'fc/stores/AppStore';
import moment from 'moment';
import EntidadeExterna from '~/domains/EntidadeExterna';
import EntidadeExternaService from '~/services/EntidadeExternaService';
import VencedorLicitacaoService from '~/services/VencedorLicitacaoService';
import TdaCaronaService from '~/services/TdaCaronaService';
import SearchTypes from 'fc/components/AdvancedSearch/SearchTypes';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import VencedoresFormStore from '../vencedores/formStore';
import VencedorLicitacaoIndexStore from '../vencedor/indexStore';
import ObrigatoriedadeArquivoService from '~/services/ObrigatoriedadeArquivoService';
import SearchOperators from 'fc/components/AdvancedSearch/SearchOperators';
import MultipleFileUploaderStore from 'fc/stores/MultipleFileUploaderStore';
import AccessPermission from '~/constants/AccessPermission';
import CaronaVencedorFormStore from './caronaVencedor/formStore';
import LicitacaoSrpIndexStore from './licitacaoSrp/indexStore';
import LicitacaoService from '~/services/LicitacaoService';
import Usuario from '~/domains/Usuario';
import UsuarioService from '~/services/UsuarioService';
import Esfera from '~/domains/Esfera';
import EsferaService from '~/services/EsferaService';

class CaronaFormStore extends FormBase {
  @observable permissionsEntities;

  @observable entidadeOrigemExternaStore;
  @observable entidadeOrigemStore;
  @observable detentoresStore;
  @observable loadingFiles = false;
  @observable anos = [];
  @observable idCarona;
  @observable arquivoCaronaList = [];
  @observable enableReqMod = false;
  @observable entidadesFiltradas;
  @observable arquivosTdaCarona = [];
  @observable fontesRecursos = [];
  @observable dialogOpen = true;
  @observable responsavelAdesaoStore;
  @observable responsaveisCarona;
  @observable idAlerta = null;

  constructor() {
    super(CaronaService, Carona);

    this.entidadeOrigemStore = new AsyncDropDownStore(Entidade, EntidadeService, 'nome', 'id');
    this.entidadeOrigemExternaStore = new AsyncDropDownStore(
      EntidadeExterna,
      EntidadeExternaService,
      'nomeEntidadeExterna',
      'id'
    );
    this.licitanteStore = new AsyncMultiselectStore(Licitante, LicitanteService, 'nome', 'id');
    this.responsavelAdesaoStore = new AsyncDropDownStore(Usuario, UsuarioService, 'nome', 'id', {
      sort: {
        by: 'nome',
        order: 'asc',
      },
      andParameters: [{ field: 'entidadesTituladas', operator: 'INCLUDES', value: AppStore.getContextEntity()?.id }],
    });
    this.fileStore = new MultipleFileUploaderStore(
      [],
      (file) => CaronaService.upload(file),
      (fileDTO, countDownload) => CaronaService.download(fileDTO, countDownload),
      (idArquivo) => this.removerArquivoCarona(idArquivo),
      (idArquivo, arquivoCaronaDTO) => this.atualizarArquivoCarona(idArquivo, arquivoCaronaDTO)
    );
    this.fileStoreTda = new MultipleFileUploaderStore(
      [],
      (file) => TdaCaronaService.upload(file),
      (fileDTO) => TdaCaronaService.download(fileDTO)
    );
    this.fileStoreLicitacao = new MultipleFileUploaderStore([], [], (fileDTO) => LicitacaoService.download(fileDTO));
    this.termoIndexStore = new TermoIndexStore();
    this.licitacaoSrpIndexStore = new LicitacaoSrpIndexStore();
    this.caronaLicitanteStore = new CaronaLicitanteFormStore();
    this.vencedoresIndexStore = new VencedorLicitacaoIndexStore();
    this.detentoresStore = new VencedoresFormStore();
    this.caronaVencedorStore = new CaronaVencedorFormStore();

    this.removerArquivoCarona = this.removerArquivoCarona.bind(this);
    this.atualizarArquivoCarona = this.atualizarArquivoCarona.bind(this);
    this.getEntidadesFiltradas = this.getEntidadesFiltradas.bind(this);
    this.setCarona = this.setCarona.bind(this);
  }

  @action
  initializeArquivos(idCarona, callback) {
    this.idCarona = idCarona;
    if (idCarona) {
      this.service
        .recuperarArquivos(idCarona)
        .then((response) =>
          runInAction(() => {
            const arquivosRecuperados = response.data;
            this.fileStore.initialize(arquivosRecuperados);
            this.arquivoCaronaList = arquivosRecuperados;
          })
        )
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        )
        .finally(() =>
          runInAction(() => {
            callback && callback();
          })
        );
    }
  }

  initializeTdaCarona(idCarona) {
    if (idCarona && this.hasPermissionTda()) {
      TdaCaronaService.tdaCaronaByIdCarona(idCarona)
        .then((response) =>
          runInAction(() => {
            if (response && response.data) {
              const tdaCarona = response.data;
              this.carregaArquivosTda(tdaCarona);
            }
          })
        )
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        );
    }
  }

  @action
  setArquivoCaronaList(arquivoCaronaList) {
    this.arquivoCaronaList = arquivoCaronaList;
  }

  @action
  carregaArquivosTda(tdaCarona) {
    if (tdaCarona) {
      TdaCaronaService.recuperarArquivos(tdaCarona.id)
        .then((response) =>
          runInAction(() => {
            if (response && response.data) {
              const arquivosRecuperados = response.data.map((arq) => {
                return {
                  ...arq,
                  analista: tdaCarona.analista.nome,
                };
              });
              this.fileStoreTda.initialize(arquivosRecuperados);
              this.arquivosTdaCarona = arquivosRecuperados;
            }
          })
        )
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        );
    }
  }

  filtraArquivosLicitacaoObrigatorios(lei) {
    const arquivos = [
      { tipo: 'ATA_SESSAO', lei: ['LEI_N_14133', 'LEI_N_8666'] },
      { tipo: 'ATA_REGISTRO_PRECOS', lei: ['LEI_N_14133', 'LEI_N_8666'] },
      { tipo: 'EDITAL_PROJETO_BASICO', lei: ['LEI_N_14133', 'LEI_N_8666'] },
      { tipo: 'MAPA_COMPARATIVO_PRECOS', lei: ['LEI_N_14133'] },
      { tipo: 'MAPA_LANCES', lei: ['LEI_N_14133'] },
      { tipo: 'PESQUISA_PRECO_MERCADO', lei: ['LEI_N_14133'] },
      { tipo: 'PESQUISA_PRECO_PUBLICO', lei: ['LEI_N_14133'] },
      { tipo: 'ESTIMATIVA_PRECO', lei: ['LEI_N_8666'] },
      { tipo: 'PROPOSTAS_VENCEDORAS', lei: ['LEI_N_14133', 'LEI_N_8666'] },
      { tipo: 'TERMO_ADJUDICACAO', lei: ['LEI_N_14133', 'LEI_N_8666'] },
      { tipo: 'TERMO_HOMOLOGACAO', lei: ['LEI_N_14133', 'LEI_N_8666'] },
      { tipo: 'PROJETO_BASICO', lei: ['LEI_N_8666'] },
    ];

    const arquivosObrigatorios = arquivos.filter((l) => l.lei.includes(lei)).map((a) => a.tipo);

    return arquivosObrigatorios;
  }

  @action
  carregarArquivosLicitacao(callback) {
    this.loading = true;
    const arquivosObrigatorios = this.filtraArquivosLicitacaoObrigatorios(this.object.lei);

    LicitacaoService.recuperarArquivos(this.object.licitacao.id)
      .then((response) =>
        runInAction(() => {
          this.arquivosLicitacao = response.data.filter((arq) => arquivosObrigatorios.includes(arq.tipo));
          callback && callback();
          this.fileStoreLicitacao.initialize(this.arquivosLicitacao);
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      )
      .finally(() =>
        runInAction(() => {
          this.loading = false;
        })
      );
  }

  @action
  carregarArquivosLicitacaoById(idLicitacao, lei) {
    const arquivosObrigatorios = this.filtraArquivosLicitacaoObrigatorios(lei);

    LicitacaoService.recuperarArquivos(idLicitacao)
      .then((response) =>
        runInAction(() => {
          this.arquivosLicitacao = response.data.filter((arq) => arquivosObrigatorios.includes(arq.tipo));
          this.fileStoreLicitacao.initialize(this.arquivosLicitacao);
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      )
      .finally(() =>
        runInAction(() => {
          this.loading = false;
        })
      );
  }

  @action
  carregarFontesRecursos(callback) {
    this.loading = true;
    Promise.all([FonteRecursoService.getAll(), CaronaService.getAnosCarona()])
      .then((response) =>
        runInAction(() => {
          this.fontesRecursos = response[0].data;
          this.anos = response[1].data.map((ano) => ({ text: ano.toString(), value: ano }));
          callback && callback();
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      )
      .finally(() =>
        runInAction(() => {
          this.loading = false;
        })
      );
  }

  @action
  resetVencedores() {
    this.object.vencedores = [];
    this.object.detentores = [];
    this.object.licitantes = [];
  }

  @action
  updateNomeEntidadeOrigem() {
    if (!this.object.processoEntidadeOrigem) {
      this.object.entidadeOrigemExterna.nome = this.object.entidadeOrigemExterna.nomeEntidadeExterna;
    }
  }

  @action
  updateEntidadeOrigem(entidade) {
    if (entidade.entidadeInterna) {
      this.updateAttribute('entidadeOrigem', { ...entidade, id: entidade.idEntidade });
      this.updateAttribute('processoEntidadeOrigem', true);
      this.updateAttribute('entidadeOrigemExterna', null);
    } else {
      this.updateAttribute('entidadeOrigemExterna', { ...entidade, id: entidade.idEntidadeExterna });
      this.updateAttribute('processoEntidadeOrigem', false);
      this.updateAttribute('lei', 'LEI_N_14133');
      this.clearEntidadeInternaFields();
    }
    this.updateAttribute('numeroLicitacaoEntidadeExterna', null);
  }

  getEntidadeOrigem() {
    let entidadeOrigem = this.object.entidadeOrigem;
    if (!this.object.processoEntidadeOrigem) {
      entidadeOrigem = this.object.entidadeOrigemExterna;
    }
    return entidadeOrigem;
  }

  @action
  initizalizeDetentorStore() {
    if (this.object.processoEntidadeOrigem && this.object.licitacao?.termoReferencia) {
      this.caronaVencedorStore.initialize(
        this.object?.licitacao?.termoReferencia,
        this.object?.licitacao?.vencedores ?? [],
        this.object?.vencedores ?? [],
        this.object.detentores ?? [],
        (detentores) => {
          this.updateAttribute('detentores', detentores);
          this.setValor(this.detentoresStore.getValorTotal());
        }
      );
      !this.object.valor && this.setValor(this.caronaVencedorStore.getValorTotal());
    } else {
      if (this.object?.licitacao?.lei == 'LEI_N_8666') {
        this.object.licitantes = this.object.licitantes?.map((l) => {
          return { ...l, valorMaximo: this.object.licitacao.vencedores.find((v) => v.licitante.id == l.id)?.valor };
        });
      }
      this.detentoresStore.initialize(
        this.object?.termoReferencia,
        this.object.licitantes ?? [],
        this.object.detentores ?? [],
        [],
        [],
        this.object.tipoAdjudicacao,
        (detentores) => {
          this.updateAttribute('detentores', detentores);
          this.setValor(this.detentoresStore.getValorTotal());
        },
        () => {},
        () => {},
        (tipoAdjudicacao) => {
          this.updateAttribute('tipoAdjudicacao', tipoAdjudicacao);
        },
        this.object.processoMigrado
      );
      !this.object.valor && this.setValor(this.detentoresStore.getValorTotal());
    }
  }

  isProcessoMigrado() {
    return (
      this.object.processoMigrado ||
      (this.object.lei === 'LEI_N_8666' && !this.object.processoEntidadeOrigem) ||
      this.object?.licitacao?.lei === 'LEI_N_8666'
    );
  }

  @action
  setLicitantes(l) {
    this.object.vencedores = l;
    this.object.licitantes = l?.map((l) => l.licitante);
  }

  @action
  setCarona(carona, callback) {
    if (carona) {
      this.object = carona;
      callback && callback();
    }
  }

  @action
  setValor(valor) {
    this.object.valor = valor;
  }

  @action
  updateLicitantes() {
    const idsUnicos = new Set();
    const licitantes = this.object.detentores
      .map((f) => f.licitante)
      .filter((licitante) => {
        if (idsUnicos.has(licitante.id)) {
          return false;
        } else {
          idsUnicos.add(licitante.id);
          return true;
        }
      });

    this.object.licitantes = licitantes;
  }

  @action
  recuperaVencedores() {
    const filtro = {
      page: { index: 1, size: this.object.detentores.length },
      andParameters: [],
    };
    this.object.detentores.forEach((d) => {
      filtro.andParameters.push({
        field: 'licitante',
        operator: SearchOperators.EQUAL_TO.value,
        value: d.licitante?.id,
      });
    });
    this.loading = true;
    VencedorLicitacaoService.advancedSearch(filtro)
      .then(({ data }) =>
        runInAction(() => {
          this.object.vencedores = data.items?.filter((v) =>
            this.object.detentores.map((d) => d.licitante.id).includes(v.licitante.id)
          );
        })
      )
      .catch((error) => showErrorNotification(error))
      .finally(() =>
        runInAction(() => {
          this.loading = false;
        })
      );
  }

  rulesDefinition() {
    const isEntidadeInterna = this.object?.processoEntidadeOrigem === true;

    let rules = {
      processoEntidadeOrigem: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      numeroProcessoGerenciadorAta: [
        { rule: 'required', message: 'Por favor, preencha o campo' },
        { rule: 'isMaxLength', maxLength: 255, message: 'Por favor, diminua o tamanho do campo' },
      ],
      numeroProcessoAdministrativo: [
        { rule: 'required', message: 'Por favor, preencha o campo' },
        { rule: 'isMaxLength', maxLength: 255, message: 'Por favor, diminua o tamanho do campo' },
      ],
      objeto: [
        { rule: 'required', message: 'Por favor, preencha o campo' },
        { rule: 'isMaxLength', maxLength: 4000, message: 'Por favor, diminua o tamanho do campo' },
      ],
      fontesDeRecurso: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      observacoes: [{ rule: 'isMaxLength', maxLength: 4000, message: 'Por favor, diminua o tamanho do campo' }],
      justificativa: [{ rule: 'isMaxLength', maxLength: 4000, message: 'Por favor, diminua o tamanho do campo' }],
      dataAdesao: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      dataValidadeAta: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      naturezasDoObjeto: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      fundamentacaoLegal: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      anoCarona: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
    };

    this.object.lei === 'OUTRA' &&
      (rules = this.mergeRules(rules, {
        legislacaoOutros: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      }));

    if (this.object?.processoMigrado) {
      rules = this.mergeRules(rules, {
        naturezasDoObjeto: [!isEntidadeInterna && { rule: 'required', message: 'Por favor, preencha o campo' }],
        valor: !this.object.termoReferencia ? [{ rule: 'required', message: 'Por favor, preencha o campo' }] : [],
        fundamentacaoLegal: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
        responsavelAdesao: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      });
    } else {
      rules = this.mergeRules(rules, {
        processoEntidadeOrigem: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
        entidadeOrigem: [isEntidadeInterna && { rule: 'required', message: 'Por favor, preencha o campo' }],
        entidadeOrigemExterna: [!isEntidadeInterna && { rule: 'required', message: 'Por favor, preencha o campo' }],
        licitacao: [isEntidadeInterna && { rule: 'required', message: 'Por favor, preencha o campo' }],
        idResponsavelAdesaoCarona: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      });

      if (!this.object.processoEntidadeOrigem) {
        rules = this.mergeRules(rules, {
          lei: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
        });

        if (this.object.lei !== 'LEI_N_8666') {
          rules = this.mergeRules(rules, {
            termoReferencia: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
          });
        }
      }

      if (this.object.entidadeOrigemExterna) {
        rules = this.mergeRules(rules, {
          numeroLicitacaoEntidadeExterna: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
        });
      }
    }
    return rules;
  }

  getFilterSuggestOrgaoGerenciador() {
    const entidade = AppStore.getContextEntity();

    const filterSuggest = [
      {
        id: '',
        field: 'entidadeInterna',
        operator: 'EQUAL_TO',
        value: this.object.processoEntidadeOrigem,
        formatted: '',
        fixed: true,
        invisible: true,
        completeParam: {
          field: 'entidadeInterna',
          label: 'entidadeInterna',
          type: SearchTypes.ENUM,
          options: DadosEstaticosService.getSimNao(),
        },
      },
    ];

    if (this.object.processoEntidadeOrigem) {
      filterSuggest.push({
        id: '',
        field: 'idEntidade',
        operator: 'NOT_EQUAL_TO',
        value: entidade?.id,
        formatted: '',
        fixed: true,
        invisible: true,
        completeParam: {
          field: 'idEntidade',
          label: 'idEntidade',
          type: SearchTypes.NUMBER,
        },
      });

      if (entidade?.esfera?.nome == 'Estadual') {
        filterSuggest.push({
          id: '',
          field: 'esfera',
          operator: 'EQUAL_TO',
          value: entidade.esfera,
          formatted: '',
          fixed: true,
          invisible: true,
          completeParam: {
            field: 'esfera',
            label: 'Esfera',
            type: SearchTypes.ASYNC_QUERY,
            store: new AsyncDropDownStore(Esfera, EsferaService, 'nome', 'id'),
          },
        });
      }
    }

    return filterSuggest;
  }

  getFilterSuggestTermoReferencia() {
    const filterSuggest = [
      {
        id: '',
        field: 'disponivel',
        operator: 'EQUAL_TO',
        value: true,
        formatted: '',
        fixed: true,
        invisible: true,
        completeParam: {
          field: 'disponivel',
          label: 'Disponível',
          type: SearchTypes.ENUM,
          options: DadosEstaticosService.getSimNao(),
        },
      },
      {
        id: '',
        field: 'modelo',
        operator: 'EQUAL_TO',
        value: false,
        formatted: '',
        fixed: true,
        invisible: true,
        completeParam: {
          field: 'modelo',
          label: 'Modelo',
          type: SearchTypes.ENUM,
          options: DadosEstaticosService.getSimNao(),
        },
      },
    ];

    const entidade = AppStore.getContextEntity();
    if (entidade) {
      filterSuggest.push({
        id: '',
        field: 'entidade',
        operator: 'EQUAL_TO',
        value: entidade,
        formatted: '',
        fixed: true,
        completeParam: {
          field: 'entidade',
          label: 'Órgão/Entidade',
          type: SearchTypes.ASYNC_QUERY,
          store: new AsyncDropDownStore(Entidade, EntidadeService, 'nome', 'id'),
        },
      });
    } else if (this.object?.entidade) {
      filterSuggest.push({
        id: '',
        field: 'entidade',
        operator: 'EQUAL_TO',
        value: this.object.entidade,
        formatted: '',
        fixed: true,
        completeParam: {
          field: 'entidade',
          label: 'Órgão/Entidade',
          type: SearchTypes.ASYNC_QUERY,
          store: new AsyncDropDownStore(Entidade, EntidadeService, 'nome', 'id'),
        },
      });
    }
    return filterSuggest;
  }

  getFilterSuggestLicitacao() {
    const filterSuggestLicitacao = [
      {
        id: '',
        field: 'entidade',
        operator: 'EQUAL_TO',
        value: this.object?.entidadeOrigem,
        formatted: '',
        fixed: true,
        invisible: true,
        completeParam: {
          field: 'entidade',
          label: 'Órgão/Entidade',
          type: SearchTypes.ASYNC_QUERY,
          store: new AsyncDropDownStore(Entidade, EntidadeService, 'nome', 'id'),
        },
      },
    ];
    return filterSuggestLicitacao;
  }

  @action
  loadTipos(callback) {
    if (this.object.lei) {
      let filtros = [];
      if (this.object?.processoMigrado) {
        filtros = ['ANTIGA'];
      } else {
        filtros =
          this.object?.naturezasDoObjeto && this.object?.naturezasDoObjeto.includes('OBRAS')
            ? [
                this.object?.lei,
                this.object?.processoEntidadeOrigem ? 'ORGAO_INTERNO' : 'ORGAO_NAO_INTERNO',
                'NATUREZAOBJ_OBRA',
              ]
            : [this.object?.lei, this.object?.processoEntidadeOrigem ? 'ORGAO_INTERNO' : 'ORGAO_NAO_INTERNO'];
      }
      ObrigatoriedadeArquivoService.getArquivosObrigatorios({ tipoProcesso: 'CARONA', filtros: filtros })
        .then((response) =>
          runInAction(() => {
            const obrgArquivo = response?.data;
            const filteredList = DadosEstaticosService.getTipoArquivoCarona().filter((aqr) =>
              obrgArquivo.some((obg) => obg.arquivoEnum === aqr.value)
            );
            this.fileStore.tipoArquivoEnum = filteredList.map((aqr) => {
              if (obrgArquivo.find((obg) => obg.arquivo === aqr.text)?.obrigatorio) {
                aqr.text = '* ' + aqr.text;
                aqr.obrigatorio = true;
              }
              return aqr;
            });
            callback && callback();
          })
        )
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        );
    } else {
      this.fileStore.tipoArquivoEnum = [];
      callback && callback();
    }
  }

  removerArquivoCarona(idArquivo) {
    return CaronaService.removerArquivo(this.idCarona, idArquivo);
  }

  atualizarArquivoCarona(idArquivo, arquivoCaronaDTO) {
    return CaronaService.atualizarArquivo(this.idCarona, idArquivo, arquivoCaronaDTO);
  }

  @action
  setArquivosCaronaList(arquivosCaronaList) {
    this.arquivoCaronaList = arquivosCaronaList;
  }

  @computed
  get renderLabelNumeroCarona() {
    const numAta = this.object.licitacao.numero + '/' + this.object.licitacao.ano;
    return this.object.licitacao ? numAta : this.object.numeroProcessoGerenciadorAta;
  }

  getFiltroArquivos() {
    let filtros = {};
    if (this.object?.processoMigrado) {
      filtros = { tipoCarona: 'ANTIGA' };
    } else {
      filtros = {
        lei: this.object.lei,
        tipoOrgao: this.object?.processoEntidadeOrigem ? 'ORGAO_INTERNO' : 'ORGAO_NAO_INTERNO',
        naturezaObjeto:
          this.object?.naturezasDoObjeto && this.object?.naturezasDoObjeto?.includes('OBRAS')
            ? 'NATUREZAOBJ_OBRA'
            : undefined,
      };
    }

    return filtros;
  }

  save(callback, type = 'edit') {
    this.loading = true;
    const saveObject = this.getObjectToSave(type);
    const caronaDTO = {
      id: this.object.id,
      carona: this.object,
      arquivosCarona: this.arquivoCaronaList,
      filtros: this.getFiltroArquivos(),
    };
    this.object.dataAdesao = !this.object.dataAdesao ? moment()._d : this.object.dataAdesao;
    if (!Object.keys(saveObject).length) {
      showNotification('warn', null, 'Nenhuma alteração realizada.');
      this.loading = false;
    } else {
      this.service
        .criarCarona(caronaDTO)
        .then(() =>
          runInAction(() => {
            callback && callback();
            showNotification('success', null, 'Registro salvo com sucesso!');
          })
        )
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        )
        .finally(() =>
          runInAction(() => {
            this.loading = false;
          })
        );
    }
  }

  @action
  validaArquivos(callback) {
    this.loading = true;
    this.service
      .validaArquivos({
        arquivosCarona: this.arquivoCaronaList,
        filtros: this.getFiltroArquivos(),
      })
      .then(() =>
        runInAction(() => {
          callback && callback();
        })
      )
      .catch((error) => showErrorNotification(error))
      .finally(() =>
        runInAction(() => {
          this.loading = false;
        })
      );
  }

  @action
  atualizaDetentores(detentores) {
    this.object.licitantes = detentores.map((d) => d.licitante);
    this.object.vencedores = detentores;
    const removidos = this.object.detentores?.filter(
      (d) => !detentores.map((det) => det.licitante.id).includes(d.licitante.id)
    );
    removidos?.forEach((f) => this.removeDetentor(f));
  }

  checkDetentorIsVencedor(detentor) {
    return this.object.detentores?.some(
      (f) => f.licitante.id == detentor.id || f.licitante.id == detentor?.licitante?.id
    );
  }

  @action
  removeDetentor(detentor) {
    this.object.licitantes = this.object.licitantes.filter((l) => l.id !== detentor.licitante.id);
    this.object.vencedores = this.object.vencedores.filter((v) => v.licitante.id !== detentor.licitante.id);
    this.object.detentores = this.object.detentores?.filter((d) => d.licitante.id !== detentor.licitante.id);
    this.updateLicitantesFornecedoresVencedoresStore();
  }

  @action
  updateLicitantesFornecedoresVencedoresStore() {
    if (this.detentoresStore) {
      this.detentoresStore.licitantes = this.object.licitantes;
      this.detentoresStore.vencedores = this.object.detentores;
    } else if (this.caronaVencedorStore) {
      this.caronaVencedorStore.vencedoresSelected = this.object?.vencedores;
      this.caronaVencedorStore.detentores = this.object.detentores;
    }
  }

  @action
  removeLicitante(licitante) {
    if (licitante) {
      this.object.licitantes = this.object.licitantes.filter((elem) => !(elem.id === licitante.id));
      this.object.detentores = this.object.detentores?.filter((elem) => !(elem.licitante.id === licitante.id));
    }
  }

  @action
  clearEntidadeInternaFields() {
    this.updateAttribute('licitacao', undefined);
    this.updateAttribute('objeto', '');
    this.updateAttribute('naturezasDoObjeto', undefined);
    this.updateAttribute('entidadeOrigem', undefined);
    this.updateAttribute('fontesDeRecurso', undefined);
  }

  @action
  setLicitacaoFields(licitacao) {
    this.updateAttribute('objeto', licitacao.objeto);
    this.updateAttribute('naturezasDoObjeto', licitacao.naturezasDoObjeto);
  }

  validaDadosBasicos() {
    const rules = this.rulesDefinition();
    const dadosBasicos = Object.keys(rules).filter((k) => rules[k].find((r) => r.rule === 'required'));
    const required = Object.keys(rules).filter(
      (k) => dadosBasicos.includes(k) && rules[k].find((r) => r.rule === 'required')
    );
    const campos = required.filter(
      (k) =>
        this.object[k] === undefined ||
        this.object[k] === null ||
        this.object[k] === '' ||
        (Array.isArray(this.object[k]) && this.object[k].length === 0)
    );
    return campos.length === 0;
  }

  validaFornecedores() {
    return this.object.licitantes?.length > 0;
  }

  dataAdesaoIsValid() {
    const { dataAdesao, dataValidadeAta } = this.object;
    return moment(dataAdesao) > moment(dataValidadeAta);
  }

  checkDataCadastro() {
    let data = this.object?.dataCadastro;
    if (data) {
      const current = moment();
      const diff = moment.duration(current.diff(data));

      this.enableReqMod = diff.asHours() > 24;
    }
  }

  @computed
  get licitanteListKeyed() {
    return this.object?.licitantes?.map((item, idx) => {
      item.key = idx;
      return item;
    });
  }

  @action
  getEntidadesFiltradas(event, callback) {
    const query = event.query.trim();

    const queryParams = {
      andParameters: [
        {
          id: '',
          field: 'nome',
          operator: 'CONTAINS',
          value: query,
          formatted: '',
        },
      ],
    };

    EntidadeService.advancedSearch(queryParams)
      .then((response) =>
        runInAction(() => {
          this.entidadesFiltradas = response.data?.items;
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      )
      .finally(() =>
        runInAction(() => {
          callback && callback();
        })
      );
  }

  @action
  getPessoasResponsaveis() {
    const entidade = AppStore.getContextEntity();
    entidade &&
      CaronaService.getPessoasResponsaveis(entidade?.id)
        .then((response) =>
          runInAction(() => {
            this.responsaveisCarona = response.data;
            if (
              this.object.idResponsavelAdesaoCarona &&
              this.object.responsavelAdesao &&
              !this.responsaveisCarona.some(
                (pessoa) =>
                  pessoa.id == this.object.idResponsavelAdesaoCarona && pessoa.nome == this.object.responsavelAdesao
              )
            ) {
              this.responsaveisCarona?.push({
                nome: this.object.responsavelAdesao,
                id: this.object.idResponsavelAdesaoCarona,
              });
            }
          })
        )
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        );
  }

  @action
  updateResponsavelCaronaAttribute(attribute, event) {
    let value = event;
    if (event && event.value) {
      value = event.value;
    }
    if (event && event.target) {
      value = event.target.value;
    }
    this.object[attribute] = value;
    const nomePessoaResponsavel = this.responsaveisCarona.find((pessoa) => pessoa.id == value)?.nome;
    this.object['responsavelAdesao'] = nomePessoaResponsavel;
  }

  hasPermissionTda() {
    return AppStore.hasPermission([AccessPermission.cadastrarTda.readPermission]);
  }

  hasPermissionAlerta() {
    return AppStore.hasPermission([AccessPermission.alerta.readPermission]);
  }

  @action
  hasAlert(idCarona) {
    idCarona &&
      this.service
        .getAlertaCarona(idCarona)
        .then((alerta) => {
          runInAction(() => {
            if (alerta?.data) {
              this.idAlerta = alerta.data;
            }
          });
        })
        .catch(() => {
          runInAction(() => {
            showErrorNotification('Ocorreu um erro ao buscar o id do alerta.');
          });
        });
  }
}

export default CaronaFormStore;
