import { action, computed, makeObservable, observable } from 'mobx';

class CaronaVencedorFormStore {
  @observable vencedores = [];
  @observable vencedor;
  @observable lote;
  @observable vencedoresSelected;
  @observable vencedoresGrouped;
  @observable detentores;

  @observable edit = false;

  onChangeVencedores;

  constructor() {
    makeObservable(this);
  }

  @action
  initialize(termoReferencia, vencedores = [], vencedoresSelected = [], detentores = [], onChangeVencedores) {
    this.termoReferencia = termoReferencia;
    this.vencedores = vencedores;
    this.vencedoresSelected = vencedoresSelected;
    this.detentores = detentores?.map((detentor) => this.getLoteByVencedor(detentor));
    this.onChangeVencedores = onChangeVencedores;
    this.initializeVencedoresGrouped();
  }

  @action
  initializeVencedoresGrouped() {
    this.vencedoresGrouped = this.itensGroupByVencedor()?.filter((v) =>
      this.vencedoresSelected.some((ls) => ls.licitante.id == v.licitante.id)
    );

    this.vencedor = this.vencedoresGrouped?.length && this.vencedoresGrouped[0];
  }

  itensGroupByVencedor() {
    const vencedoresDistinct = [];
    this.vencedores?.map((v) => {
      if (vencedoresDistinct.find((vencedor) => vencedor.licitante.id == v.licitante.id)) {
        const vencedorPersitedIndex = vencedoresDistinct.findIndex(
          (vencedor) => vencedor.licitante.id == v.licitante.id
        );
        const vencedorSelected = vencedoresDistinct[vencedorPersitedIndex];
        const vencedorByLote = this.getLoteByVencedor(v);
        vencedorSelected.itens.push(vencedorByLote);
        vencedoresDistinct[vencedorPersitedIndex] = vencedorSelected;
      } else {
        const vencedorByLote = this.getLoteByVencedor(v);
        const vencedor = { licitante: v.licitante, itens: [vencedorByLote] };
        vencedoresDistinct.push(vencedor);
      }
    });
    return vencedoresDistinct;
  }

  @computed
  get itensGroupByDetentor() {
    const vencedoresDistinct = [];
    this.detentores?.map((v) => {
      if (vencedoresDistinct.find((vencedor) => vencedor.licitante.id == v.licitante.id)) {
        const vencedorPersitedIndex = vencedoresDistinct.findIndex(
          (vencedor) => vencedor.licitante.id == v.licitante.id
        );
        const vencedorSelected = vencedoresDistinct[vencedorPersitedIndex];
        const vencedorByLote = this.getLoteByVencedor(v);
        vencedorSelected.itens.push(vencedorByLote);
        vencedoresDistinct[vencedorPersitedIndex] = vencedorSelected;
      } else {
        const vencedorByLote = this.getLoteByVencedor(v);
        const vencedor = { licitante: v.licitante, itens: [vencedorByLote] };
        vencedoresDistinct.push(vencedor);
      }
    });
    return vencedoresDistinct;
  }

  getLoteByVencedor(vencedor) {
    let result;
    this.termoReferencia?.lotes?.forEach((l) => {
      if (l.itens.some((i) => i.id == vencedor.itemLote.id)) {
        const loteCopy = { ...l };
        result = { ...vencedor, lote: loteCopy };
      }
    });
    return result;
  }

  @action
  setVencedor(v) {
    this.vencedor = v;
  }

  @action
  setLote(l) {
    this.lote = l;
  }

  @action
  onChangePickList(e) {
    let itensVencedor = [];

    e.target.forEach((iv) => {
      if (!this.detentores.some((v) => v.licitante.id == iv.licitante.id && v.itemLote.id == iv.itemLote.id)) {
        let itemVencedor = {
          motivoAlteracao: '',
          obsMotivoAlteracao: '',
          licitante: iv.licitante,
          itemLote: iv.itemLote,
          valor: iv.valor,
          quantidade: iv.quantidade,
          valorUnitario: iv.valorUnitario,
          valor: parseFloat(iv.quantidade) * parseFloat(iv.valorUnitario),
          marcaModelo: iv.marcaModelo,
          especificacao: iv.especificacao,
          observacao: '',
          desconto: 0,
          preenchido: false,
          quantidade: iv.quantidade,
          descricaoComplementar: iv.descricaoComplementar ?? '',
        };
        itemVencedor = this.getLoteByVencedor(itemVencedor);
        itensVencedor.push(itemVencedor);
      }
    });

    this.detentores = [...this.detentores, ...itensVencedor].filter(
      (d) =>
        d.licitante.id != this.vencedor.licitante.id ||
        e.target?.some((e) => e.itemLote.id === d.itemLote.id && e.licitante.id === d.licitante.id)
    );

    this.onChangeVencedores(this.detentores);
  }

  getQuantidadeFifty(quantidade, fracionario = false) {
    return fracionario ? parseFloat((quantidade / 2).toFixed(2)) : Math.floor(quantidade / 2);
  }

  @action
  updateAttributeItem(idItemLote, idLicitante, attribute, event) {
    let value = event;
    if (event && event.value) {
      value = event.value;
    }
    if (event && event.target) {
      value = event.target.value;
    }

    const indexVencedor = this.detentores.findIndex(
      (v) => v.itemLote.id === idItemLote && v.licitante.id == idLicitante
    );

    if (attribute == 'quantidade' || attribute == 'valorUnitario' || attribute == 'desconto') {
      const vencedorLicitacao = this.vencedores.find(
        (v) => v.itemLote.id === idItemLote && v.licitante.id == idLicitante
      );

      const qtdTotal = this.getQuantidadeFifty(vencedorLicitacao.quantidade, vencedorLicitacao.itemLote.fracionario);

      if (attribute == 'quantidade') {
        if (parseFloat(value) <= parseFloat(qtdTotal)) {
          this.detentores[indexVencedor][attribute] = value;
        } else {
          this.detentores[indexVencedor][attribute] = qtdTotal;
        }
      } else if (attribute == 'valorUnitario' || attribute == 'desconto') {
        this.detentores[indexVencedor][attribute] = value;
      }

      const valor = parseFloat(
        (
          this.detentores[indexVencedor].valorUnitario * this.detentores[indexVencedor].quantidade -
          (this.detentores[indexVencedor].valorUnitario *
            this.detentores[indexVencedor].quantidade *
            this.detentores[indexVencedor].desconto) /
            100
        ).toFixed(3)
      );

      this.updateAttributeItem(idItemLote, idLicitante, 'valor', valor);
    } else {
      this.detentores[indexVencedor][attribute] = value;
    }

    this.onChangeVencedores(this.detentores);
  }

  @action
  setDescontoItem(idItemLote, idLicitante, desconto, attr = 'desconto') {
    if (desconto > 100) {
      desconto = 100;
    }
    this.updateAttributeItem(idItemLote, idLicitante, attr, desconto);
    this.onChangeVencedores(this.detentores);
  }

  @computed
  get hasLicitantesNotAssign() {
    return (
      !this.vencedoresSelected ||
      this.vencedoresSelected?.some((l) => !this.detentores?.some((d) => d.licitante.id === l.licitante.id))
    );
  }

  @computed
  get hasItensNotFilleds() {
    return this.detentores?.some((v) => !v.preenchido);
  }

  getValorTotal() {
    const valorTotal = (this.detentores || []).reduce((acc, vencedor) => {
      const valor = vencedor.valor;
      return acc + parseFloat(valor);
    }, 0);
    return Number.isNaN(valorTotal) ? 0 : valorTotal;
  }
}

export default CaronaVencedorFormStore;
