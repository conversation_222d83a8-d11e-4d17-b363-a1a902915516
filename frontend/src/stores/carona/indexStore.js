import SearchTypes from 'fc/components/AdvancedSearch/SearchTypes';
import Entidade from '~/domains/Entidade';
import Licitante from '~/domains/Licitante';
import CaronaView from '~/domains/CaronaView';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import EntidadeService from '~/services/EntidadeService';
import LicitanteService from '~/services/LicitanteService';
import FonteRecursoService from '~/services/FonteRecursoService';
import CaronaService from '~/services/CaronaService';
import CaronaViewService from '~/services/CaronaViewService';
import AsyncDropDownStore from 'fc/stores/AsyncDropdownStore';
import IndexBase from 'fc/stores/IndexBase';
import AppStore from 'fc/stores/AppStore';
import moment from 'moment';
import AsyncMultiselectStore from 'fc/stores/AsyncMultiselectStore';
import FonteRecurso from '~/domains/FonteRecurso';
import { checkUserGroup, showErrorNotification } from 'fc/utils/utils';
import { runInAction } from 'mobx';

class CaronaIndexStore extends IndexBase {
  constructor() {
    super(CaronaViewService, CaronaView, 'updatedAt', 'desc');
  }
  getAdvancedSearchParams() {
    const isAuditor = checkUserGroup(['Auditor', 'Auditor - Demais Inspetorias']);
    let searchParams = [];

    if (isAuditor) {
      searchParams = [
        {
          field: 'entidade',
          label: 'Órgão/Entidade',
          type: SearchTypes.ASYNC_QUERY,
          store: new AsyncDropDownStore(Entidade, EntidadeService, 'nome', 'id'),
        },
      ];
    }
    searchParams.push(
      {
        field: 'numeroProcessoAdministrativo',
        label: 'Número do Processo Administrativo (Aderente)',
        type: SearchTypes.TEXT,
      },
      {
        field: 'dataAdesao',
        label: 'Data de Adesão',
        type: SearchTypes.DATE,
      },
      {
        field: 'dataValidadeAta',
        label: 'Data de Validade da Ata',
        type: SearchTypes.DATE,
      },
      {
        field: 'objeto',
        label: 'Objeto',
        type: SearchTypes.TEXT,
      },
      {
        field: 'naturezasDoObjeto',
        label: 'Natureza do Objeto',
        type: SearchTypes.ENUM,
        options: DadosEstaticosService.getNaturezaObjetoLicitacao(),
      },
      {
        field: 'valor',
        label: 'Valor',
        type: SearchTypes.NUMBER,
      },
      {
        field: 'numProcessoGerenciadorAta',
        label: 'Número do Processo Gerenciador da Ata',
        type: SearchTypes.TEXT,
      },
      {
        field: 'observacoes',
        label: 'Observações',
        type: SearchTypes.TEXT,
      },
      {
        field: 'dataCadastro',
        label: 'Data de Cadastro',
        type: SearchTypes.DATE,
      },
      {
        field: 'licitantes',
        label: 'Detentor',
        type: SearchTypes.ASYNC_QUERY,
        store: new AsyncMultiselectStore(Licitante, LicitanteService, 'nome', 'id'),
      },
      {
        field: 'fontesDeRecurso',
        label: 'Fonte de Recurso',
        type: SearchTypes.ASYNC_QUERY,
        store: new AsyncMultiselectStore(FonteRecurso, FonteRecursoService, 'nome', 'id'),
      }
    );
    return searchParams;
  }

  getFilterSuggest() {
    const filterSuggest = [
      {
        id: '',
        field: 'status',
        operator: 'NOT_EQUAL_TO',
        value: 'REMOVIDA',
        formatted: '',
        fixed: true,
        invisible: true,
        completeParam: {
          field: 'status',
          label: 'Status',
          type: SearchTypes.ENUM,
          options: DadosEstaticosService.getStatusContrato(),
        },
      },
    ];

    const entidade = AppStore.getContextEntity();
    if (entidade) {
      filterSuggest.push({
        id: '',
        field: 'entidade',
        operator: 'EQUAL_TO',
        value: entidade,
        formatted: '',
        fixed: true,
        invisible: true,
        completeParam: {
          field: 'entidade',
          label: 'Órgão/Entidade',
          type: SearchTypes.ASYNC_QUERY,
          store: new AsyncDropDownStore(Entidade, EntidadeService, 'nome', 'id'),
        },
      });
    }

    return filterSuggest;
  }

  isRequisicaoModificacao(dataCadastro) {
    let result = false;
    if (dataCadastro) {
      const data = moment(dataCadastro);
      const current = moment();
      const diff = moment.duration(current.diff(data));

      if (diff.asHours() > 24) {
        result = true;
      }
    }
    return result;
  }

  getById(id, callback) {
    if (id) {
      CaronaService.getById(id)
        .then((response) => {
          return callback && runInAction(() => callback(response.data));
        })
        .catch((e) => {
          showErrorNotification(e);
        });
    }
  }
}

export default CaronaIndexStore;
