import FormBase from 'fc/stores/FormBase';
import UsuarioService from '~/services/UsuarioService';
import TdaService from '~/services/TdaService';
import TdaLicitacaoService from '~/services/TdaLicitacaoService';
import TdaCaronaService from '~/services/TdaCaronaService';
import TdaInexigibilidadeService from '~/services/TdaInexigibilidadeService';
import TdaDispensaService from '~/services/TdaDispensaService';
import UsuarioAuditorViewService from '~/services/UsuarioAuditorViewService';
import Tda from '~/domains/Tda';
import AsyncDropDownStore from 'fc/stores/AsyncDropdownStore';
import { action, observable, override, runInAction } from 'mobx';
import { checkUserContextIsInspetor, showErrorNotification } from 'fc/utils/utils';
import UsuarioAuditorView from '~/domains/UsuarioAuditorView';
import TdaCredenciamentoService from '~/services/TdaCredenciamentoService';

class TdaFormStore extends FormBase {
  @observable loadingTda = false;

  constructor(tipoProcesso) {
    let service = null;
    if (tipoProcesso === 'licitacao') service = TdaLicitacaoService;
    else if (tipoProcesso === 'carona') service = TdaCaronaService;
    else if (tipoProcesso === 'dispensa') service = TdaDispensaService;
    else if (tipoProcesso === 'inexigibilidade') service = TdaInexigibilidadeService;
    else if (tipoProcesso === 'credenciamento') service = TdaCredenciamentoService;

    super(service, Tda);
    this.tdaService = TdaService;

    const optionsAtribuidor = {
      andParameters: [
        {
          field: 'nomeGrupo',
          operator: 'EQUAL_TO',
          value: 'Inspetor',
        },
      ],
    };
    const optionsAuditor = {
      andParameters: [
        {
          field: 'nomeGrupo',
          operator: 'EQUAL_TO',
          value: 'Auditor',
        },
      ],
    };
    this.atribuidorStore = new AsyncDropDownStore(
      UsuarioAuditorView,
      UsuarioAuditorViewService,
      'nome',
      'id',
      optionsAtribuidor
    );

    this.auditorStore = new AsyncDropDownStore(
      UsuarioAuditorView,
      UsuarioAuditorViewService,
      'nome',
      'id',
      optionsAuditor
    );

    this.setTda = this.setTda.bind(this);
  }

  rulesDefinition() {
    return {
      tda: [
        { rule: 'required', message: 'Por favor, preencha o campo' },
        { rule: 'isMaxLength', maxLength: 100, message: 'Por favor, diminua o tamanho do campo' },
      ],
      dataTda: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      setor: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      analista: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      responsavel: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      dataInicio: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      dataFinal: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      observacao: [{ rule: 'isMaxLength', maxLength: 4000, message: 'Por favor, diminua o tamanho do campo' }],
    };
  }

  @override
  initialize(id, defaultValues = {}, callback, userDetails) {
    if (!id) {
      this.object = Object.assign({}, defaultValues);
      callback && callback();

      if (checkUserContextIsInspetor()) {
        UsuarioService.getById(userDetails.id)
          .then((response) => {
            runInAction(() => {
              this.object.analista = response.data;
            });
          })
          .catch((error) =>
            runInAction(() => {
              showErrorNotification(error);
            })
          );
      }
    } else {
      this.loading = true;
      this.service
        .getById(id)
        .then((response) =>
          runInAction(() => {
            this.object = response.data;
            this.loadedObject = response.data;
            callback && callback();
          })
        )
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        )
        .finally(() =>
          runInAction(() => {
            this.loading = false;
          })
        );
    }
  }

  @action
  setTda() {
    this.loadingTda = true;
    this.tdaService
      .getTdaCorrente()
      .then((response) =>
        runInAction(() => {
          this.object.tda = response.data;
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      )
      .finally(() =>
        runInAction(() => {
          this.loadingTda = false;
        })
      );
  }

  setIdProcesso(idProcesso, tipoProcesso) {
    if (tipoProcesso === 'licitacao') this.object['idLicitacao'] = idProcesso;
    else if (tipoProcesso === 'carona') this.object['idCarona'] = idProcesso;
    else if (tipoProcesso === 'dispensa') this.object['idDispensa'] = idProcesso;
    else if (tipoProcesso === 'inexigibilidade') this.object['idInexigibilidade'] = idProcesso;
    else if (tipoProcesso === 'credenciamento') this.object['idCredenciamento'] = idProcesso;
    else showErrorNotification('Processo licitatório inválido');
  }
}

export default TdaFormStore;
