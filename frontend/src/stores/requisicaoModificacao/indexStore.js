import IndexBase from 'fc/stores/IndexBase';
import RequisicaoModificacao from '~/domains/RequisicaoModificacao';
import SearchTypes from 'fc/components/AdvancedSearch/SearchTypes';
import RequisicaoModificacaoService from '~/services/RequisicaoModificacaoService';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import { checkUserGroup, showErrorNotification } from 'fc/utils/utils';
import { observable, runInAction, override } from 'mobx';
import AppStore from 'fc/stores/AppStore';
import AsyncDropDownStore from 'fc/stores/AsyncDropdownStore';
import Entidade from '~/domains/Entidade';
import EntidadeService from '~/services/EntidadeService';

class RequisicaoModificacaoIndexStore extends IndexBase {
  @observable objetoRequisicao;
  @observable camposModificados;
  @observable tipoProcesso;

  @observable listEmAberto = [];
  @observable paginationEmAberto = {
    total: 0,
    page: {
      index: 1,
      size: 10,
    },
  };

  @observable listHistorico = [];
  @observable paginationHistorico = {
    total: 0,
    page: {
      index: 1,
      size: 10,
    },
  };

  constructor() {
    super(RequisicaoModificacaoService, RequisicaoModificacao, 'dataRequisicao', 'desc');
  }

  setRequisicaoObject(requisicao) {
    this.requisicaoSelecionada = requisicao;
  }

  getAdvancedSearchParams() {
    const isAuditor = checkUserGroup(['Auditor', 'Administrador']);
    let searchParams = [];

    if (isAuditor) {
      searchParams = [
        {
          field: 'entidade',
          label: 'Órgão/Entidade',
          type: SearchTypes.ASYNC_QUERY,
          store: new AsyncDropDownStore(Entidade, EntidadeService, 'nome', 'id'),
        },
      ];
    }
    searchParams.push(
      {
        field: 'tipoProcesso',
        label: 'Tipo do Processo',
        type: SearchTypes.ENUM,
        options: DadosEstaticosService.getTipoProcessoReqModificacaoSemEmpenho(),
      },
      {
        field: 'tituloProcesso',
        label: 'Título do Processo',
        type: SearchTypes.TEXT,
      },
      {
        field: 'dataRequisicao',
        label: 'Data da Requisição',
        type: SearchTypes.DATE,
      },
      {
        field: 'status',
        label: 'Status',
        type: SearchTypes.ENUM,
        options: DadosEstaticosService.getStatusRequisicaoModificacao(),
      }
    );
    return searchParams;
  }

  isValidDate(date) {
    const date_regex = /^\d{4}\-\d{1,2}\-\d{1,2}$/;
    return date_regex.test(date);
  }

  getFilterSuggest(idReqMod) {
    const entidade = AppStore.getContextEntity();
    const filterSuggest = [];

    filterSuggest.push({
      id: '',
      field: 'tipoProcesso',
      operator: 'NOT_EQUAL_TO',
      value: 'EMPENHO_LICITACAO',
      formatted: '',
      fixed: true,
      invisible: true,
      completeParam: {
        field: 'tipoProcesso',
        label: 'Tipo do Processo',
        type: SearchTypes.ENUM,
        options: DadosEstaticosService.getTipoProcessoReqModificacao(),
      },
    });

    if (entidade && !checkUserGroup('Administrador')) {
      filterSuggest.push({
        id: '',
        field: 'entidade',
        operator: 'EQUAL_TO',
        value: entidade,
        formatted: '',
        fixed: true,
        invisible: true,
        completeParam: {
          field: 'entidade',
          label: 'Órgão/Entidade',
          type: SearchTypes.ASYNC_QUERY,
          store: new AsyncDropDownStore(Entidade, EntidadeService, 'nome', 'id'),
        },
      });
    }
    if (idReqMod) {
      filterSuggest.push({
        id: '',
        field: 'id',
        operator: 'EQUAL_TO',
        value: idReqMod,
        formatted: '',
        completeParam: {
          field: 'id',
          label: 'Id',
          type: SearchTypes.NUMBER,
        },
      });
    }
    return filterSuggest;
  }

  @override
  load(options = {}, filter, callback) {
    this.loading = true;

    const advancedSearchParams = JSON.parse(JSON.stringify(this.advancedSearchParams));
    const parameters = Object.assign(advancedSearchParams, options);

    if (options && options.page && options.page.index && options.page.size) {
      parameters.page = { index: options.page.index, size: options.page.size };
    } else {
      parameters.page = { index: 1, size: 10 };
    }

    if (options.andParameters && options.andParameters.length > 0) {
      parameters.andParameters = options.andParameters;
    }

    if (options.orParameters && options.orParameters.length > 0) {
      parameters.orParameters = options.orParameters;
    }

    if (options && options['sort'] && options['sort'].by && options['sort'].order) {
      parameters.sort = { by: options['sort'].by, order: options['sort'].order };
    } else {
      parameters.sort = { by: this.defaultSortBy, order: this.defaultSortOrder };
    }

    this.advancedSearchParams = advancedSearchParams;

    const promises = [];
    if (!filter || filter === 'emAberto') {
      const parametersEmAberto = JSON.parse(JSON.stringify(parameters));
      parametersEmAberto.andParameters.push({
        field: 'status',
        operator: 'EQUAL_TO',
        value: 'ENVIADA',
        formatted: '',
        fixed: true,
        invisible: true,
        completeParam: {
          field: 'status',
          label: 'Status',
          type: SearchTypes.TEXT,
        },
      });
      this.paginationEmAberto = parametersEmAberto;
      promises.push(this.service.advancedSearch(parametersEmAberto));
    }

    if (!filter || filter === 'historico') {
      const parametersHistorico = JSON.parse(JSON.stringify(parameters));
      parametersHistorico.andParameters.push({
        field: 'status',
        operator: 'NOT_EQUAL_TO',
        value: 'ENVIADA',
        formatted: '',
        fixed: true,
        invisible: true,
        completeParam: {
          field: 'status',
          label: 'Status',
          type: SearchTypes.TEXT,
        },
      });
      this.paginationHistorico = parametersHistorico;
      promises.push(this.service.advancedSearch(parametersHistorico));
    }
    Promise.all(promises)
      .then((responses) =>
        runInAction(() => {
          if (filter === 'emAberto') {
            this.paginationEmAberto.total = responses[0].data.total;
            this.listEmAberto = this.initializeLoadedList(responses[0].data.items);
          } else if (filter === 'historico') {
            this.paginationHistorico.total = responses[0].data.total;
            this.listHistorico = this.initializeLoadedList(responses[0].data.items);
          } else {
            this.paginationEmAberto.total = responses[0].data.total;
            this.listEmAberto = this.initializeLoadedList(responses[0].data.items);

            this.paginationHistorico.total = responses[1].data.total;
            this.listHistorico = this.initializeLoadedList(responses[1].data.items);
          }
          callback && callback();
        })
      )
      .catch((error) => showErrorNotification(error))
      .finally(() =>
        runInAction(() => {
          this.loading = false;
        })
      );
  }
}

export default RequisicaoModificacaoIndexStore;
