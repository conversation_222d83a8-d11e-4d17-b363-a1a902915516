import FormBase from 'fc/stores/FormBase';
import { action, override, observable, runInAction } from 'mobx';
import RequisicaoModificacao from '~/domains/RequisicaoModificacao';
import AppStore from 'fc/stores/AppStore';
import RequisicaoModificacaoService from '~/services/RequisicaoModificacaoService';
import JulgamentoRequisicaoService from '~/services/JulgamentoRequisicaoService';
import { getValueByKey, showErrorNotification, showNotification } from 'fc/utils/utils';
import LicitacaoService from '~/services/LicitacaoService';
import VencedorFormStore from '../vencedor/formStore';
import DadosEstaticosService from '~/services/DadosEstaticosService';

class RequisicaoModificacaoFormStore extends FormBase {
  @observable idProcesso;
  @observable idEntidade;
  @observable justificativaJurisdicionado;
  @observable justificativaAuditor;
  @observable objetoModificado;
  @observable arquivosProcesso;
  @observable vencedorStore;
  @observable lotes;
  @observable modificacoesAtributos = 0;
  @observable modificacoesVencedores = 0;
  @observable modificacoesArquivos = 0;
  @observable modificacoesPublicacoes = 0;
  @observable modificacoesLicitantes = 0;
  @observable modificacoesOcorrencias = 0;
  @observable modificacoesLotes = 0;
  @observable modificacoesSecoes = 0;

  constructor() {
    super(RequisicaoModificacaoService, RequisicaoModificacao);
    this.enviarRequisicaoLicitacao = this.enviarRequisicaoLicitacao.bind(this);
    this.julgamentoAuditorRequisicao = this.julgamentoAuditorRequisicao.bind(this);
    this.setIdProcesso = this.setIdProcesso.bind(this);
    this.downloadArquivoProcesso = this.downloadArquivoProcesso.bind(this);
    this.enviarRequisicaoCarona = this.enviarRequisicaoCarona.bind(this);
  }

  _getNomeAtributoItensByProcesso(tipoProcesso) {
    let att;
    if (tipoProcesso === 'LICITACAO') {
      att = 'itensLicitacao';
    } else if (tipoProcesso === 'DISPENSA') {
      att = 'itensDispensa';
    } else if (tipoProcesso === 'CARONA') {
      att = 'itensCarona';
    } else if (tipoProcesso === 'INEXIGIBILIDADE') {
      att = 'itensInexigibilidade';
    }

    return att;
  }

  @action
  getObjetoRequisicao(requisicao, callback) {
    requisicao = requisicao ?? this.object;
    if (requisicao) {
      const isLicitacao = requisicao.tipoProcesso === 'LICITACAO';
      const isTermoReferencia = requisicao.tipoProcesso === 'TERMO_REFERENCIA';
      const hasVencedores = ['LICITACAO', 'CARONA', 'DISPENSA', 'INEXIGIBILIDADE'].includes(requisicao.tipoProcesso);

      this.loading = true;
      this.tipoProcesso = requisicao.tipoProcesso;

      RequisicaoModificacaoService.getModificacoes(requisicao.id)
        .then((response) =>
          runInAction(() => {
            this.objetoModificado = response.data;
            this.modificacoesAtributos =
              Object.keys(this.objetoModificado).filter(
                (key) => key.startsWith('new') || (key === 'obra' && this.objetoModificado.obra?.modificado)
              )?.length ?? 0;
            this.modificacoesArquivos =
              (this.objetoModificado?.arquivosTemporarios ?? this.objetoModificado?.files)?.filter(
                (file) => file.modificado
              )?.length ?? 0;

            if (hasVencedores) {
              const nomeLicitante = getValueByKey(
                this.object?.tipoProcesso[0],
                DadosEstaticosService.getTipoProcesso(),
                'value',
                'licitantes'
              );
              this.modificacoesVencedores =
                this.objetoModificado[nomeLicitante]?.filter((venc) => venc.modificado)?.length ?? 0;
              this.setVencedorStore(nomeLicitante);
            }

            if (isLicitacao) {
              this.modificacoesPublicacoes =
                this.objetoModificado.publicacoes?.filter((publi) => publi.modificado)?.length ?? 0;
              this.modificacoesLicitantes =
                this.objetoModificado.licitantes?.filter((licitante) => licitante.modificado)?.length ?? 0;
              this.modificacoesOcorrencias =
                this.objetoModificado.ocorrencias?.filter((ocorrencia) => ocorrencia.modificado)?.length ?? 0;
            } else if (isTermoReferencia) {
              this.modificacoesLotes =
                this.objetoModificado.lotes?.filter(
                  (lote) => lote.modificado || lote.itensLote?.filter((item) => item.modificado)?.length
                )?.length ?? 0;
              this.modificacoesSecoes = this.objetoModificado.secoes?.filter((lote) => lote.modificado)?.length ?? 0;
            }
            callback && callback();
          })
        )
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        )
        .finally(() =>
          runInAction(() => {
            this.loading = false;
          })
        );
    }
  }

  @action
  setVencedorStore(atributoLicitante, callback) {
    const atributoItens = this._getNomeAtributoItensByProcesso(this.object?.tipoProcesso);
    const vencedorStore = new VencedorFormStore(atributoItens);
    let vencedores = this.objetoModificado[atributoLicitante].map((vencedor) => ({
      ...vencedor,
      lote: vencedor.originalLote ?? vencedor.lote,
    }));
    const vencedoresToRemove = [];

    vencedores.forEach((v) => {
      if (v.modificado === 'REMOCAO' && v.lote?.id) {
        v.modificado = 'EDICAO';
        const newVencedor = vencedores.find((venc) => venc?.lote?.id === v.lote.id && venc.modificado === 'ADICAO');
        v.newLicitante = newVencedor?.licitante;
        v[atributoItens] = newVencedor[atributoItens];
        vencedoresToRemove.push(newVencedor);
      }
    });

    vencedores = vencedores.filter((v) => !vencedoresToRemove.includes(v));

    vencedorStore.initialize(
      this.objetoModificado.licitantes,
      vencedores,
      this.objetoModificado.originalTermoReferencia ?? this.objetoModificado.termoReferencia,
      this.object.lotesFracassados?.length ? this.object.lotesFracassados : [],
      (vencedores) => {
        this.updateAttribute(atributoLicitante, vencedores);
      },
      (fracassado) => this.updateAttribute('lotesFracassados', fracassado)
    );

    this.vencedorStore = vencedorStore;
    callback && callback();
  }

  _isAditionalMod(requisicao, modificacao) {
    return (
      (requisicao.status !== 'ACEITA' && modificacao.tipo === 'ADICAO') ||
      (requisicao.status === 'ACEITA' && modificacao.tipo === 'REMOCAO')
    );
  }

  @action
  setIdProcesso(idProcesso) {
    this.idProcesso = idProcesso;
  }

  rulesDefinition() {
    return {
      justificativaAuditor: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
    };
  }

  clearObject() {
    this.object.justificativaJurisdicionado = undefined;
    this.object.justificativaAuditor = undefined;
  }

  @override
  updateAttribute(tipo, event) {
    let value = event;
    if (event && event.value) {
      value = event.value;
    }
    if (event && event.target) {
      value = event.target.value;
    }
    if (tipo === 'jurisdicionado') {
      this.justificativaJurisdicionado = value;
    } else if (tipo === 'justificativaAuditor') {
      this.justificativaAuditor = value;
      this.object.justificativaAuditor = value;
    }
  }

  recuperarEntidade(idLicitacao) {
    if (!AppStore.getContextEntity()?.id) {
      LicitacaoService.getById(idLicitacao)
        .then((response) =>
          runInAction(() => {
            this.idEntidade = response.data?.entidade?.id;
          })
        )
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        );
    } else {
      this.idEntidade = AppStore.getContextEntity()?.id;
    }
  }

  @action
  enviarRequisicaoLicitacao(dto, callback) {
    this.loading = true;

    if (!dto.idEntidade) {
      dto.idEntidade = this.idEntidade;
    }

    const somaVencedores = this.getSomaVencedores(dto);
    if (somaVencedores !== dto.valorAdjudicado) {
      dto.cadastroLicitacaoDTO.licitacao.valorAdjudicado = somaVencedores;
    }

    dto.justificativaJurisdicionado = this.justificativaJurisdicionado;
    RequisicaoModificacaoService.requisicaoModificacaoLicitacao(dto.idLicitacao, dto)
      .then(() =>
        runInAction(() => {
          callback && callback();
          showNotification('success', null, 'Requisição de Modificação enviada com sucesso!');
          this.justificativaJurisdicionado = undefined;
          this.justificativaAuditor = undefined;
        })
      )
      .catch((error) =>
        runInAction(() => {
          this.justificativaJurisdicionado = undefined;
          this.justificativaAuditor = undefined;
          showErrorNotification(error);
        })
      )
      .finally(() =>
        runInAction(() => {
          this.loading = false;
        })
      );
  }

  getSomaVencedores(dto) {
    return dto.cadastroLicitacaoDTO.licitacao.vencedores?.reduce((soma, vencedor) => soma + vencedor.valor, 0);
  }

  @action
  julgamentoAuditorRequisicao(idRequisicao, parecer, callback) {
    if (!this.object.justificativaAuditor) {
      showErrorNotification('Justificativa é de preenchimento obrigatório');
    } else {
      const julgamentoDTO = {
        justificativaAuditor: this.object.justificativaAuditor,
        parecer: parecer,
      };
      this.loading = true;

      JulgamentoRequisicaoService.julgamentoRequisicao(idRequisicao, julgamentoDTO)
        .then(() =>
          runInAction(() => {
            callback && callback();
            showNotification('success', null, 'Julgamento salvo com sucesso!');
          })
        )
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        )
        .finally(() =>
          runInAction(() => {
            this.loading = false;
          })
        );
    }
  }

  @action
  downloadArquivoProcesso(fileDTO) {
    RequisicaoModificacaoService.download(this.object?.id, fileDTO)
      .then((response) =>
        runInAction(() => {
          const filename = fileDTO.nomeOriginal;
          const link = document.createElement('a');
          link.setAttribute('href', URL.createObjectURL(response.data));
          link.setAttribute('download', filename);
          link.style.display = 'none';
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      );
  }

  @action
  enviarRequisicaoCarona(caronaDTO, callback) {
    this.loading = true;
    const dto = {};
    dto.idEntidade = AppStore.getContextEntity()?.id ?? caronaDTO.carona?.entidade?.id;
    dto.caronaDTO = caronaDTO;
    dto.justificativaJurisdicionado = this.justificativaJurisdicionado;
    RequisicaoModificacaoService.requisicaoModificacaoCarona(caronaDTO.carona.id, dto)
      .then(() =>
        runInAction(() => {
          callback && callback();
          showNotification('success', null, 'Requisição de Modificação enviada com sucesso!');
          this.justificativaJurisdicionado = undefined;
          this.justificativaAuditor = undefined;
        })
      )
      .catch((error) =>
        runInAction(() => {
          this.justificativaJurisdicionado = undefined;
          this.justificativaAuditor = undefined;
          showErrorNotification(error);
        })
      )
      .finally(() =>
        runInAction(() => {
          this.loading = false;
        })
      );
  }

  @action
  enviarRequisicaoTermoReferencia(termoReferencia, callback) {
    this.loading = true;
    const dto = {};
    dto.idEntidade = AppStore.getContextEntity()?.id ?? termoReferencia?.entidade?.id;
    dto.termoReferencia = termoReferencia;
    dto.justificativaJurisdicionado = this.justificativaJurisdicionado;
    RequisicaoModificacaoService.requisicaoModificacaoTermoReferencia(termoReferencia.id, dto)
      .then(() =>
        runInAction(() => {
          callback && callback();
          showNotification('success', null, 'Requisição de Modificação enviada com sucesso!');
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      )
      .finally(() =>
        runInAction(() => {
          this.justificativaJurisdicionado = undefined;
          this.justificativaAuditor = undefined;
          this.loading = false;
        })
      );
  }

  @action
  enviarRequisicaoObraMedicao(obraMedicaoDTO, callback) {
    this.loading = true;
    const dto = {};
    dto.idEntidade = AppStore.getContextEntity()?.id ?? obraMedicaoDTO?.obraMedicao?.obra?.licitacao?.entidade?.id;
    dto.obraMedicaoDTO = obraMedicaoDTO;
    dto.justificativaJurisdicionado = this.justificativaJurisdicionado;
    RequisicaoModificacaoService.requisicaoModificacaoObraMedicao(obraMedicaoDTO.obraMedicao.id, dto)
      .then(() =>
        runInAction(() => {
          callback && callback();
          showNotification('success', null, 'Requisição de Modificação enviada com sucesso!');
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      )
      .finally(() =>
        runInAction(() => {
          this.justificativaJurisdicionado = undefined;
          this.justificativaAuditor = undefined;
          this.loading = false;
        })
      );
  }

  @action
  enviarRequisicaoCredenciamento(credenciamentoDTO, callback) {
    this.loading = true;
    const dto = {};
    dto.idEntidade = AppStore.getContextEntity()?.id ?? credenciamentoDTO?.credenciamento?.entidade?.id;
    dto.credenciamentoDTO = credenciamentoDTO;
    dto.justificativaJurisdicionado = this.justificativaJurisdicionado;
    RequisicaoModificacaoService.requisicaoModificacaoCredenciamento(credenciamentoDTO.credenciamento.id, dto)
      .then(() =>
        runInAction(() => {
          callback && callback();
          showNotification('success', null, 'Requisição de Modificação enviada com sucesso!');
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      )
      .finally(() =>
        runInAction(() => {
          this.justificativaJurisdicionado = undefined;
          this.justificativaAuditor = undefined;
          this.loading = false;
        })
      );
  }

  @action
  enviarRequisicaoAnulacaoRevogacao(anulacaoRevogacaoDTO, callback) {
    const RequisicaoModificacaoAnulacaoRevogacaoDTO = {};
    RequisicaoModificacaoAnulacaoRevogacaoDTO.id = anulacaoRevogacaoDTO.anulacaoRevogacao.id;
    RequisicaoModificacaoAnulacaoRevogacaoDTO.justificativaJurisdicionado = this.justificativaJurisdicionado;
    RequisicaoModificacaoAnulacaoRevogacaoDTO.idEntidade = AppStore.getContextEntity().id;
    RequisicaoModificacaoAnulacaoRevogacaoDTO.anulacaoRevogacaoDTO = anulacaoRevogacaoDTO;

    this.loading = true;

    RequisicaoModificacaoService.requisicaoModificacaoAnulacaoRevogacao(
      anulacaoRevogacaoDTO.anulacaoRevogacao.id,
      RequisicaoModificacaoAnulacaoRevogacaoDTO
    )
      .then(() =>
        runInAction(() => {
          callback && callback();
          showNotification('success', null, 'Requisição de Modificação enviada com sucesso!');
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      )
      .finally(() =>
        runInAction(() => {
          this.justificativaJurisdicionado = undefined;
          this.justificativaAuditor = undefined;
          this.loading = false;
        })
      );
  }
}

export default RequisicaoModificacaoFormStore;
