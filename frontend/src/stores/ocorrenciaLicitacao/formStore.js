import OcorrenciaLicitacao from '~/domains/OcorrenciaLicitacao';
import OcorrenciaLicitacaoService from '~/services/OcorrenciaLicitacaoService';
import { action, observable, override, runInAction } from 'mobx';
import FormBase from 'fc/stores/FormBase';
import { getValueByKey, showErrorNotification, showNotification } from 'fc/utils/utils';
import moment from 'moment';
import { DATE_PARSE_FORMAT_WITH_HOURS } from 'fc/utils/date';
import MultipleFileUploaderStore from 'fc/stores/MultipleFileUploaderStore';
import AsyncMultiselectStore from 'fc/stores/AsyncMultiselectStore';
import LicitacaoService from '~/services/LicitacaoService';
import LicitanteService from '~/services/LicitanteService';
import Licitante from '~/domains/Licitante';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import ObrigatoriedadeArquivoService from '~/services/ObrigatoriedadeArquivoService';

class OcorrenciaLicitacaoFormStore extends FormBase {
  @observable fileStore;

  constructor(openDate, licitantes, initFiles = true) {
    super(OcorrenciaLicitacaoService, OcorrenciaLicitacao);
    this.fileStore = new MultipleFileUploaderStore(
      DadosEstaticosService.getTipoArquivoFinalizar(),
      (file) => OcorrenciaLicitacaoService.upload(file),
      (fileDTO) => OcorrenciaLicitacaoService.download(fileDTO),
      () => {},
      () => {}
    );

    this.openDate = openDate;
    this.licitantes = licitantes ?? [];
    this.licitanteStore = new AsyncMultiselectStore(Licitante, LicitanteService, 'nome', 'id');
    this.arquivosObrigatorios = [];
    initFiles && this.carregarArquivosObrigatorios();
  }

  @override
  initialize(idLicitacao, tipoOcorrencia, defaultValues = {}) {
    this.object = Object.assign({}, defaultValues);
    if (tipoOcorrencia === 'SUSPENDER' || tipoOcorrencia === 'REABRIR') {
      this.loading = true;
      Promise.all([this.service.getPendenciasLicitacao(idLicitacao), LicitacaoService.getById(idLicitacao)])
        .then((response) =>
          runInAction(() => {
            this.pendencias = response[0].data;
            this.licitantes = response[1].data.licitantes;
          })
        )
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        )
        .finally(() =>
          runInAction(() => {
            this.loading = false;
          })
        );
    }
  }

  @action
  initializeFormValues(tipoOcorrencia) {
    this.tipoOcorrencia = tipoOcorrencia;
    if (tipoOcorrencia === 'REABRIR') this.object.tipoOcorrencia = 'REABRIR';
    if (tipoOcorrencia === 'CONTINUAR') this.object.tipoOcorrencia = 'CONTINUAR';
    if (tipoOcorrencia === 'SUSPENDER' || tipoOcorrencia === 'FINALIZAR')
      this.object.dataProrrogada = moment().format(DATE_PARSE_FORMAT_WITH_HOURS);
    if (tipoOcorrencia === 'FINALIZAR' && this.licitantes) this.object.licitantes = this.licitantes;
    tipoOcorrencia !== 'CONTINUAR' && this.carregarArquivosObrigatorios();
  }

  @action
  setArquivoOcorrenciaList(list) {
    this.object.arquivos = list;
  }

  getArquivoOcorrenciaList() {
    return this.object?.arquivos ?? [];
  }

  @action
  suspenderLicitacao(ocorrenciaLicitacaoDTO, idLicitacao, callback) {
    if (this.validateSubmittedFiles()) {
      this.loading = true;
      this.service
        .suspenderLicitacao(ocorrenciaLicitacaoDTO, idLicitacao)
        .then(() =>
          runInAction(() => {
            callback && callback();
            showNotification('success', null, 'Licitação suspensa com sucesso!');
          })
        )
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        )
        .finally(() =>
          runInAction(() => {
            this.loading = false;
          })
        );
    }
  }

  @action
  prorrogarLicitacao(ocorrenciaLicitacaoDTO, idLicitacao, callback) {
    if (this.validateSubmittedFiles()) {
      this.loading = true;
      this.service
        .prorrogarLicitacao(ocorrenciaLicitacaoDTO, idLicitacao)
        .then(() =>
          runInAction(() => {
            callback && callback();
            showNotification('success', null, 'Licitação prorrogada com sucesso!');
          })
        )
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        )
        .finally(() =>
          runInAction(() => {
            this.loading = false;
          })
        );
    }
  }

  @action
  reabrirLicitacao(ocorrenciaLicitacaoDTO, idLicitacao, callback) {
    if (this.validateSubmittedFiles()) {
      this.loading = true;
      this.service
        .reabrirLicitacao(ocorrenciaLicitacaoDTO, idLicitacao)
        .then(() =>
          runInAction(() => {
            callback && callback();
            showNotification('success', null, 'Licitação reaberta com sucesso!');
          })
        )
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        )
        .finally(() =>
          runInAction(() => {
            this.loading = false;
          })
        );
    }
  }

  @action
  continuarLicitacao(ocorrenciaLicitacao, idLicitacao, callback) {
    if (this.validateSubmittedFiles()) {
      this.loading = true;
      this.service
        .continuarLicitacao(ocorrenciaLicitacao, idLicitacao)
        .then(() =>
          runInAction(() => {
            callback && callback();
            showNotification('success', null, 'Licitação pronta para continuar!');
          })
        )
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        )
        .finally(() =>
          runInAction(() => {
            this.loading = false;
          })
        );
    }
  }

  @action
  finalizarLicitacao(ocorrenciaLicitacaoDTO, idLicitacao, callback) {
    if (this.validateSubmittedFiles()) {
      this.loading = true;
      this.service
        .finalizarLicitacao(ocorrenciaLicitacaoDTO, idLicitacao)
        .then(() =>
          runInAction(() => {
            callback && callback();
            showNotification('success', null, 'Licitação finalizada com sucesso!');
          })
        )
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        )
        .finally(() =>
          runInAction(() => {
            this.loading = false;
          })
        );
    }
  }

  @action
  getStatusOcorrenciaLicitacaoReabrir(fase) {
    let options = [{ value: 'REABRIR_DETERMINACAO_JUDICIAL', text: 'Reaberta por determinação judicial' }];
    if (this.licitantes.length !== 0) {
      options.push({ value: 'OUTROS_MOTIVOS', text: 'Outros Motivos' });
    } else if (!['APRESENTACAO_PROPOSTAS_LANCES', 'FINALIZACAO'].includes(fase)) {
      options.push({ value: 'REEDICAO', text: 'Reedição (Em casos de deserta ou fracassada)' });
    }
    return options;
  }

  filtraStatusOcorrenciaLicitacaoSuspender(ocorrencia) {
    return [
      'SUSPENSA_DETERMINACAO_JUDICIAL',
      'EDITAL_IMPUGNADO_ANALISE',
      'SUSTADA_MEDIDA_CAUTELAR',
      'ALERTA_TCE',
      'PARA_RETIFICACAO_EDITAL',
      'OUTROS_MOTIVOS',
    ].includes(ocorrencia.value);
  }

  filtraStatusOcorrenciaLicitacaoProrrogar(ocorrencia) {
    return [
      'ALERTA_TCE_PRORROGACAO',
      'EDITAL_IMPUGNADO_ANALISE_PRORROGACAO',
      'PRORROGADA_DETERMINACAO_JUDICIAL',
      'PRORROGADA_MEDIDA_CAUTELAR',
      'PARA_RETIFICACAO_EDITAL_PRORROGACAO',
      'RETIFICACAO_EDITAL',
      'OUTRA',
    ].includes(ocorrencia.value);
  }

  filtraStatusOcorrenciaLicitacaoFinalizar(ocorrencia) {
    return ['ANULADA', 'REVOGADA'].includes(ocorrencia.value);
  }

  filtraStatusOcorrenciaLicitacaoFinalizarFracassada(ocorrencia) {
    return ['ANULADA', 'REVOGADA', 'FRACASSADA', 'DESERTA'].includes(ocorrencia.value);
  }

  filtraStatusOcorrenciaLicitacaoContinuar(ocorrencia) {
    return ['CONTINUAR', 'OUTROS_MOTIVOS'].includes(ocorrencia.value);
  }

  @action
  resetObject() {
    this.object = Object.assign({}, {});
    this.fileStore.initialize();
  }

  rulesDefinition() {
    let rules = {
      tipoOcorrencia: [{ rule: 'required', message: 'Por favor, selecione um valor' }],
      motivoOcorrencia: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
    };

    if (this.tipoOcorrencia !== 'CONTINUAR' && ['ANULADA', 'REVOGADA'].includes(this.object.tipoOcorrencia)) {
      rules = this.mergeRules(rules, {
        dataAviso: [{ rule: 'required', message: 'Por favor, selecione um valor' }],
      });
    }

    if (this.tipoOcorrencia === 'REABRIR') {
      rules = this.mergeRules(rules, {
        dataProrrogada: [
          {
            rule: 'isAfterDateWarning',
            message: 'Por favor, selecione uma data superior à data de aviso',
            dateWarning: this.object.dataAviso,
          },
        ],
      });
    }
    if (this.tipoOcorrencia === 'PRORROGAR') {
      rules = this.mergeRules(rules, {
        dataProrrogada: [
          { rule: 'required', message: 'Por favor, preencha o campo' },
          { rule: 'isBusinessHours', message: 'A nova data de abertura deve ser apenas em horário comercial' },
          {
            rule: 'isAfterOpenDate',
            message: 'Por favor, selecione uma data superior à data de aviso',
            openDate: this.object.dataAviso,
          },
        ],
      });
    }

    if (
      (this.tipoOcorrencia !== 'SUSPENDER' &&
        this.tipoOcorrencia !== 'FINALIZAR' &&
        this.tipoOcorrencia !== 'CONTINUAR') ||
      this.tipoOcorrencia === 'REABRIR'
    ) {
      rules = this.mergeRules(rules, {
        dataProrrogada: [
          { rule: 'required', message: 'Por favor, preencha o campo' },
          { rule: 'isBusinessHours', message: 'A nova data de abertura deve ser apenas em horário comercial' },
        ],
      });
    }
    if (this.object.tipoOcorrencia === 'FRACASSADA') {
      rules = this.mergeRules(rules, {
        licitantes: [{ rule: 'required', message: 'Por favor, selecione um valor' }],
      });
    }

    return rules;
  }

  @action
  carregarArquivosObrigatorios(tipoOcorrencia = 'OUTRA') {
    ObrigatoriedadeArquivoService.getArquivosObrigatorios({
      tipoProcesso: 'OCORRENCIA_LICITACAO',
      filtros: [tipoOcorrencia],
    })
      .then((response) =>
        runInAction(() => {
          const arquivosObrigatorios = response?.data ?? [];
          this.arquivosObrigatorios = arquivosObrigatorios
            .filter((file) => file.obrigatorio)
            .map((file) => file.arquivoEnum);

          this.fileStore.tipoArquivoEnum = arquivosObrigatorios.map((arqObg) => {
            const arq = DadosEstaticosService.getTipoArquivoFinalizar().find((arq) => arq.value === arqObg.arquivoEnum);
            const arquivos = {
              ...arqObg,
              text: arqObg.obrigatorio ? '* ' + arqObg.arquivo : arqObg.arquivo,
              value: arq.value,
            };

            return arquivos;
          });
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      );
  }

  validateSubmittedFiles() {
    const files = this.object.arquivos;
    let returnValue = true;

    if (!files && this.arquivosObrigatorios.length) {
      showNotification('error', null, 'É necessário adicionar ao menos um arquivo');
      return false;
    } else if (!files) {
      return true;
    }

    files.forEach((file) => {
      if (!file.tipo) {
        returnValue = false;
        showNotification('error', null, 'O tipo dos arquivos é obrigatório');
      }
    });

    if (this.arquivosObrigatorios.length) {
      const arquivosAdicionados = files.filter((file) => file.tipo.length > 0).map((file) => file.tipo);
      this.arquivosObrigatorios.forEach((arq) => {
        if (!arquivosAdicionados.includes(arq)) {
          showNotification(
            'error',
            null,
            'É necessário adicionar ao menos um arquivo do tipo ' +
              getValueByKey(arq, DadosEstaticosService.getTipoArquivoFinalizar())
          );
          returnValue = false;
        }
      });
    }

    return returnValue;
  }
}

export default OcorrenciaLicitacaoFormStore;
