import IndexBase from 'fc/stores/IndexBase';
import { action, observable, runInAction } from 'mobx';
import MultipleFileUploaderStore from 'fc/stores/MultipleFileUploaderStore';
import { showErrorNotification } from 'fc/utils/utils';
import OcorrenciaLicitacaoService from '~/services/OcorrenciaLicitacaoService';
import DadosEstaticosService from '~/services/DadosEstaticosService';

class OcorrenciaLicitacaoDetailStore extends IndexBase {
  @observable arquivos = [];
  @observable fileStoreOcorrencia;
  @observable fileStoreApresentacao;
  @observable fileStoreFinalizacao;
  @observable idOcorrenciaLicitacao;

  constructor() {
    super(OcorrenciaLicitacaoService);

    const tipoArquivoFinalizar = DadosEstaticosService.getTipoArquivoFinalizar();
    const uploadFunction = (file) => OcorrenciaLicitacaoService.upload(file);
    const downloadFunction = (fileDTO) => OcorrenciaLicitacaoService.download(fileDTO);

    this.fileStoreOcorrencia = new MultipleFileUploaderStore(
      tipoArquivoFinalizar,
      uploadFunction,
      downloadFunction,
      () => {},
      () => {}
    );

    this.fileStoreApresentacao = new MultipleFileUploaderStore(
      tipoArquivoFinalizar,
      uploadFunction,
      downloadFunction,
      () => {},
      () => {}
    );

    this.fileStoreFinalizacao = new MultipleFileUploaderStore(
      tipoArquivoFinalizar,
      uploadFunction,
      downloadFunction,
      () => {},
      () => {}
    );
  }

  @action
  initializeArquivos(idOcorrenciaLicitacao, tipoOcorrencia, callback) {
    if (idOcorrenciaLicitacao) {
      this.service
        .recuperarArquivos(idOcorrenciaLicitacao)
        .then((response) => {
          runInAction(() => {
            const arquivosRecuperados = response.data;
            this.arquivos = arquivosRecuperados;
            tipoOcorrencia === 'REABRIR'
              ? this.filtraArquivosOcorrenciaLicitacao()
              : this.fileStoreOcorrencia.initialize(this.arquivos);
          });
        })
        .catch((error) => {
          runInAction(() => {
            showErrorNotification(error);
          });
        })
        .finally(() => {
          runInAction(() => {
            if (callback) callback();
          });
        });
    }
  }

  @action
  filtraArquivosOcorrenciaLicitacao() {
    const tiposArquivosOcorrencia = [
      'JUSTIFICATIVA_REABERTURA',
      'SENTENCA_JUDICIAL',
      'AVISO_REABERTURA',
      'ATA_SESSAO_DESERTA_OU_FRACASSADA',
      'OUTROS_DOCUMENTOS',
    ];
    const tiposArquivosPropostas = ['MAPA_LANCES', 'ATA_SESSAO', 'PROPOSTAS_VENCEDORAS', 'OUTROS_DOCUMENTOS_EXTERNO'];
    const tiposArquivosFinalizacao = [
      'TERMO_HOMOLOGACAO',
      'PROPOSTA_ADJUDICADA',
      'RECURSOS',
      'TERMO_ADJUDICACAO',
      'ATA_REGISTRO_PRECOS',
      'OUTROS_DOCUMENTOS_EXTERNO',
    ];

    const arquivosOcorrencia = this.arquivos.filter((arquivo) => tiposArquivosOcorrencia.includes(arquivo.tipo));
    const arquivosLicitacao = this.arquivos.filter(
      (arquivo) =>
        (tiposArquivosPropostas.includes(arquivo.tipo) &&
          (arquivo.fase === 'APRESENTACAO_PROPOSTAS_LANCES' || arquivo.fase === undefined)) ||
        (arquivo.tipo === 'OUTROS_DOCUMENTOS_EXTERNO' && arquivo.fase === undefined)
    );

    const arquivosLicitacaoFinalizacao = this.arquivos.filter(
      (arquivo) =>
        tiposArquivosFinalizacao.includes(arquivo.tipo) &&
        (arquivo.fase === 'FINALIZACAO' || (arquivo.fase === undefined && arquivo.tipo !== 'OUTROS_DOCUMENTOS_EXTERNO'))
    );

    this.fileStoreOcorrencia.initialize(arquivosOcorrencia);
    this.fileStoreApresentacao.initialize(arquivosLicitacao);
    this.fileStoreFinalizacao.initialize(arquivosLicitacaoFinalizacao);
  }
}

export default OcorrenciaLicitacaoDetailStore;
