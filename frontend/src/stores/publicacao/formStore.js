import Publicacao from '../../domains/Publicacao';
import PublicacaoService from '../../services/PublicacaoService';
import FormBase from 'fc/stores/FormBase';
import { action, computed, observable, runInAction } from 'mobx';
import AsyncDropDownStore from 'fc/stores/AsyncDropdownStore';
import FormaPublicacao from '~/domains/FormaPublicacao';
import FormaPublicacaoService from '../../services/FormaPublicacaoService';

class PublicacaoFormStore extends FormBase {
  publicacaoList = observable.array();
  oldObject = { ...this.object };
  constructor() {
    super(PublicacaoService, Publicacao);
    this.formaPublicacaoStore = new AsyncDropDownStore(FormaPublicacao, FormaPublicacaoService, 'nome', 'id', {
      sort: {
        by: 'nome',
        order: 'asc',
      },
    });
  }

  initialize(idLicitacao, defaultValues = {}, callback) {
    this.object = Object.assign({}, defaultValues);
    this.actualIndex = idLicitacao;
    if (idLicitacao) {
      this.loading = true;
      this.service
        .getAllByLicitacao(idLicitacao)
        .then((response) =>
          runInAction(() => {
            const publicacoes = response.data;
            publicacoes.forEach((publicacao) => {
              this.publicacaoList.push(publicacao);
            });
            callback && callback();
          })
        )
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        )
        .finally(() =>
          runInAction(() => {
            this.loading = false;
          })
        );
    }
  }

  @computed
  get hasPagField() {
    return ['Diário Oficial da União (DOU)', 'Diário Oficial de Contas', 'Diário Oficial do Estado (DOE)'].includes(
      this.object.tpFormaPublicacao?.nome
    );
  }

  @action
  addPublicacao() {
    if (!this.hasPagField) {
      this.object.pagina = '';
    }
    this.object.pagina = this.object.pagina ?? '';
    if (this.object.key >= 0) {
      this.publicacaoList[this.object.key] = this.object;
    } else {
      this.publicacaoList.push(this.object);
    }
    this.object = {};
  }

  @action
  removePublicacao(position) {
    this.publicacaoList.splice(position, 1);
  }

  @computed
  get publicacaoListKeyed() {
    return this.publicacaoList.map((item, idx) => {
      item.key = idx;
      return item;
    });
  }

  @action
  setPublicacaoObject(obj) {
    this.object = { ...obj };
    this.oldObject = { ...obj };
  }

  @action
  resetPublicacaoOldObject() {
    this.object = this.oldObject;
  }

  @action
  resetPublicacaoObject() {
    this.object = Object.assign({}, {});
  }

  @action
  rulesDefinition() {
    let rules = {
      tpFormaPublicacao: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      dataPublicacao: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      descricao: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
    };
    if (this.hasPagField) {
      rules = { ...rules, pagina: [{ rule: 'required', message: 'Por favor, preencha o campo' }] };
    }
    return rules;
  }
}

export default PublicacaoFormStore;
