import FormBase from 'fc/stores/FormBase';
import FuncaoRiscoService from '~/services/FuncaoRiscoService';
import FuncaoRisco from '~/domains/FuncaoRisco';
import TipoProcessoService from '~/services/TipoProcessoService';
import TipoProcesso from '~/domains/TipoProcesso';
import { action, observable, override, runInAction } from 'mobx';
import { getNumberFractionDigits, getValueByKey, showErrorNotification, showNotification } from 'fc/utils/utils';
import TipoLicitacaoService from '~/services/TipoLicitacaoService';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import AsyncMultiselectStore from 'fc/stores/AsyncMultiselectStore';

class FuncaoRiscoFormStore extends FormBase {
  @observable conditions = [];
  @observable conditionsTemp = {};
  @observable response;
  @observable editingRows = [];

  constructor() {
    super(FuncaoRiscoService, FuncaoRisco);
    this.tiposProcessosStore = new AsyncMultiselectStore(TipoProcesso, TipoProcessoService, 'nome', 'id');
  }

  setToEditMode(rowKey) {
    this.conditionsTemp[rowKey] = { ...this.conditions[rowKey] };
  }

  removeEditMode(rowKey) {
    this.conditionsTemp[rowKey] = undefined;
  }

  setToPersistentMode(rowKey) {
    this.conditions[rowKey] = { ...this.conditionsTemp[rowKey] };
    this.conditionsTemp[rowKey] = undefined;
  }

  filterLegislacao(data) {
    return data.filter((item) => item.legislacao.includes('LEI_N_14133'));
  }

  getColumnsMatrizRisco() {
    return {
      VALOR_ESTIMADO: {
        value: 'VALOR_ESTIMADO',
        text: 'Valor estimado',
        type: 'numeric',
        bodyFormatter: (value) => getNumberFractionDigits(value, 3),
        optionsComponent: {
          decimalPlaces: 3,
          placeholder: 'Digite o valor',
        },
      },
      TIPOS_LICITACAO: {
        text: 'Tipo da licitação',
        type: 'list',
        loadData: () => TipoLicitacaoService.getAll(),
        handleResponse: (data) => this.filterLegislacao(data),
        bodyFormatter: (value) => value.join(', '),
        optionsComponent: {
          placeholder: 'Selecione os tipos de licitação',
          optionLabel: 'nome',
          optionValue: 'nome',
          filterBy: 'text',
        },
      },
      NATUREZAS_OBJETO: {
        text: 'Naturezas de objeto',
        type: 'list',
        loadData: () => DadosEstaticosService.getNaturezaObjetoLicitacao(),
        bodyFormatter: (value) =>
          value
            .map((v) => {
              const result = getValueByKey(v, DadosEstaticosService.getNaturezaObjetoLicitacao());
              return result === '-' ? v : result;
            })
            ?.join(', ') ?? '-',
        optionsComponent: {
          placeholder: 'Selecione as naturezas do objeto',
          optionLabel: 'text',
          optionValue: 'value',
          filterBy: 'text',
        },
      },
      PERMITE_CONSORCIO: {
        text: 'Permite consórcio',
        type: 'boolean',
        loadData: () => DadosEstaticosService.getSimNao(),
        bodyFormatter: (value) => getValueByKey(value, DadosEstaticosService.getSimNao()),
        optionsComponent: {
          placeholder: 'Permite consórcio',
          optionLabel: 'text',
          optionValue: 'value',
          filterBy: 'text',
        },
      },
    };
  }

  @override
  initialize(id, defaultValues = {}, callback) {
    if (!id) {
      this.object = Object.assign({}, defaultValues);
    } else {
      this.loading = true;
      this.service
        .getById(id)
        .then((response) =>
          runInAction(() => {
            this.object = response.data;
            this.conditions = JSON.parse(JSON.stringify(this.object.condicoes));
            const tipoColuna = DadosEstaticosService.getColumnsMatrizRisco(this.object.tipoProcesso)?.filter(
              (column) => column.value === this.object.nomeColuna
            );
            this.conditions.forEach((condicao, index) => {
              if (condicao.tipoComparacao === 'CONTEM' || condicao.tipoComparacao === 'INTERVALO') {
                condicao.valorComparavel = condicao.valorComparavel.split(';');
              } else if (tipoColuna === 'boolean') {
                condicao.valorComparavel = condicao.valorComparavel === '1' ? true : false;
              }
              condicao.key = index;
              condicao.persistent = true;
            });
            this.loadedObject = response.data;
            this.initializeLoadData(this.object.nomeColuna);
            callback && callback();
          })
        )
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        )
        .finally(() =>
          runInAction(() => {
            this.loading = false;
          })
        );
    }
  }

  @override
  save(callback, type = 'edit') {
    const saveObject = this.getObjectToSave(type);
    if (Object.keys(saveObject).length === 0) {
      showNotification('warn', null, 'Nenhuma alteração realizada.');
    } else {
      this.object.condicoes = this.conditions;
      const tipoColuna = DadosEstaticosService.getColumnsMatrizRisco(this.object.tipoProcesso).filter(
        (column) => column.value === this.object.nomeColuna
      );
      this.object.condicoes.map((condicao) => {
        if (condicao.tipoComparacao === 'CONTEM' || condicao.tipoComparacao === 'INTERVALO') {
          condicao.valorComparavel = condicao.valorComparavel.toString().replaceAll(',', ';');
        } else if (tipoColuna === 'boolean') {
          condicao.valorComparavel = condicao.valorComparavel ? 1 : 0;
        }
      });
      this.loading = true;
      this.service
        .save(this.object, type, this.object.id)
        .then(() => {
          callback && callback();
          showNotification('success', null, 'Registro salvo com sucesso!');
        })
        .catch((error) => showErrorNotification(error))
        .finally(() =>
          runInAction(() => {
            this.loading = false;
          })
        );
    }
  }

  rulesDefinition() {
    return {
      nome: [
        { rule: 'required', message: 'Por favor, preencha o campo' },
        { rule: 'isMaxLength', maxLength: 255, message: 'Por favor, diminua o tamanho do campo' },
      ],
      descricao: [{ rule: 'isMaxLength', maxLength: 500, message: 'Por favor, diminua o tamanho do campo' }],
      peso: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      nomeColuna: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      tiposProcessos: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      ativa: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
    };
  }

  @action
  initializeLoadData(value) {
    const loadData = this.getColumnsMatrizRisco()[value]?.loadData;
    if (loadData) {
      Promise.resolve(loadData())
        .then((response) =>
          runInAction(() => {
            const handleResponse = this.getColumnsMatrizRisco()[value].handleResponse;
            const data = response.data ?? response;
            this.response = handleResponse ? handleResponse(data) : data;
          })
        )
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        );
    }
  }

  @action
  newCondicao(callback) {
    const conditionInitialBody = {
      risco: undefined,
      tipoComparacao: undefined,
      valorComparavel: undefined,
      key: this.conditions.length,
      persistent: false,
    };
    this.conditions.push({ ...conditionInitialBody });
    this.conditionsTemp[this.conditions.length - 1] = conditionInitialBody;
    callback && callback();
  }

  @action
  resetConditionsTiposProcesso() {
    this.conditions = [];
    this.object.nomeColuna = undefined;
  }

  @action
  resetConditions() {
    this.conditions = [];
  }

  @action
  deleteCondicao(idx) {
    this.conditions.splice(idx, 1);
    this.conditions = this.conditions.map((condition, idx) => {
      return { ...condition, key: idx };
    });
  }

  @action
  updateAttributeNomeColuna(value) {
    this.object.nomeColuna = value;
    this.initializeLoadData(value);
  }

  @action
  updatePersistFlag(idx) {
    this.conditions[idx] = { ...this.conditions[idx], persistent: true };
  }

  @action
  updateAttributeRisco(value, idx) {
    this.conditionsTemp[idx] = { ...this.conditionsTemp[idx], risco: value };
  }

  @action
  handleRiscoChange(newValue, id) {
    const value = Math.min(newValue, 100);
    this.updateAttributeRisco(value, id);
  }

  @action
  updateAttributeTipoComparacao(value, idx, callback) {
    if (value === 'INTERVALO') {
      this.conditionsTemp[idx] = { ...this.conditionsTemp[idx], tipoComparacao: value, valorComparavel: [0, 0] };
    } else {
      this.conditionsTemp[idx] = {
        ...this.conditionsTemp[idx],
        tipoComparacao: value,
        valorComparavel: '',
      };
    }
    callback && callback();
  }

  @action
  updateAttributeValorComparavel(value, idx, idxInterval, callback) {
    if (this.conditionsTemp[idx].tipoComparacao === 'INTERVALO') {
      this.conditionsTemp[idx].valorComparavel[idxInterval] = value;
    } else {
      this.conditionsTemp[idx].valorComparavel = value;
    }
    callback && callback();
  }
}

export default FuncaoRiscoFormStore;
