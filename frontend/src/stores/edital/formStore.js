import FormBase from 'fc/stores/FormBase';
import Edital from '~/domains/Edital';
import MultipleFileUploaderStore from 'fc/stores/MultipleFileUploaderStore';
import EditalService from '~/services/EditalService';
import { action, observable, override, runInAction } from 'mobx';
import { showErrorNotification, showNotification } from 'fc/utils/utils';

class EditalFormStore extends FormBase {
  @observable arquivos;

  constructor() {
    super(EditalService, Edital);
    this.fileStore = new MultipleFileUploaderStore(
      [],
      (file) => this.service.upload(file),
      (fileDTO) => this.service.download(fileDTO),
      (idArquivo) => this.service.removerArquivo(idArquivo)
    );
  }

  rulesDefinition() {
    return {
      titulo: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      dataPublicacao: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
    };
  }

  @override
  save(callback, type = 'edit') {
    const saveObject = this.getObjectToSave(type);
    if (Object.keys(saveObject).length === 0) {
      showNotification('warn', null, 'Nenhuma alteração realizada.');
    } else if (!this.arquivos) {
      showNotification('error', null, 'O arquivo do Edital deve ser adicionado.');
    } else {
      this.loading = true;
      this.service
        .analisarEdital(this.object, this.arquivos)
        .then(() => {
          callback && callback();
          showNotification('success', null, 'Edital salvo com sucesso!');
        })
        .catch((error) => showErrorNotification(error))
        .finally(() =>
          runInAction(() => {
            this.loading = false;
          })
        );
    }
  }

  @action
  setFileList(list) {
    this.arquivos = list;
  }
}
export default EditalFormStore;
