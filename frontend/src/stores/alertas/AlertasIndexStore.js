import SearchTypes from 'fc/components/AdvancedSearch/SearchTypes';
import AlertaAnaliseEntidadeViewService from '~/services/AlertaAnaliseEntidadeViewService';
import AnalisarAlertaDafoService from '~/services/AnalisarAlertaDafoService';
import IndexBase from 'fc/stores/IndexBase';
import AlertaAnaliseEntidadeView from '~/domains/AlertaAnaliseEntidadeView';
import LicitacaoService from '~/services/LicitacaoService';
import CaronaService from '~/services/CaronaService';
import DispensaService from '~/services/DispensaService';
import InexigibilidadeService from '~/services/InexigibilidadeService';
import { action, observable, runInAction } from 'mobx';
import { getValueByKey, isValueValid, showErrorNotification, showNotification } from 'fc/utils/utils';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import TdaDispensaService from '~/services/TdaDispensaService';
import TdaLicitacaoService from '~/services/TdaLicitacaoService';
import TdaCaronaService from '~/services/TdaCaronaService';
import TdaInexigibilidadeService from '~/services/TdaInexigibilidadeService';
import AsyncDropDownStore from 'fc/stores/AsyncDropdownStore';
import UsuarioAuditorView from '~/domains/UsuarioAuditorView';
import UsuarioAuditorServiceView from '~/services/UsuarioAuditorViewService';
import Entidade from '~/domains/Entidade';
import EntidadeService from '~/services/EntidadeService';
import UsuarioDiretorView from '~/domains/UsuarioDiretorView';
import UsuarioDiretorViewService from '~/services/UsuarioDiretorViewService';
import CredenciamentoService from '~/services/CredenciamentoService';
import TdaCredenciamentoService from '~/services/TdaCredenciamentoService';

class AlertasIndexStore extends IndexBase {
  @observable processo;
  @observable tipoProcesso;
  @observable loadingProcesso = false;
  @observable object;
  @observable mensagem = null;
  @observable tda;

  constructor() {
    const domainsAndServices = {
      licitacao: {
        serviceProcesso: LicitacaoService,
        serviceTda: TdaLicitacaoService,
        tda: 'tdaLicitacao',
        idProcesso: 'idLicitacao',
      },
      carona: {
        serviceProcesso: CaronaService,
        serviceTda: TdaCaronaService,
        tda: 'tdaCarona',
        idProcesso: 'idCarona',
      },
      dispensa: {
        serviceProcesso: DispensaService,
        serviceTda: TdaDispensaService,
        tda: 'tdaDispensa',
        idProcesso: 'idDispensa',
      },
      inexigibilidade: {
        serviceProcesso: InexigibilidadeService,
        serviceTda: TdaInexigibilidadeService,
        tda: 'tdaInexigibilidade',
        idProcesso: 'idInexigibilidade',
      },
      credenciamento: {
        serviceProcesso: CredenciamentoService,
        serviceTda: TdaCredenciamentoService,
        tda: 'tdaCredenciamento',
        idProcesso: 'idCredenciamento',
      },
    };
    super(AlertaAnaliseEntidadeViewService, AlertaAnaliseEntidadeView, 'data', 'desc');
    this.domainsAndServices = domainsAndServices;

    this.updateMensagem = this.updateMensagem.bind(this);
    this.updateRespostaRejeicao = this.updateRespostaRejeicao.bind(this);
  }

  getAdvancedSearchParams() {
    return [
      {
        field: 'data',
        label: 'Data Emissão',
        type: SearchTypes.DATE,
      },
      {
        field: 'tipo',
        label: 'Tipo do Processo',
        type: SearchTypes.ENUM,
        options: DadosEstaticosService.getTipoProcesso(),
      },
      {
        field: 'usuarioResponsavel',
        label: 'Auditor Responsável',
        type: SearchTypes.ASYNC_QUERY,
        store: new AsyncDropDownStore(UsuarioAuditorView, UsuarioAuditorServiceView, 'nome', 'id'),
      },
      {
        field: 'diretorDafo',
        label: 'Diretor DAFO Responsável',
        type: SearchTypes.ASYNC_QUERY,
        store: new AsyncDropDownStore(UsuarioDiretorView, UsuarioDiretorViewService, 'nome', 'id'),
      },
      {
        field: 'entidade',
        label: 'Órgão/Entidade',
        type: SearchTypes.ASYNC_QUERY,
        store: new AsyncDropDownStore(Entidade, EntidadeService, 'nome', 'id'),
      },
    ];
  }

  @action
  loadProcesso(alerta, callback) {
    this.loadingProcesso = true;
    this.object = alerta;
    this.tipoProcesso = getValueByKey(alerta.tipo, DadosEstaticosService.getTiposProcesso(), 'key', 'lowerText');
    this.mensagem = this.object.mensagem;
    const processo = this.domainsAndServices[this.tipoProcesso];
    this.tda = alerta[processo.tda];
    processo.serviceProcesso
      .getById(this.tda[processo.idProcesso])
      .then((response) =>
        runInAction(() => {
          this.processo = response.data;
          this.loadingProcesso = false;
          callback && callback();
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      )
      .finally(() =>
        runInAction(() => {
          this.loadingProcesso = false;
        })
      );
  }

  @action
  updateMensagem(event) {
    let value = event;
    if (event) {
      if (isValueValid(event.target)) {
        value = event.target.value;
      } else if (isValueValid(event.value)) {
        value = event.value;
      } else if (typeof event.getData === 'function') {
        value = event.getData();
      } else {
        value = undefined;
      }
    }
    this.mensagem = value;
  }

  @action
  updateRespostaRejeicao(event) {
    let value = event;
    if (event) {
      if (isValueValid(event.target)) {
        value = event.target.value;
      } else if (isValueValid(event.value)) {
        value = event.value;
      } else {
        value = undefined;
      }
    }
    this.object.respostaRejeicao = value;
  }

  getFilterSuggest() {
    const filterSuggest = [];
    filterSuggest.push({
      id: '',
      field: 'status',
      operator: 'EQUAL_TO',
      value: 'ENCAMINHADO',
      formatted: '',
      invisible: true,
      fixed: true,
      completeParam: {
        field: 'status',
        label: 'Status',
        type: SearchTypes.ENUM,
        options: DadosEstaticosService.getStatusAlertaAnalise(),
      },
    });
    return filterSuggest;
  }

  @action
  rejeitarAlerta(alerta, callback, callbackOnFail) {
    if (!alerta?.respostaRejeicao) {
      showNotification('error', null, 'Por favor, informe um motivo para rejeição!');
      callbackOnFail && callbackOnFail();
      return;
    }

    AnalisarAlertaDafoService.rejeitarAlerta(alerta.id, alerta.respostaRejeicao)
      .then(() => {
        runInAction(() => {
          showNotification('success', null, 'Alerta rejeitado com sucesso!');
        });
      })
      .catch((error) => {
        runInAction(() => {
          showErrorNotification(error);
        });
      })
      .finally(() => {
        callback && callback();
      });
  }

  @action
  enviarAlertaJurisdicionado(alerta, callback) {
    AnalisarAlertaDafoService.enviarAlertaJurisdicionado(alerta.id, this.mensagem)
      .then(() => {
        runInAction(() => {
          showNotification('success', null, 'Alerta enviado com sucesso!');
          callback && callback();
        });
      })
      .catch((error) => {
        runInAction(() => {
          showErrorNotification(error);
        });
      })
      .finally(() => {
        callback && callback();
      });
  }
}

export default AlertasIndexStore;
