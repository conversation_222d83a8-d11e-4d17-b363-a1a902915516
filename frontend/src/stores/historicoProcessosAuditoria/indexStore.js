import IndexBase from 'fc/stores/IndexBase';
import { action, computed, makeObservable, observable, runInAction } from 'mobx';
import Entidade from '~/domains/Entidade';
import EntidadeService from '~/services/EntidadeService';
import AsyncDropDownStore from 'fc/stores/AsyncDropdownStore';
import SearchTypes from 'fc/components/AdvancedSearch/SearchTypes';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import AnaliseProcessoView from '~/domains/AnaliseProcessoView';
import AnaliseProcessoViewService from '~/services/AnaliseProcessoViewService';
import AppStore from 'fc/stores/AppStore';
import Usuario from '~/domains/Usuario';
import UsuarioService from '~/services/UsuarioService';
import { showErrorNotification } from 'fc/utils/utils';
import SearchOperators from 'fc/components/AdvancedSearch/SearchOperators';
import HistoricoProcessosAuditoriaViewService from '~/services/HistoricoProcessosAuditoriaView';

class HistoricoProcessosAuditoriaIndexStore extends IndexBase {
  @observable historicoProcessos = [];
  constructor() {
    super(AnaliseProcessoViewService, AnaliseProcessoView);

    makeObservable(this);
  }

  @computed
  get listHistoricoProcessosComkey() {
    const arrLength = this.historicoProcessos.length;
    return this.historicoProcessos.map((item, idx) => {
      if (!item.key) {
        item.key = arrLength - idx;
      }
      return item;
    });
  }

  @action
  loadHistoricoProcesso(idProcesso, tipoProcesso, callback) {
    const filtro = {
      page: { index: 1, size: 100 },
      sort: {
        by: 'idAuditoria',
        order: 'desc',
      },
      andParameters: [
        { field: 'idProcesso', operator: SearchOperators.EQUAL_TO.value, value: idProcesso },
        { field: 'tipoProcesso', operator: SearchOperators.EQUAL_TO.value, value: tipoProcesso },
      ],
    };

    HistoricoProcessosAuditoriaViewService.advancedSearch(filtro)
      .then((response) =>
        runInAction(() => {
          this.historicoProcessos = response?.data?.items ? [...response.data.items] : [];
          callback && callback();
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      );
  }

  getAdvancedSearchParams() {
    return [
      {
        field: 'entidade',
        label: 'Órgão/Entidade',
        type: SearchTypes.ASYNC_QUERY,
        store: new AsyncDropDownStore(Entidade, EntidadeService, 'nome', 'id'),
      },
      {
        field: 'analista',
        label: 'Auditor Atribuidor',
        type: SearchTypes.ASYNC_QUERY,
        store: new AsyncDropDownStore(Usuario, UsuarioService, 'nome', 'id'),
      },
      {
        field: 'responsavel',
        label: 'Auditor Responsável',
        type: SearchTypes.ASYNC_QUERY,
        store: new AsyncDropDownStore(Usuario, UsuarioService, 'nome', 'id'),
      },
      {
        field: 'numero',
        label: 'Número',
        type: SearchTypes.TEXT,
      },
      {
        field: 'dataCadastro',
        label: 'Data de Cadastro',
        type: SearchTypes.DATE,
      },
      {
        field: 'dataProcesso',
        label: 'Data do Processo',
        type: SearchTypes.DATE,
      },
      {
        field: 'tipoProcesso',
        label: 'Tipo do Processo',
        type: SearchTypes.ENUM,
        options: DadosEstaticosService.getTipoProcesso(),
      },
      {
        field: 'objeto',
        label: 'Objeto',
        type: SearchTypes.TEXT,
      },
      {
        field: 'valor',
        label: 'Valor',
        type: SearchTypes.NUMBER,
      },
    ];
  }

  getFilterSuggest() {
    const entidade = AppStore.getContextEntity();
    const filterSuggest = [];
    if (entidade) {
      filterSuggest.push({
        id: '',
        field: 'entidade',
        operator: 'EQUAL_TO',
        value: entidade,
        formatted: '',
        fixed: true,
        invisible: true,
        completeParam: {
          field: 'entidade',
          label: 'Órgão/Entidade',
          type: SearchTypes.ASYNC_QUERY,
          store: new AsyncDropDownStore(Entidade, EntidadeService, 'nome', 'id'),
        },
      });
    }
    filterSuggest.push({
      id: '',
      field: 'tipoProcesso',
      operator: 'EQUAL_TO',
      value: 'L',
      formatted: '',
      fixed: true,
      completeParam: {
        field: 'tipoProcesso',
        label: 'Tipo do Processo',
        type: SearchTypes.ENUM,
        options: DadosEstaticosService.getTipoProcesso(),
      },
    });

    return filterSuggest;
  }
}

export default HistoricoProcessosAuditoriaIndexStore;
