import SearchTypes from 'fc/components/AdvancedSearch/SearchTypes';
import Dispensa from '~/domains/Dispensa';
import DispensaService from '../../services/DispensaService';
import IndexBase from 'fc/stores/IndexBase';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import AsyncDropDownStore from 'fc/stores/AsyncDropdownStore';
import AsyncMultiselectStore from 'fc/stores/AsyncMultiselectStore';
import FonteRecurso from '~/domains/FonteRecurso';
import FonteRecursoService from '~/services/FonteRecursoService';
import Entidade from '~/domains/Entidade';
import EntidadeService from '~/services/EntidadeService';
import Usuario from '~/domains/Usuario';
import UsuarioService from '~/services/UsuarioService';
import Licitante from '~/domains/Licitante';
import LicitanteService from '~/services/LicitanteService';
import AppStore from 'fc/stores/AppStore';
import moment from 'moment';
import { runInAction } from 'mobx';
import { checkUserGroup, showErrorNotification } from 'fc/utils/utils';

class DispensaIndexStore extends IndexBase {
  constructor() {
    super(DispensaService, Dispensa, 'updatedAt', 'desc');
  }

  isRequisicaoModificacao(dataCadastro) {
    let result = false;
    if (dataCadastro) {
      const data = moment(dataCadastro);
      const current = moment();
      const diff = moment.duration(current.diff(data));

      if (diff.asHours() > 24) {
        result = true;
      }
    }
    return result;
  }

  getAdvancedSearchParams() {
    const isAuditor = checkUserGroup(['Auditor', 'Auditor - Demais Inspetorias']);
    let searchParams = [];

    if (isAuditor) {
      searchParams = [
        {
          field: 'entidade',
          label: 'Órgão/Entidade',
          type: SearchTypes.ASYNC_QUERY,
          store: new AsyncDropDownStore(Entidade, EntidadeService, 'nome', 'id'),
        },
      ];
    }
    searchParams.push(
      {
        field: 'dataPedido',
        label: 'Data da Autorização',
        type: SearchTypes.DATE,
      },
      {
        field: 'numeroProcesso',
        label: 'Número do Processo',
        type: SearchTypes.TEXT,
      },
      {
        field: 'objeto',
        label: 'Objeto',
        type: SearchTypes.TEXT,
      },
      {
        field: 'fontesDeRecurso',
        label: 'Fontes de Recurso',
        type: SearchTypes.ASYNC_QUERY,
        store: new AsyncMultiselectStore(FonteRecurso, FonteRecursoService, 'nome', 'id'),
      },
      {
        field: 'usuario',
        label: 'Usuário',
        type: SearchTypes.ASYNC_QUERY,
        store: new AsyncDropDownStore(Usuario, UsuarioService, 'nome', 'id'),
      },
      {
        field: 'status',
        label: 'Status do Processo',
        type: SearchTypes.ENUM,
        options: DadosEstaticosService.getStatusLicitacao(),
      },
      {
        field: 'dataCadastro',
        label: 'Data de Cadastro',
        type: SearchTypes.DATE,
      },
      {
        field: 'justificativa',
        label: 'Justificativa',
        type: SearchTypes.TEXT,
      },
      {
        field: 'elementoDespesa',
        label: 'Elemento de Despesa',
        type: SearchTypes.TEXT,
      },
      {
        field: 'observacoes',
        label: 'Observações',
        type: SearchTypes.TEXT,
      },
      {
        field: 'licitantes',
        label: 'Fornecedor',
        type: SearchTypes.ASYNC_QUERY,
        store: new AsyncMultiselectStore(Licitante, LicitanteService, 'nome', 'id'),
      },
      {
        field: 'valor',
        label: 'Valor',
        type: SearchTypes.NUMBER,
      },
      {
        field: 'numeroDownloads',
        label: 'Número de Downloads',
        type: SearchTypes.NUMBER,
      },
      {
        field: 'houveModificacao',
        label: 'Requisição de MOdificação Pendente',
        type: SearchTypes.ENUM,
        options: DadosEstaticosService.getSimNao(),
      }
    );
    return searchParams;
  }

  getFilterSuggest() {
    const filterSuggest = [
      {
        id: '',
        field: 'status',
        operator: 'NOT_EQUAL_TO',
        value: 'REMOVIDA',
        formatted: '',
        fixed: true,
        invisible: true,
        completeParam: {
          field: 'status',
          label: 'Status',
          type: SearchTypes.ENUM,
          options: DadosEstaticosService.getStatusContrato(),
        },
      },
    ];

    const entidade = AppStore.getContextEntity();
    if (entidade) {
      filterSuggest.push({
        id: '',
        field: 'entidade',
        operator: 'EQUAL_TO',
        value: entidade,
        formatted: '',
        fixed: true,
        invisible: true,
        completeParam: {
          field: 'entidade',
          label: 'Órgão/Entidade',
          type: SearchTypes.ASYNC_QUERY,
          store: new AsyncDropDownStore(Entidade, EntidadeService, 'nome', 'id'),
        },
      });
    }

    return filterSuggest;
  }

  getById(id, callback) {
    if (id) {
      this.service
        .getById(id)
        .then((response) => {
          return callback && runInAction(() => callback(response.data));
        })
        .catch((e) => {
          showErrorNotification(e);
        });
    }
  }
}

export default DispensaIndexStore;
