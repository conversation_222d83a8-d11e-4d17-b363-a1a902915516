import { action, makeObservable, observable, runInAction } from 'mobx';
import AppStore from 'fc/stores/AppStore';
import { objectToText } from 'fc/utils/utils';
import AuthService from '~/services/AuthService';

class LoginStore {
  @observable loading = false;
  @observable error;
  @observable errorType = 'warn';

  constructor() {
    makeObservable(this);
  }

  @action
  sendLogin(username, password, callback) {
    this.loading = true;
    AuthService.authenticate({ username, password })
      .then((response) =>
        runInAction(() => {
          if (response.data.jwtToken) {
            localStorage.setItem('token', response.data.jwtToken);
            localStorage.setItem('userDetails', objectToText(response.data.usuario));
            AppStore.setData('userDetails', response.data.usuario);
            this.error = undefined;
            callback && callback();
          }
        })
      )
      .catch((error) =>
        runInAction(() => {
          if (error && error.response && error.response.status < 500) {
            this.error = 'Não foi possível realizar a autenticação! Verifique seu login e senha!';
            this.errorType = 'warn';
          } else if (error && error.response && error.response.status >= 500) {
            this.error = 'Ocorreu um erro no servidor!';
            this.errorType = 'error';
          } else {
            this.error = 'Ocorreu um erro!';
            this.errorType = 'error';
          }
        })
      )
      .finally(() =>
        runInAction(() => {
          this.loading = false;
        })
      );
  }
}

export default LoginStore;
