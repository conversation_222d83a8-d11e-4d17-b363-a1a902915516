import SearchTypes from 'fc/components/AdvancedSearch/SearchTypes';
import AppStore from 'fc/stores/AppStore';
import AsyncDropDownStore from 'fc/stores/AsyncDropdownStore';
import IndexBase from 'fc/stores/IndexBase';
import { observable } from 'mobx';
import Entidade from '~/domains/Entidade';
import Acompanhamento from '~/domains/geoObras/Acompanhamento';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import EntidadeService from '~/services/EntidadeService';
import AcompanhamentoService from '~/services/geoObras/AcompanhamentoService';
import MedicaoFormStore from '~/stores/geoObras/acompanhamento/medicao/formStore';
import EntregaObraFormStore from '~/stores/geoObras/obra/formEntregaStore';
import FimObraFormStore from '~/stores/geoObras/obra/formFimStore';
import InicioObraFormStore from '~/stores/geoObras/obra/formInicioStore';
import InterrupcaoObraFormStore from '../obra/formInterrupcaoStore';
import ObraFormStore from '../obra/formStore';

class AcompanhamentoIndexStore extends IndexBase {
  @observable storeMedicao;

  constructor() {
    super(AcompanhamentoService, Acompanhamento, 'numero', 'desc');
    this.storeObra = new ObraFormStore();
    this.storeMedicao = new MedicaoFormStore();
    this.storeObraInicio = new InicioObraFormStore();
    this.storeObraFim = new FimObraFormStore();
    this.storeObraInterrupcao = new InterrupcaoObraFormStore();

    this.storeObraEntrega = new EntregaObraFormStore();

    this.storeMedicao.initialize(null, {});
  }

  getAdvancedSearchParams() {
    return [
      {
        field: 'numero',
        label: 'Número',
        type: SearchTypes.TEXT,
      },
      {
        field: 'descricao',
        label: 'Descrição',
        type: SearchTypes.TEXT,
      },
      {
        field: 'valor',
        label: 'Valor',
        type: SearchTypes.NUMBER,
      },
      {
        field: 'status',
        label: 'Status',
        type: SearchTypes.ENUM,
        options: DadosEstaticosService.getStatusObra(),
      },
    ];
  }

  getFilterSuggest() {
    const entidade = AppStore.getContextEntity();
    const filterSuggest = [];

    if (entidade) {
      filterSuggest.push({
        id: '',
        field: 'entidade',
        operator: 'EQUAL_TO',
        value: entidade,
        formatted: '',
        fixed: true,
        invisible: true,
        completeParam: {
          field: 'entidade',
          label: 'Órgão/Entidade',
          type: SearchTypes.ASYNC_QUERY,
          store: new AsyncDropDownStore(Entidade, EntidadeService, 'nome', 'id'),
        },
      });
    }

    return filterSuggest;
  }
}

export default AcompanhamentoIndexStore;
