import IndexBase from 'fc/stores/IndexBase';
import MultipleFileUploaderStore from 'fc/stores/MultipleFileUploaderStore';
import { showErrorNotification } from 'fc/utils/utils';
import { action, makeObservable, observable, runInAction } from 'mobx';
import Medicao from '~/domains/geoObras/Medicao';
import MedicaoService from '~/services/geoObras/MedicaoService';

class MedicaoIndexStore extends IndexBase {
  @observable medicoes;

  constructor(idObra) {
    super(MedicaoService, Medicao);
    this.fileStore = new MultipleFileUploaderStore(
      [],
      (file) => MedicaoService.upload(idObra, file),
      (key) => this.downloadArquivo(key),
      (idArquivo) => this.removerArquivoMedicao(idArquivo),
      (idArquivo, arquivoDTO) => this.atualizarArquivoMedicao(idArquivo, arquivoDTO)
    );
    this.carregarPorObra = this.carregarPorObra.bind(this);
    makeObservable(this);
  }

  @action
  carregarPorObra(idObra) {
    this.loading = true;
    MedicaoService.carregarPorObra(idObra)
      .then((response) =>
        runInAction(() => {
          this.medicoes = response.data;
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      )
      .finally(() =>
        runInAction(() => {
          this.loading = false;
        })
      );
  }

  downloadArquivo(key) {
    const arquivo = this.fileStore.keyedUploadedFiles.find((f) => f.key === key);
    return MedicaoService.download(this.obra.id, arquivo.arquivo);
  }
}

export default MedicaoIndexStore;
