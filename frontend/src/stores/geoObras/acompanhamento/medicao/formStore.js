import AppStore from 'fc/stores/AppStore';
import AsyncDropDownStore from 'fc/stores/AsyncDropdownStore';
import FormBase from 'fc/stores/FormBase';
import MultipleFileUploaderStore from 'fc/stores/MultipleFileUploaderStore';
import { getNumberFractionDigits, showErrorNotification, showNotification } from 'fc/utils/utils';
import { action, makeObservable, observable, override, runInAction } from 'mobx';
import moment from 'moment';
import EmpenhoContrato from '~/domains/EmpenhoContrato';
import Medicao from '~/domains/geoObras/Medicao';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import EmpenhoContratoService from '~/services/EmpenhoContratoService';
import MedicaoService from '~/services/geoObras/MedicaoService';
import ObrigatoriedadeArquivoService from '~/services/ObrigatoriedadeArquivoService';

class MedicaoFormStore extends FormBase {
  @observable idMedicao;
  @observable arquivoMedicaoList = [];
  @observable fileStore;
  @observable obra;
  @observable fase;
  constructor() {
    super(MedicaoService, Medicao);
    this.fase = 'MEDICAO';
    this.fileStore = new MultipleFileUploaderStore(
      [],
      (file) => MedicaoService.upload(this.obra.id, file),
      (fileDTO) => this.downloadArquivo(fileDTO),
      (idArquivo) => this.removerArquivoMedicao(idArquivo),
      (idArquivo, arquivoDTO) => this.atualizarArquivoMedicao(idArquivo, arquivoDTO)
    );

    this.setArquivoMedicaoList = this.setArquivoMedicaoList.bind(this);
    this.updateAttributeFile = this.updateAttributeFile.bind(this);
    makeObservable(this);
  }

  @override
  initialize(id, obra, callback) {
    this.setArquivoMedicaoList([]);
    this.fileStore.initialize([]);
    this.obra = obra;
    ObrigatoriedadeArquivoService.getArquivosObrigatorios({ tipoProcesso: 'MEDICAO', filtros: ['MEDICAO'] }).then(
      (response) =>
        runInAction(() => {
          const arquivosObrigatorios = response?.data ?? [];
          this.fileStore.tipoArquivoEnum = arquivosObrigatorios.map((arqObg) => {
            const arq = DadosEstaticosService.getTipoArquivoMedicaoObra().find(
              (arq) => arq.value === arqObg.arquivoEnum
            );
            return { ...arqObg, text: arqObg.obrigatorio ? '* ' + arqObg.arquivo : arqObg.arquivo, value: arq.value };
          });

          super.initialize(id, { obra: { id: id } }, callback);
          !id && callback && callback();
        })
    );

    this.storeEmpenho = new AsyncDropDownStore(
      EmpenhoContrato,
      EmpenhoContratoService,
      'numeroEmpenho',
      'id',
      {
        sort: {
          by: 'numeroEmpenho',
          order: 'asc',
        },
        andParameters: [
          { field: 'contrato', operator: 'EQUAL_TO', value: obra?.contrato?.id },
          { field: 'status', operator: 'NOT_EQUAL_TO', value: 'REMOVIDA' },
        ],
      },
      (empenho) => `${empenho?.numeroEmpenho ?? ''}`
    );
  }

  @action
  initializeArquivos(obra, idMedicao, callback) {
    this.obra = obra;
    if (this.obra.id) {
      this.service
        .recuperarArquivos(this.obra.id, idMedicao)
        .then((response) =>
          runInAction(() => {
            const arquivosRecuperados = response.data;
            this.fileStore.initialize(arquivosRecuperados);
            this.arquivos = arquivosRecuperados;
          })
        )
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        )
        .finally(() =>
          runInAction(() => {
            callback && callback();
          })
        );
    }
  }

  downloadFotos = (fotos) => {
    const promisses = [];
    fotos?.map((a) => {
      const p = new Promise((resolve) => {
        this.fileStore?.downloadFile(a.arquivo, null, (link) => resolve(link));
      });
      promisses.push(p);
    });
    return Promise.all(promisses);
  };

  downloadArquivo(fileDTO) {
    return MedicaoService.download(this.obra.id, fileDTO);
  }

  removerArquivoMedicao(idArquivo) {
    return MedicaoService.removerArquivo(this.obra.id, this.idMedicao, idArquivo);
  }

  atualizarArquivoMedicao(idArquivo, arquivoMedicaoDTO) {
    return MedicaoService.atualizarArquivo(this.obra.id, this.idMedicao, idArquivo, arquivoMedicaoDTO);
  }

  rulesDefinition() {
    let rules = {
      dataInicio: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      dataFim: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      valor: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      percentualConclusao: [
        { rule: 'required', message: 'Por favor, preencha o campo' },
        {
          rule: 'isMaxValue',
          maxValue: 100,
          message: 'Por favor, defina uma quantidade menor ou igual à 100%',
        },
      ],
      empenho: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
    };

    if (this.obra?.ultimaMedicao) {
      rules = this.mergeRules(rules, {
        dataInicio: [
          {
            rule: 'isAfterDateWarning',
            message: 'Por favor, selecione uma data superior à data final da última medição',
            dateWarning: moment(this.obra.ultimaMedicao),
          },
        ],
      });
    } else if (this.obra?.dataInicio) {
      rules = this.mergeRules(rules, {
        dataInicio: [
          {
            rule: 'isAfterDateWarning',
            message: 'Por favor, selecione uma data superior à data de inicio da obra',
            dateWarning: moment(this.obra.dataInicio).subtract(1, 'days'),
          },
        ],
      });
    }

    if (this.obra?.percentualConclusao) {
      rules.percentualConclusao.push({
        rule: 'isMinValue',
        maxValue: this.obra.percentualConclusao,
        message: ` Por favor, defina um percentual maior que ${getNumberFractionDigits(
          this.obra.percentualConclusao
        )}%`,
      });
    }
    return rules;
  }

  validaDadosBasicos() {
    const rules = this.rulesDefinition();
    const dadosBasicos = Object.keys(rules).filter((k) => rules[k].find((r) => r.rule === 'required'));
    const required = Object.keys(rules).filter(
      (k) => dadosBasicos.includes(k) && rules[k].find((r) => r.rule === 'required')
    );
    const campos = required.filter(
      (k) =>
        this.object[k] === undefined ||
        this.object[k] === null ||
        this.object[k] === '' ||
        (Array.isArray(this.object[k]) && this.object[k].length === 0)
    );

    const notaFiscal = this.arquivoMedicaoList?.find((file) => file.tipo === 'NOTA_FISCAL');
    const notaFiscalObrigatorio = this.fileStore.tipoArquivoEnum.find(
      (tipo) => tipo.value === 'NOTA_FISCAL' && tipo.obrigatorio
    );
    const georreferenciamento = this.arquivoMedicaoList?.find((file) => file.tipo === 'GEORREFERENCIAMENTO');
    const georreferenciamentoObrigatorio = this.fileStore.tipoArquivoEnum.find(
      (tipo) => tipo.value === 'GEORREFERENCIAMENTO' && tipo.obrigatorio
    );
    return (
      campos.length === 0 &&
      (notaFiscal || !notaFiscalObrigatorio) &&
      (georreferenciamento || !georreferenciamentoObrigatorio)
    );
  }

  validaFotos() {
    const fotos = this.arquivoMedicaoList?.filter((file) => file.tipo === 'FOTO');
    const fotosObrigatorio = this.fileStore.tipoArquivoEnum.find((tipo) => tipo.value === 'FOTO' && tipo.obrigatorio);
    return fotos?.length > 0 || !fotosObrigatorio;
  }

  validaDocumentos() {
    const relatorios = this.arquivoMedicaoList?.filter((file) => file.tipo === 'RELATORIO_TECNICO');
    const relatoriosObrigatorio = this.fileStore.tipoArquivoEnum.find(
      (tipo) => tipo.value === 'RELATORIO_TECNICO' && tipo.obrigatorio
    );
    const planilhas = this.arquivoMedicaoList?.filter((file) => file.tipo === 'PLANILHA_MEDICAO');
    const planilhasObrigatorio = this.fileStore.tipoArquivoEnum.find(
      (tipo) => tipo.value === 'PLANILHA_MEDICAO' && tipo.obrigatorio
    );

    return (
      (relatorios?.length > 0 || !relatoriosObrigatorio?.value) &&
      (planilhas?.length > 0 || !planilhasObrigatorio?.value)
    );
  }

  validaTiposArquivos() {
    const arquivosSemTipo = this.arquivoMedicaoList?.filter((f) => !f.tipo);
    return !this.arquivoMedicaoList || arquivosSemTipo?.length === 0;
  }

  validaMedicao() {
    return this.validaDadosBasicos() && this.validaFotos() && this.validaDocumentos() && this.validaTiposArquivos();
  }

  @action
  setArquivoMedicaoList(arquivoMedicaoList) {
    this.arquivoMedicaoList = arquivoMedicaoList;
  }

  @action
  updateAttributeFile = (key, attribute, value) => {
    const arquivos = this.arquivoMedicaoList.map((arquivo) => {
      if (key && arquivo.key === key) {
        arquivo[attribute] = value;
      }
      return arquivo;
    });
    this.setArquivoMedicaoList(arquivos);
  };

  @action
  validaArquivos(callback) {
    this.loading = true;
    this.service
      .validaArquivos(this.obra.id, {
        arquivosMedicao: this.arquivoMedicaoList,
        entidade: AppStore.getContextEntity(),
      })
      .then(() =>
        runInAction(() => {
          callback && callback();
        })
      )
      .catch((error) => showErrorNotification(error))
      .finally(() =>
        runInAction(() => {
          this.loading = false;
        })
      );
  }

  @action
  criarMedicao(callback) {
    this.loading = true;
    const saveObject = this.getObjectToSave();
    const medicaoDTO = {
      id: this.object.id,
      medicao: this.object,
      arquivosMedicao: this.arquivoMedicaoList,
      entidade: AppStore.getContextEntity(),
    };

    if (!Object.keys(saveObject).length) {
      showNotification('warn', null, 'Nenhuma alteração realizada.');
      this.loading = false;
    } else {
      this.service
        .criarMedicao(this.obra.id, medicaoDTO)
        .then(() =>
          runInAction(() => {
            callback && callback();
            showNotification('success', null, 'Registro salvo com sucesso!');
          })
        )
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        )
        .finally(() =>
          runInAction(() => {
            this.loading = false;
          })
        );
    }
  }
}

export default MedicaoFormStore;
