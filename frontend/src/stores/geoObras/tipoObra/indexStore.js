import SearchTypes from 'fc/components/AdvancedSearch/SearchTypes';
import IndexBase from 'fc/stores/IndexBase';
import TipoObra from '~/domains/geoObras/TipoObra';
import TipoObraService from '~/services/geoObras/TipoObraService';

class TipoObraIndexStore extends IndexBase {
  constructor() {
    super(TipoObraService, TipoObra, 'nome');
  }

  getAdvancedSearchParams() {
    return [
      {
        field: 'nome',
        label: 'Nome',
        type: SearchTypes.TEXT,
      },
      {
        field: 'descricao',
        label: 'Descrição',
        type: SearchTypes.TEXT,
      },
    ];
  }
}

export default TipoObraIndexStore;
