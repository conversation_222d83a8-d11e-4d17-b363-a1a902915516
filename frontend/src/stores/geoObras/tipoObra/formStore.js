import FormBase from 'fc/stores/FormBase';
import TipoObra from '~/domains/geoObras/TipoObra';
import TipoObraService from '~/services/geoObras/TipoObraService';

class TipoObraFormStore extends FormBase {
  constructor() {
    super(TipoObraService, TipoObra);
  }

  rulesDefinition() {
    return {
      nome: [
        { rule: 'required', message: 'Por favor, preencha o campo' },
        { rule: 'isMaxLength', maxLength: 255, message: 'Por favor, diminua o tamanho do campo' },
      ],
      tipoDimensao: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      descricao: [{ rule: 'isMaxLength', maxLength: 4000, message: 'Por favor, diminua o tamanho do campo' }],
    };
  }
}

export default TipoObraFormStore;
