import { action, observable, runInAction } from 'mobx';
import SearchTypes from 'fc/components/AdvancedSearch/SearchTypes';
import MultipleFileUploaderStore from 'fc/stores/MultipleFileUploaderStore';
import IndexBase from 'fc/stores/IndexBase';
import RelatorioObraService from '~/services/geoObras/RelatorioObraService';
import ObrigatoriedadeArquivoService from '~/services/ObrigatoriedadeArquivoService';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import RelatorioObra from '~/domains/geoObras/RelatorioObra';
import { showErrorNotification, showNotification } from 'fc/utils/utils';
import AppStore from 'fc/stores/AppStore';

class RelatorioObraIndexStore extends IndexBase {
  @observable newRelatorio = new RelatorioObra();
  @observable arquivos = [];

  constructor() {
    super(RelatorioObraService, RelatorioObra, 'dataCadastro', 'desc');
    this.fileStore = new MultipleFileUploaderStore(
      [],
      (file) => RelatorioObraService.upload(file),
      (fileDTO) => RelatorioObraService.download(fileDTO),
      (idArquivo) => RelatorioObraService.removerArquivo(idArquivo),
      (idArquivo, arquivoRelatorioObraDTO) => RelatorioObraService.atualizarArquivo(idArquivo, arquivoRelatorioObraDTO)
    );
  }

  getFilterSuggest() {
    const usuario = AppStore.getData('userDetails');

    const filterSuggest = [
      {
        id: '',
        field: 'usuario',
        operator: 'EQUAL_TO',
        value: usuario?.id,
        formatted: '',
        fixed: true,
        invisible: true,
        completeParam: {
          field: 'usuario',
          label: 'Usuario',
          type: SearchTypes.NUMBER,
        },
      },
    ];

    return filterSuggest;
  }

  @action
  clear() {
    this.newRelatorio = new RelatorioObra();
    this.arquivos = [];
    this.fileStore.removeAllFiles();
  }

  @action
  setArquivos(arquivos) {
    this.arquivos = arquivos;
  }

  @action
  carregarArquivosObrigatorios() {
    ObrigatoriedadeArquivoService.getArquivosObrigatorios({
      tipoProcesso: 'RELATORIO_OBRA',
      filtros: ['RELATORIO_OBRA'],
    })
      .then((response) =>
        runInAction(() => {
          const arquivosObrigatorios = response?.data ?? [];
          this.fileStore.tipoArquivoEnum = arquivosObrigatorios.map((arqObg) => {
            const arq = DadosEstaticosService.getTipoArquivoRelatorioObra().find(
              (arq) => arq.value === arqObg.arquivoEnum
            );
            return { ...arqObg, text: arqObg.obrigatorio ? '* ' + arqObg.arquivo : arqObg.arquivo, value: arq.value };
          });
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      );
  }

  @action
  updateAttribute(attribute, event) {
    let value = event;
    if (event && event.value) {
      value = event.value;
    }
    if (event && event.target) {
      value = event.target.value;
    }
    this.newRelatorio[attribute] = value;
  }

  @action
  importarRelatorioObra(callback, loadingCallback) {
    const relatorioObraDTO = {
      arquivos: this.arquivos,
      relatorioObra: this.newRelatorio,
    };

    this.service
      .importarRelatorioObra(relatorioObraDTO)
      .then((response) =>
        runInAction(() => {
          callback && callback();
          loadingCallback && loadingCallback();
          showNotification('success', null, response.data);
        })
      )
      .catch((error) =>
        runInAction(() => {
          loadingCallback && loadingCallback();
          showErrorNotification(error);
        })
      );
  }

  getFilterSuggestLicitacao() {
    const filterSuggestLicitacao = [];
    return filterSuggestLicitacao;
  }

  getAdvancedSearchParams() {
    return [
      {
        field: 'titulo',
        label: 'Título',
        type: SearchTypes.TEXT,
      },
      {
        field: 'dataAnalise',
        label: 'Data Analise',
        type: SearchTypes.DATE_TIME,
      },
    ];
  }
}

export default RelatorioObraIndexStore;
