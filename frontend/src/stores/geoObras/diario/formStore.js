import FormBase from 'fc/stores/FormBase';
import MultipleFileUploaderStore from 'fc/stores/MultipleFileUploaderStore';
import { showErrorNotification, showNotification } from 'fc/utils/utils';
import { action, computed, makeObservable, observable, runInAction } from 'mobx';
import DiarioObra from '~/domains/DiarioObra';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import DiarioObraService from '~/services/geoObras/DiarioObraService';
import ObrigatoriedadeArquivoService from '~/services/ObrigatoriedadeArquivoService';

class DiarioObraFormStore extends FormBase {
  obraId;
  fileStore;
  @observable arquivosDiarioObra = [];

  constructor(obraId) {
    super(DiarioObraService, DiarioObra);
    makeObservable(this);

    this.setArquivoList = this.setArquivoList.bind(this);
    this.fileStore = new MultipleFileUploaderStore(
      [],
      (file) => DiarioObraService.upload(file),
      (fileDTO) => DiarioObraService.download(fileDTO),
      (idArquivo) => this.removerArquivoDiarioObra(idArquivo),
      (idArquivo, arquivoDispensaDTO) => this.atualizarArquivoDiarioObra(idArquivo, arquivoDispensaDTO)
    );
    this.obraId = obraId;

    this.loadTipos();
  }

  initialize(id, defaultValues = {}, callback) {
    super.initialize(id, defaultValues, callback);
    if (!id) {
      callback && callback();
    }
  }

  save(callback) {
    this.loading = true;
    const diarioObraDTO = {
      id: this.object.id,
      diarioObra: this.object,
      arquivosDiarioObra: this.arquivosDiarioObra ?? [],
      obra: { id: this.obraId },
    };

    this.service
      .criarDiarioObra(diarioObraDTO)
      .then(() =>
        runInAction(() => {
          callback && callback();
          showNotification('success', null, 'Registro salvo com sucesso!');
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      )
      .finally(() =>
        runInAction(() => {
          this.loading = false;
        })
      );
  }

  @computed
  get arquivosValidos() {
    return this.arquivosDiarioObra?.length > 0 && !this.arquivosDiarioObra.some((a) => !a.tipo);
  }

  @action
  validaArquivos(callback) {
    this.loading = true;
    this.service
      .validaArquivos({
        arquivosDiarioObra: this.arquivosDiarioObra ?? [],
      })
      .then(() =>
        runInAction(() => {
          callback && callback();
        })
      )
      .catch((error) => showErrorNotification(error))
      .finally(() =>
        runInAction(() => {
          this.loading = false;
        })
      );
  }

  @action
  loadTipos() {
    ObrigatoriedadeArquivoService.getArquivosObrigatorios({ tipoProcesso: 'DIARIO_OBRA', filtros: ['DIARIO_OBRA'] })
      .then((response) =>
        runInAction(() => {
          const arquivosObrigatorios = response?.data ?? [];
          this.fileStore.tipoArquivoEnum = arquivosObrigatorios.map((arqObg) => {
            const arq = DadosEstaticosService.getTipoArquivoDiarioObra().find(
              (arq) => arq.value === arqObg.arquivoEnum
            );
            return { ...arqObg, text: arqObg.obrigatorio ? '* ' + arqObg.arquivo : arqObg.arquivo, value: arq.value };
          });
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      );
  }

  @action
  recuperarArquivos(idDiarioObra, callback) {
    idDiarioObra &&
      this.service
        .recuperarArquivos(idDiarioObra)
        .then((response) =>
          runInAction(() => {
            this.arquivosDiarioObra = response.data;
            this.fileStore.initialize(this.arquivosDiarioObra);
          })
        )
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        )
        .finally(() =>
          runInAction(() => {
            callback && callback();
          })
        );
  }

  removerArquivoDiarioObra(idArquivo) {
    return DiarioObraService.removerArquivo(this.object?.id, idArquivo);
  }

  atualizarArquivoDiarioObra(idArquivo, arquivoDiarioObraDTO) {
    return DiarioObraService.atualizarArquivo(this.object?.id, idArquivo, arquivoDiarioObraDTO);
  }

  rulesDefinition() {
    let rules = {
      dataDiario: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      observacoes: [
        { rule: 'required', message: 'Por favor, preencha o campo' },
        { rule: 'isMaxLength', maxLength: 255, message: 'Por favor, diminua o tamanho do campo' },
      ],
    };
    return rules;
  }

  @action
  setArquivoList(arquivoList) {
    this.arquivosDiarioObra = arquivoList;
  }
}

export default DiarioObraFormStore;
