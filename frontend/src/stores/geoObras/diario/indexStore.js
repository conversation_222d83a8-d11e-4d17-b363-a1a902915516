import SearchOperators from 'fc/components/AdvancedSearch/SearchOperators';
import IndexBase from 'fc/stores/IndexBase';
import { override } from 'mobx';
import DiarioObra from '~/domains/DiarioObra';
import DiarioObraService from '~/services/geoObras/DiarioObraService';

class DiarioObraIndexStore extends IndexBase {
  obraId;
  constructor(obraId) {
    super(DiarioObraService, DiarioObra);
    this.obraId = obraId;
  }

  @override
  load(callback) {
    const filtro = {
      page: { index: 1, size: 999 },
      sort: {
        by: 'dataDiario',
        order: 'desc',
      },
      andParameters: [
        {
          field: 'obra',
          operator: SearchOperators.EQUAL_TO.value,
          value: this.obraId,
        },
      ],
    };
    super.load(filtro, callback);
  }
}

export default DiarioObraIndexStore;
