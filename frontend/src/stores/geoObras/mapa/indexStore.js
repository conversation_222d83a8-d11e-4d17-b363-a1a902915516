import SearchTypes from 'fc/components/AdvancedSearch/SearchTypes';
import IndexBase from 'fc/stores/IndexBase';
import { action, makeObservable, observable, runInAction } from 'mobx';
import Mapa from '~/domains/geoObras/Mapa';
import MapaObraService from '~/services/geoObras/MapaObraService';

class MapaObraIndexStore extends IndexBase {
  @observable geoSelected;
  @observable mascara;
  @observable loadingMascara;

  constructor() {
    super(MapaObraService, Mapa);
    this.setGeoSelected = this.setGeoSelected.bind(this);
    this.initializeMascara = this.initializeMascara.bind(this);
    makeObservable(this);

    this.initializeMascara();
  }

  @action
  initializeMascara() {
    try {
      this.loadingMascara = true;
      MapaObraService.getMascara().then((result) => {
        runInAction(() => {
          this.mascara = result.data;
        });
      });
    } catch (error) {
      console.error(error);
    } finally {
      runInAction(() => {
        this.loadingMascara = false;
      });
    }
  }

  @action
  setGeoSelected(geoSelected) {
    this.geoSelected = geoSelected;
  }

  getAdvancedSearchParams() {
    return [
      {
        field: 'fase',
        label: 'Fase',
        type: SearchTypes.TEXT,
      },
    ];
  }
}

export default MapaObraIndexStore;
