import SearchTypes from 'fc/components/AdvancedSearch/SearchTypes';
import AppStore from 'fc/stores/AppStore';
import AsyncDropDownStore from 'fc/stores/AsyncDropdownStore';
import IndexBase from 'fc/stores/IndexBase';
import Entidade from '~/domains/Entidade';
import Obra from '~/domains/geoObras/Obra';
import EntidadeService from '~/services/EntidadeService';
import ObraService from '~/services/geoObras/ObraService';

class ObraIndexStore extends IndexBase {
  constructor() {
    super(ObraService, Obra, 'numero');
  }

  getAdvancedSearchParams() {
    return [
      {
        field: 'numero',
        label: 'Número',
        type: SearchTypes.TEXT,
      },
    ];
  }

  getFilterSuggest() {
    const entidade = AppStore.getContextEntity();
    const filterSuggest = [];

    if (entidade) {
      filterSuggest.push({
        id: '',
        field: 'entidade',
        operator: 'EQUAL_TO',
        value: entidade,
        formatted: '',
        fixed: true,
        invisible: true,
        completeParam: {
          field: 'entidade',
          label: 'Órgão/Entidade',
          type: SearchTypes.ASYNC_QUERY,
          store: new AsyncDropDownStore(Entidade, EntidadeService, 'nome', 'id'),
        },
      });
    }

    return filterSuggest;
  }
}

export default ObraIndexStore;
