import SearchOperators from 'fc/components/AdvancedSearch/SearchOperators';
import AppStore from 'fc/stores/AppStore';
import AsyncDropDownStore from 'fc/stores/AsyncDropdownStore';
import FormBase from 'fc/stores/FormBase';
import MultipleFileUploaderStore from 'fc/stores/MultipleFileUploaderStore';
import { showErrorNotification, showNotification } from 'fc/utils/utils';
import { action, makeObservable, observable, override, runInAction } from 'mobx';
import Obra from '~/domains/geoObras/Obra';
import Municipio from '~/domains/Municipio';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import ObraService from '~/services/geoObras/ObraService';
import MunicipioService from '~/services/MunicipioService';
import ObrigatoriedadeArquivoService from '~/services/ObrigatoriedadeArquivoService';

class ObraFormStore extends FormBase {
  @observable idObra;
  @observable arquivos;
  @observable municipioSelectStore;

  constructor(fase = 'CADASTRAL') {
    super(ObraService, Obra);
    makeObservable(this);

    this.arquivos = [];
    this.fileStore = new MultipleFileUploaderStore(
      [],
      (file) => ObraService.upload(file),
      (fileDTO, countDownload) => ObraService.download(fileDTO, countDownload),
      (idArquivo) => this.removerArquivoObra(idArquivo),
      (idArquivo, arquivoObraDTO) => this.atualizarArquivoObra(idArquivo, arquivoObraDTO)
    );

    this.removerArquivoObra = this.removerArquivoObra.bind(this);
    this.atualizarArquivoObra = this.atualizarArquivoObra.bind(this);
    this.setArquivos = this.setArquivos.bind(this);

    this.municipioSelectStore = new AsyncDropDownStore(
      Municipio,
      MunicipioService,
      'nomeMun',
      'id',
      {
        sort: {
          by: 'nomeMun',
          order: 'asc',
        },
        andParameters: [{ field: 'nomeUF', operator: SearchOperators.EQUAL_TO.value, value: 'AC' }],
      },
      (municipio) => `${municipio?.nomeMun ?? ''} - ${municipio?.nomeUF ?? ''} `
    );

    this.loadTipos(fase);
  }

  rulesDefinition() {
    let rules = {
      numero: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      contrato: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      tipo: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      endereco: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      cep: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      dimensoes: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      valor: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      descricao: [
        { rule: 'required', message: 'Por favor, preencha o campo' },
        { rule: 'isMaxLength', maxLength: 4000, message: 'Por favor, diminua o tamanho do campo' },
      ],
      tipoObjeto: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      dataPrevistaInicio: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      dataPrevistaConclusao: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      municipio: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
    };

    if (this.object.tipoObjeto && this.object.tipoObjeto !== 'SERVICO') {
      rules = { ...rules, subtipoObjeto: [{ rule: 'required', message: 'Por favor, preencha o campo' }] };
    }

    return rules;
  }

  getTypeFilesByPhase(fase) {
    return DadosEstaticosService.getTipoArquivoObra()
      .filter((arq) => arq.fase.includes(fase))
      .map((arq) => arq.value);
  }

  validaDadosGerais() {
    const rules = this.rulesDefinition();
    const dadosGerais = Object.keys(rules).filter((k) => rules[k].find((r) => r.rule === 'required'));
    const required = Object.keys(rules).filter(
      (k) => dadosGerais.includes(k) && rules[k].find((r) => r.rule === 'required')
    );
    const campos = required.filter(
      (k) =>
        this.object[k] === undefined ||
        this.object[k] === null ||
        this.object[k] === '' ||
        (Array.isArray(this.object[k]) && this.object[k].length === 0)
    );

    return campos.length === 0;
  }

  @action
  initializeArquivos(idObra, callback) {
    this.idObra = idObra;

    if (idObra) {
      this.service
        .recuperarArquivos(idObra)
        .then((response) =>
          runInAction(() => {
            const arquivosRecuperados = response.data;
            this.fileStore.initialize(arquivosRecuperados);
            this.arquivos = arquivosRecuperados;
          })
        )
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        )
        .finally(() =>
          runInAction(() => {
            callback && callback();
          })
        );
    }
  }

  @action
  onChangeStep(index) {
    this.currentStep = index;
  }

  @action
  setContrato(contrato) {
    this.object.contrato = contrato;
  }

  @action
  addConvenio(convenio) {
    if (!convenio) return false;

    const convenioExistente = this.object.convenios.some((c) => c.numero === convenio.numero);

    if (convenioExistente) {
      showNotification('error', null, 'Já existe um convênio com esse número adicionado.');
      return false;
    }

    this.object.convenios.push(convenio);
    return true;
  }

  @action
  addRecursoProprio(recursoProprio) {
    if (!recursoProprio) return false;

    const recursoProprioExistente = this.object.recursosProprios.some(
      (r) => r.contrato.id === recursoProprio.contrato.id
    );

    if (recursoProprioExistente) {
      showNotification('error', null, 'Esse contrato já foi adicionado.');
      return false;
    }

    if (recursoProprio.contrato.id === this.object.contrato.id) {
      showNotification('error', null, 'O contrato de recurso próprio não pode ser o mesmo do contrato da obra.');
      return false;
    }

    this.object.recursosProprios.push(recursoProprio);
    return true;
  }

  @action
  updateConvenio(convenio, novoConvenio) {
    if (!novoConvenio) return false;

    if (convenio.numero !== novoConvenio.numero) {
      const novoNumeroUtilizado = this.object.convenios.some((c) => c.numero === novoConvenio.numero);

      if (novoNumeroUtilizado) {
        showNotification('error', null, 'Já existe um convênio com esse número adicionado.');
        return false;
      }
    }

    this.object.convenios = this.object.convenios.map((c) => (c.numero === convenio.numero ? novoConvenio : c));
    return true;
  }

  @action
  removeConvenio(convenio) {
    if (convenio) {
      this.object.convenios = this.object.convenios.filter((c) => c.numero !== convenio.numero);
    }
  }

  @action
  removeRecursoProprio(recursoProprio) {
    if (recursoProprio) {
      this.object.recursosProprios = this.object.recursosProprios.filter(
        (r) => r.contrato.id !== recursoProprio.contrato.id
      );
    }
  }

  removerArquivoObra(idArquivo) {
    return ObraService.removerArquivo(this.idObra, idArquivo);
  }

  atualizarArquivoObra(idArquivo, arquivoObraDTO) {
    return ObraService.atualizarArquivo(this.idObra, idArquivo, arquivoObraDTO);
  }

  @action
  setArquivos(arquivos) {
    this.arquivos = arquivos;
  }

  @action
  validaArquivos(callback) {
    this.loading = true;
    this.service
      .validaArquivos({ arquivos: this.arquivos, entidade: AppStore.getContextEntity() })
      .then(() =>
        runInAction(() => {
          callback && callback();
        })
      )
      .catch((error) => showErrorNotification(error))
      .finally(() =>
        runInAction(() => {
          this.loading = false;
        })
      );
  }

  @action
  loadTipos(fase) {
    ObrigatoriedadeArquivoService.getArquivosObrigatorios({
      tipoProcesso: 'GEOOBRA_OBRA',
      filtros: [fase],
    })
      .then((response) =>
        runInAction(() => {
          const arquivosObrigatorios = response?.data ?? [];
          this.fileStore.tipoArquivoEnum = arquivosObrigatorios.map((arqObg) => {
            const arq = DadosEstaticosService.getTipoArquivoObra().find((arq) => arq.value === arqObg.arquivoEnum);
            return {
              ...arqObg,
              text: arqObg.obrigatorio ? '* ' + arqObg.arquivo : arqObg.arquivo,
              value: arq.value,
              fase: fase,
            };
          });
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      );
  }

  downloadFotos = (fotos) => {
    const promisses = [];
    fotos?.map((a) => {
      const p = new Promise((resolve) => {
        this.fileStore?.downloadFile(a.arquivo, null, (link) => resolve(link));
      });
      promisses.push(p);
    });
    return Promise.all(promisses);
  };

  @override
  save(callback, type = 'edit') {
    this.loading = true;
    const saveObject = this.getObjectToSave(type);
    const obraDTO = {
      id: this.object.id,
      obra: { ...this.object, entidade: AppStore.getContextEntity() },
      arquivos: this.arquivos,
      entidade: AppStore.getContextEntity(),
    };

    if (!Object.keys(saveObject).length) {
      showNotification('warn', null, 'Nenhuma alteração realizada.');
      this.loading = false;
    } else {
      this.service
        .salvarObra(obraDTO)
        .then(() =>
          runInAction(() => {
            callback && callback();
            showNotification('success', null, 'Registro salvo com sucesso!');
          })
        )
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        )
        .finally(() =>
          runInAction(() => {
            this.loading = false;
          })
        );
    }
  }

  @action
  confirmParalisacao(callback) {
    const { id, status } = this.object;
    const paralisar = status !== 'PARALISADA';
    this.loading = true;
    if (paralisar) {
      this.service
        .paralisarObra({ id })
        .then(() =>
          runInAction(() => {
            callback && callback();
            showNotification('success', null, 'Obra paralisada com sucesso!');
          })
        )
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        )
        .finally(() =>
          runInAction(() => {
            this.loading = false;
          })
        );
    } else {
      this.service
        .continuarObra({ id })
        .then(() =>
          runInAction(() => {
            callback && callback();
            showNotification('success', null, 'Obra liberada para continuação!');
          })
        )
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        )
        .finally(() =>
          runInAction(() => {
            this.loading = false;
          })
        );
    }
  }
}

export default ObraFormStore;
