import AppStore from 'fc/stores/AppStore';
import { showErrorNotification, showNotification } from 'fc/utils/utils';
import { action, makeObservable, observable, runInAction } from 'mobx';
import ObraFormStore from './formStore';

class InicioObraFormStore extends ObraFormStore {
  @observable fase;
  constructor() {
    super('INICIAL');
    this.fase = 'INICIAL';
    makeObservable(this);
  }

  @action
  updateAttributeFile = (key, attribute, value) => {
    const arquivos = this.arquivos.map((arquivo) => {
      if (key && arquivo.key === key) {
        arquivo[attribute] = value;
      }
      return arquivo;
    });
    this.setArquivos(arquivos);
  };

  rulesDefinition() {
    let rules = {
      dataInicio: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
    };

    return rules;
  }

  validaDadosBasicos(attribute) {
    const rules = this.rulesDefinition();
    const dadosGerais = Object.keys(rules).filter(
      (k) => k === attribute && rules[k].find((r) => r.rule === 'required')
    );
    const required = Object.keys(rules).filter(
      (k) => dadosGerais.includes(k) && rules[k].find((r) => r.rule === 'required')
    );
    const campos = required.filter(
      (k) =>
        this.object[k] === undefined ||
        this.object[k] === null ||
        this.object[k] === '' ||
        (Array.isArray(this.object[k]) && this.object[k].length === 0)
    );

    const georreferenciamento = this.arquivos?.find((file) => file.tipo === 'GEORREFERENCIAMENTO');
    const georreferenciamentoObrigatorio = this.fileStore.tipoArquivoEnum.find(
      (tipo) => tipo.value === 'GEORREFERENCIAMENTO' && tipo.obrigatorio
    );

    return campos.length === 0 && (georreferenciamento || !georreferenciamentoObrigatorio);
  }

  validaFotos() {
    const fotos = this.arquivos?.filter((file) => file.tipo === 'FOTO');
    const fotosObrigatorio = this.fileStore.tipoArquivoEnum.find((tipo) => tipo.value === 'FOTO' && tipo.obrigatorio);
    return fotos?.length > 0 || !fotosObrigatorio;
  }

  validaForm(attribute) {
    return this.validaDadosBasicos(attribute) && this.validaFotos();
  }

  @action
  iniciarObra(callback) {
    this.loading = true;
    const obraDTO = {
      id: this.object.id,
      obra: this.object,
      arquivos: this.arquivos,
      entidade: AppStore.getContextEntity(),
    };

    this.service
      .iniciarObra(obraDTO)
      .then(() =>
        runInAction(() => {
          callback && callback();
          showNotification('success', null, 'Obra iniciada com sucesso!');
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      )
      .finally(() =>
        runInAction(() => {
          this.loading = false;
        })
      );
  }
}

export default InicioObraFormStore;
