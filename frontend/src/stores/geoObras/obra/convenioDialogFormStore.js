import FormBase from 'fc/stores/FormBase';
import { action, makeObservable } from 'mobx';
import ConvenioObra from '~/domains/geoObras/ConvenioObra';

class ConvenioDialogFormStore extends FormBase {
  constructor() {
    super(null, ConvenioObra);

    makeObservable(this);
  }

  rulesDefinition() {
    return {
      numero: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      convenente: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      cnpjConvenente: [
        { rule: 'required', message: 'Por favor, preencha o campo' },
        { rule: 'isCpfCnpj', message: 'Por favor, informe um CNPJ válido' },
      ],
      dataAssinatura: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      dataTermino: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      valor: [
        { rule: 'required', message: 'Por favor, preencha o campo' },
        { rule: 'isNumber', message: 'Por favor, informe um valor válido' },
      ],
    };
  }

  @action
  cleanObjeto() {
    this.object = {};
  }
}

export default ConvenioDialogFormStore;
