import AppStore from 'fc/stores/AppStore';
import { showErrorNotification, showNotification } from 'fc/utils/utils';
import { action, makeObservable, observable, runInAction } from 'mobx';
import ObraFormStore from './formStore';

class InterrupcaoObraFormStore extends ObraFormStore {
  @observable fase;
  constructor() {
    super('INTERRUPCAO');
    this.fase = 'INTERRUPCAO';
    makeObservable(this);
  }

  @action
  updateAttributeFile = (key, attribute, value) => {
    const arquivos = this.arquivos.map((arquivo) => {
      if (key && arquivo.key === key) {
        arquivo[attribute] = value;
      }
      return arquivo;
    });
    this.setArquivos(arquivos);
  };

  rulesDefinition() {
    let rules = {
      motivoInterrupcao: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      descricaoInterrupcao: [
        { rule: 'required', message: 'Por favor, preencha o campo' },
        { rule: 'isMaxLength', maxLength: 4000, message: 'Por favor, diminua o tamanho do campo' },
      ],
    };

    return rules;
  }

  validaDadosBasicos(attribute) {
    const rules = this.rulesDefinition();
    const dadosGerais = Object.keys(rules).filter(
      (k) => k === attribute && rules[k].find((r) => r.rule === 'required')
    );
    const required = Object.keys(rules).filter(
      (k) => dadosGerais.includes(k) && rules[k].find((r) => r.rule === 'required')
    );
    const campos = required.filter(
      (k) =>
        this.object[k] === undefined ||
        this.object[k] === null ||
        this.object[k] === '' ||
        (Array.isArray(this.object[k]) && this.object[k].length === 0)
    );

    const arquivosOutros = this.arquivos?.find((file) => file.tipo === 'OUTROS_DOCUMENTOS');
    const outrosObrigatorio = this.fileStore.tipoArquivoEnum.find(
      (tipo) => tipo.value === 'OUTROS_DOCUMENTOS' && tipo.obrigatorio
    );

    return campos.length === 0 && (arquivosOutros || !outrosObrigatorio);
  }

  @action
  interromperObra(callback) {
    this.loading = true;
    const obraDTO = {
      id: this.object.id,
      obra: this.object,
      arquivos: this.arquivos,
      entidade: AppStore.getContextEntity(),
    };

    this.service
      .interromperObra(obraDTO)
      .then(() =>
        runInAction(() => {
          callback && callback();
          showNotification('success', null, 'Obra interrompida com sucesso!');
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      )
      .finally(() =>
        runInAction(() => {
          this.loading = false;
        })
      );
  }
}

export default InterrupcaoObraFormStore;
