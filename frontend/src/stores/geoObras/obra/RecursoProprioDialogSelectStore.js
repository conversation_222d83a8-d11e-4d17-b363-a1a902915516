import FormBase from 'fc/stores/FormBase';
import { getValueMoney } from 'fc/utils/utils';
import { action } from 'mobx';
import RecursoProprio from '~/domains/geoObras/RecursoProprio';

class RecursoProprioSelectStore extends FormBase {
  constructor() {
    super(null, RecursoProprio);

    this.initialize(null);
  }

  rulesDefinition() {
    const contrato = this.object?.contrato;
    return {
      valorUtilizado: [
        {
          rule: 'isMaxValue',
          maxValue: contrato?.valorGlobal,
          message: ` Por favor, defina um valor menor ou igual a ${getValueMoney(
            contrato?.valorGlobal,
            contrato?.termoReferencia?.tresCasasDecimais ? 3 : 2
          )}`,
        },
      ],
    };
  }

  @action
  cleanObjeto() {
    this.object = {};
  }
}

export default RecursoProprioSelectStore;
