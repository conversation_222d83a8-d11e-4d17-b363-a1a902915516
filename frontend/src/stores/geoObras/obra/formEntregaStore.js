import AppStore from 'fc/stores/AppStore';
import { showErrorNotification, showNotification } from 'fc/utils/utils';
import { action, makeObservable, observable, override, runInAction } from 'mobx';
import moment from 'moment';
import ObraFormStore from './formStore';

class EntregaObraFormStore extends ObraFormStore {
  @observable obra;
  @observable fase;
  constructor() {
    super('ENTREGA');
    this.fase = 'ENTREGA';
    makeObservable(this);
  }

  @override
  initialize(id, obra, callback) {
    super.initialize(id, {}, () => {
      runInAction(() => {
        this.obra = obra;
      });
      callback && callback();
    });
  }

  @action
  updateAttributeFile = (key, attribute, value) => {
    const arquivos = this.arquivos.map((arquivo) => {
      if (key && arquivo.key === key) {
        arquivo[attribute] = value;
      }
      return arquivo;
    });
    this.setArquivos(arquivos);
  };

  rulesDefinition() {
    let rules = {
      dataConclusao: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      dataRecebimento: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      tipoEncerramento: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
    };

    if (this.obra?.dataFim) {
      rules = this.mergeRules(rules, {
        dataConclusao: [
          {
            rule: 'isAfterDateWarning',
            message: 'Por favor, selecione uma data igual ou superior à data de finalização da obra',
            dateWarning: moment(this.obra.dataFim).subtract(1, 'days'),
          },
        ],
      });
    }

    if (this.object?.dataConclusao) {
      rules = this.mergeRules(rules, {
        dataRecebimento: [
          {
            rule: 'isAfterDateWarning',
            message: 'Por favor, selecione uma data igual ou superior à data de conclusão da obra',
            dateWarning: moment(this.object.dataConclusao).subtract(1, 'days'),
          },
        ],
      });
    }

    return rules;
  }

  validaForm() {
    const rules = this.rulesDefinition();
    const dadosGerais = Object.keys(rules).filter((k) => rules[k].find((r) => r.rule === 'required'));
    const required = Object.keys(rules).filter(
      (k) => dadosGerais.includes(k) && rules[k].find((r) => r.rule === 'required')
    );
    const campos = required.filter(
      (k) =>
        this.object[k] === undefined ||
        this.object[k] === null ||
        this.object[k] === '' ||
        (Array.isArray(this.object[k]) && this.object[k].length === 0)
    );

    const outros = this.arquivos?.find((file) => file.tipo === 'OUTROS_DOCUMENTOS');
    const outrosObrigatorio = this.fileStore.tipoArquivoEnum.find(
      (tipo) => tipo.value === 'OUTROS_DOCUMENTOS' && tipo.obrigatorio
    );

    return campos.length === 0 && (outros || !outrosObrigatorio);
  }

  @action
  entregarObra(callback) {
    this.loading = true;
    const obraDTO = {
      id: this.object.id,
      obra: this.object,
      arquivos: this.arquivos,
      entidade: AppStore.getContextEntity(),
    };

    this.service
      .entregarObra(obraDTO)
      .then(() =>
        runInAction(() => {
          callback && callback();
          showNotification('success', null, 'Obra entregue com sucesso!');
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      )
      .finally(() =>
        runInAction(() => {
          this.loading = false;
        })
      );
  }
}

export default EntregaObraFormStore;
