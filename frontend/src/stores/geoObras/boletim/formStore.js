import FormBase from 'fc/stores/FormBase';
import MultipleFileUploaderStore from 'fc/stores/MultipleFileUploaderStore';
import { showErrorNotification, showNotification } from 'fc/utils/utils';
import { action, computed, makeObservable, observable, runInAction } from 'mobx';
import BoletimObra from '~/domains/BoletimObra';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import BoletimObraService from '~/services/geoObras/BoletimObraService';
import ObrigatoriedadeArquivoService from '~/services/ObrigatoriedadeArquivoService';

class BoletimObraFormStore extends FormBase {
  obraId;
  fileStore;
  @observable arquivosBoletimObra = [];

  constructor(obraId) {
    super(BoletimObraService, BoletimObra);
    makeObservable(this);

    this.setArquivoList = this.setArquivoList.bind(this);
    this.fileStore = new MultipleFileUploaderStore(
      [],
      (file) => BoletimObraService.upload(file),
      (fileDTO) => BoletimObraService.download(fileDTO),
      (idArquivo) => this.removerArquivoBoletimObra(idArquivo),
      (idArquivo, arquivoDispensaDTO) => this.atualizarArquivoBoletimObra(idArquivo, arquivoDispensaDTO)
    );
    this.obraId = obraId;

    this.loadTipos();
  }

  initialize(id, defaultValues = {}, callback) {
    super.initialize(id, defaultValues, callback);
    if (!id) {
      callback && callback();
    }
  }

  save(callback) {
    this.loading = true;
    const boletimObraDTO = {
      id: this.object.id,
      boletimObra: this.object,
      arquivosBoletimObra: this.arquivosBoletimObra ?? [],
      obra: { id: this.obraId },
    };

    this.service
      .criarBoletimObra(boletimObraDTO)
      .then(() =>
        runInAction(() => {
          callback && callback();
          showNotification('success', null, 'Registro salvo com sucesso!');
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      )
      .finally(() =>
        runInAction(() => {
          this.loading = false;
        })
      );
  }

  @computed
  get arquivosValidos() {
    return this.arquivosBoletimObra?.length > 0 && !this.arquivosBoletimObra.some((a) => !a.tipo);
  }

  @action
  validaArquivos(callback) {
    this.loading = true;
    this.service
      .validaArquivos({
        arquivosBoletimObra: this.arquivosBoletimObra ?? [],
      })
      .then(() =>
        runInAction(() => {
          callback && callback();
        })
      )
      .catch((error) => showErrorNotification(error))
      .finally(() =>
        runInAction(() => {
          this.loading = false;
        })
      );
  }

  @action
  loadTipos() {
    ObrigatoriedadeArquivoService.getArquivosObrigatorios({ tipoProcesso: 'BOLETIM_OBRA', filtros: ['BOLETIM_OBRA'] })
      .then((response) =>
        runInAction(() => {
          const arquivosObrigatorios = response?.data ?? [];
          this.fileStore.tipoArquivoEnum = arquivosObrigatorios.map((arqObg) => {
            const arq = DadosEstaticosService.getTipoArquivoBoletimObra().find(
              (arq) => arq.value === arqObg.arquivoEnum
            );
            return { ...arqObg, text: arqObg.obrigatorio ? '* ' + arqObg.arquivo : arqObg.arquivo, value: arq.value };
          });
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      );
  }

  @action
  recuperarArquivos(idBoletimObra, callback) {
    idBoletimObra &&
      this.service
        .recuperarArquivos(idBoletimObra)
        .then((response) =>
          runInAction(() => {
            this.arquivosBoletimObra = response.data;
            this.fileStore.initialize(this.arquivosBoletimObra);
          })
        )
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        )
        .finally(() =>
          runInAction(() => {
            callback && callback();
          })
        );
  }

  removerArquivoBoletimObra(idArquivo) {
    return BoletimObraService.removerArquivo(this.object?.id, idArquivo);
  }

  atualizarArquivoBoletimObra(idArquivo, arquivoBoletimObraDTO) {
    return BoletimObraService.atualizarArquivo(this.object?.id, idArquivo, arquivoBoletimObraDTO);
  }

  rulesDefinition() {
    let rules = {
      dataBoletim: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      observacoes: [
        { rule: 'required', message: 'Por favor, preencha o campo' },
        { rule: 'isMaxLength', maxLength: 255, message: 'Por favor, diminua o tamanho do campo' },
      ],
    };
    return rules;
  }

  @action
  setArquivoList(arquivoList) {
    this.arquivosBoletimObra = arquivoList;
  }
}

export default BoletimObraFormStore;
