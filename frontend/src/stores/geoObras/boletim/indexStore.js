import SearchOperators from 'fc/components/AdvancedSearch/SearchOperators';
import IndexBase from 'fc/stores/IndexBase';
import { override } from 'mobx';
import BoletimObra from '~/domains/BoletimObra';
import BoletimObraService from '~/services/geoObras/BoletimObraService';

class BoletimObraIndexStore extends IndexBase {
  obraId;
  constructor(obraId) {
    super(BoletimObraService, BoletimObra);
    this.obraId = obraId;
  }

  @override
  load(callback) {
    const filtro = {
      page: { index: 1, size: 999 },
      sort: {
        by: 'dataBoletim',
        order: 'desc',
      },
      andParameters: [
        {
          field: 'obra',
          operator: SearchOperators.EQUAL_TO.value,
          value: this.obraId,
        },
      ],
    };
    super.load(filtro, callback);
  }
}

export default BoletimObraIndexStore;
