import SearchTypes from 'fc/components/AdvancedSearch/SearchTypes';
import AppStore from 'fc/stores/AppStore';
import AsyncDropDownStore from 'fc/stores/AsyncDropdownStore';
import AsyncMultiselectStore from 'fc/stores/AsyncMultiselectStore';
import IndexBase from 'fc/stores/IndexBase';
import { checkUserGroup } from 'fc/utils/utils';
import { observable } from 'mobx';
import ContratoView from '~/domains/ContratoView';
import Entidade from '~/domains/Entidade';
import EntidadeExterna from '~/domains/EntidadeExterna';
import FonteRecurso from '~/domains/FonteRecurso';
import Licitante from '~/domains/Licitante';
import ContratoObraViewService from '~/services/geoObras/ContratoObraViewService';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import EntidadeExternaService from '~/services/EntidadeExternaService';
import EntidadeService from '~/services/EntidadeService';
import FonteRecursoService from '~/services/FonteRecursoService';
import LicitanteService from '~/services/LicitanteService';

class ContratoObraIndexStore extends IndexBase {
  @observable licitacao;
  @observable origem;
  @observable tresCasasDecimais = false;
  @observable loadingItem = false;
  @observable loadingDecimalPlaces = false;

  constructor() {
    super(ContratoObraViewService, ContratoView, 'dataCadastro', 'desc');
  }

  getFilterSuggest(idLicitacao = undefined) {
    const filterSuggest = [];
    const entidade = AppStore.getContextEntity();
    if (entidade && !idLicitacao) {
      filterSuggest.push({
        id: '',
        field: 'entidade',
        operator: 'EQUAL_TO',
        value: entidade,
        formatted: '',
        fixed: true,
        invisible: true,
        completeParam: {
          field: 'entidade',
          label: 'Órgão/Entidade',
          type: SearchTypes.ASYNC_QUERY,
          store: new AsyncDropDownStore(Entidade, EntidadeService, 'nome', 'id'),
        },
      });
    }

    return filterSuggest;
  }

  getAdvancedSearchParams() {
    const isAuditor = checkUserGroup(['Auditor', 'Auditor - Demais Inspetorias']);
    let searchParams = [];

    if (isAuditor) {
      searchParams = [
        {
          field: 'entidade',
          label: 'Órgão/Entidade',
          type: SearchTypes.ASYNC_QUERY,
          store: new AsyncDropDownStore(Entidade, EntidadeService, 'nome', 'id'),
        },
      ];
    }
    searchParams.push(
      {
        field: 'entidadeExterna',
        label: 'Entidade Externa',
        type: SearchTypes.ASYNC_QUERY,
        store: new AsyncDropDownStore(EntidadeExterna, EntidadeExternaService, 'nomeEntidadeExterna', 'id'),
      },
      {
        field: 'tipo',
        label: 'Origem',
        type: SearchTypes.ENUM,
        options: DadosEstaticosService.getTipoProcesso(),
      },
      {
        field: 'numero',
        label: 'Número',
        type: SearchTypes.TEXT,
      },
      {
        field: 'formaContrato',
        label: 'Forma de Contrato',
        type: SearchTypes.ENUM,
        options: DadosEstaticosService.getFormaContrato(),
      },
      {
        field: 'valorGlobal',
        label: 'Valor',
        type: SearchTypes.NUMBER,
      },
      {
        field: 'objeto',
        label: 'Objeto',
        type: SearchTypes.TEXT,
      },
      {
        field: 'licitante',
        label: 'Contratado(a)',
        type: SearchTypes.ASYNC_QUERY,
        store: new AsyncDropDownStore(Licitante, LicitanteService, 'nome', 'id'),
      },
      {
        field: 'fontesDeRecurso',
        label: 'Fontes de Recurso',
        type: SearchTypes.ASYNC_QUERY,
        store: new AsyncMultiselectStore(FonteRecurso, FonteRecursoService, 'nome', 'id'),
      },
      {
        field: 'dataCadastro',
        label: 'Data de Cadastro',
        type: SearchTypes.DATE,
      },
      {
        field: 'dataVigenciaInicial',
        label: 'Início da Vigência',
        type: SearchTypes.DATE,
      },
      {
        field: 'dataVigenciaFinal',
        label: 'Fim da Vigência',
        type: SearchTypes.DATE,
      },
      {
        field: 'cnpjContratado',
        label: 'CNPJ do Contratado',
        type: SearchTypes.CNPJ,
      }
    );
    return searchParams;
  }
}

export default ContratoObraIndexStore;
