import ObraEdifcacaoViewService from '~/services/ObraEdificacaoViewService';
import IndexBase from 'fc/stores/IndexBase';
import { action, makeObservable, observable, override, runInAction } from 'mobx';
import AppStore from 'fc/stores/AppStore';
import ObraEdificacaoView from '~/domains/ObraEdificacaoView';
import SearchTypes from 'fc/components/AdvancedSearch/SearchTypes';
import AsyncDropDownStore from 'fc/stores/AsyncDropdownStore';
import Entidade from '~/domains/Entidade';
import EntidadeService from '~/services/EntidadeService';
import AsyncMultiselectStore from 'fc/stores/AsyncMultiselectStore';
import ObraCategoria from '~/domains/ObraCategoria';
import ObraCategoriaService from '~/services/ObraCategoriaService';
import ObraTipo from '~/domains/ObraTipo';
import ObraTipoService from '~/services/ObraTipoService';
import ObraService from '~/services/ObraService';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import { showErrorNotification, showNotification } from 'fc/utils/utils';

class ObraIndexStore extends IndexBase {
  @observable panMapToPointFunction;

  @observable ente;
  @observable selectedObra;

  constructor() {
    super(ObraEdifcacaoViewService, ObraEdificacaoView, 'licitacao.dataCadastroPreparatoria', 'desc');

    this.loadCurrentEnte = this.loadCurrentEnte.bind(this);
    this.setPanMapToPointFunction = this.setPanMapToPointFunction.bind(this);
    makeObservable(this);
  }

  setPanMapToPointFunction(func) {
    this.panMapToPointFunction = func;
  }

  panToSelectedObra() {
    if (this.selectedObra) {
      const latLong = this.selectedObra?.localizacao?.coordinates;
      this.panMapToPointFunction([latLong[0], latLong[1]]);
    }
  }

  updateSelectedObra(obra) {
    this.selectedObra = obra;
  }

  @override
  load(options = {}, callback, increment = false) {
    this.setLoading(true, increment);
    const parameters = Object.assign(this.advancedSearchParams, options);

    if (options && options.page && options.page.index && options.page.size) {
      parameters.page = { index: options.page.index, size: options.page.size };
    } else {
      parameters.page = { index: 1, size: 30 };
    }

    if (options.andParameters && options.andParameters.length > 0) {
      parameters.andParameters = options.andParameters;
    }

    if (options.orParameters && options.orParameters.length > 0) {
      parameters.orParameters = options.orParameters;
    }

    if (options && options['sort'] && options['sort'].by && options['sort'].order) {
      parameters.sort = { by: options['sort'].by, order: options['sort'].order };
    } else {
      parameters.sort = { by: this.defaultSortBy, order: this.defaultSortOrder };
    }

    this.pagination = parameters;
    this.service
      .advancedSearch(parameters)
      .then((response) =>
        runInAction(() => {
          if (increment) {
            this.pagination.total += response.data.total;
            this.list = this.initializeLoadedList([...this.list, ...response.data.items]);
          } else {
            this.pagination.total = response.data.total;
            this.list = this.initializeLoadedList(response.data.items ?? []);
          }
          if (!this.list?.map((obra) => obra.id)?.includes(this.selectedObra?.id)) {
            this.selectedObra = null;
          }
          callback && callback(response);
        })
      )
      .catch((error) => showErrorNotification(error))
      .finally(() =>
        runInAction(() => {
          this.setLoading(false, increment);
        })
      );
  }

  getFilterSuggest() {
    const entidade = AppStore.getContextEntity();
    const filterSuggest = [];

    if (entidade) {
      filterSuggest.push({
        id: '',
        field: 'entidade',
        operator: 'EQUAL_TO',
        value: entidade,
        formatted: '',
        fixed: true,
        invisible: true,
        completeParam: {
          field: 'entidade',
          label: 'Órgão/Entidade',
          type: SearchTypes.ASYNC_QUERY,
          store: new AsyncDropDownStore(Entidade, EntidadeService, 'nome', 'id'),
        },
      });
    }

    return filterSuggest;
  }

  getAdvancedSearchParams() {
    const searchParams = [];
    searchParams.push(
      {
        field: 'categoria',
        label: 'Categoria',
        type: SearchTypes.ASYNC_QUERY,
        store: new AsyncMultiselectStore(ObraCategoria, ObraCategoriaService, 'nome', 'id'),
      },
      {
        field: 'tipo',
        label: 'Tipo',
        type: SearchTypes.ASYNC_QUERY,
        store: new AsyncMultiselectStore(ObraTipo, ObraTipoService, 'nome', 'id'),
      },
      {
        field: 'status',
        label: 'Status da obra',
        type: SearchTypes.ENUM,
        options: DadosEstaticosService.getStatusObra(),
      },
      {
        field: 'finalizada',
        label: 'Obra finalizada',
        type: SearchTypes.ENUM,
        options: DadosEstaticosService.getSimNao(),
      }
    );
    return searchParams;
  }

  @action
  loadCurrentEnte() {
    const entidade = AppStore.getContextEntity();
    this.ente = entidade?.ente ?? {};
  }

  @action
  setStatusObra(idObra, status, callback) {
    ObraService.setStatusById(idObra, status)
      .then((response) =>
        runInAction(() => {
          this.load();
          showNotification('success', null, 'Status da obra alterado com sucesso!');
          callback && callback(response);
        })
      )
      .catch((error) => showErrorNotification(error));
  }
}

export default ObraIndexStore;
