import { observable } from 'mobx';
import SearchTypes from 'fc/components/AdvancedSearch/SearchTypes';
import LinkConsultaService from '../../services/LinkConsultaService';
import IndexBase from 'fc/stores/IndexBase';
import AppStore from 'fc/stores/AppStore';
import AsyncDropDownStore from 'fc/stores/AsyncDropdownStore';
import Entidade from '~/domains/Entidade';
import EntidadeService from '~/services/EntidadeService';

class LinkConsultaIndexStore extends IndexBase {
  @observable urlPortal;
  @observable linkRedirector;
  constructor() {
    super(LinkConsultaService);
  }
  getAdvancedSearchParams() {
    return [
      {
        field: 'entidade',
        label: 'Entidade',
        type: SearchTypes.TEXT,
      },
      {
        field: 'descricaoLink',
        label: 'Descrição',
        type: SearchTypes.TEXT,
      },
    ];
  }

  getFilterSuggest() {
    const entidade = AppStore.getContextEntity();
    const filterSuggest = [];
    if (entidade) {
      filterSuggest.push({
        id: '',
        field: 'entidade',
        operator: 'EQUAL_TO',
        value: entidade,
        formatted: '',
        fixed: true,
        invisible: true,
        completeParam: {
          field: 'entidade',
          label: 'Órgão/Entidade',
          type: SearchTypes.ASYNC_QUERY,
          store: new AsyncDropDownStore(Entidade, EntidadeService, 'nome', 'id'),
        },
      });
    }

    return filterSuggest;
  }
}

export default LinkConsultaIndexStore;
