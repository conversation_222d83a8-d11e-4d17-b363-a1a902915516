import { action, makeObservable, observable, runInAction } from 'mobx';
import LicitacaoService from '~/services/LicitacaoService';
import ObrigatoriedadeArquivoService from '~/services/ObrigatoriedadeArquivoService';
import { showErrorNotification, showNotification } from 'fc/utils/utils';
import MultipleFileUploaderStore from 'fc/stores/MultipleFileUploaderStore';
import DadosEstaticosService from '~/services/DadosEstaticosService';

class GenericArquivoFormStore {
  @observable arquivoLicitacaoList = [];
  @observable fileStore;
  @observable licitacao;
  fase;
  service;
  action;
  @observable loading = false;

  constructor(fase, action) {
    this.service = LicitacaoService;
    this.fase = fase;
    this.fileStore = new MultipleFileUploaderStore(
      [],
      (file) => LicitacaoService.upload(file),
      (fileDTO, countDownload) => LicitacaoService.download(fileDTO, countDownload),
      (idArquivo) => this.removerArquivoLicitacao(idArquivo),
      (idArquivo, arquivoLicitacaoDTO) => this.atualizarArquivoLicitacao(idArquivo, arquivoLicitacaoDTO)
    );
    this.removerArquivoLicitacao = this.removerArquivoLicitacao.bind(this);
    this.atualizarArquivoLicitacao = this.atualizarArquivoLicitacao.bind(this);
    this.validateSubmittedFiles = this.validateSubmittedFiles.bind(this);

    makeObservable(this);

    action == 'new' && this.loadFases();
  }

  @action
  initialize(idLicitacao, callback) {
    if (idLicitacao) {
      const promisses = [];
      promisses.push(this.service.getById(idLicitacao));
      promisses.push(this.service.recuperarArquivos(idLicitacao));

      Promise.all(promisses)
        .then((response) => {
          const arquivosRecuperados = response[1].data;
          this.fileStore.initialize(arquivosRecuperados);
          this.arquivoLicitacaoList = arquivosRecuperados;
          this.licitacao = response[0].data;
          this.loadFases();

          callback && callback();
        })
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        );
    }
  }

  removerArquivoLicitacao(idArquivo) {
    return this.service.removerArquivo(this.licitacao.id, idArquivo);
  }

  atualizarArquivoLicitacao(idArquivo, arquivoLicitacaoDTO) {
    return this.service.atualizarArquivo(this.licitacao.id, idArquivo, arquivoLicitacaoDTO);
  }

  @action
  setArquivoLicitacaoList(arquivoLicitacaoList) {
    this.arquivoLicitacaoList = arquivoLicitacaoList;
  }

  getFiltrosByPhaseLicitacao(lei, srp, naturezasDoObjeto) {
    return (
      {
        PREPARATORIA: {
          tipoProcesso: 'LICITACAO',
          filtros: naturezasDoObjeto ? [lei, 'PREPARATORIA', 'NATUREZAOBJ_OBRA'] : [lei, 'PREPARATORIA'],
        },
        DIVULGACAO_PUBLICACAO_LICITACAO: {
          tipoProcesso: 'LICITACAO',
          filtros: [lei, 'DIVULGACAO_PUBLICACAO_LICITACAO'],
        },
        APRESENTACAO_PROPOSTAS_LANCES: {
          tipoProcesso: 'LICITACAO',
          filtros: [lei, 'APRESENTACAO_PROPOSTAS_LANCES'],
        },
        FINALIZACAO: {
          tipoProcesso: 'LICITACAO',
          filtros: srp ? [lei, 'FINALIZACAO', 'SRP'] : [lei, 'FINALIZACAO'],
        },
      }[this.fase] ?? []
    );
  }

  @action
  loadFases(lei, naturezasDoObjeto, callback) {
    let legislacao;
    let naturezas;
    if (lei) {
      legislacao = lei;
    } else {
      legislacao = this.licitacao?.processoMigrado
        ? 'LEI_N_8666'
        : this.licitacao?.lei
        ? this.licitacao?.lei
        : 'LEI_N_14133';
    }
    if (naturezasDoObjeto) {
      naturezas = naturezasDoObjeto?.includes('OBRAS');
    } else {
      naturezas = this.licitacao?.naturezasDoObjeto?.includes('OBRAS');
    }
    ObrigatoriedadeArquivoService.getArquivosObrigatorios(
      this.getFiltrosByPhaseLicitacao(
        legislacao,
        this.licitacao?.lei == 'LEI_N_8666' ? this.licitacao?.srp : this.licitacao?.termoReferencia?.srp,
        naturezas || this.legislacao?.naturezasDoObjeto
      )
    )
      .then((response) =>
        runInAction(() => {
          const arquivosObrigatorios = response?.data ?? [];
          this.fileStore.tipoArquivoEnum = arquivosObrigatorios.map((arqObg) => {
            const arq = DadosEstaticosService.getTipoArquivoLicitacao().find(
              (arq) => arq.value === arqObg.arquivoEnum && arq.fase.includes(this.fase)
            );
            return { ...arqObg, text: arqObg.obrigatorio ? '* ' + arqObg.arquivo : arqObg.arquivo, value: arq.value };
          });

          callback && callback();
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      );
  }

  @action
  updateAttributeLicitacao(attribute, event) {
    let value;
    if (event && event.value) {
      value = event.value;
    } else if (event && event.target) {
      value = event.target.value;
    } else if (event) {
      value = event;
    }
    this.licitacao[attribute] = value;
  }

  validateSubmittedFiles(files) {
    let returnValue = true;
    files.forEach((file) => {
      if (!file.tipo) returnValue = false;
    });
    if (!returnValue) showNotification('error', null, 'O tipo dos arquivos é obrigatório!');
    return returnValue;
  }
}

export default GenericArquivoFormStore;
