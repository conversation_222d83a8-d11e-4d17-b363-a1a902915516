import SearchTypes from 'fc/components/AdvancedSearch/SearchTypes';
import Entidade from '~/domains/Entidade';
import Licitacao from '~/domains/Licitacao';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import EntidadeService from '~/services/EntidadeService';
import LicitacaoService from '../../services/LicitacaoService';
import AsyncDropDownStore from 'fc/stores/AsyncDropdownStore';
import IndexBase from 'fc/stores/IndexBase';
import AppStore from 'fc/stores/AppStore';
import { override, runInAction } from 'mobx';
import { showErrorNotification } from 'fc/utils/utils';

class LicitacaoIndexStore extends IndexBase {
  constructor() {
    super(LicitacaoService, Licitacao, 'dataCadastro', 'desc');
  }

  getAdvancedSearchParams() {
    const groups = AppStore.getData('userGroups').map((group) => group.nome);
    let result = [];
    if (groups.includes('Auditor')) {
      result = [
        {
          field: 'valorDeRisco',
          label: 'Risco',
          type: SearchTypes.NUMBER,
        },
      ];
    }

    result.push(
      {
        field: 'dataCadastro',
        label: 'Data de Cadastro',
        type: SearchTypes.DATE,
      },
      {
        field: 'dataAbertura',
        label: 'Data de Abertura',
        type: SearchTypes.DATE,
      },
      {
        field: 'entidade',
        label: 'Órgão/Entidade',
        type: SearchTypes.ASYNC_QUERY,
        store: new AsyncDropDownStore(Entidade, EntidadeService, 'nome', 'id'),
      },
      {
        field: 'orgao',
        label: 'Órgão',
        type: SearchTypes.TEXT,
      },
      {
        field: 'numero',
        label: 'Número',
        type: SearchTypes.TEXT,
      },
      {
        field: 'ano',
        label: 'Ano',
        type: SearchTypes.UNGROUPED_NUMBER,
      },
      {
        field: 'objeto',
        label: 'Objeto',
        type: SearchTypes.TEXT,
      },
      {
        field: 'fase',
        label: 'Fase',
        type: SearchTypes.ENUM,
        options: DadosEstaticosService.getFasesLicitacao(),
      },
      {
        field: 'valorEstimado',
        label: 'Valor Estimado',
        type: SearchTypes.NUMBER,
      }
    );

    return result;
  }

  getFilterSuggest() {
    const entidade = AppStore.getContextEntity();
    const filterSuggest = [];
    if (entidade) {
      filterSuggest.push({
        id: '',
        field: 'entidade',
        operator: 'EQUAL_TO',
        value: entidade,
        formatted: '',
        fixed: true,
        invisible: true,
        completeParam: {
          field: 'entidade',
          label: 'Órgão/Entidade',
          type: SearchTypes.ASYNC_QUERY,
          store: new AsyncDropDownStore(Entidade, EntidadeService, 'nome', 'id'),
        },
      });
    }

    return filterSuggest;
  }

  @override
  load(options = {}, callback, increment = false) {
    this.setLoading(true, increment);
    const parameters = Object.assign(this.advancedSearchParams, options);

    if (options && options.page && options.page.index && options.page.size) {
      parameters.page = { index: options.page.index, size: options.page.size };
    } else {
      parameters.page = { index: 1, size: 10 };
    }

    if (options.andParameters && options.andParameters.length > 0) {
      parameters.andParameters = options.andParameters;
    }

    if (options.orParameters && options.orParameters.length > 0) {
      parameters.orParameters = options.orParameters;
    }

    if (options && options['sort'] && options['sort'].by && options['sort'].order) {
      parameters.sort = { by: options['sort'].by, order: options['sort'].order };
    } else {
      parameters.sort = { by: this.defaultSortBy, order: this.defaultSortOrder };
    }

    this.pagination = parameters;

    let parametersCopy = JSON.parse(JSON.stringify(parameters));
    if (parameters.sort.by === 'risco') {
      parametersCopy.sort.by = 'valorDeRisco';
    }

    this.service
      .advancedSearch(parametersCopy)
      .then((response) =>
        runInAction(() => {
          if (increment) {
            this.pagination.total += response.data.total;
            this.list = this.initializeLoadedList([...this.list, ...response.data.items]);
          } else {
            this.pagination.total = response.data.total;
            this.list = this.initializeLoadedList(response.data.items);
          }
          callback && callback(response);
        })
      )
      .catch((error) => showErrorNotification(error))
      .finally(() =>
        runInAction(() => {
          this.setLoading(false, increment);
        })
      );
  }
}

export default LicitacaoIndexStore;
