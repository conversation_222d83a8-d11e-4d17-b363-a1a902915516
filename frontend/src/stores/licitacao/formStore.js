import FormBase from 'fc/stores/FormBase';
import Licitacao from '~/domains/Licitacao';
import LicitacaoService from '~/services/LicitacaoService';
import TipoLicitacaoService from '~/services/TipoLicitacaoService';
import ComissaoService from '~/services/ComissaoService';
import FonteRecursoService from '~/services/FonteRecursoService';
import Entidade from '~/domains/Entidade';
import EntidadeService from '~/services/EntidadeService';
import LicitanteService from '~/services/LicitanteService';
import TdaLicitacaoService from '~/services/TdaLicitacaoService';
import AsyncDropDownStore from 'fc/stores/AsyncDropdownStore';
import TermoIndexStore from './termoIndex';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import { action, computed, observable, override, runInAction } from 'mobx';
import PareceristaIndexStore from '~/stores/parecerista/indexStore';
import AsyncMultiselectStore from 'fc/stores/AsyncMultiselectStore';
import { showNotification, showErrorNotification } from 'fc/utils/utils';
import Licitante from '~/domains/Licitante';
import AppStore from 'fc/stores/AppStore';
import SearchTypes from 'fc/components/AdvancedSearch/SearchTypes';
import ObraTipo from '~/domains/ObraTipo';
import ObraCategoria from '~/domains/ObraCategoria';
import ObraTipoService from '~/services/ObraTipoService';
import ObraCategoriaService from '~/services/ObraCategoriaService';
import moment from 'moment';
import EdificacaoService from '~/services/EdificacaoService';
import SearchOperators from 'fc/components/AdvancedSearch/SearchOperators';
import Comissao from '~/domains/Comissao';
import FonteRecurso from '~/domains/FonteRecurso';
import AccessPermission from '~/constants/AccessPermission';
import MultipleFileUploaderStore from 'fc/stores/MultipleFileUploaderStore';
import ArquivoLicitacaoService from '~/services/ArquivoLicitacaoService';
import AnalisarEditaisIndexStore from '~/stores/analisarEditais/indexStore';

class LicitacaoFormStore extends FormBase {
  intervalId;
  @observable enableReqPreparatoria = false;
  @observable obraObject = {};
  @observable tiposLicitacao = [];
  @observable edificacao;
  @observable entidadesFiltradas;
  @observable stateObra = {};
  @observable arquivosTdaLicitacao = [];
  @observable anosLicitacao = [];
  @observable fontesRecursos = [];
  @observable edital;
  @observable statusEditalProcessamento;
  @observable updatingStatusEditalProcessamento = false;
  @observable idAlerta;

  constructor() {
    super(LicitacaoService, Licitacao);

    this.comissaoStore = new AsyncDropDownStore(
      Comissao,
      ComissaoService,
      'numero',
      'id',
      {},
      ({ numero, tipo }) => `${numero} / ${tipo}`
    );
    this.fonteRecursoStore = new AsyncMultiselectStore(FonteRecurso, FonteRecursoService, 'nome', 'id', {
      sort: {
        by: 'nome',
        order: 'asc',
      },
    });
    this.licitanteStore = new AsyncMultiselectStore(Licitante, LicitanteService, 'nome', 'id');
    this.entidadeStore = new AsyncDropDownStore(Entidade, EntidadeService, 'nome', 'id');
    this.obraTipoStore = new AsyncDropDownStore(ObraTipo, ObraTipoService, 'nome', 'id', {
      sort: {
        by: 'nome',
        order: 'asc',
      },
    });
    this.obraCategoriaStore = new AsyncDropDownStore(ObraCategoria, ObraCategoriaService, 'nome', 'id', {
      sort: {
        by: 'nome',
        order: 'asc',
      },
    });
    this.orgaosParticipantesStore = new AsyncMultiselectStore(Entidade, EntidadeService, 'nome', 'id', {
      sort: {
        by: 'nome',
        order: 'asc',
      },
    });
    this.AnalisarEditaisStore = new AnalisarEditaisIndexStore();
    this.termoIndexStore = new TermoIndexStore();
    this.entidadeService = EntidadeService;
    this.pareceristaIndexStore = new PareceristaIndexStore();
    this.carregarEdificacaoObra = this.carregarEdificacaoObra.bind(this);
    this.getStatusEditalProcessamento = this.getStatusEditalProcessamento.bind(this);
    this.getEntidadesFiltradas = this.getEntidadesFiltradas.bind(this);
    this.fileStoreTda = new MultipleFileUploaderStore(
      [],
      (file) => TdaLicitacaoService.upload(file),
      (fileDTO) => TdaLicitacaoService.download(fileDTO)
    );
  }

  isEntidadeLiconAntigo() {
    return this.object.lei === 'LEI_N_8666' || this.object?.processoMigrado;
  }

  rulesDefinition() {
    const isObra = ['OBRAS', 'SERVICOS_DE_ENGENHARIA'].some((natureza) =>
      this.object?.naturezasDoObjeto?.includes(natureza)
    );

    let rules = {
      numero: [
        { rule: 'required', message: 'Por favor, preencha o campo' },
        { rule: 'isMaxLength', maxLength: 255, message: 'Por favor, diminua o tamanho do campo' },
      ],
      numeroProcessoAdm: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      ano: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      dataAbertura: [
        { rule: 'required', message: 'Por favor, preencha o campo' },
        { rule: 'isBusinessHours', message: 'Por favor, cadastre licitações apenas em horário comercial' },
        {
          rule: 'isLessThanYearByValue',
          message: 'Por favor, selecione uma data dentro do intervalo de um ano',
          valueComparable: this.object.dataCadastroPreparatoria,
        },
      ],
      objeto: [
        { rule: 'required', message: 'Por favor, preencha o campo' },
        { rule: 'isMaxLength', maxLength: 4000, message: 'Por favor, diminua o tamanho do campo' },
      ],
      naturezasDoObjeto: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      justificativa: [{ rule: 'isMaxLength', maxLength: 4000, message: 'Por favor, diminua o tamanho do campo' }],
      regenciaLegal: [{ rule: 'isMaxLength', maxLength: 4000, message: 'Por favor, diminua o tamanho do campo' }],
      fontesDeRecurso: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      valorEstimado: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      fase: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      tipoObra: [isObra && { rule: 'required', message: 'Por favor, preencha o campo' }],
      categoria: [isObra && { rule: 'required', message: 'Por favor, preencha o campo' }],
      coordenadas: [isObra && { rule: 'required', message: 'Por favor, preencha o campo' }],
      comissao: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      pregoeiro: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
    };
    this.object.lei === 'LEI_N_14133' || this.object.lei === 'OUTRA'
      ? (rules.tiposLicitacao = [{ rule: 'required', message: 'Por favor, preencha o campo' }])
      : (rules.tipo = [{ rule: 'required', message: 'Por favor, preencha o campo' }]);

    if (!this.isEntidadeLiconAntigo()) {
      rules = this.mergeRules(rules, {
        termoReferencia: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
        tipoAdjudicacao: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
        participacaoExclusiva: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      });
    }

    if (this.object.lei === 'OUTRA') {
      rules = this.mergeRules(rules, {
        legislacaoOutros: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      });
    }

    return rules;
  }

  getTypeFilesByPhase() {
    return DadosEstaticosService.getTipoArquivoLicitacao()
      .filter((arq) => arq.fase.includes('PREPARATORIA'))
      .map((arq) => arq.value);
  }

  @computed
  get targetNaturezasDoObjeto() {
    if (this.object.naturezasDoObjeto) {
      return DadosEstaticosService.getNaturezaObjetoLicitacao().filter((element) =>
        this.object.naturezasDoObjeto.includes(element.value)
      );
    } else {
      return [];
    }
  }

  @override
  initialize(id, defaultValues = {}, callback) {
    if (!id) {
      this.object = Object.assign({}, defaultValues);
      callback && callback();
    } else {
      this.loading = true;
      this.service
        .getById(id)
        .then((response) =>
          runInAction(() => {
            this.object = response.data;
            this.loadedObject = response.data;
            this.edital = response.data.edital;
            callback && callback(this.object.termoReferencia?.id);
          })
        )
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        )
        .finally(() =>
          runInAction(() => {
            this.loading = false;
          })
        );
    }
  }

  @action
  editalProcessement(idLicitacao, callback) {
    ArquivoLicitacaoService.analisarEditalProcesso(idLicitacao)
      .then(() => {
        callback && callback();
        showNotification('success', null, 'Processamento de Edital inicializado com sucesso!');
        this.loading = false;
      })
      .catch((error) => {
        showErrorNotification(error);
        this.loading = false;
      });
  }

  @action
  carregarTiposLicitacaoModalidades(callback) {
    this.loading = true;
    const promises = [];
    promises.push(TipoLicitacaoService.advancedSearchAll());

    Promise.all(promises)
      .then((response) =>
        runInAction(() => {
          this.tiposLicitacao = this.filterLegislacao(response[0].data);
          if (!this.object.processoMigrado) {
            this.tiposLicitacao = this.tiposLicitacao.filter((criterioLicitacao) => {
              return criterioLicitacao.ativo;
            });
          }
          callback && callback();
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      )
      .finally(() =>
        runInAction(() => {
          this.loading = false;
        })
      );
  }

  @action
  carregarFontesRecursos(callback) {
    this.loading = true;
    FonteRecursoService.getAll()
      .then((response) =>
        runInAction(() => {
          this.fontesRecursos = response.data;
          callback && callback();
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      )
      .finally(() =>
        runInAction(() => {
          this.loading = false;
        })
      );
  }

  @action
  carregaAnos() {
    this.service
      .getAnosLicitacao()
      .then((response) =>
        runInAction(() => {
          this.anos = response.data.map((ano) => ({ text: String(ano), value: ano }));
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      );
  }

  @action
  getStatusEditalProcessamento(callback) {
    const idEdital = this.edital?.id;
    if (!this.updatingStatusEditalProcessamento) {
      this.updatingStatusEditalProcessamento = true;
      this.AnalisarEditaisStore.getStatusProcessamentoEditaisLicitacao(idEdital)
        .then((status) =>
          runInAction(() => {
            if (status !== 'PROCESSADO') {
              this.intervalId = setInterval(() => {
                this.AnalisarEditaisStore.getStatusProcessamentoEditaisLicitacao(idEdital)
                  .then((status) =>
                    runInAction(() => {
                      if (status === 'PROCESSADO') {
                        clearInterval(this.intervalId);
                        this.updatingStatusEditalProcessamento = false;
                      }
                      if (status !== this.statusEditalProcessamento) {
                        this.statusEditalProcessamento = status;
                        callback && callback();
                      }
                    })
                  )
                  .catch((error) =>
                    runInAction(() => {
                      showErrorNotification('Ocorreu um erro ao buscar o status de análise do edital.');
                      clearInterval(this.intervalId);
                      this.updatingStatusEditalProcessamento = false;
                      reject(error);
                    })
                  );
              }, 6500);
            }
            this.statusEditalProcessamento = status;
          })
        )
        .catch((error) => {
          showErrorNotification('Ocorreu um erro ao buscar o status de análise do edital.');
          reject(error);
        });
    }
  }

  filterLegislacao(data) {
    return data.filter((item) => item.legislacao.includes(this.object.lei ?? 'LEI_N_8666'));
  }

  initializeObra() {
    const localizacao = this.object?.obra?.edificacao?.localizacao;
    const coordenadas = [];
    this.object.categoria = this.object?.obra?.categoria;
    this.object.tipoObra = this.object?.obra?.tipo;

    if (localizacao) {
      if (localizacao.type === 'Polygon') {
        localizacao.coordinates[0].forEach((element) => {
          coordenadas.push(element.join(' '));
        });
      } else if (localizacao.type === 'LineString') {
        localizacao.coordinates.forEach((element) => {
          coordenadas.push(element.join(' '));
        });
      } else {
        coordenadas.push(localizacao.coordinates.join(' '));
      }
    }

    this.object.coordenadas = coordenadas.join(', ');
    this.object.tipoSelecao = this.getTipoSelecaoByTipoEdificacao(localizacao?.type);
  }

  getTipoSelecaoByTipoEdificacao(tipo) {
    return { Point: 'PONTO', LineString: 'LINHA', Polygon: 'POLIGONO' }[tipo];
  }

  hasPermissionTda() {
    return AppStore.hasPermission([AccessPermission.cadastrarTda.readPermission]);
  }

  hasPermissionAlerta() {
    return AppStore.hasPermission([AccessPermission.alerta.readPermission]);
  }

  @action
  hasAlert(idLicitacao, callback) {
    idLicitacao &&
      this.service
        .getAlertaLicitacao(idLicitacao)
        .then((alerta) => {
          runInAction(() => {
            if (alerta?.data) {
              this.idAlerta = alerta.data;
              callback && callback();
            }
          });
        })
        .catch(() => {
          runInAction(() => {
            showErrorNotification('Ocorreu um erro ao buscar o id do alerta.');
          });
        });
  }

  @action
  updateObraDTOAtt(att, value) {
    this.obraObject[att] = value;
  }

  @action
  updateObraAttribute(att, value) {
    this.object.obra[att] = value;
  }

  @action
  updateStateObraAttribute(att, value) {
    this.stateObra[att] = value;
  }

  @action
  resetStateObra() {
    this.stateObra = {};
  }

  @action
  initializeObraDTO() {
    this.obraObject = {};
    this.edificacao = undefined;
  }

  setStatus(phase) {
    if (phase === 'preparatoria') {
      this.object.status = 'NAO_PUBLICADA';
    }
  }

  @action
  cadastrarLicitacao(cadastroLicitacaoDTO, callback) {
    this.loading = true;
    LicitacaoService.cadastrarLicitacao(cadastroLicitacaoDTO)
      .then(() =>
        runInAction(() => {
          callback && callback();
          showNotification('success', null, 'Licitação cadastrada com sucesso!');
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      )
      .finally(() =>
        runInAction(() => {
          this.loading = false;
        })
      );
  }

  @action
  initializeTdaLicitacao(idLicitacao) {
    this.idLicitacao = idLicitacao;
    if (idLicitacao && this.hasPermissionTda()) {
      TdaLicitacaoService.tdaLicitacaoByIdLicitacao(idLicitacao)
        .then((response) =>
          runInAction(() => {
            if (response && response.data) {
              const tdaLicitacao = response.data;
              this.carregaArquivosTda(tdaLicitacao);
            }
          })
        )
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        );
    }
  }

  @action
  carregaArquivosTda(tdaLicitacao) {
    if (tdaLicitacao) {
      TdaLicitacaoService.recuperarArquivos(tdaLicitacao.id)
        .then((response) =>
          runInAction(() => {
            if (response && response.data) {
              const arquivosRecuperados = response.data.map((arq) => {
                return {
                  ...arq,
                  analista: tdaLicitacao.analista.nome,
                };
              });
              this.fileStoreTda.initialize(arquivosRecuperados);
              this.arquivosTdaLicitacao = arquivosRecuperados;
            }
          })
        )
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        );
    }
  }

  entidadeFilterSuggest() {
    const filterSuggest = [
      {
        id: '',
        field: 'disponivel',
        operator: 'EQUAL_TO',
        value: true,
        formatted: '',
        fixed: true,
        invisible: true,
        completeParam: {
          field: 'disponivel',
          label: 'Disponível',
          type: SearchTypes.ENUM,
          options: DadosEstaticosService.getSimNao(),
        },
      },
      {
        id: '',
        field: 'modelo',
        operator: 'EQUAL_TO',
        value: false,
        formatted: '',
        fixed: true,
        invisible: true,
        completeParam: {
          field: 'modelo',
          label: 'Modelo',
          type: SearchTypes.ENUM,
          options: DadosEstaticosService.getSimNao(),
        },
      },
    ];

    const entidade = AppStore.getContextEntity();
    if (entidade) {
      filterSuggest.push({
        id: '',
        field: 'entidade',
        operator: 'EQUAL_TO',
        value: entidade,
        formatted: '',
        fixed: true,
        invisible: true,
        completeParam: {
          field: 'entidade',
          label: 'Órgão/Entidade',
          type: SearchTypes.ASYNC_QUERY,
          store: new AsyncDropDownStore(Entidade, EntidadeService, 'nome', 'id'),
        },
      });
    }

    return filterSuggest;
  }

  comissaoFilterSuggest() {
    const filterSuggest = [];

    const entidade = AppStore.getContextEntity();
    if (entidade) {
      filterSuggest.push({
        id: '',
        field: 'entidade',
        operator: 'EQUAL_TO',
        value: entidade,
        formatted: '',
        fixed: true,
        completeParam: {
          field: 'entidade',
          label: 'Órgão/Entidade',
          type: SearchTypes.ASYNC_QUERY,
          store: new AsyncDropDownStore(Entidade, EntidadeService, 'nome', 'id'),
        },
      });
    }

    const today = moment();

    filterSuggest.push({
      id: '',
      field: 'dataVigenciaInicial',
      operator: 'LESSER_THAN_EQUAL',
      value: today,
      formatted: '',
      fixed: true,
      completeParam: {
        field: 'dataVigenciaInicial',
        label: 'Início da Vigência',
        type: SearchTypes.DATE,
      },
    });

    filterSuggest.push({
      id: '',
      field: 'dataVigenciaFinal',
      operator: 'GREATER_THAN_EQUAL',
      value: today,
      formatted: '',
      fixed: true,
      completeParam: {
        field: 'dataVigenciaFinal',
        label: 'Fim da Vigência',
        type: SearchTypes.DATE,
      },
    });

    return filterSuggest;
  }

  setEntidadeContexto(entidadeId) {
    if (this.object && entidadeId) {
      this.entidadeStore.initialize(entidadeId, () => (this.object.entidade = this.entidadeStore.selectedItem));
    }
  }

  updateAttribute(attribute, event) {
    let value = event;
    if (event && event.value) {
      value = event.value;
    }
    if (event && event.target) {
      value = event.target.value;
    }
    this.object[attribute] = value;
  }

  updateAttributePregoeiro(attribute, event, list) {
    let value = event;
    if (event && event.value) {
      value = event.value;
    }
    if (event && event.target) {
      value = event.target.value;
    }

    list.forEach((user) => {
      if (user.id == value) this.object[attribute] = user;
    });
  }

  @action
  carregarEdificacaoObra() {
    this.loading = true;
    const filtro = {
      page: { index: 1, size: 100 },
      sort: {},
      andParameters: [{ field: 'obra', operator: SearchOperators.EQUAL_TO.value, value: this.object.obra.id }],
    };
    EdificacaoService.advancedSearch(filtro)
      .then((response) =>
        runInAction(() => {
          const edificacao = response.data?.items[0];
          this.object.obra.edificacao = edificacao;
          this.edificacao = edificacao;
          this.initializeObra();
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      )
      .finally(() =>
        runInAction(() => {
          this.loading = false;
        })
      );
  }

  @computed
  get labelPregoeiro() {
    const tipoConjunto = this.object?.comissao?.tipoConjunto;

    if (tipoConjunto === 'CONTRATACAO') {
      return 'Presidente da Comissão';
    } else {
      return 'Agente de Contratação';
    }
  }

  @action
  getEntidadesFiltradas(event, callback) {
    const query = event.query.trim();

    const queryParams = {
      andParameters: [
        {
          id: '',
          field: 'nome',
          operator: 'CONTAINS',
          value: query,
          formatted: '',
        },
      ],
    };

    EntidadeService.advancedSearch(queryParams)
      .then((response) =>
        runInAction(() => {
          this.entidadesFiltradas = response.data?.items;
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      )
      .finally(() =>
        runInAction(() => {
          callback && callback();
        })
      );
  }
}

export default LicitacaoFormStore;
