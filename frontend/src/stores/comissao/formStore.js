import ComissaoService from '~/services/ComissaoService';
import EntidadeService from '~/services/EntidadeService';
import PessoaComissaoService from '~/services/PessoaComissaoService';
import FormBase from 'fc/stores/FormBase';
import Comissao from '~/domains/Comissao';
import AsyncDropDownStore from 'fc/stores/AsyncDropdownStore';
import Entidade from '~/domains/Entidade';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import { action, observable, runInAction } from 'mobx';
import { showErrorNotification } from 'fc/utils/utils';
import AsyncPicklistStore from 'fc/stores/AsyncPicklistStore';
import MultipleFileUploaderStore from 'fc/stores/MultipleFileUploaderStore';
import SearchOperators from 'fc/components/AdvancedSearch/SearchOperators';
import PessoaComissao from '~/domains/PessoaComissao';
import ObrigatoriedadeArquivoService from '~/services/ObrigatoriedadeArquivoService';

class ComissaoFormStore extends FormBase {
  entidadeStore;
  fileStore;
  @observable pessoaStore;
  @observable arquivosRecuperados = [];

  constructor() {
    super(ComissaoService, Comissao);
    this.entidadeStore = new AsyncDropDownStore(Entidade, EntidadeService, 'nome', 'id');
    this.fileStore = new MultipleFileUploaderStore(
      [],
      (file) => ComissaoService.upload(file),
      (fileDTO) => ComissaoService.download(fileDTO),
      (idArquivo) => this.removerArquivoComissao(idArquivo),
      (idArquivo, arquivoDispensaDTO) => this.atualizarArquivoComissao(idArquivo, arquivoDispensaDTO)
    );

    this.loadTipos();
  }

  initialize(id, defaultValues = {}, callback) {
    super.initialize(id, defaultValues, callback);
    if (!id) {
      callback && callback();
    }
  }

  @action
  setPessoaStore(idEntidade = this.object.entidade?.id) {
    this.pessoaStore = new AsyncPicklistStore(
      PessoaComissao,
      PessoaComissaoService,
      'nome',
      'id',
      {
        andParameters: [{ field: 'entidade', operator: SearchOperators.EQUAL_TO.value, value: idEntidade }],
      },
      {
        by: 'nome',
        order: 'asc',
      },
      {
        filter: true,
        filterBy: 'nome',
      },
      true
    );
  }

  @action
  loadTipos() {
    ObrigatoriedadeArquivoService.getArquivosObrigatorios({ tipoProcesso: 'COMISSAO', filtros: ['COMISSAO'] })
      .then((response) =>
        runInAction(() => {
          const arquivosObrigatorios = response?.data ?? [];
          this.fileStore.tipoArquivoEnum = arquivosObrigatorios.map((arqObg) => {
            const arq = DadosEstaticosService.getTipoArquivoComissao().find((arq) => arq.value === arqObg.arquivoEnum);
            return { ...arqObg, text: arqObg.obrigatorio ? '* ' + arqObg.arquivo : arqObg.arquivo, value: arq.value };
          });
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      );
  }

  @action
  recuperarArquivos(idComissao, callback) {
    idComissao &&
      this.service
        .recuperarArquivos(idComissao)
        .then((response) =>
          runInAction(() => {
            this.arquivosRecuperados = response.data;
            this.fileStore.initialize(this.arquivosRecuperados);
          })
        )
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        )
        .finally(() =>
          runInAction(() => {
            callback && callback();
          })
        );
  }

  removerArquivoComissao(idArquivo) {
    return ComissaoService.removerArquivo(this.object?.id, idArquivo);
  }

  atualizarArquivoComissao(idArquivo, arquivoComissaoDTO) {
    return ComissaoService.atualizarArquivo(this.object?.id, idArquivo, arquivoComissaoDTO);
  }

  rulesDefinition() {
    return {
      numero: [
        { rule: 'required', message: 'Por favor, preencha o campo' },
        { rule: 'isMaxLength', maxLength: 255, message: 'Por favor, diminua o tamanho do campo' },
      ],
      tipo: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      tipoConjunto: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      entidade: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      dataVigenciaInicial: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      dataVigenciaFinal: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      membros: [{ rule: 'required', message: 'Por favor, adicione pelo menos um membro' }],
    };
  }

  @action
  setArquivoList(arquivoList) {
    this.object.arquivosTemporarios = arquivoList;
  }
}

export default ComissaoFormStore;
