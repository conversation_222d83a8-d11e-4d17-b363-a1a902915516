import SearchTypes from 'fc/components/AdvancedSearch/SearchTypes';
import ComissaoService from '~/services/ComissaoService';
import IndexBase from 'fc/stores/IndexBase';
import Comissao from '~/domains/Comissao';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import AppStore from 'fc/stores/AppStore';
import AsyncDropDownStore from 'fc/stores/AsyncDropdownStore';
import Entidade from '~/domains/Entidade';
import EntidadeService from '~/services/EntidadeService';
import { checkUserGroup } from 'fc/utils/utils';

class ComissaoIndexStore extends IndexBase {
  constructor() {
    super(ComissaoService, Comissao);
  }

  getAdvancedSearchParams() {
    const isAuditor = checkUserGroup(['Auditor', 'Auditor - Demais Inspetorias']);
    let searchParams = [];

    if (isAuditor) {
      searchParams = [
        {
          field: 'entidade',
          label: 'Órgão/Entidade',
          type: SearchTypes.ASYNC_QUERY,
          store: new AsyncDropDownStore(Entidade, EntidadeService, 'nome', 'id'),
        },
      ];
    }
    searchParams.push(
      {
        field: 'numero',
        label: 'Número',
        type: SearchTypes.TEXT,
      },
      {
        field: 'tipo',
        label: 'Tipo',
        type: SearchTypes.ENUM,
        options: DadosEstaticosService.getTipoComissao(),
      },
      {
        field: 'tipoConjunto',
        label: 'TipoConjunto',
        type: SearchTypes.ENUM,
        options: DadosEstaticosService.getTipoConjuntoComissao(),
      },
      {
        field: 'dataVigenciaInicial',
        label: 'Início da Vigência',
        type: SearchTypes.DATE,
      },
      {
        field: 'dataVigencialFinal',
        label: 'Fim da Vigência',
        type: SearchTypes.DATE,
      }
    );
    return searchParams;
  }

  getFilterSuggest() {
    const filterSuggest = [];

    const entidade = AppStore.getContextEntity();
    if (entidade) {
      filterSuggest.push({
        id: '',
        field: 'entidade',
        operator: 'EQUAL_TO',
        value: entidade,
        formatted: '',
        fixed: true,
        invisible: true,
        completeParam: {
          field: 'entidade',
          label: 'Órgão/Entidade',
          type: SearchTypes.ASYNC_QUERY,
          store: new AsyncDropDownStore(Entidade, EntidadeService, 'nome', 'id'),
        },
      });
    }
    return filterSuggest;
  }
}

export default ComissaoIndexStore;
