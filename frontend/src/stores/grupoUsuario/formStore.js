import { observable, runInAction } from 'mobx';
import Permissao from '~/domains/Permissao';
import GrupoUsuario from '../../domains/GrupoUsuario';
import GrupoUsuarioService from '../../services/GrupoUsuarioService';
import PermissaoService from '../../services/PermissaoService';
import UsuarioAuditorViewService from '../../services/UsuarioAuditorViewService';
import UsuarioService from '../../services/UsuarioService';
import AsyncPickListStore from 'fc/stores/AsyncPicklistStore';
import FormBase from 'fc/stores/FormBase';
import UsuarioAuditorView from '~/domains/UsuarioAuditorView';
import Usuario from '~/domains/Usuario';

class GrupoUsuarioFormStore extends FormBase {
  @observable permissaoStore;
  @observable usuarioStore;
  @observable disableDeleteGroups = [
    'Administrador',
    'Auditor',
    'Jurisdicionado',
    'DAFO',
    '<PERSON>tor - De<PERSON><PERSON> Inspetorias',
    'Inspetor',
  ];
  @observable isInspetorForm = false;

  constructor() {
    super(GrupoUsuarioService, GrupoUsuario);
  }

  rulesDefinition() {
    return {
      nome: [
        { rule: 'required', message: 'Por favor, preencha o campo' },
        { rule: 'isMaxLength', maxLength: 60, message: 'Por favor, diminua o tamanho do campo' },
      ],
      descricao: [{ rule: 'isMaxLength', maxLength: 255, message: 'Por favor, diminua o tamanho do campo' }],
      usuarios: [
        { rule: 'required', message: 'Por favor, preencha o campo' },
        {
          rule: 'isArrayMaxLength',
          maxLength: this.object.numeroParticipantes,
          message: 'O número de usuários não pode ultrapassar o valor informado em "Número Máximo de Participantes"',
        },
      ],
      permissoes: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
    };
  }

  initialize(id, defaultValues = {}, callback) {
    if (!id) {
      this.object = Object.assign({}, defaultValues);
      this.inicializeStores();
    } else {
      this.loading = true;
      this.service
        .getById(id)
        .then((response) =>
          runInAction(() => {
            this.object = response.data;
            this.loadedObject = response.data;

            this.object.nome === 'Inspetor' && this.setInspetorForm(true);
            this.inicializeStores();
            callback && callback();
          })
        )
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        )
        .finally(() =>
          runInAction(() => {
            this.loading = false;
          })
        );
    }
  }

  inicializeStores() {
    this.permissaoStore = new AsyncPickListStore(
      Permissao,
      PermissaoService,
      'nome',
      'id',
      {},
      {
        by: 'nome',
        order: 'asc',
      },
      {
        filter: true,
        filterBy: 'nome',
      }
    );

    if (this.isInspetorForm) {
      this.usuarioStore = new AsyncPickListStore(
        UsuarioAuditorView,
        UsuarioAuditorViewService,
        'nome',
        'id',
        {},
        {
          by: 'nome',
          order: 'asc',
        },
        {},
        true
      );
    } else {
      this.usuarioStore = new AsyncPickListStore(
        Usuario,
        UsuarioService,
        'nome',
        'id',
        {},
        {
          by: 'nome',
          order: 'asc',
        },
        {
          filter: true,
          filterBy: 'nome',
        },
        true
      );
    }
  }

  setInspetorForm(value) {
    this.isInspetorForm = value;
  }
}

export default GrupoUsuarioFormStore;
