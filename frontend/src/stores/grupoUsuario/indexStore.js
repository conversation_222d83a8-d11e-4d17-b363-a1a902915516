import GrupoUsuarioService from '../../services/GrupoUsuarioService';
import SearchTypes from 'fc/components/AdvancedSearch/SearchTypes';
import IndexBase from 'fc/stores/IndexBase';
import GrupoUsuario from '~/domains/GrupoUsuario';
import { observable } from 'mobx';

class GrupoUsuarioIndexStore extends IndexBase {
  @observable disableDeleteGroups = [
    'Administrador',
    'Auditor',
    'Jurisdicionado',
    'DAFO',
    'Auditor - Demais Inspetorias',
    'Inspetor',
  ];

  constructor() {
    super(GrupoUsuarioService, GrupoUsuario, 'nome');
  }
  getAdvancedSearchParams() {
    return [
      {
        field: 'nome',
        label: 'Nome',
        type: SearchTypes.TEXT,
      },
      {
        field: 'descricao',
        label: 'Descrição',
        type: SearchTypes.TEXT,
      },
    ];
  }
}

export default GrupoUsuarioIndexStore;
