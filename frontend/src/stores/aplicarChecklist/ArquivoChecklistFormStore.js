import { action, observable, runInAction } from 'mobx';
import ArquivoTdaCarona from '~/domains/ArquivoTdaCarona';
import ArquivoTdaDispensa from '~/domains/ArquivoTdaDispensa';
import ArquivoTdaInexigibilidade from '~/domains/ArquivoTdaInexigibilidade';
import ArquivoTdaLicitacao from '~/domains/ArquivoTdaLicitacao';
import FormBase from 'fc/stores/FormBase';
import TdaLicitacaoService from '~/services/TdaLicitacaoService';
import TdaCaronaService from '~/services/TdaCaronaService';
import TdaDispensaService from '~/services/TdaDispensaService';
import TdaInexigibilidadeService from '~/services/TdaInexigibilidadeService';
import { showErrorNotification } from 'fc/utils/utils';
import MultipleFileUploaderStore from 'fc/stores/MultipleFileUploaderStore';
import TdaCredenciamentoService from '~/services/TdaCredenciamentoService';
import ArquivoTdaCredenciamento from '~/domains/ArquivoTdaCredenciamento';

class ArquivoChecklistFormStore extends FormBase {
  @observable filesList = [];
  @observable fileStore;
  @observable loadedFiles = false;

  constructor(idProcesso, tipoProcesso, changeFlagModifier, callback) {
    const domainsAndServices = {
      licitacao: {
        service: TdaLicitacaoService,
        domain: ArquivoTdaLicitacao,
      },
      carona: {
        service: TdaCaronaService,
        domain: ArquivoTdaCarona,
      },
      dispensa: {
        service: TdaDispensaService,
        domain: ArquivoTdaDispensa,
      },
      inexigibilidade: {
        service: TdaInexigibilidadeService,
        domain: ArquivoTdaInexigibilidade,
      },
      credenciamento: {
        service: TdaCredenciamentoService,
        domain: ArquivoTdaCredenciamento,
      },
    };
    super(domainsAndServices[tipoProcesso].service, domainsAndServices[tipoProcesso].domain);
    this.domainsAndServices = domainsAndServices;
    this.idProcesso = idProcesso;
    this.fileStore = new MultipleFileUploaderStore(
      [],
      (file) => domainsAndServices[tipoProcesso].service.upload(file),
      (fileDTO) => domainsAndServices[tipoProcesso].service.download(fileDTO),
      (idArquivo) => this.removerArquivoChecklist(idArquivo),
      (idArquivo, arquivoInexigibilidadeDTO) => this.atualizarArquivoChecklist(idArquivo, arquivoInexigibilidadeDTO)
    );
    this.initializeFiles(idProcesso, domainsAndServices[tipoProcesso].service, callback);
    this.removerArquivoChecklist = this.removerArquivoChecklist.bind(this);
    this.atualizarArquivoChecklist = this.atualizarArquivoChecklist.bind(this);
    this.changeFlagModifier = changeFlagModifier;
  }

  @action
  setFileList(files) {
    this.filesList = files;
    this.changeFlagModifier(true);
  }

  @action
  removerArquivoChecklist(idArquivo) {
    return this.domainsAndServices[this.tipoProcesso].service.removerArquivo(this.idProcesso, idArquivo);
  }

  @action
  atualizarArquivoChecklist(idArquivo, arquivoChecklistDTO) {
    return this.domainsAndServices[this.tipoProcesso].service.atualizarArquivo(
      this.idProcesso,
      idArquivo,
      arquivoChecklistDTO
    );
  }

  @action
  initializeFiles(idProcesso, service, callback) {
    if (idProcesso) {
      this.loadingFiles = true;
      service
        .recuperarArquivos(idProcesso)
        .then((response) =>
          runInAction(() => {
            if (response && response.data) {
              const arquivosRecuperados = response.data;
              this.fileStore.initialize(arquivosRecuperados);
              this.filesList = arquivosRecuperados;
            }
          })
        )
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        )
        .finally(() =>
          runInAction(() => {
            this.loadedFiles = true;
            this.loadingFiles = false;
            callback && callback();
          })
        );
    }
  }
}

export default ArquivoChecklistFormStore;
