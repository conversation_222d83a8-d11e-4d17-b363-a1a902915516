import FormBase from 'fc/stores/FormBase';
import LicitacaoService from '~/services/LicitacaoService';
import CaronaService from '~/services/CaronaService';
import DispensaService from '~/services/DispensaService';
import InexigibilidadeService from '~/services/InexigibilidadeService';
import TdaLicitacaoService from '~/services/TdaLicitacaoService';
import TdaCaronaService from '~/services/TdaCaronaService';
import TdaDispensaService from '~/services/TdaDispensaService';
import TdaInexigibilidadeService from '~/services/TdaInexigibilidadeService';
import Licitacao from '~/domains/Licitacao';
import Carona from '~/domains/Carona';
import Dispensa from '~/domains/Dispensa';
import Inexigibilidade from '~/domains/Inexigibilidade';
import SecaoChecklistService from '~/services/SecaoChecklistService';
import { action, observable, override, runInAction } from 'mobx';
import { checkUserGroup, isValueValid, showErrorNotification, showNotification, getValue } from 'fc/utils/utils';
import ArquivoChecklistFormStore from './ArquivoChecklistFormStore';
import ChecklistLicitacaoService from '~/services/ChecklistLicitacaoService';
import ChecklistDispensaService from '~/services/ChecklistDispensaService';
import ChecklistCaronaService from '~/services/ChecklistCaronaService';
import ChecklistInexigibilidadeService from '~/services/ChecklistInexigibilidadeService';
import AppStore from 'fc/stores/AppStore';
import Credenciamento from '~/domains/Credenciamento';
import ChecklistCredenciamentoService from '~/services/ChecklistCredenciamentoService';
import TdaCredenciamentoService from '~/services/TdaCredenciamentoService';
import CredenciamentoService from '~/services/CredenciamentoService';

class AplicarChecklistFormStore extends FormBase {
  @observable secoes;
  @observable secoesLoading;
  @observable houveModificacao = false;
  @observable arquivoStore;
  @observable tda;

  constructor(idProcesso, tipoProcesso) {
    const domainsAndServices = {
      licitacao: {
        service: LicitacaoService,
        serviceChecklist: ChecklistLicitacaoService,
        serviceTda: TdaLicitacaoService,
        domain: Licitacao,
        text: 'licitação',
      },
      carona: {
        service: CaronaService,
        serviceChecklist: ChecklistCaronaService,
        serviceTda: TdaCaronaService,
        domain: Carona,
        text: 'carona',
      },
      dispensa: {
        service: DispensaService,
        serviceChecklist: ChecklistDispensaService,
        serviceTda: TdaDispensaService,
        domain: Dispensa,
        text: 'dispensa',
      },
      inexigibilidade: {
        service: InexigibilidadeService,
        serviceChecklist: ChecklistInexigibilidadeService,
        serviceTda: TdaInexigibilidadeService,
        domain: Inexigibilidade,
        text: 'inexigibilidade',
      },
      credenciamento: {
        service: CredenciamentoService,
        serviceChecklist: ChecklistCredenciamentoService,
        serviceTda: TdaCredenciamentoService,
        domain: Credenciamento,
        text: 'credenciamento',
      },
    };

    super(domainsAndServices[tipoProcesso].service, domainsAndServices[tipoProcesso].domain);
    this.domainsAndServices = domainsAndServices;
    this.tipoProcesso = tipoProcesso;
    this.idProcesso = idProcesso;
    this.changeFlagModifier = this.changeFlagModifier.bind(this);
  }

  @action
  updateStatusChecklist(callback) {
    this.tda['checklistFinalizado'] = true;
    this.tda['checklistRejeitado'] = false;
    this.tda['motivoChecklistRejeitado'] = '';
    this.tda['dataRejeicao'] = null;
    const promises = [];
    promises.push(
      this.domainsAndServices[this.tipoProcesso].serviceChecklist.saveItems(
        this.object.id,
        this.generateItemsChecklistByEntity()
      )
    );
    promises.push(
      this.domainsAndServices[this.tipoProcesso].serviceTda.saveArquivos(
        this.object.tda.id,
        this.arquivoStore.filesList
      )
    );

    promises.push(this.domainsAndServices[this.tipoProcesso].serviceTda.update(this.tda, this.tda.id));
    Promise.all(promises)
      .then(() =>
        runInAction(() => {
          showNotification('success', null, 'Checklist concluído com sucesso!');
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      )
      .finally(() =>
        runInAction(() => {
          callback && callback();
        })
      );
  }

  @action
  changeFlagModifier(value) {
    this.houveModificacao = value;
  }

  initializeArquivoChecklistFormStore(callback) {
    this.arquivoStore = new ArquivoChecklistFormStore(
      this.object.tda.id,
      this.tipoProcesso,
      this.changeFlagModifier,
      callback
    );
  }

  @action
  getSecoesChecklistByProcesso(callback) {
    this.secoesLoading = true;
    SecaoChecklistService.getSecoesChecklistByProcesso(this.tipoProcesso.toUpperCase(), this.idProcesso)
      .then((response) =>
        runInAction(() => {
          this.secoes = response.data;
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      )
      .finally(() =>
        runInAction(() => {
          this.secoesLoading = false;
          callback && callback();
        })
      );
  }

  @action
  updateAtrributeRespostaChecklist(respostaChecklist, row) {
    this.secoes[
      this.secoes.findIndex((obj) => obj.itemChecklistProcesso.item.id === row.itemChecklistProcesso.item.id)
    ].itemChecklistProcesso.resposta = respostaChecklist.value;
    this.changeFlagModifier(true);
  }

  @action
  generateItemsChecklistByEntity() {
    let items = [];
    this.secoes.forEach((obj) => {
      if (obj.itemChecklistProcesso.resposta && obj.itemChecklistProcesso.resposta != 'SELECIONE') {
        items.push(JSON.parse(JSON.stringify(obj.itemChecklistProcesso)));
      }
    });
    return items;
  }

  checkUserIsResponsavel() {
    const user = AppStore.getData('userDetails');
    return user.id == this.object.tda?.responsavel?.id || checkUserGroup(['Administrador']);
  }

  disabledButton() {
    return !(this.checkUserIsResponsavel() && !this.object.tda?.checklistFinalizado);
  }

  @override
  save(callback) {
    const promises = [];
    promises.push(
      this.domainsAndServices[this.tipoProcesso].serviceChecklist.saveItems(
        this.object.id,
        this.generateItemsChecklistByEntity()
      )
    );
    promises.push(
      this.domainsAndServices[this.tipoProcesso].serviceTda.saveArquivos(
        this.object.tda.id,
        this.arquivoStore.filesList
      )
    );
    promises.push(this.domainsAndServices[this.tipoProcesso].serviceTda.save(this.tda, 'edit', this.tda.id));

    this.loading = true;
    Promise.all(promises)
      .then(() => {
        runInAction(() => {
          showNotification('success', null, 'Registro salvo com sucesso!');
        });
      })
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      )
      .finally(() =>
        runInAction(() => {
          this.loading = false;
          this.changeFlagModifier(false);
          callback && callback();
        })
      );
  }

  @action
  automaticSave(callback) {
    const promises = [];
    promises.push(
      this.domainsAndServices[this.tipoProcesso].serviceChecklist.saveItems(
        this.object.id,
        this.generateItemsChecklistByEntity()
      )
    );

    promises.push(
      this.domainsAndServices[this.tipoProcesso].serviceTda.saveArquivos(
        this.object.tda.id,
        this.arquivoStore.filesList
      )
    );
    promises.push(this.domainsAndServices[this.tipoProcesso].serviceTda.save(this.tda, 'edit', this.tda.id));

    this.loading = true;
    Promise.all(promises)
      .then((response) => {
        runInAction(() => {
          this.fillPostSave(response[0]?.data);
          showNotification('success', null, 'Registro salvo com sucesso!');
        });
      })
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      )
      .finally(() =>
        runInAction(() => {
          this.loading = false;
          this.changeFlagModifier(false);
          callback && callback();
        })
      );
  }

  fillPostSave(checklists) {
    checklists.forEach((checklist) => {
      this.secoes[
        this.secoes.findIndex((obj) => obj.itemChecklistProcesso.item.id === checklist.item.id)
      ].itemChecklistProcesso.id = checklist.id;
    });
  }

  @action
  initializeTda(callback) {
    this.domainsAndServices[this.tipoProcesso].serviceTda
      .getById(this.object.tda.id)
      .then((response) =>
        runInAction(() => {
          this.tda = response.data;
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      )
      .finally(() =>
        runInAction(() => {
          this.loading = false;
          callback && callback();
        })
      );
  }

  @action
  updateRelatorio(event) {
    let value = event;
    if (event) {
      if (isValueValid(event.target)) {
        value = event.target.value;
      } else if (isValueValid(event.value)) {
        value = event.value;
      } else if (typeof event.getData === 'function') {
        value = event.getData();
      } else {
        value = undefined;
      }
    }
    this.tda.relatorioChecklist = value;
    this.changeFlagModifier(true);
  }

  @action
  generateReport(callback) {
    let items = '<ol>';
    this.secoes?.forEach((v) => {
      const descricaoRelatorioChecklist = v.itemChecklistProcesso.item.descricaoRelatorioChecklist;
      if (v.itemChecklistProcesso.resposta === 'NAO') {
        const newText = descricaoRelatorioChecklist ? getValue(descricaoRelatorioChecklist) : 'Texto';
        items += this._generateItemReport(newText);
      }
    });
    items += '</ol>';

    this.tda.relatorioChecklist = `
    <p><span style="color:black;">Prezados,</span></p>
    <p><span style="color:black;">&nbsp;</span></p>
    <p style="text-align:justify;"><span style="color:black;">O <strong>Tribunal de Contas do Estado</strong> do Acre por meio da Resolução TCE/AC nº 97 de 14 de maio de 2015 que dispõe sobre o cadastro eletrônico dos processos licitatórios, dispensas e inexigibilidades, adesão à ata de registro de preços e contratos no Portal de Licitações do Tribunal de Contas do Estado do Acre - LICON e dá outras providências, que entrou em vigor no dia 01 de outubro de 2015, <strong>está acompanhando</strong> concomitantemente os atos administrativos vinculados a <strong>aquisições</strong> e <strong>contratações</strong> públicas.</span></p>
    <p style="text-align:justify;"><span style="color:black;">Ao analisar as informações cadastradas eletronicamente deste certame no Portal do LICON detectamos possíveis <strong>INCONFORMIDADES</strong>/<strong>IRREGULARIDADES</strong>.</span></p>
    <p style="text-align:justify;"><span style="color:black;">Nesse sentido, solicitamos ao Responsável que promova RETIFICAÇÃO e/ou a devida JUSTIFICATIVA, a saber:</span></p>
    ${items}
    <p style="text-align:justify;"><span style="color:black;">Ressaltamos que, conforme exposto no art. 43, inciso III da lei nº 8666/93, a ${
      this.domainsAndServices[this.tipoProcesso].text
    } somente será processada e julgada após o julgamento dos recursos interpostos, e, de acordo como art. 113, § 2º, os órgãos ou entidades da Administração interessada estão obrigados à adoção de medidas corretivas pertinentes, que em função do exame, forem determinadas pelos Tribunais de Contas.</span></p>
    <p style="text-align:justify;"><span style="color:black;">No mais, aguardamos posicionamento no tocante a JUSTIFICATIVA e/ou RETIFICAÇÃO.</span></p>
    <p><span style="color:black;">Equipe DAFO/LICON.</span></p>
    <p><span style="color:black;">Atenciosamente</span></p>`;

    this.houveModificacao = true;
    callback && callback();
  }

  _generateItemReport(value) {
    return `<li>${value}</li>`;
  }
}

export default AplicarChecklistFormStore;
