import SearchTypes from 'fc/components/AdvancedSearch/SearchTypes';
import Entidade from '~/domains/Entidade';
import GerenciamentoTermoView from '~/domains/GerenciamentoTermoView';
import Usuario from '~/domains/Usuario';
import GerenciamentoTermoViewService from '~/services/GerenciamentoTermoViewService';
import TermoReferenciaService from '~/services/TermoReferenciaService';
import UsuarioService from '~/services/UsuarioService';
import EntidadeService from '~/services/EntidadeService';
import SecaoService from '~/services/SecaoService';
import AsyncDropDownStore from 'fc/stores/AsyncDropdownStore';
import IndexBase from 'fc/stores/IndexBase';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import { checkUserGroup, showErrorNotification, showNotification } from 'fc/utils/utils';
import { action, observable, override, runInAction } from 'mobx';
import AppStore from 'fc/stores/AppStore';
import UrlRouter from '~/constants/UrlRouter';

class GerenciamentoTermoIndexStore extends IndexBase {
  @observable secoes = [];
  @observable loadingSecoes;
  @observable messageError;

  @observable parametersDisponivel = {};
  @observable listDisponivel = [];
  @observable paginationDisponivel = {
    total: 0,
    page: {
      index: 1,
      size: 10,
    },
  };

  @observable parametersAssociado = {};
  @observable listAssociado = [];
  @observable paginationAssociado = {
    total: 0,
    page: {
      index: 1,
      size: 10,
    },
  };

  @observable parametersModelo = {};
  @observable listModelo = [];
  @observable paginationModelo = {
    total: 0,
    page: {
      index: 1,
      size: 10,
    },
  };

  @observable
  tabActive = '';

  constructor() {
    super(GerenciamentoTermoViewService, GerenciamentoTermoView, 'dataCadastro', 'desc');
  }

  getFilterSuggest() {
    const entidade = AppStore.getContextEntity();
    const filterSuggest = [];
    if (entidade) {
      filterSuggest.push({
        id: '',
        field: 'entidade',
        operator: 'EQUAL_TO',
        value: entidade,
        formatted: '',
        fixed: true,
        invisible: true,
        completeParam: {
          field: 'entidade',
          label: 'Órgão/Entidade',
          type: SearchTypes.ASYNC_QUERY,
          store: new AsyncDropDownStore(Entidade, EntidadeService, 'nome', 'id'),
        },
      });
    }

    return filterSuggest;
  }

  @action
  setExportItens(exportItens) {
    this.exportItens = exportItens;
  }

  @action
  loadSecoes(callback) {
    this.loadingSecoes = true;
    SecaoService.getAllFixes()
      .then((response) =>
        runInAction(() => {
          this.secoes = response.data;
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      )
      .finally(() =>
        runInAction(() => {
          this.secoes = this.secoes.sort((a, b) => a.ordem - b.ordem);
          this.loadingSecoes = false;
          callback && callback();
        })
      );
  }

  @override
  load(options = {}, filter, callback) {
    this.loading = true;

    const advancedSearchParams = JSON.parse(JSON.stringify(this.advancedSearchParams));
    const parameters = Object.assign(advancedSearchParams, options);

    if (options && options.page && options.page.index && options.page.size) {
      parameters.page = { index: options.page.index, size: options.page.size };
    } else {
      parameters.page = { index: 1, size: 10 };
    }

    if (options.andParameters && options.andParameters.length > 0) {
      parameters.andParameters = options.andParameters;
    }

    if (options.orParameters && options.orParameters.length > 0) {
      parameters.orParameters = options.orParameters;
    }

    if (options && options['sort'] && options['sort'].by && options['sort'].order) {
      parameters.sort = { by: options['sort'].by, order: options['sort'].order };
    } else {
      parameters.sort = { by: this.defaultSortBy, order: this.defaultSortOrder };
    }

    this.advancedSearchParams = advancedSearchParams;

    const promises = [];

    if (!filter || filter === 'todos') {
      promises.push(this.service.advancedSearch(parameters));
    }

    if (!filter || filter === 'disponivel') {
      const parametersDisponivel = JSON.parse(JSON.stringify(parameters));
      parametersDisponivel.andParameters.push({
        id: '',
        field: 'disponivel',
        operator: 'EQUAL_TO',
        value: true,
        formatted: '',
        fixed: true,
        invisible: true,
        completeParam: {
          field: 'disponivel',
          label: 'Disponível',
          type: SearchTypes.ENUM,
          options: DadosEstaticosService.getSimNao(),
        },
      });
      parametersDisponivel.andParameters.push({
        id: '',
        field: 'modelo',
        operator: 'EQUAL_TO',
        value: false,
        formatted: '',
        fixed: true,
        invisible: true,
        completeParam: {
          field: 'modelo',
          label: 'Modelo',
          type: SearchTypes.ENUM,
          options: DadosEstaticosService.getSimNao(),
        },
      });
      this.parametersDisponivel = parametersDisponivel;
      this.paginationDisponivel = parametersDisponivel;
      promises.push(this.service.advancedSearch(parametersDisponivel));
    }

    if (!filter || filter === 'associado') {
      const parametersAssociado = JSON.parse(JSON.stringify(parameters));
      parametersAssociado.andParameters.push({
        id: '',
        field: 'disponivel',
        operator: 'EQUAL_TO',
        value: false,
        formatted: '',
        fixed: true,
        invisible: true,
        completeParam: {
          field: 'disponivel',
          label: 'Disponível',
          type: SearchTypes.ENUM,
          options: DadosEstaticosService.getSimNao(),
        },
      });
      parametersAssociado.andParameters.push({
        id: '',
        field: 'modelo',
        operator: 'EQUAL_TO',
        value: false,
        formatted: '',
        fixed: true,
        invisible: true,
        completeParam: {
          field: 'modelo',
          label: 'Modelo',
          type: SearchTypes.ENUM,
          options: DadosEstaticosService.getSimNao(),
        },
      });
      this.parametersAssociado = parametersAssociado;
      this.paginationAssociado = parametersAssociado;
      promises.push(this.service.advancedSearch(parametersAssociado));
    }

    if (!filter || filter === 'modelo') {
      const parametersModelo = JSON.parse(JSON.stringify(parameters));
      parametersModelo.andParameters.push({
        id: '',
        field: 'modelo',
        operator: 'EQUAL_TO',
        value: true,
        formatted: '',
        fixed: true,
        invisible: true,
        completeParam: {
          field: 'modelo',
          label: 'Modelo',
          type: SearchTypes.ENUM,
          options: DadosEstaticosService.getSimNao(),
        },
      });
      this.parametersModelo = parametersModelo;
      this.paginationModelo = parametersModelo;
      promises.push(this.service.advancedSearch(parametersModelo));
    }

    Promise.all(promises)
      .then((responses) =>
        runInAction(() => {
          if (filter === 'todos') {
            this.pagination.total = responses[0].data.total;
            this.list = this.initializeLoadedList(responses[0].data.items);
          } else if (filter === 'disponivel') {
            this.paginationDisponivel.total = responses[0].data.total;
            this.listDisponivel = this.initializeLoadedList(responses[0].data.items);
          } else if (filter === 'associado') {
            this.paginationAssociado.total = responses[0].data.total;
            this.listAssociado = this.initializeLoadedList(responses[0].data.items);
          } else if (filter === 'modelo') {
            this.paginationModelo.total = responses[0].data.total;
            this.listModelo = this.initializeLoadedList(responses[0].data.items);
          } else {
            this.pagination.total = responses[0].data.total;
            this.list = this.initializeLoadedList(responses[0].data.items);

            this.paginationDisponivel.total = responses[1].data.total;
            this.listDisponivel = this.initializeLoadedList(responses[1].data.items);

            this.paginationAssociado.total = responses[2].data.total;
            this.listAssociado = this.initializeLoadedList(responses[2].data.items);

            this.paginationModelo.total = responses[3].data.total;
            this.listModelo = this.initializeLoadedList(responses[3].data.items);
          }
          callback && callback();
        })
      )
      .catch((error) => showErrorNotification(error))
      .finally(() =>
        runInAction(() => {
          this.loading = false;
        })
      );
  }

  @override
  async loadExportList() {
    let parametersTab = {
      ...this.advancedSearchParams,
      page: { index: this.pagination.page.index, size: this.rangeExportData },
    };
    switch (this.tabActive) {
      case 'todos':
        parametersTab = this.advancedSearchParams;
        break;
      case 'disponivel':
        parametersTab = this.parametersDisponivel;
        break;
      case 'associado':
        parametersTab = this.parametersAssociado;
        break;
      case 'modelo':
        parametersTab = this.parametersModelo;
        break;
    }
    parametersTab = {
      ...parametersTab,
      page: { index: this.pagination.page.index, size: this.rangeExportData },
    };
    await this.service
      .advancedSearch(parametersTab)
      .then((response) => {
        runInAction(() => {
          this.exportList = response.data.items;
        });
      })
      .catch(() => {
        showNotification('error', null, 'Ocorreu um erro na exportação dos dados');
      });
  }

  @action
  clone(id, identificadorProcesso, callback) {
    this.loading = true;
    TermoReferenciaService.cloneById(id, identificadorProcesso)
      .then((response) =>
        runInAction(() => {
          this.idClone = response.data.id;
          showNotification('success', null, 'Termo de Referência clonado com sucesso!');
          callback && callback(UrlRouter.termoReferencia.gerenciamentoTermos.editar.replace(':id', this.idClone));
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      )
      .finally(() =>
        runInAction(() => {
          this.loading = false;
        })
      );
  }

  getTermoById(idTermo, callback) {
    if (idTermo) {
      TermoReferenciaService.getById(idTermo)
        .then((response) => {
          return callback && callback(response.data);
        })
        .catch((e) => {
          showErrorNotification(e);
        });
    }
  }

  getAdvancedSearchParams() {
    let filters = [
      {
        field: 'usuario',
        label: 'Usuário',
        type: SearchTypes.ASYNC_QUERY,
        store: new AsyncDropDownStore(Usuario, UsuarioService, 'nome', 'id'),
      },
      {
        field: 'dataCadastro',
        label: 'Data de Cadastro',
        type: SearchTypes.DATE,
      },
      {
        field: 'identificadorProcesso',
        label: 'Identificador do Termo de Referência',
        type: SearchTypes.TEXT,
      },
    ];

    if (checkUserGroup(['Auditor', 'Auditor - Demais Inspetorias'])) {
      filters.push({
        field: 'entidade',
        label: 'Órgão/Entidade',
        type: SearchTypes.ASYNC_QUERY,
        store: new AsyncDropDownStore(Entidade, EntidadeService, 'nome', 'id'),
      });
    }

    return filters;
  }

  @action
  setTabActive(tab) {
    this.tabActive = tab;
  }

  @override
  deleteRow(id, callback) {
    this.loading = true;
    TermoReferenciaService.delete(id)
      .then(() => {
        callback && callback();
        showNotification('success', null, 'Registro excluído com sucesso!');
      })
      .catch((error) => showErrorNotification(error))
      .finally(() =>
        runInAction(() => {
          this.loading = false;
        })
      );
  }

  isItensTermoDepreciadosSuspensos(lotes) {
    let itensDepreciadosSupensos = false;
    lotes.map((lote) => {
      lote.itens.map((item) => {
        const itemSuspenso = item.materialDetalhamento.itemSuspenso === 'S';
        const itemDepreciado = item.materialDetalhamento.status === 'D';

        if (itemSuspenso || itemDepreciado) {
          itensDepreciadosSupensos = true;
        }
      });
    });
    return itensDepreciadosSupensos;
  }
}

export default GerenciamentoTermoIndexStore;
