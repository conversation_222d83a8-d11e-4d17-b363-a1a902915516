import Entidade from '~/domains/Entidade';
import TermoReferencia from '../../domains/TermoReferencia';
import TermoReferenciaService from '../../services/TermoReferenciaService';
import SecaoService from '../../services/SecaoService';
import EntidadeService from '../../services/EntidadeService';
import AsyncDropDownStore from 'fc/stores/AsyncDropdownStore';
import FormBase from 'fc/stores/FormBase';
import { action, computed, observable, runInAction } from 'mobx';
import TermoReferenciaSecao from '~/domains/TermoReferenciaSecao';
import { isValueValid, showErrorNotification, showNotification } from 'fc/utils/utils';
import ItemLote from '~/domains/ItemLote';
import Lote from '~/domains/Lote';
import CryptoJS from 'crypto-js';
import { extractRules } from 'fc/utils/formRules';
import Secao from '~/domains/Secao';
import moment from 'moment';
import MultipleFileUploaderStore from 'fc/stores/MultipleFileUploaderStore';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import ObrigatoriedadeArquivoService from '~/services/ObrigatoriedadeArquivoService';

class GerenciamentoTermoFormStore extends FormBase {
  secaoService;
  @observable loadingSecoes;
  @observable editMaterial;
  @observable houveModificacao = false;

  @observable lotes = observable.map({});
  @observable nomeLote;
  @observable editingLote;
  @observable loteSelecionado;
  @observable editarLote;

  @observable secoes = [];
  @observable secoesTermo = {};
  @observable novaSecao;
  @observable editedSecao;

  fileStore;
  @observable arquivosRecuperados = [];
  @observable processoAssociado;
  @observable itensInvalidos = false;

  constructor() {
    super(TermoReferenciaService, TermoReferencia);
    this.secaoService = SecaoService;
    this.entidadeSelectStore = new AsyncDropDownStore(Entidade, EntidadeService, 'nome', 'id');
    this.entidadeService = EntidadeService;

    this.updateAttributeAba = this.updateAttributeAba.bind(this);
    this.updateAttributeCheckboxEditMaterial = this.updateAttributeCheckboxEditMaterial.bind(this);
    this.validateMandatorySections = this.validateMandatorySections.bind(this);
    this.getRuleItem = this.getRuleItem.bind(this);
    this.postInitialize = this.postInitialize.bind(this);
    this.isItensTermoDepreciadosSuspensos = this.isItensTermoDepreciadosSuspensos.bind(this);

    this.fileStore = new MultipleFileUploaderStore(
      [],
      (file) => TermoReferenciaService.upload(file),
      (fileDTO) => TermoReferenciaService.download(fileDTO),
      (idArquivo) => this.removerArquivoTermoReferencia(idArquivo),
      (idArquivo, arquivoTermoReferenciaDTO) =>
        this.atualizarArquivoTermoReferencia(idArquivo, arquivoTermoReferenciaDTO)
    );
  }

  initialize(id, defaultValues = {}, callback, callbackSaveAutomatic) {
    if (!id) {
      this.object = Object.assign({}, defaultValues);
      callbackSaveAutomatic && callbackSaveAutomatic();
    } else {
      this.loading = true;
      Promise.all([this.service.getById(id), this.service.processoAssociado(id)])
        .then((response) =>
          runInAction(() => {
            this.object = response[0].data;
            this.loadedObject = response[0].data;
            this.processoAssociado = response[1].data;
            callback && callback();
            callbackSaveAutomatic && callbackSaveAutomatic();
          })
        )
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        )
        .finally(() =>
          runInAction(() => {
            this.loading = false;
          })
        );
    }
  }

  @action
  carregarArquivosObrigatorios(obraEngenharia) {
    ObrigatoriedadeArquivoService.getArquivosObrigatorios({
      tipoProcesso: 'TERMO_REFERENCIA',
      filtros: [obraEngenharia ?? this.object?.obraEngenharia ? 'TERMO_OBRA' : 'TERMO_REFERENCIA'],
    })
      .then((response) =>
        runInAction(() => {
          const arquivosObrigatorios = response?.data ?? [];
          this.fileStore.tipoArquivoEnum = arquivosObrigatorios.map((arqObg) => {
            const arq = DadosEstaticosService.getTipoArquivoTermoReferencia().find(
              (arq) => arq.value === arqObg.arquivoEnum
            );
            return { ...arqObg, text: arqObg.obrigatorio ? '* ' + arqObg.arquivo : arqObg.arquivo, value: arq.value };
          });
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      );
  }

  validaDadosBasicos() {
    const rules = this.rulesDefinition();
    const dadosBasicos = Object.keys(rules).filter((k) => rules[k].find((r) => r.rule === 'required'));
    const required = Object.keys(rules).filter(
      (k) => dadosBasicos.includes(k) && rules[k].find((r) => r.rule === 'required')
    );
    const campos = required.filter(
      (k) => !isValueValid(this.object[k]) || (Array.isArray(this.object[k]) && this.object[k].length === 0)
    );
    return campos.length === 0;
  }

  rulesDefinition() {
    return {
      entidade: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      formaPreenchimentoSecao: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      identificadorProcesso: [
        { rule: 'isMaxLength', maxLength: 255, message: 'Por favor, diminua o tamanho do campo' },
        { rule: 'required', message: 'Por favor, preencha o campo' },
      ],
      justificativa: [{ rule: 'isMaxLength', maxLength: 4000, message: 'Por favor, diminua o tamanho do campo' }],
      modelo: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
      srp: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
    };
  }

  loadArvore(callback) {
    this.loadingSecoes = true;
    this.secaoService
      .getAllFixes()
      .then((response) =>
        runInAction(() => {
          this.secoes = response.data;
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      )
      .finally(() =>
        runInAction(() => {
          this.secoes.forEach((secao) => {
            if (!this.secoesTermo[secao.id]) {
              const newItem = new TermoReferenciaSecao();
              newItem.secao = secao;
              this.secoesTermo[secao.id] = newItem;
            }
          });

          this.secoes.sort((a, b) => a.ordem - b.ordem);
          this.loadingSecoes = false;
          callback && callback();
        })
      );
  }

  postInitialize(callback) {
    if (this.object.id) {
      if (this.object.secoes) this.object.secoes.forEach((item) => (this.secoesTermo[item.secao.id] = item));
      else this.object.secoes = [];

      if (this.object.secoesCriadas) {
        this.object.secoesCriadas.forEach((secao) => {
          if (!this.secoesTermo[secao.id]) {
            const newItem = new TermoReferenciaSecao();
            newItem.secao = secao;
            this.secoesTermo[secao.id] = newItem;
          }

          delete this.secoesTermo[CryptoJS.SHA1(secao.titulo).toString(CryptoJS.enc.Base64)];
        });
        this.object.secoesCriadas = this.object.secoesCriadas.sort((a, b) => a.ordem - b.ordem);
      } else {
        this.object.secoesCriadas = [];
      }

      if (this.object.lotes) this.object.lotes.forEach((lote) => this.lotes.set(lote.nome, lote));
      else this.object.lotes = [];

      this.recuperarArquivos(this.object.id, callback);
    }
  }

  getItem(id) {
    return this.secoesTermo[id];
  }

  preencheDadosSecoes() {
    this.secoes?.forEach((s) => {
      this.updateAttributeAba(s.id, { value: 'Termo importado de arquivo' });
    });
  }

  @action
  updateAttributeAba(id, event) {
    if (this.secoesTermo[id]) {
      let value = event;
      if (event) {
        if (isValueValid(event.target)) {
          value = event.target.value;
        } else if (isValueValid(event.value)) {
          value = event.value;
        } else if (typeof event.getData === 'function') {
          value = event.getData();
        } else {
          value = undefined;
        }
      }
      this.secoesTermo[id].valor = value;
      this.houveModificacao = true;
    }
  }

  save(callback, type = 'edit') {
    if (this.object.id) type = 'edit';
    this.beforeSave();

    if (!this.itensInvalidos) {
      super.save(callback, type);
    }
  }

  beforeSave() {
    this.itensInvalidos = this.isItensTermoDepreciadosSuspensos(this.object.lotes);
    if (this.itensInvalidos) {
      showNotification(
        'error',
        null,
        'Há itens depreciados ou suspensos no Termo de Referência. Eles devem ser excluídos antes de salvar'
      );
      return;
    }

    const secoesPreenchidas = [];

    Object.keys(this.secoesTermo).forEach((key) => {
      const item = this.secoesTermo[key];
      if (item.valor) {
        secoesPreenchidas.push(item);
      }
    });
    this.object.secoes = secoesPreenchidas.map((s) => {
      if (this.object.arquivosTemporarios?.length > 0) {
        return { ...s, valor: 'Termo importado de arquivo' };
      } else {
        return s;
      }
    });

    const lotesPreenchidas = [];

    for (const key of this.lotes.keys()) {
      const lote = Object.assign({}, this.lotes.get(key));

      const cloneItens = [];

      lote.itens.forEach((item) => {
        cloneItens.push(Object.assign({}, item, { lote: undefined }));
      });

      lote.itens = cloneItens;

      if (lote.itens?.length) {
        lotesPreenchidas.push(lote);
      }
    }

    this.object.lotes = lotesPreenchidas;

    this.object.secoesCriadas.forEach((secao, index) => (secao.ordem = index));
    let idx = 0;
    this.object.lotes = this.object.lotes?.map((lote) => {
      if (lote.gerado) {
        return {
          ...lote,
          itens: lote.itens.map((item) => {
            idx += 1;
            return { ...item, numero: idx };
          }),
        };
      } else {
        return {
          ...lote,
          itens: lote.itens.map((item, index) => {
            return { ...item, numero: index + 1 };
          }),
        };
      }
    });
  }

  validateMandatorySections() {
    const mandatoryEmptyFields = [];
    const { secoesTermo } = this;
    Object.keys(secoesTermo).forEach((key) => {
      if (!secoesTermo[key]?.valor && secoesTermo[key]?.secao.obrigatoria) {
        mandatoryEmptyFields.push(secoesTermo[key].secao.titulo);
      }
    });
    return mandatoryEmptyFields;
  }

  validate(value) {
    return value !== null && value !== undefined && value !== '';
  }

  @action
  automaticSave(callback) {
    this.beforeSave();
    let type = 'new';
    if (this.object.id) {
      type = 'edit';
    }

    this.loading = true;
    this.service
      .save(this.object, type, this.object.id)
      .then((response) =>
        runInAction(() => {
          this.fillPostSave(response.data);
          showNotification('success', null, 'Registro salvo com sucesso!');
          callback && callback();
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      )
      .finally(() =>
        runInAction(() => {
          this.loading = false;
          this.houveModificacao = false;
        })
      );
  }

  fillPostSave(termo) {
    const { id, usuario, dataCadastro, secoes, secoesCriadas, lotes } = termo;
    this.object.id = id;
    this.object.usuario = usuario;
    this.object.dataCadastro = dataCadastro;

    this.object.secoesCriadas.forEach((secao) => {
      if (secao.id === undefined) {
        secao.id = secoesCriadas.find((s) => s.titulo === secao.titulo)?.id;
      }
    });

    Object.keys(this.secoesTermo).forEach((key) => {
      const item = this.secoesTermo[key];
      if (!item.id) {
        item.id = secoes.find((secPreenchida) => secPreenchida.secao.id === item.secao.id)?.id;

        if (item.secao.id != key) {
          delete this.secoesTermo[key];
          this.secoesTermo[item.secao.id] = item;
        }
      }
    });

    const itensToFill = [];
    const itensToSearch = [];

    for (const key of this.lotes.keys()) {
      const loteCache = this.lotes.get(key);
      const loteSalvo = lotes.find((lote) => lote.nome === loteCache.nome);

      if (loteSalvo) {
        loteCache.id = loteSalvo.id;

        loteCache.itens.forEach((item) => {
          if (!item.id) {
            itensToFill.push(item);
          }
          item.lote = loteCache;
        });

        itensToSearch.push(...loteSalvo.itens);
      }
    }

    itensToFill.forEach((item) => {
      const indexItem = itensToSearch.findIndex((itm) => moment(itm.dataCadastro).isSame(item.dataCadastro));
      if (indexItem !== undefined) {
        item.id = itensToSearch[indexItem].id;
        item.usuario = itensToSearch[indexItem].usuario;
        itensToSearch.splice(indexItem, 1);
      }
    });
  }

  @action
  setMaterial(referencia, type) {
    if (type === 'new') {
      const newItem = new ItemLote();
      newItem.lote = this.loteSelecionado;
      newItem.materialDetalhamento = referencia;
      newItem.fracionario = false;
      newItem.quantidade = 0;
      if (this.object.srp) {
        newItem.quantidadeConsumo = 0;
      }
      this.editMaterial = newItem;
    } else {
      this.editMaterial = Object.assign({}, this.lotes.get(referencia.lote).itens[referencia.index]);
    }
  }

  isRequisicaoModificacao() {
    return this.object?.isFinalizado;
  }

  @action
  saveItemCatalogo(callback, saveCallBack) {
    const loteKey = this.editMaterial.lote.nome;

    const itemKey = this.editMaterial.key;

    if (
      this.editMaterial?.materialDetalhamento?.pdm?.tipoMaterial === 'S' &&
      this.editMaterial?.materialDetalhamento?.pdm?.unidadesMedida?.length
    ) {
      this.editMaterial.unidadeMedida = this.editMaterial.materialDetalhamento.pdm.unidadesMedida[0];
    }

    if (!this.editMaterial.id && itemKey && this.lotes.get(itemKey.lote).itens[itemKey.index].id) {
      this.editMaterial.id = this.lotes.get(itemKey.lote).itens[itemKey.index].id;
      this.editMaterial.usuario = this.lotes.get(itemKey.lote).itens[itemKey.index].usuario;
    }

    if (!this.editMaterial.dataCadastro) {
      this.editMaterial.dataCadastro = moment().format('YYYY-MM-DD[T]HH:mm:ss.SSS');
    }

    if (itemKey && itemKey.lote === loteKey) {
      this.lotes.get(itemKey.lote).itens[itemKey.index] = this.editMaterial;
    } else {
      if (itemKey && itemKey.lote !== this.editMaterial.lote) {
        this.deleteItemCatalogo(itemKey);
      }

      const item = this.lotes
        .get(loteKey)
        .itens.find(
          (item) =>
            item.materialDetalhamento.id === this.editMaterial.materialDetalhamento.id &&
            item.descricaoComplementar === this.editMaterial.descricaoComplementar &&
            item.valorUnitarioEstimado === this.editMaterial.valorUnitarioEstimado
        );

      if (item) {
        item.quantidade += this.editMaterial.quantidade;
      } else {
        this.lotes.get(loteKey).itens.push(this.editMaterial);
      }
    }

    this.editMaterial = null;
    this.houveModificacao = true;
    if (!this.isRequisicaoModificacao()) {
      saveCallBack && saveCallBack();
    }
    callback && callback();
  }

  @action
  deleteItemCatalogo(key) {
    if (this.lotes.get(key.lote).gerado) {
      this.lotes.delete(key.lote);
    } else {
      this.lotes.get(key.lote).itens.splice(key.index, 1);
      !this.lotes.get(key.lote).itens.length && this.lotes.delete(key.lote);
    }
  }

  @action
  checkNomeLoteUnico(nome) {
    const nomeFormatado = nome.normalize('NFD').replace(/[\u0300-\u036f]/g, '');
    if (nomeFormatado.toLowerCase().includes('lote') && nomeFormatado.toLowerCase().includes('unico')) {
      return true;
    } else {
      return false;
    }
  }

  @action
  removeLote({ key, gerado }) {
    if (gerado) {
      this.lotes.forEach((lote, key) => lote.gerado && this.lotes.delete(key));
    } else {
      this.lotes.delete(key);
    }
    if (this.lotes.size == 0) {
      this.initializeLoteUnico();
    }
  }

  @action
  excluiTodosOsItens() {
    this.lotes.clear();
    this.initializeLoteUnico();
  }

  @action
  updateAttributeMaterial(attribute, event) {
    let value = event;
    if (event && event.value) {
      value = event.value;
    }
    if (event && event.target) {
      value = event.target.value;
    }
    this.editMaterial[attribute] = value;
    this.houveModificacao = true;

    if (attribute === 'lote') {
      this.loteSelecionado = value;
    }
  }

  updateAttribute(attribute, event) {
    super.updateAttribute(attribute, event);
    this.houveModificacao = true;
  }

  @action
  updateAttributeCheckboxEditMaterial(attribute, e) {
    this.editMaterial[attribute] = e.checked;
  }

  @action
  setEntidadeContexto(entidadeId) {
    if (this.object && entidadeId) {
      this.entidadeSelectStore.initialize(
        entidadeId,
        () => (this.object.entidade = this.entidadeSelectStore.selectedItem)
      );
    }
  }

  @action
  setNomeLote(nome, clean) {
    this.nomeLote = nome;
    if (clean) {
      this.editingLote = '';
    }
  }

  @action
  editLote(nome) {
    this.nomeLote = nome;
    this.editingLote = nome;
  }

  @action
  handleUpdateFormaPreenchimentoSecao(e) {
    const forma = e.value;
    if (forma === 'PREENCHIMENTO_MANUAL') {
      this.secoesTermo = {};
      this.secoes = [];
      this.updateAttribute('arquivosTemporarios', []);
      this.fileStore.initialize([]);

      this.updateAttribute('secoes', []);
      this.updateAttribute('secoesCriadas', []);
    }
    this.updateAttribute('formaPreenchimentoSecao', e);
  }

  @action
  initializeLoteUnico() {
    const loteAtual = this.lotes.get('Lote Único');
    if (!loteAtual) {
      const lote = new Lote();
      this.setNomeLote('Lote Único');
      lote.nome = this.nomeLote;
      lote.gerado = false;
      lote.itens = observable.array();
      this.lotes.set(this.nomeLote, lote);
      this.loteSelecionado = lote;
    } else {
      this.loteSelecionado = loteAtual;
    }
    !this.object.tipo && this.object.tipo == 'ITENS';
  }

  newLoteDisabled() {
    const lote = this.lotes.get('Lote Único');
    return lote && lote.itens?.length > 0;
  }

  @action
  criarLote() {
    if (!this.lotes.get(this.nomeLote)) {
      if (this.editarLote && this.lotes.has(this.editarLote) && this.checkNomeLoteUnico(this.nomeLote)) {
        const antigoLote = this.lotes.get(this.editarLote);
        antigoLote.nome = 'Lote Único';

        this.lotes.set('Lote Único', antigoLote);

        this.lotes.delete(this.editarLote);
      } else if (this.editarLote && this.lotes.has(this.editarLote)) {
        const antigoLote = this.lotes.get(this.editarLote);
        antigoLote.nome = this.nomeLote;

        this.lotes.set(this.nomeLote, antigoLote);

        this.lotes.delete(this.editarLote);
      } else {
        const lote = new Lote();
        lote.nome = this.nomeLote;
        lote.gerado = false;
        lote.itens = observable.array();
        this.lotes.set(this.nomeLote, lote);

        this.loteSelecionado = lote;
        this.editMaterial.lote = lote;
      }

      const loteAtual = this.lotes.get('Lote Único');
      if (this.lotes.size != 1 && loteAtual) {
        this.lotes.delete('Lote Único');
      }

      if (this.object.tipo !== 'LOTES') {
        this.object.tipo = 'LOTES';
      }
    }
  }

  @action
  showNotificacaoErroLoteUnico() {
    showNotification(
      'error',
      'Nome inválido',
      'Não é possível nomear o lote como único pois existe mais de um lote no termo'
    );
  }

  @computed
  get keyedLotes() {
    const list = [];
    for (const key of this.lotes.keys()) {
      if (!this.lotes.get(key).gerado) {
        list.push({ value: this.lotes.get(key), text: key });
      }
    }

    return list;
  }

  @computed
  get computedLoteValues() {
    const list = [];
    const itensSemLote = { nome: 'Itens', gerado: true, itens: [] };

    for (const key of this.lotes.keys()) {
      const keyedList = this.lotes.get(key).itens.map((item, index) => {
        item.lote = this.lotes.get(key);
        item.key = { lote: key, index };
        return item;
      });

      if (!this.lotes.get(key).gerado) {
        list.push({
          nome: 'Lote: ' + key,
          key: key,
          gerado: false,
          itens: keyedList,
        });
      } else {
        Array.prototype.push.apply(itensSemLote.itens, keyedList);
      }
    }

    list.forEach((lote) => {
      lote.itens.forEach((item, index) => {
        item.numero = index + 1;
      });
    });

    if (itensSemLote.itens.length > 0) {
      list.unshift(itensSemLote);
    }

    return list;
  }

  rulesDefinitionItem() {
    const { tipoMaterial, unidadesMedida } = this.editMaterial.materialDetalhamento.pdm;
    let rules = {};
    if (this.object.tipo === 'LOTES') {
      rules = {
        lote: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
        quantidade: [
          { rule: 'required', message: 'Por favor, preencha o campo' },
          {
            rule: 'isGreaterThanZero',
            message: 'Por favor, informe um valor maior que zero',
          },
        ],
        valorUnitarioEstimado: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
        descricaoComplementar: [
          { rule: 'isMaxLength', maxLength: 4000, message: 'Por favor, diminua o tamanho do campo' },
        ],
      };
    } else {
      rules = {
        quantidade: [
          { rule: 'required', message: 'Por favor, preencha o campo' },
          {
            rule: 'isGreaterThanZero',
            message: 'Por favor, informe um valor maior que zero',
          },
        ],
        valorUnitarioEstimado: [{ rule: 'required', message: 'Por favor, preencha o campo' }],
        descricaoComplementar: [
          { rule: 'isMaxLength', maxLength: 4000, message: 'Por favor, diminua o tamanho do campo' },
        ],
      };
    }

    if (this.object.srp) {
      rules.quantidadeConsumo = [
        {
          rule: 'isMaxValue',
          maxValue: this.editMaterial.quantidade,
          message: 'Por favor, defina uma quantidade menor ou igual à quantidade de registro',
        },
      ];
    }

    if (tipoMaterial === 'M' && unidadesMedida?.length) {
      rules.unidadeMedida = [{ rule: 'required', message: 'Por favor, preencha o campo' }];
    }

    return rules;
  }

  @action
  getRuleItem(field) {
    return this.rulesItem[field] ? this.rulesItem[field] : {};
  }

  @computed.struct
  get rulesItem() {
    const definition = this.rulesDefinitionItem();
    const result = extractRules(definition, this.editMaterial);
    Object.keys(result).forEach((key) => {
      const error = result[key].error;
      if (error) {
        result.hasError = true;
      }
    });
    return result;
  }

  @action
  setTituloSecao(titulo, novo = false) {
    if (novo) {
      this.novaSecao = new Secao();
      this.novaSecao.obrigatoria = false;
      this.novaSecao.geradaTermo = true;
    }
    this.novaSecao.titulo = titulo;
  }

  @action
  criarSecao() {
    if (this.novaSecao.titulo && !this.object.secoesCriadas.find((secao) => secao.titulo === this.novaSecao.titulo)) {
      const cryptKey =
        this.object.secoesCriadas[this.editedSecao]?.id ??
        CryptoJS.SHA1(this.novaSecao.titulo).toString(CryptoJS.enc.Base64);

      if (this.editedSecao !== undefined) {
        if (this.object.secoesCriadas[this.editedSecao].id === undefined) {
          const oldCrypt = CryptoJS.SHA1(this.object.secoesCriadas[this.editedSecao].titulo).toString(
            CryptoJS.enc.Base64
          );
          [this.secoesTermo[cryptKey], this.secoesTermo[oldCrypt]] = [
            this.secoesTermo[oldCrypt],
            this.secoesTermo[cryptKey],
          ];
          delete this.secoesTermo[oldCrypt];
        }

        this.object.secoesCriadas[this.editedSecao].titulo = this.novaSecao.titulo;
        this.editedSecao = undefined;
      } else {
        this.object.secoesCriadas.push(this.novaSecao);
        const newItem = new TermoReferenciaSecao();
        newItem.secao = this.novaSecao;
        this.secoesTermo[cryptKey] = newItem;
      }
    }
  }

  @action
  removeSecao(index, key) {
    if (this.object.secoesCriadas[index]) {
      this.object.secoesCriadas.splice(index, 1);
      delete this.secoesTermo[key];
    }
  }

  @action
  reorderItem(index, action) {
    if (action === 'down') {
      [this.object.secoesCriadas[index], this.object.secoesCriadas[index + 1]] = [
        this.object.secoesCriadas[index + 1],
        this.object.secoesCriadas[index],
      ];
    } else {
      [this.object.secoesCriadas[index], this.object.secoesCriadas[index - 1]] = [
        this.object.secoesCriadas[index - 1],
        this.object.secoesCriadas[index],
      ];
    }
  }

  @action
  editSecao(index) {
    this.editedSecao = index;
    this.novaSecao = { ...this.object.secoesCriadas[index] };
  }

  @action
  recuperarArquivos(idTermoReferencia, callback) {
    this.service
      .recuperarArquivos(idTermoReferencia)
      .then((response) =>
        runInAction(() => {
          this.arquivosRecuperados = response.data;
          if (this.object) {
            this.object.arquivosTemporarios = response.data;
          }
          this.fileStore.initialize(this.arquivosRecuperados);
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      )
      .finally(() =>
        runInAction(() => {
          callback && callback();
        })
      );
  }

  removerArquivoTermoReferencia(idArquivo) {
    return TermoReferenciaService.removerArquivo(this.object?.id, idArquivo);
  }

  atualizarArquivoTermoReferencia(idArquivo, arquivoTermoReferenciaDTO) {
    return TermoReferenciaService.atualizarArquivo(this.object?.id, idArquivo, arquivoTermoReferenciaDTO);
  }

  @action
  updateTipo(event) {
    if (event.value && this.object.tipo != event.value) {
      this.updateAttribute('tipo', event);
    }
  }

  isItensTermoDepreciadosSuspensos(lotes) {
    let itensDepreciadosSupensos = false;
    lotes.map((lote) => {
      lote.itens.map((item) => {
        const itemSuspenso = item.materialDetalhamento.itemSuspenso === 'S';
        const itemDepreciado = item.materialDetalhamento.status === 'D';

        if (itemSuspenso || itemDepreciado) {
          itensDepreciadosSupensos = true;
        }
      });
    });
    return itensDepreciadosSupensos;
  }
}

export default GerenciamentoTermoFormStore;
