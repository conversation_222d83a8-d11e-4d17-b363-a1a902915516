import IndexBase from 'fc/stores/IndexBase';
import { action, makeObservable, observable, override, runInAction } from 'mobx';
import Entidade from '~/domains/Entidade';
import EntidadeService from '~/services/EntidadeService';
import AsyncDropDownStore from 'fc/stores/AsyncDropdownStore';
import SearchTypes from 'fc/components/AdvancedSearch/SearchTypes';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import AnaliseProcessoView from '~/domains/AnaliseProcessoView';
import AnaliseProcessoViewService from '~/services/AnaliseProcessoViewService';
import { checkUserContextIsInspetor, checkUserGroup, showErrorNotification, showNotification } from 'fc/utils/utils';
import TdaDispensaService from '~/services/TdaDispensaService';
import TdaLicitacaoService from '~/services/TdaLicitacaoService';
import TdaCaronaService from '~/services/TdaCaronaService';
import TdaInexigibilidadeService from '~/services/TdaInexigibilidadeService';
import SelecioneAnaliseAuditoriaService from '~/services/SelecioneAnaliseAuditoriaService';
import AppStore from 'fc/stores/AppStore';
import Usuario from '~/domains/Usuario';
import UsuarioService from '~/services/UsuarioService';
import AlertaAnaliseService from '~/services/AlertaAnaliseService';
import ConfiguracoesService from '~/services/ConfiguracoesService';
import TdaCredenciamentoService from '~/services/TdaCredenciamentoService';

class BoardAnaliseProcessoIndexStore extends IndexBase {
  @observable
  refresh;

  @observable tdaProcesso;
  @observable statusArquivamento;
  @observable limiarValorRisco;

  constructor() {
    super(AnaliseProcessoViewService, AnaliseProcessoView);
    this.selecioneAnaliseService = SelecioneAnaliseAuditoriaService;
    makeObservable(this);
  }

  @action
  setRefresh(refresh) {
    this.refresh = refresh;
  }

  @override
  load() {
    this.refresh && this.refresh();
  }

  @action
  initializeLimiarRisco() {
    this.loading = true;
    ConfiguracoesService.get()
      .then((response) => {
        runInAction(() => {
          if (response.data) {
            const data = response.data;
            this.limiarValorRisco = data.risco.limiarValorRisco;
          }
        });
      })
      .catch((error) => {
        runInAction(() => {
          showErrorNotification(error);
        });
      })
      .finally(() => {
        runInAction(() => {
          this.loading = false;
        });
      });
  }

  getProcessoByIdAndTipo(idProcesso, tipoProcesso, callback) {
    if (idProcesso && tipoProcesso) {
      this.service
        .getProcesso(idProcesso, tipoProcesso)
        .then((response) => {
          return callback && callback(response.data);
        })
        .catch((e) => {
          showErrorNotification(e);
        });
    }
  }

  getAdvancedSearchParams() {
    let filtros = [];
    if (checkUserGroup('Auditor')) {
      filtros.push({
        field: 'entidade',
        label: 'Órgão/Entidade',
        type: SearchTypes.ASYNC_QUERY,
        store: new AsyncDropDownStore(Entidade, EntidadeService, 'nome', 'id'),
      });
    }
    filtros = [
      ...filtros,
      {
        field: 'analista',
        label: 'Auditor Atribuidor',
        type: SearchTypes.ASYNC_QUERY,
        store: new AsyncDropDownStore(Usuario, UsuarioService, 'nome', 'id'),
      },
      {
        field: 'responsavel',
        label: 'Auditor Responsável',
        type: SearchTypes.ASYNC_QUERY,
        store: new AsyncDropDownStore(Usuario, UsuarioService, 'nome', 'id'),
      },
      {
        field: 'numero',
        label: 'Número',
        type: SearchTypes.TEXT,
      },
      {
        field: 'dataCadastro',
        label: 'Data de Cadastro',
        type: SearchTypes.DATE,
      },
      {
        field: 'dataProcesso',
        label: 'Data de Abertura',
        type: SearchTypes.DATE,
      },
      {
        field: 'tipoProcesso',
        label: 'Tipo do Processo',
        type: SearchTypes.ENUM,
        options: DadosEstaticosService.getTipoProcesso(),
      },
      {
        field: 'objeto',
        label: 'Objeto',
        type: SearchTypes.TEXT,
      },
      {
        field: 'valor',
        label: 'Valor',
        type: SearchTypes.NUMBER,
      },
      {
        field: 'ano',
        label: 'Ano',
        type: SearchTypes.UNGROUPED_NUMBER,
      },
    ];

    return filtros;
  }

  @action
  getTdaProcesso(idProcesso, tipoProcesso, callback) {
    if (idProcesso) {
      this.loading = true;
      let getTda = '';
      if (tipoProcesso === 'L') {
        getTda = TdaLicitacaoService.tdaLicitacaoByIdLicitacao(idProcesso);
      } else if (tipoProcesso === 'C') {
        getTda = TdaCaronaService.tdaCaronaByIdCarona(idProcesso);
      } else if (tipoProcesso === 'I') {
        getTda = TdaInexigibilidadeService.tdaInexigibilidadeByIdInexigibilidade(idProcesso);
      } else if (tipoProcesso === 'D') {
        getTda = TdaDispensaService.tdaDispensaByIdDispensa(idProcesso);
      } else if (tipoProcesso === 'CR') {
        getTda = TdaCredenciamentoService.tdaCredenciamentoByIdCredenciamento(idProcesso);
      }
      if (getTda) {
        getTda
          .then((response) => this.setTdaProcesso(response.data))
          .catch((e) =>
            runInAction(() => {
              showErrorNotification(e);
            })
          )
          .finally(() =>
            runInAction(() => {
              this.loading = false;
              callback && callback();
            })
          );
      }
    }
  }

  @action
  setTdaProcesso(tdaProcesso) {
    this.tdaProcesso = tdaProcesso;
  }

  @action
  updateStatusArquivamento(value) {
    this.statusArquivamento = value;
  }

  getFilterSuggest() {
    const entidade = AppStore.getContextEntity();
    const filterSuggest = [];
    if (entidade) {
      filterSuggest.push({
        id: '',
        field: 'entidade',
        operator: 'EQUAL_TO',
        value: entidade,
        formatted: '',
        fixed: true,
        invisible: true,
        completeParam: {
          field: 'entidade',
          label: 'Órgão/Entidade',
          type: SearchTypes.ASYNC_QUERY,
          store: new AsyncDropDownStore(Entidade, EntidadeService, 'nome', 'id'),
        },
      });
    }
    if (!checkUserContextIsInspetor() && checkUserGroup('Auditoria')) {
      filterSuggest.push({
        id: '',
        field: 'responsavel',
        operator: 'EQUAL_TO',
        value: AppStore.getData('userDetails'),
        formatted: '',
        fixed: false,
        completeParam: {
          field: 'responsavel',
          label: 'Auditor Responsável',
          type: SearchTypes.ASYNC_QUERY,
          store: new AsyncDropDownStore(Usuario, UsuarioService, 'nome', 'id'),
        },
      });
    }

    return filterSuggest;
  }

  @action
  moverParaAnalise(id, tipoProcesso, emAnalise, callback) {
    this.selecioneAnaliseService
      .setEmAnalise(id, tipoProcesso, emAnalise)
      .then(() =>
        runInAction(() => {
          showNotification(
            'success',
            null,
            emAnalise ? 'Processo movido para análise com sucesso!' : 'Processo removido de análise com sucesso!'
          );
          callback && callback();
          this.reloadTableData();
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      );
  }

  @action
  desarquivarProcesso(id, tipoProcesso, callback) {
    this.selecioneAnaliseService
      .desarquivarProcesso(id, tipoProcesso)
      .then(() =>
        runInAction(() => {
          showNotification('success', null, 'Processo desarquivado com sucesso!');
          callback && callback();
          this.reloadTableData();
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      );
  }

  getServiceTda(tipoProcesso) {
    let service = null;
    if (tipoProcesso === 'L') service = TdaLicitacaoService;
    else if (tipoProcesso === 'C') service = TdaCaronaService;
    else if (tipoProcesso === 'D') service = TdaDispensaService;
    else if (tipoProcesso === 'I') service = TdaInexigibilidadeService;
    else if (tipoProcesso === 'CR') service = TdaCredenciamentoService;
    return service;
  }

  @action
  arquivarAlertaAnalise(idAlertaAnalise, idProcesso, tipoProcesso, callback) {
    let promise;
    if (idAlertaAnalise) {
      promise = AlertaAnaliseService.arquivarAlerta(idAlertaAnalise, this.statusArquivamento);
    } else {
      promise = this.getServiceTda(tipoProcesso).arquivarProcesso(idProcesso, this.statusArquivamento);
    }

    promise
      .then(() => {
        runInAction(() => {
          this.updateStatusArquivamento(undefined);
          showNotification('success', null, 'Processo arquivado com sucesso');
          callback && callback();
        });
      })
      .catch((error) => {
        runInAction(() => {
          showErrorNotification(error);
        });
      });
  }
}

export default BoardAnaliseProcessoIndexStore;
