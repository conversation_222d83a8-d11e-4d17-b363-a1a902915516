import { action, makeObservable, observable, runInAction } from 'mobx';
import { showErrorNotification } from 'fc/utils/utils';
import SearchTypes from 'fc/components/AdvancedSearch/SearchTypes';

class LazyScrollerStore {
  service;
  phaseKey;

  @observable data = [];
  @observable loading = false;
  @observable page = 1;
  @observable orderBy = 'desc';
  @observable column = 'updatedAtAudit';
  @observable blockRequests = false;
  @observable advancedSearchParams = {};

  constructor(service, phaseKey, advancedSearchParams) {
    this.service = service;
    this.phaseKey = phaseKey;
    this.advancedSearchParams = advancedSearchParams;

    makeObservable(this);
  }

  @action
  resetStates(advancedSearchParams, sort = false) {
    if (!sort) {
      this.orderBy = 'desc';
      this.column = 'updatedAtAudit';
    }
    this.data = [];
    this.page = 1;
    this.blockRequests = false;
    this.advancedSearchParams = advancedSearchParams;
  }

  @action
  loadContent(options = {}) {
    const parameters = Object.assign(this.advancedSearchParams, options);

    if (options && options.page && options.page.index && options.page.size) {
      parameters.page = { index: options.page.index, size: options.page.size };
    } else {
      parameters.page = { index: this.page, size: 10 };
    }

    if (options.andParameters && options.andParameters.length > 0) {
      parameters.andParameters = options.andParameters;
    }

    if (options.orParameters && options.orParameters.length > 0) {
      parameters.orParameters = options.orParameters;
    }
    if (options && options['sort'] && options['sort'].by && options['sort'].order) {
      parameters.sort = { by: options['sort'].by, order: options['sort'].order };
    } else {
      parameters.sort = { by: this.column, order: this.orderBy };
    }

    let customParameters = JSON.parse(JSON.stringify(parameters));
    let operator;
    if (this.phaseKey === 'ALERTA' || this.phaseKey === 'EM_ANALISE') {
      operator = 'CONTAINS';
    } else {
      operator = 'EQUAL_TO';
    }

    customParameters.andParameters.push({
      id: '',
      field: 'labels',
      operator,
      value: this.phaseKey,
      formatted: '',
      completeParam: {
        field: 'labels',
        label: 'Fase',
        type: SearchTypes.TEXT,
      },
    });

    this.loading = true;
    return this.service
      .advancedSearch(customParameters)
      .then((response) =>
        runInAction(() => {
          if (response?.data?.items?.length === 0) {
            this.blockRequests = true;
          } else {
            this.data.push(...(response?.data?.items || []));
            this.page += 1;
          }
          this.loading = false;
        })
      )
      .catch((error) => showErrorNotification(error))
      .finally(() =>
        runInAction(() => {
          this.loading = false;
        })
      );
  }
}

export default LazyScrollerStore;
