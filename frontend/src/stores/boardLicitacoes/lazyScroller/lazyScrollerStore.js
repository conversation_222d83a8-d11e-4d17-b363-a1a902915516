import { action, makeObservable, observable, runInAction } from 'mobx';
import { showErrorNotification } from 'fc/utils/utils';
import SearchTypes from 'fc/components/AdvancedSearch/SearchTypes';

class LazyScrollerStore {
  service;
  phaseKey;
  dateField;
  faseFinalizacao;

  @observable data = [];
  @observable loading = false;
  @observable page = 1;
  @observable orderBy = 'desc';
  @observable column = 'updatedAt';
  @observable blockRequests = false;
  @observable advancedSearchParams = {};

  constructor(service, phaseKey, advancedSearchParams) {
    this.service = service;
    this.phaseKey = phaseKey.value;
    this.dateField = phaseKey.dateField;
    this.advancedSearchParams = advancedSearchParams;
    this.faseFinalizacao = phaseKey.value == 'FINALIZACAO';

    makeObservable(this);
  }

  @action
  resetStates(advancedSearchParams) {
    this.data = [];
    this.page = 1;
    this.blockRequests = false;
    this.advancedSearchParams = advancedSearchParams;
  }

  @action
  loadContent(options = {}, motivoOcorrencia) {
    const parameters = Object.assign(this.advancedSearchParams, options);

    if (options && options.page && options.page.index && options.page.size) {
      parameters.page = { index: options.page.index, size: options.page.size };
    } else {
      parameters.page = { index: this.page, size: 10 };
    }

    if (options.andParameters && options.andParameters.length > 0) {
      parameters.andParameters = options.andParameters;
    }

    if (options.orParameters && options.orParameters.length > 0) {
      parameters.orParameters = options.orParameters;
    }
    if (options && options['sort'] && options['sort'].by && options['sort'].order) {
      parameters.sort = { by: options['sort'].by, order: options['sort'].order };
    } else {
      parameters.sort = { by: this.column, order: this.orderBy };
    }

    let customParameters = JSON.parse(JSON.stringify(parameters));

    if (this.faseFinalizacao) {
      customParameters.andParameters.push({
        field: 'ultimaOcorrencia',
        operator: 'EQUAL_TO',
        value: 'FINALIZAR',
        formatted: '',
        completeParam: {
          field: 'ultimaOcorrencia',
          label: 'Última Ocorrência',
          type: SearchTypes.TEXT,
        },
      });
      if (motivoOcorrencia) {
        customParameters.andParameters.push({
          field: 'statusOcorrenciaAtual',
          operator: 'EQUAL_TO',
          value: motivoOcorrencia,
          formatted: '',
          completeParam: {
            field: 'statusOcorrenciaAtual',
            label: 'Status Ocorrência Atual',
            type: SearchTypes.TEXT,
          },
        });
      }
    } else {
      customParameters.andParameters.push({
        id: '',
        field: 'fase',
        operator: 'EQUAL_TO',
        value: this.phaseKey,
        formatted: '',
        completeParam: {
          field: 'fase',
          label: 'Fase',
          type: SearchTypes.TEXT,
        },
      });
      customParameters.andParameters.push({
        field: 'ultimaOcorrencia',
        operator: 'NOT_EQUAL_TO',
        value: 'FINALIZAR',
        formatted: '',
        completeParam: {
          field: 'ultimaOcorrencia',
          label: 'Última Ocorrência',
          type: SearchTypes.TEXT,
        },
      });
    }

    this.loading = true;
    return this.service
      .advancedSearch(customParameters)
      .then((response) =>
        runInAction(() => {
          if (response?.data?.items?.length === 0) {
            this.blockRequests = true;
          } else {
            this.data.push(...(response?.data?.items || []));
            this.page += 1;
          }
          this.loading = false;
        })
      )
      .catch((error) =>
        runInAction(() => {
          this.loading = false;
          showErrorNotification(error);
        })
      );
  }
}

export default LazyScrollerStore;
