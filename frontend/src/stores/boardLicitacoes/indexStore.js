import SearchTypes from 'fc/components/AdvancedSearch/SearchTypes';
import Entidade from '~/domains/Entidade';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import EntidadeService from '~/services/EntidadeService';
import LicitacaoService from '~/services/LicitacaoService';
import BoardLicitacaoViewService from '~/services/BoardLicitacaoViewService';
import BoardLicitacaoView from '~/domains/BoardLicitacaoView';
import AsyncDropDownStore from 'fc/stores/AsyncDropdownStore';
import IndexBase from 'fc/stores/IndexBase';
import { runInAction, override, observable, action } from 'mobx';
import { checkUserGroup, showErrorNotification, showNotification } from 'fc/utils/utils';
import AppStore from 'fc/stores/AppStore';
import SearchOperators from 'fc/components/AdvancedSearch/SearchOperators';
import EdificacaoService from '~/services/EdificacaoService';
import MultipleFileUploaderStore from 'fc/stores/MultipleFileUploaderStore';

class BoardLicitacoesIndexStore extends IndexBase {
  tagList = {
    PREPARATORIA: { label: 'Preparatória', backgroundColor: '#3f51b5', fontColor: '#ffffff' },
    DIVULGACAO_PUBLICACAO_LICITACAO: { label: 'Divulgação', backgroundColor: '#fbc02d', fontColor: '#000000' },
    APRESENTACAO_PROPOSTAS_LANCES: { label: 'Apresentação', backgroundColor: '#9c27b0', fontColor: '#ffffff' },
    HABILITACAO: { label: 'Habilitação', backgroundColor: '#4b0082', fontColor: '#ffffff' },
    HOMOLOGACAO: { label: 'Homologação', backgroundColor: '#008b8b', fontColor: '#ffffff' },
    SUSPENDER: { label: 'Suspensa', backgroundColor: '#f97316', fontColor: '#ffffff' },
    PRORROGAR: { label: 'Prorrogada', backgroundColor: '#617C4D', fontColor: '#ffffff' },
    REABRIR: { label: 'Reaberta', backgroundColor: '#4682B4', fontColor: '#ffffff' },
    FINALIZAR: { label: 'Finalizada', backgroundColor: '#022B3A', fontColor: '#ffffff' },
  };
  @observable fileStore;
  @observable arquivoLicitacaoList = [];
  @observable idLicitacao;
  @observable ultimaAlteracao;
  refresh;
  @observable
  licitacoes = {};

  constructor() {
    super(BoardLicitacaoViewService, BoardLicitacaoView, 'dataCadastro', 'desc');
    this.initializeLicitacoesList();
    this.licitacaoService = LicitacaoService;
    this.carregaUltimaAlteracao = this.carregaUltimaAlteracao.bind(this);
    this.fileStore = new MultipleFileUploaderStore(
      DadosEstaticosService.getTipoArquivoLicitacao(),
      (file) => LicitacaoService.upload(file),
      (fileDTO, countDownload) => LicitacaoService.download(fileDTO, countDownload),
      (idArquivo) => this.removerArquivoLicitacao(idArquivo),
      (idArquivo, arquivoLicitacaoDTO) => this.atualizarArquivoLicitacao(idArquivo, arquivoLicitacaoDTO)
    );
  }

  getFilterSuggest(idLicitacao) {
    const filterSuggest = [
      {
        id: '',
        field: 'status',
        operator: 'NOT_EQUAL_TO',
        value: 'REMOVIDA',
        formatted: '',
        fixed: true,
        invisible: true,
        completeParam: {
          field: 'status',
          label: 'Status',
          type: SearchTypes.ENUM,
          options: DadosEstaticosService.getStatusContrato(),
        },
      },
    ];
    if (idLicitacao) {
      filterSuggest.pop();
      filterSuggest.push({
        id: '',
        field: 'id',
        operator: 'EQUAL_TO',
        value: idLicitacao,
        formatted: '',
        completeParam: {
          field: 'id',
          label: 'Identificador',
          type: SearchTypes.TEXT,
        },
      });
    }
    const entidade = AppStore.getContextEntity();
    if (entidade) {
      filterSuggest.push({
        id: '',
        field: 'entidade',
        operator: 'EQUAL_TO',
        value: entidade,
        formatted: '',
        fixed: true,
        invisible: true,
        completeParam: {
          field: 'entidade',
          label: 'Órgão/Entidade',
          type: SearchTypes.ASYNC_QUERY,
          store: new AsyncDropDownStore(Entidade, EntidadeService, 'nome', 'id'),
        },
      });
    }

    return filterSuggest;
  }

  @action
  setRefresh(refresh) {
    this.refresh = refresh;
  }

  initializeLicitacoesList() {
    DadosEstaticosService.getFasesLicitacao().map((fase) => {
      this.licitacoes[fase.value] = [];
    });
  }

  @action
  initialize(idLicitacao) {
    this.idLicitacao = idLicitacao;
    if (idLicitacao) {
      this.licitacaoService
        .recuperarArquivos(idLicitacao)
        .then((response) =>
          runInAction(() => {
            if (response && response.data) {
              const arquivosRecuperados = response.data;
              this.fileStore.initialize(arquivosRecuperados);
              this.arquivoLicitacaoList = arquivosRecuperados;
            }
          })
        )
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        );
    }
  }

  @override
  load(_, callback) {
    this.refresh && this.refresh();
    callback && callback();
  }

  getLicitacaoById(idLicitacao, callback) {
    if (idLicitacao) {
      this.licitacaoService
        .getById(idLicitacao)
        .then((response) => {
          return callback && callback(response.data);
        })
        .catch((e) => {
          showErrorNotification(e);
        });
    }
  }

  @action
  carregaUltimaAlteracao(id) {
    if (id) {
      this.licitacaoService
        .getUltimaAlteracao(id)
        .then(({ data }) =>
          runInAction(() => {
            if (data) this.ultimaAlteracao = data;
          })
        )
        .catch((error) => showErrorNotification(error));
    }
  }

  getAdvancedSearchParams() {
    const isAuditor = checkUserGroup(['Auditor', 'Auditor - Demais Inspetorias']);
    let searchParams = [];

    if (isAuditor) {
      searchParams = [
        {
          field: 'entidade',
          label: 'Órgão/Entidade',
          type: SearchTypes.ASYNC_QUERY,
          store: new AsyncDropDownStore(Entidade, EntidadeService, 'nome', 'id'),
        },
      ];
    }
    searchParams.push(
      {
        field: 'dataCadastro',
        label: 'Data de Cadastro',
        type: SearchTypes.DATE,
      },
      {
        field: 'dataAbertura',
        label: 'Data de Abertura',
        type: SearchTypes.DATE,
      },
      {
        field: 'numero',
        label: 'Número',
        type: SearchTypes.TEXT,
      },
      {
        field: 'ano',
        label: 'Ano',
        type: SearchTypes.UNGROUPED_NUMBER,
      },
      {
        field: 'objeto',
        label: 'Objeto',
        type: SearchTypes.TEXT,
      },
      {
        field: 'valorEstimado',
        label: 'Valor Estimado',
        type: SearchTypes.NUMBER,
      }
    );
    return searchParams;
  }

  @action
  carregarEdificacaoObra(licitacao, callback) {
    this.loading = true;
    const filtro = {
      page: { index: 1, size: 100 },
      sort: {},
      andParameters: [{ field: 'obra', operator: SearchOperators.EQUAL_TO.value, value: licitacao.obra.id }],
    };
    EdificacaoService.advancedSearch(filtro)
      .then((response) =>
        runInAction(() => {
          if (response.data && response.data.items && response.data.items.length === 1) {
            const edificacao = response.data.items[0];
            licitacao.obra.edificacao = edificacao;
          }
          callback && callback(licitacao);
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      )
      .finally(() =>
        runInAction(() => {
          this.loading = false;
        })
      );
  }

  @override
  deleteRow(id, callback) {
    this.loading = true;
    LicitacaoService.removerLicitacao(id)
      .then(() => {
        callback && callback();
        showNotification('success', null, 'Licitação removida com sucesso!');
      })
      .catch((error) => showErrorNotification(error))
      .finally(() =>
        runInAction(() => {
          this.loading = false;
        })
      );
  }
}

export default BoardLicitacoesIndexStore;
