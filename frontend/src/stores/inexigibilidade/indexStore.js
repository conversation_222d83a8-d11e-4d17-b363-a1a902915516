import SearchTypes from 'fc/components/AdvancedSearch/SearchTypes';
import Inexigibilidade from '~/domains/Inexigibilidade';
import InexigibilidadeService from '~/services/InexigibilidadeService';
import IndexBase from 'fc/stores/IndexBase';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import AsyncDropDownStore from 'fc/stores/AsyncDropdownStore';
import AsyncMultiselectStore from 'fc/stores/AsyncMultiselectStore';
import FonteRecurso from '~/domains/FonteRecurso';
import FonteRecursoService from '~/services/FonteRecursoService';
import Entidade from '~/domains/Entidade';
import EntidadeService from '~/services/EntidadeService';
import Licitante from '~/domains/Licitante';
import LicitanteService from '~/services/LicitanteService';
import AppStore from 'fc/stores/AppStore';
import FundamentacaoLegal from '~/domains/FundamentacaoLegal';
import FundamentacaoLegalService from '~/services/FundamentacaoLegalService';
import moment from 'moment';
import { runInAction } from 'mobx';
import { checkUserGroup, showErrorNotification } from 'fc/utils/utils';

class InexigibilidadeIndexStore extends IndexBase {
  constructor() {
    super(InexigibilidadeService, Inexigibilidade, 'updatedAt', 'desc');
  }

  getFilterSuggest() {
    const entidade = AppStore.getContextEntity();
    const filterSuggest = [
      {
        id: '',
        field: 'status',
        operator: 'NOT_EQUAL_TO',
        value: 'REMOVIDA',
        formatted: '',
        fixed: true,
        invisible: true,
        completeParam: {
          field: 'status',
          label: 'Status',
          type: SearchTypes.ENUM,
          options: DadosEstaticosService.getStatusContrato(),
        },
      },
    ];

    if (entidade) {
      filterSuggest.push({
        id: '',
        field: 'entidade',
        operator: 'EQUAL_TO',
        value: entidade,
        formatted: '',
        fixed: true,
        invisible: true,
        completeParam: {
          field: 'entidade',
          label: 'Órgão/Entidade',
          type: SearchTypes.ASYNC_QUERY,
          store: new AsyncDropDownStore(Entidade, EntidadeService, 'nome', 'id'),
        },
      });
    }

    return filterSuggest;
  }

  getAdvancedSearchParams() {
    const isAuditor = checkUserGroup(['Auditor', 'Auditor - Demais Inspetorias']);
    let searchParams = [];

    if (isAuditor) {
      searchParams = [
        {
          field: 'entidade',
          label: 'Órgão/Entidade',
          type: SearchTypes.ASYNC_QUERY,
          store: new AsyncDropDownStore(Entidade, EntidadeService, 'nome', 'id'),
        },
      ];
    }
    searchParams.push(
      {
        field: 'dataPedido',
        label: 'Data da Autorização',
        type: SearchTypes.DATE,
      },
      {
        field: 'numeroProcesso',
        label: 'Número da Inexigibilidade',
        type: SearchTypes.TEXT,
      },
      {
        field: 'numeroSei',
        label: 'Número do Processo Administrativo',
        type: SearchTypes.TEXT,
      },
      {
        field: 'objeto',
        label: 'Objeto',
        type: SearchTypes.TEXT,
      },
      {
        field: 'fontesDeRecurso',
        label: 'Fontes de Recurso',
        type: SearchTypes.ASYNC_QUERY,
        store: new AsyncMultiselectStore(FonteRecurso, FonteRecursoService, 'nome', 'id'),
      },
      {
        field: 'fundamentacao',
        label: 'Fundamentação Legal',
        type: SearchTypes.ASYNC_QUERY,
        store: new AsyncMultiselectStore(FundamentacaoLegal, FundamentacaoLegalService, 'fundamentacao', 'id'),
      },
      {
        field: 'status',
        label: 'Status do Processo',
        type: SearchTypes.ENUM,
        options: DadosEstaticosService.getStatusLicitacao(),
      },
      {
        field: 'observacoes',
        label: 'Observações',
        type: SearchTypes.TEXT,
      },
      {
        field: 'licitantes',
        label: 'Fornecedor',
        type: SearchTypes.ASYNC_QUERY,
        store: new AsyncMultiselectStore(Licitante, LicitanteService, 'nome', 'id'),
      },
      {
        field: 'valor',
        label: 'Valor',
        type: SearchTypes.NUMBER,
      },
      {
        field: 'houveModificacao',
        label: 'Requisição de Modificação Pendente',
        type: SearchTypes.ENUM,
        options: DadosEstaticosService.getSimNao(),
      },
      {
        field: 'dataCadastro',
        label: 'Data de Cadastro',
        type: SearchTypes.DATE,
      }
    );
    return searchParams;
  }

  isRequisicaoModificacao(dataCadastro) {
    let result = false;
    if (dataCadastro) {
      const data = moment(dataCadastro);
      const current = moment();
      const diff = moment.duration(current.diff(data));

      if (diff.asHours() > 24) {
        result = true;
      }
    }
    return result;
  }

  getById(id, callback) {
    if (id) {
      this.service
        .getById(id)
        .then((response) => {
          return callback && runInAction(() => callback(response.data));
        })
        .catch((e) => {
          showErrorNotification(e);
        });
    }
  }
}

export default InexigibilidadeIndexStore;
