import SearchTypes from 'fc/components/AdvancedSearch/SearchTypes';
import EntidadeInternaExterna from '~/domains/EntidadeInternaExterna';
import EntidadeInternaExternaService from '~/services/EntidadeInternaExternaService';
import IndexBase from 'fc/stores/IndexBase';
import { override, runInAction } from 'mobx';
import { showErrorNotification } from 'fc/utils/utils';

class EntidadeInternaExternaIndexStore extends IndexBase {
  constructor() {
    super(EntidadeInternaExternaService, EntidadeInternaExterna, 'nome');
  }

  @override
  load(options = {}, callback, increment = false) {
    this.setLoading(true, increment);

    const parameters = Object.assign(this.advancedSearchParams, options);

    if (options && options.page && options.page.index && options.page.size) {
      parameters.page = { index: options.page.index, size: options.page.size };
    } else {
      parameters.page = { index: 1, size: 10 };
    }

    if (options.andParameters && options.andParameters.length > 0) {
      parameters.andParameters = options.andParameters;
    }

    if (options.orParameters && options.orParameters.length > 0) {
      parameters.orParameters = options.orParameters;
    }

    if (options && options['sort'] && options['sort'].by && options['sort'].order) {
      parameters.sort = { by: options['sort'].by, order: options['sort'].order };
    } else {
      parameters.sort = { by: this.defaultSortBy, order: this.defaultSortOrder };
    }

    this.pagination = parameters;
    this.service
      .advancedSearch(parameters)
      .then((response) =>
        runInAction(() => {
          if (increment) {
            this.pagination.total += response.data.total;
            this.list = this.initializeLoadedList([...this.list, ...response.data.items]);
          } else {
            this.pagination.total = response.data.total;
            this.list = this.initializeLoadedList(response.data.items ?? []);
          }
          callback && callback(response);
        })
      )
      .catch((error) => showErrorNotification(error))
      .finally(() =>
        runInAction(() => {
          this.setLoading(false, increment);
        })
      );
  }

  getAdvancedSearchParams() {
    return [
      {
        field: 'nome',
        label: 'Nome',
        type: SearchTypes.TEXT,
      },
    ];
  }
}

export default EntidadeInternaExternaIndexStore;
