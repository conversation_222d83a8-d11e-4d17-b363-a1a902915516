import IndexBase from 'fc/stores/IndexBase';
import SearchTypes from 'fc/components/AdvancedSearch/SearchTypes';
import AsyncDropDownStore from 'fc/stores/AsyncDropdownStore';
import { checkUserGroup, showErrorNotification, showNotification } from 'fc/utils/utils';
import AlertaAnaliseEntidadeViewService from '~/services/AlertaAnaliseEntidadeViewService';
import AlertaAnaliseEntidadeView from '~/domains/AlertaAnaliseEntidadeView';
import Entidade from '~/domains/Entidade';
import EntidadeService from '~/services/EntidadeService';
import UsuarioAuditorView from '~/domains/UsuarioAuditorView';
import UsuarioAuditorServiceView from '~/services/UsuarioAuditorViewService';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import { action, observable, runInAction } from 'mobx';
import AlertaAnaliseService from '~/services/AlertaAnaliseService';
import AppStore from 'fc/stores/AppStore';

class AlertaHistoricoIndexStore extends IndexBase {
  @observable abaVisualizacao = 0;
  @observable statusArquivamento;

  constructor() {
    super(AlertaAnaliseEntidadeViewService, AlertaAnaliseEntidadeView, 'data', 'desc');

    this.changeAbaVisualizacao = this.changeAbaVisualizacao.bind(this);
  }

  getAdvancedSearchParams() {
    let filters = [
      {
        field: 'data',
        label: 'Data Emissão',
        type: SearchTypes.DATE,
      },
      {
        field: 'tipo',
        label: 'Tipo do Processo',
        type: SearchTypes.ENUM,
        options: DadosEstaticosService.getTipoProcesso(),
      },
      {
        field: 'usuarioResponsavel',
        label: 'Auditor Responsável',
        type: SearchTypes.ASYNC_QUERY,
        store: new AsyncDropDownStore(UsuarioAuditorView, UsuarioAuditorServiceView, 'nome', 'id'),
      },
    ];

    if (checkUserGroup(['Auditor', 'Auditor - Demais Inspetorias'])) {
      filters.push({
        field: 'entidade',
        label: 'Órgão/Entidade',
        type: SearchTypes.ASYNC_QUERY,
        store: new AsyncDropDownStore(Entidade, EntidadeService, 'nome', 'id'),
      });
    }

    return filters;
  }

  @action
  changeAbaVisualizacao(aba) {
    this.abaVisualizacao = aba;
  }

  getFilterSuggest() {
    const entidade = AppStore.getContextEntity();
    const filterSuggest = [];
    if (entidade && checkUserGroup('Jurisdicionado')) {
      filterSuggest.push({
        id: '',
        field: 'entidade',
        operator: 'EQUAL_TO',
        value: entidade,
        formatted: '',
        fixed: true,
        invisible: true,
        completeParam: {
          field: 'entidade',
          label: 'Órgão/Entidade',
          type: SearchTypes.ASYNC_QUERY,
          store: new AsyncDropDownStore(Entidade, EntidadeService, 'nome', 'id'),
        },
      });
    }

    if (checkUserGroup(['Auditor', 'Administrador'])) {
      filterSuggest.push({
        id: '',
        field: 'status',
        operator: this.abaVisualizacao === 0 ? 'IN' : 'EQUAL_TO',
        value:
          this.abaVisualizacao === 0
            ? ['RESPONDIDO', 'REJEITADO_INSPETOR', 'ESCLARECIDO', 'JURISDICIONADO']
            : 'ARQUIVADO',
        formatted: '',
        fixed: true,
        invisible: true,
        completeParam: {
          field: 'status',
          label: 'Status',
          type: SearchTypes.ENUM,
          options: DadosEstaticosService.getStatusAlertaAnalise(),
        },
      });
    } else if (checkUserGroup('Jurisdicionado')) {
      filterSuggest.push({
        id: '',
        field: 'status',
        operator: this.abaVisualizacao === 0 ? 'IN' : 'EQUAL_TO',
        value: this.abaVisualizacao === 0 ? ['ESCLARECIDO', 'JURISDICIONADO', 'RESPONDIDO'] : 'ARQUIVADO',
        formatted: '',
        fixed: true,
        invisible: true,
        completeParam: {
          field: 'status',
          label: 'Status',
          type: SearchTypes.ENUM,
          options: DadosEstaticosService.getStatusAlertaAnalise(),
        },
      });
    }
    return filterSuggest;
  }

  @action
  updateStatusArquivamento(value) {
    this.statusArquivamento = value;
  }

  @action
  arquivarAlertaAnalise(idAlertaAnalise, callback) {
    if (!this.statusArquivamento) {
      showNotification('error', null, 'Por favor, selecione um status');
      return;
    }
    AlertaAnaliseService.arquivarAlerta(idAlertaAnalise, this.statusArquivamento)
      .then(() => {
        runInAction(() => {
          showNotification('success', null, 'Alerta arquivado com sucesso!');
          callback && callback();
        });
      })
      .catch((error) => {
        runInAction(() => {
          showErrorNotification(error);
        });
      });
  }
}

export default AlertaHistoricoIndexStore;
