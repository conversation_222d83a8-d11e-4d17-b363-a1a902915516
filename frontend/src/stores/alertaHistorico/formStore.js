import FormBase from 'fc/stores/FormBase';
import AlertaMensagemService from '~/services/AlertaMensagemService';
import AlertaMensagem from '~/domains/AlertaMensagem';
import { action, computed, observable, override, runInAction } from 'mobx';
import { checkUserGroup, getValueDate, isValueValid, showErrorNotification, showNotification } from 'fc/utils/utils';
import moment from 'moment';
import { DATE_FORMAT, DATE_PARSE_FORMAT, DATE_PARSE_FORMAT_WITH_HOURS_AND_SECONDS } from 'fc/utils/date';
import MultipleFileUploaderStore from 'fc/stores/MultipleFileUploaderStore';
import AlertaAnaliseService from '~/services/AlertaAnaliseService';
import LicitacaoService from '~/services/LicitacaoService';
import DispensaService from '~/services/DispensaService';
import InexigibilidadeService from '~/services/InexigibilidadeService';
import CaronaService from '~/services/CaronaService';
import TdaLicitacaoService from '~/services/TdaLicitacaoService';
import TdaDispensaService from '~/services/TdaDispensaService';
import TdaInexigibilidadeService from '~/services/TdaInexigibilidadeService';
import TdaCaronaService from '~/services/TdaCaronaService';
import AppStore from 'fc/stores/AppStore';
import CredenciamentoService from '~/services/CredenciamentoService';
import TdaCredenciamentoService from '~/services/TdaCredenciamentoService';

class AlertaHistoricoFormStore extends FormBase {
  @observable mensagens = [];
  @observable alertaAnalise;
  @observable readFileStore;
  @observable readFileStoreTda;
  @observable comissao;
  @observable statusArquivamento;
  @observable mensagensAlerta;
  @observable modalidade;
  @observable entidade;
  @observable dataAbertura;
  @observable loadingProcess = false;
  @observable arquivosTda;

  constructor() {
    super(AlertaMensagemService, AlertaMensagem);
    this.readFileStore = new MultipleFileUploaderStore(
      [],
      (file) => AlertaMensagemService.upload(file),
      (fileDTO) => AlertaMensagemService.download(fileDTO),
      () => {},
      () => {}
    );
    this.writeFileStore = new MultipleFileUploaderStore(
      [],
      (file) => AlertaMensagemService.upload(file),
      (fileDTO) => AlertaMensagemService.download(fileDTO),
      () => {},
      () => {}
    );
  }

  rulesDefinition() {
    let rules = { mensagem: [{ rule: 'required', message: 'Por favor, preencha o campo' }] };
    return rules;
  }

  @action
  setReadFileStore(list, isTdaFile, callback) {
    isTdaFile ? this.readFileStoreTda.initialize(list) : this.readFileStore.initialize(list);
    callback && callback();
  }

  @action
  setFileList(list) {
    this.arquivos = list;
  }

  @action
  updateStatusArquivamento(value) {
    this.statusArquivamento = value;
  }

  @action
  arquivarAlertaAnalise(idAlertaAnalise, callback) {
    if (!this.statusArquivamento) {
      showNotification('error', null, 'Por favor, selecione um status');
      return;
    }
    AlertaAnaliseService.arquivarAlerta(idAlertaAnalise, this.statusArquivamento)
      .then(() => {
        runInAction(() => {
          showNotification('success', null, 'Alerta arquivado com sucesso!');
          callback && callback();
        });
      })
      .catch((error) => {
        runInAction(() => {
          showErrorNotification(error);
        });
      });
  }

  @override
  initialize(id, callback) {
    const promises = [];
    promises.push(AlertaAnaliseService.getById(id));
    promises.push(this.service.listarMensagens(id));

    this.loading = true;
    Promise.all(promises)
      .then((responses) =>
        runInAction(() => {
          this.object = {};
          this.alertaAnalise = responses[0].data;
          this.mensagensAlerta = responses[1].data;
          this._getProcess(callback);
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      )
      .finally(() =>
        runInAction(() => {
          this.loading = false;
        })
      );
  }

  @action
  _handleMessages() {
    if (this.mensagemRejeitada) {
      this.mensagemRejeicao = this.mensagensAlerta[this.mensagensAlerta.length - 1].alertaMensagem.mensagemInspetor;
      this.object.mensagem = this.mensagensAlerta[this.mensagensAlerta.length - 1].alertaMensagem.mensagemAuditor;

      this.mensagens = this.mensagensAlerta
        .filter((_, idx) => idx !== this.mensagensAlerta.length - 1)
        .map((mensagem, idx) => {
          let temp = [];

          const baseMessage = {
            mensagem: mensagem.alertaMensagem.mensagemAuditor,
            data: mensagem.alertaMensagem.dataMensagem,
            usuario: mensagem.alertaMensagem.usuarioMensagem,
            prazoResposta: mensagem.alertaMensagem.prazoResposta,
          };

          if (idx === 0) {
            baseMessage.arquivos = this.arquivosTda;
            baseMessage.tdaFiles = true;
          }

          temp.push(baseMessage);

          mensagem.alertaMensagem.usuarioResposta &&
            temp.push({
              mensagem: mensagem.alertaMensagem.respostaJurisdicionado,
              data: mensagem.alertaMensagem.dataResposta,
              usuario: mensagem.alertaMensagem.usuarioResposta,
              arquivos: mensagem.arquivosAlertaMensagem,
              resposta: true,
            });

          return temp;
        })
        .flat();
    } else {
      this.mensagens = this.mensagensAlerta
        .map((mensagem, idx) => {
          let temp = [];

          const baseMessage = {
            mensagem: mensagem.alertaMensagem.mensagemAuditor,
            data: mensagem.alertaMensagem.dataMensagem,
            usuario: mensagem.alertaMensagem.usuarioMensagem,
            prazoResposta: mensagem.alertaMensagem.prazoResposta,
          };

          if (idx === 0) {
            baseMessage.arquivos = this.arquivosTda;
            baseMessage.tdaFiles = true;
          }

          temp.push(baseMessage);

          mensagem.alertaMensagem.usuarioResposta &&
            temp.push({
              mensagem: mensagem.alertaMensagem.respostaJurisdicionado,
              data: mensagem.alertaMensagem.dataResposta,
              usuario: mensagem.alertaMensagem.usuarioResposta,
              arquivos: mensagem.arquivosAlertaMensagem,
              resposta: true,
            });

          return temp;
        })
        .flat();
    }
  }

  @override
  save(callback) {
    this.loading = true;
    let mensagem = undefined;
    if (checkUserGroup('Administrador')) {
      if (!this.mensagens[this.mensagens.length - 1]?.resposta) {
        mensagem = this._generateObjectMessageByGroup('Jurisdicionado');
      } else {
        mensagem = this._generateObjectMessageByGroup('Auditor');
      }
    } else if (checkUserGroup('Jurisdicionado')) {
      mensagem = this._generateObjectMessageByGroup('Jurisdicionado');
    } else {
      mensagem = this._generateObjectMessageByGroup('Auditor');
    }
    AlertaMensagemService.cadastrarMensagem(mensagem)
      .then(() => {
        callback && callback();
        showNotification('success', null, 'Registro salvo com sucesso!');
      })
      .catch((error) => showErrorNotification(error))
      .finally(() =>
        runInAction(() => {
          this.loading = false;
        })
      );
  }

  _generateObjectMessageByGroup(group) {
    if (group === 'Auditor') {
      return {
        alertaMensagem: {
          prazoResposta: this.object.prazoResposta,
          mensagemAuditor: this.object.mensagem,
          dataMensagem: moment().format(DATE_PARSE_FORMAT_WITH_HOURS_AND_SECONDS),
          alertaAnalise: this.alertaAnalise,
        },
      };
    } else if (group === 'Jurisdicionado') {
      const lastMessage = this.alertaAnalise.mensagens[this.alertaAnalise.mensagens.length - 1];
      return {
        alertaMensagem: {
          ...lastMessage,
          respostaJurisdicionado: this.object.mensagem,
          dataResposta: moment().format(DATE_PARSE_FORMAT_WITH_HOURS_AND_SECONDS),
          alertaAnalise: this.alertaAnalise,
        },
        arquivosAlertaMensagem: this.arquivos,
      };
    }
  }

  @action
  updateMensagem(event) {
    let value = event;
    if (event) {
      if (isValueValid(event.target)) {
        value = event.target.value;
      } else if (isValueValid(event.value)) {
        value = event.value;
      } else if (typeof event.getData === 'function') {
        value = event.getData();
      } else {
        value = undefined;
      }
    }
    this.object.mensagem = value;
  }

  @computed
  get mensagemRejeitada() {
    return this.alertaAnalise?.status === 'REJEITADO_INSPETOR';
  }

  _hasAnswer() {
    if (this.alertaAnalise?.status === 'ARQUIVADO') {
      return false;
    } else if (checkUserGroup('Administrador')) {
      return true;
    } else if (checkUserGroup('Jurisdicionado')) {
      const possuiResposta = this.mensagens[this.mensagens.length - 1]?.resposta;
      const estaComJurisdicionado = this.alertaAnalise?.status === 'JURISDICIONADO';
      if (!possuiResposta && estaComJurisdicionado) {
        const idCurrentUser = AppStore.getData('userDetails').id;
        if (this.comissao) {
          const usersId = this.comissao?.map((user) => user.id);
          if (usersId.includes(Number(idCurrentUser))) {
            return true;
          } else {
            return false;
          }
        }
        return true;
      } else {
        return false;
      }
    } else {
      return (
        (this.mensagens.length == 0 ||
          this.mensagens[this.mensagens.length - 1]?.resposta ||
          this.alertaAnalise?.status === 'RESPONDIDO') &&
        this.getAuditorResponsavelId() == AppStore.getData('userDetails')?.id
      );
    }
  }

  getAuditorResponsavelId() {
    const tipoProcesso = this.alertaAnalise?.tipo;

    if (tipoProcesso === 'L') {
      return this.alertaAnalise?.tdaLicitacao?.responsavel?.id;
    } else if (tipoProcesso === 'D') {
      return this.alertaAnalise?.tdaDispensa?.responsavel?.id;
    } else if (tipoProcesso === 'I') {
      return this.alertaAnalise?.tdaInexigibilidade?.responsavel?.id;
    } else if (tipoProcesso === 'C') {
      return this.alertaAnalise?.tdaCarona?.responsavel?.id;
    } else if (tipoProcesso === 'CR') {
      return this.alertaAnalise?.tdaCredenciamento?.responsavel?.id;
    }
  }

  showFieldsJurisdicionado() {
    let result;
    if (checkUserGroup('Jurisdicionado')) {
      result = true;
    } else if (checkUserGroup('Administrador')) {
      if (!this.mensagens[this.mensagens.length - 1]?.resposta) {
        result = true;
      } else {
        result = false;
      }
    } else {
      result = false;
    }
    return result;
  }

  @action
  _getProcess(callback) {
    const promise = [];
    const tipoProcesso = this.alertaAnalise?.tipo;

    if (tipoProcesso === 'L') {
      const idLicitacao = this.alertaAnalise?.tdaLicitacao?.idLicitacao;
      idLicitacao &&
        promise.push(
          LicitacaoService.getById(idLicitacao),
          TdaLicitacaoService.recuperarArquivos(this.alertaAnalise?.tdaLicitacao?.id)
        );
    } else if (tipoProcesso === 'D') {
      const idDispensa = this.alertaAnalise?.tdaDispensa?.idDispensa;
      idDispensa &&
        promise.push(
          DispensaService.getById(idDispensa),
          TdaDispensaService.recuperarArquivos(this.alertaAnalise?.tdaDispensa?.id)
        );
    } else if (tipoProcesso === 'I') {
      const idInexigibilidade = this.alertaAnalise?.tdaInexigibilidade?.idInexigibilidade;
      idInexigibilidade &&
        promise.push(
          InexigibilidadeService.getById(idInexigibilidade),
          TdaInexigibilidadeService.recuperarArquivos(this.alertaAnalise?.tdaInexigibilidade?.id)
        );
    } else if (tipoProcesso === 'C') {
      const idCarona = this.alertaAnalise?.tdaCarona?.idCarona;
      idCarona &&
        promise.push(
          CaronaService.getById(idCarona),
          TdaCaronaService.recuperarArquivos(this.alertaAnalise?.tdaCarona?.id)
        );
    } else if (tipoProcesso === 'CR') {
      const idCredenciamento = this.alertaAnalise?.tdaCredenciamento?.idCredenciamento;
      idCredenciamento &&
        promise.push(
          CredenciamentoService.getById(idCredenciamento),
          TdaCredenciamentoService.recuperarArquivos(this.alertaAnalise?.tdaCredenciamento?.id)
        );
    }

    this.loadingProcess = true;
    Promise.all(promise)
      .then((responses) =>
        runInAction(() => {
          this._handleProcess(tipoProcesso, responses[0]?.data);
          this.arquivosTda = responses[1]?.data;
          this._handleMessages();
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
        })
      )
      .finally(() =>
        runInAction(() => {
          this.loadingProcess = false;
          callback && callback();
        })
      );
  }

  @action
  _handleProcess(tipoProcesso, data) {
    if (tipoProcesso === 'L') {
      if (data?.comissao?.membros?.length) {
        this.comissao = data.comissao.membros;
      }
      this.modalidade = `${data?.numero}/${data?.ano}`;
      this.entidade = data?.entidade.nome;
      this.dataAbertura = getValueDate(data?.dataAbertura, DATE_FORMAT, DATE_PARSE_FORMAT);

      this.readFileStoreTda = new MultipleFileUploaderStore(
        [],
        (file) => TdaLicitacaoService.upload(file),
        (fileDTO) => TdaLicitacaoService.download(fileDTO),
        () => {},
        () => {}
      );
    } else if (tipoProcesso === 'I') {
      this.modalidade = `${'Inexigibilidade '} - ${data?.numeroProcesso}`;
      this.entidade = data?.entidade.nome;
      this.dataAbertura = getValueDate(data?.dataPedido, DATE_FORMAT, DATE_PARSE_FORMAT);

      this.readFileStoreTda = new MultipleFileUploaderStore(
        [],
        (file) => TdaInexigibilidadeService.upload(file),
        (fileDTO) => TdaInexigibilidadeService.download(fileDTO),
        () => {},
        () => {}
      );
    } else if (tipoProcesso === 'D') {
      this.modalidade = `${'Dispensa '} - ${data?.numeroProcesso}`;
      this.entidade = data?.entidade.nome;
      this.dataAbertura = getValueDate(data?.dataPedido, DATE_FORMAT, DATE_PARSE_FORMAT);

      this.readFileStoreTda = new MultipleFileUploaderStore(
        [],
        (file) => TdaDispensaService.upload(file),
        (fileDTO) => TdaDispensaService.download(fileDTO),
        () => {},
        () => {}
      );
    } else if (tipoProcesso === 'C') {
      this.modalidade = `${'Carona '} - ${data?.numeroProcessoAdministrativo}`;
      this.entidade = data?.entidade.nome;
      this.dataAbertura = getValueDate(data?.dataAdesao, DATE_FORMAT, DATE_PARSE_FORMAT);

      this.readFileStoreTda = new MultipleFileUploaderStore(
        [],
        (file) => TdaCaronaService.upload(file),
        (fileDTO) => TdaCaronaService.download(fileDTO),
        () => {},
        () => {}
      );
    } else {
      this.modalidade = `${'Credenciamento'} - ${data?.numeroProcesso}`;
      this.entidade = data?.entidade.nome;
      this.dataAbertura = getValueDate(data?.dataCadastro, DATE_FORMAT, DATE_PARSE_FORMAT);

      this.readFileStoreTda = new MultipleFileUploaderStore(
        [],
        (file) => TdaCredenciamentoService.upload(file),
        (fileDTO) => TdaCredenciamentoService.download(fileDTO),
        () => {},
        () => {}
      );
    }
  }
}

export default AlertaHistoricoFormStore;
