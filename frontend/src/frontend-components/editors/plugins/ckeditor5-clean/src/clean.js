import Plugin from '@ckeditor/ckeditor5-core/src/plugin';
import ButtonView from '@ckeditor/ckeditor5-ui/src/button/buttonview';

import cleanIcon from '../theme/icons/clean.svg';

export default class Clean extends Plugin {
  static get pluginName() {
    return 'Clean';
  }

  init() {
    const editor = this.editor;

    editor.ui.componentFactory.add('clean', () => {
      const button = new ButtonView();
      const command = editor.commands.get('bold');
      button.bind('isEnabled').to(command);

      button.set({
        label: 'Limpar',
        icon: cleanIcon,
        tooltip: true,
        withText: true,
      });

      button.on('execute', () => {
        editor.data.set('');
      });

      return button;
    });
  }
}
