import { useEffect, useRef } from 'react';

const useDrag = (ref) => {
  const isDraggingRef = useRef(false);
  const coords = useRef({
    startX: 0,
    startY: 0,
    lastX: 0,
    lastY: 0,
  });

  const startDrag = (event) => {
    if (ref.current.contains(event.target)) {
      isDraggingRef.current = true;
      coords.current.startX = event.clientX;
      coords.current.startY = event.clientY;
    }
  };

  const endDrag = (event) => {
    if (isDraggingRef.current) {
      isDraggingRef.current = false;
      coords.current.lastX += event.clientX - coords.current.startX;
      coords.current.lastY += event.clientY - coords.current.startY;
    }
  };

  const dragging = (event) => {
    if (isDraggingRef.current) {
      const nextX = event.clientX - coords.current.startX + coords.current.lastX;
      const nextY = event.clientY - coords.current.startY + coords.current.lastY;

      ref.current.style.transform = `translate(${nextX}px, ${nextY}px)`;
    }
  };

  useEffect(() => {
    window.addEventListener('mousedown', startDrag);
    window.addEventListener('mouseup', endDrag);
    window.addEventListener('mousemove', dragging);

    return () => {
      window.removeEventListener('mousedown', startDrag);
      window.removeEventListener('mouseup', endDrag);
      window.removeEventListener('mousemove', dragging);
    };
  }, [ref]);
};

export default useDrag;
