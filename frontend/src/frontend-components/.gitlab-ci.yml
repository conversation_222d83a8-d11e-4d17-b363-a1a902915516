variables:
  GIT_STRATEGY: fetch
  GIT_SUBMODULE_STRATEGY: recursive

stages:
  - static-analysis

.default-not-scheduled:
  except:
    - schedules

sonarqube:
  image: registry.gitlab.com/lsi-ufcg/tce-ac/novo-licon/licon:sonar-scanner-cli
  stage: static-analysis
  tags:
    - lsi-ufcg
    - docker
  rules:
    - if: $CI_PIPELINE_SOURCE == "schedule" && $CI_COMMIT_BRANCH == "master"
      when: always
  script:
    - sonar-scanner -Dsonar.host.url=$SONAR_HOST_URL -Dsonar.token=$SONAR_TOKEN -Dsonar.links.ci=$CI_PIPELINE_URL
