# Changelog - Frontend Components

Todos as alterações notáveis neste projeto serão documentadas neste arquivo.

Exemplo de publicação:

- [yyyy-mm-dd] Nova funcionalidade incrível para o recurso X. (link da issue)

## [2.x.x] - Não Publicado

### Novas funcionalidades

### Melhorias

### Bugs fixes

### Ajustes Visuais

## [2.43.0] - 2025-03-27

### Novas funcionalidades

### Melhorias

### Bugs fixes

### Ajustes Visuais

## [2.42.0] - 2025-03-10

### Novas funcionalidades

### Melhorias

### Bugs fixes

## [2.41.0] - 2025-03-25

### Novas funcionalidades

### Melhorias

### Bugs fixes

## [2.40.0] - 2025-02-25

### Novas funcionalidades

### Melhorias

### Bugs fixes

### Ajustes Visuais

## [2.39.0] - 2025-02-16

### Novas funcionalidades

### Melhorias

### Bugs fixes

### Ajustes Visuais

## [2.38.0] - 2025-02-09

### Novas funcionalidades

### Melhorias

### Bugs fixes

### Ajustes Visuais

## [2.37.0] - 2025-01-07

### Novas funcionalidades

### Melhorias

### Bugs fixes

### Ajustes Visuais

## [2.36.0] - 2024-12-11

### Novas funcionalidades

### Melhorias

### Bugs fixes

### Ajustes Visuais

## [2.35.0] - 2024-11-27

### Novas funcionalidades

### Melhorias

### Bugs fixes

### Ajustes Visuais

## [2.34.0] - 2024-11-13

### Novas funcionalidades

### Melhorias

### Bugs fixes

### Ajustes Visuais

## [2.33.0] - 2024-10-24

### Novas funcionalidades

### Melhorias

### Bugs fixes

### Ajustes Visuais

## [2.32.0] - 2024-10-08

### Novas funcionalidades

### Melhorias

### Bugs fixes

### Ajustes Visuais

## [2.31.0] - 2024-09-27

### Novas funcionalidades

### Melhorias

### Bugs fixes

### Ajustes Visuais

## [2.30.0] - 2024-09-26

### Novas funcionalidades

### Melhorias

### Bugs fixes

### Ajustes Visuais

## [2.29.0] - 2024-09-16

### Novas funcionalidades

### Melhorias

### Bugs fixes

### Ajustes Visuais

## [2.28.0] - 2024-09-03

### Novas funcionalidades

### Melhorias

### Bugs fixes

### Ajustes Visuais

## [2.27.0] - 2024-08-21

### Novas funcionalidades

### Melhorias

### Bugs fixes

### Ajustes Visuais

## [2.26.0] - 2024-07-22

### Novas funcionalidades

### Melhorias

### Bugs fixes

### Ajustes Visuais

## [2.25.0] - 2024-07-22

### Novas funcionalidades

### Melhorias

### Bugs fixes

### Ajustes Visuais

## [2.24.0] - 2024-06-28

### Novas funcionalidades

### Melhorias

### Bugs fixes

### Ajustes Visuais

## [2.23.0] - 2024-06-10

### Novas funcionalidades

### Melhorias

### Bugs fixes

### Ajustes Visuais

## [2.22.0] - 2024-05-26

### Novas funcionalidades

### Melhorias

### Bugs fixes

### Ajustes Visuais

## [2.21.0] - 2024-05-17

### Novas funcionalidades

### Melhorias

### Bugs fixes

### Ajustes Visuais

## [2.20.0] - 2024-05-03

### Novas funcionalidades

### Melhorias

### Bugs fixes

### Ajustes Visuais

## [2.19.0] - 2024-04-23

### Novas funcionalidades

### Melhorias

### Bugs fixes

### Ajustes Visuais

## [2.18.1] - 2024-04-16

### Novas funcionalidades

### Melhorias

### Bugs fixes

### Ajustes Visuais

## [2.18.0] - 2024-04-15

### Novas funcionalidades

### Melhorias

### Bugs fixes

### Ajustes Visuais

## [2.17.0] - 2024-04-11

### Novas funcionalidades

### Melhorias

### Bugs fixes

### Ajustes Visuais

## [2.16.0] - 2024-04-05

### Novas funcionalidades

### Melhorias

### Bugs fixes

### Ajustes Visuais

## [2.15.0] - 2024-03-25

### Novas funcionalidades

### Melhorias

### Bugs fixes

### Ajustes Visuais

## [2.14.1] - 2024-03-19

### Novas funcionalidades

### Melhorias

### Bugs fixes

### Ajustes Visuais

## [2.14.0] - 2024-03-19

### Novas funcionalidades

### Melhorias

### Bugs fixes

### Ajustes Visuais

## [2.13.0] - 2024-02-29

### Novas funcionalidades

### Melhorias

### Bugs fixes

### Ajustes Visuais

## [2.12.1] - 2024-02-07

### Novas funcionalidades

### Melhorias

### Bugs fixes

### Ajustes Visuais

## [2.12.0] - Não Publicado

### Novas funcionalidades

### Melhorias

### Bugs fixes

### Ajustes Visuais

## [2.11.0] - 2024-01-23

### Novas funcionalidades

### Melhorias

### Bugs fixes

### Ajustes Visuais

## [2.10.0] - 2023-12-15

### Novas funcionalidades

### Melhorias

### Bugs fixes

### Ajustes Visuais

## [2.9.0] - 2023-12-05

### Novas funcionalidades

### Melhorias

### Bugs fixes

### Ajustes Visuais

## [2.8.0] - 2023-11-23

### Novas funcionalidades

### Melhorias

### Bugs fixes

### Ajustes Visuais

## [2.7.0] - 2023-11-14

### Novas funcionalidades

### Melhorias

- [2023-11-03] FcInputTextArea - Texto Justificado por padrão (novo-licon#1102)

### Bugs fixes

- [2023-10-24] - Correção no método checkUserGroup quando utilizado no primeiro login ocorrido no sistema. (novo-licon#839)

### Ajustes Visuais

## [2.6.0] - 2023-10-24

### Novas funcionalidades

### Melhorias

- [2023-10-24] - Melhoria no advancedSearch, adição de botões para limpar filtros aplicados. (novo-licon#1054)

- [2023-11-06] - Rules - Adicionando restrição de dias para validação de horário comercial. (novo-licon#1101)

- [2023-10-24] - Melhoria na busca textual livre. (novo-licon#1054)

### Bugs fixes

- [2023-11-08] Advanced Search/FcCloseableTabView - Adicionando comportamento para fechar filtros quando o usuário troca de tela ou adiciona tabs (novo-licon#1008)

- [2023-10-24] - Corrigindo callback ao realizar download do fileuploader. (novo-licon#1083)

### Ajustes Visuais

## [2.5.2] - 2023-10-04

### Novas funcionalidades

### Melhorias

### Bugs fixes

### Ajustes Visuais

## [2.5.1] - 2023-09-27

### Novas funcionalidades

### Melhorias

### Bugs fixes

- [2023-09-15] - Correção no AsyncMultiselect para evitar valores duplicados. (novo-licon#992)

### Ajustes Visuais

## [2.5.0] - 2023-09-15

### Novas funcionalidades

### Melhorias

- [2023-09-06] - CkEditor - Habilitando disabled para o botão de limpar (novo-licon#1027)

- [2023-09-01] - Adaptação no advancedSearch para uso do elasticsearch e requisição no AsyncDropdown pelo valor (novo-licon#973)

### Bugs fixes

### Ajustes Visuais

- [2023-08-31] FcButton - Adicionado a propriedade tooltipOptions (novo-licon#1053)

## [2.4.0] - 2023-08-25

### Novas funcionalidades

- [2023-08-24] Readicionando o botão para visualizar arquivos. (novo-licon#1023)

- [2023-07-31] Nova propridade rule para o componente Permission Proxy. (novo-licon#985)

### Melhorias

### Bugs fixes

### Ajustes Visuais

## [2.3.0] - 2023-08-16

### Novas funcionalidades

### Melhorias

- [2023-08-14] AppStore recebe estado do layout padrão do user para telas de detalhamento. (novo-licon#1012)

- [2023-08-10] Adicionando defaultValue no getValueMoney e tratamento para NaN no isValueValid. (novo-licon#1017)

- [2023-07-31] Sumário fechado por padrão nas telas de detalhes. (novo-licon#1017)

- [2023-07-30] AdvancedSearch - Permite a estilização do componente. (novo-licon#964)

### Bugs fixes

### Ajustes Visuais

## [2.2.0] - 2023-08-01

### Novas funcionalidades

### Melhorias

- [2023-07-17] CardList - Mensagem para quando não houver resultados. (novo-licon#970)

### Bugs fixes

### Ajustes Visuais

## [2.1.0] - 2023-07-11

### Novas funcionalidades

- [2023-07-06] Licitação - Permitindo visualização de contratos nos detalhes da licitação. (novo-licon#887)

### Melhorias

- [2023-07-05] Melhoria no componente do SelectDialog - Props `showInput`, `labelButtonDialog`, `iconButtonDialog` (novo-licon#963)

- [2023-07-04] Usuário Administrador - Permissões gerais no novo-licon. (novo-licon#981)

- [2023-06-23] Telas com Cardlist - Alterações. (novo-licon#948)

- [2023-06-20] AsyncPickList - Melhoria no carregamento de dados. (novo-licon#961)

### Bugs fixes

- [2023-06-30] Retirada de duplicações de código no AdvancedSearch

### Ajustes Visuais

- [2023-06-23] ScrollMenu - ajustes visuais no componente (novo-licon#980)

## [2.0.0] - 2023-06-20
