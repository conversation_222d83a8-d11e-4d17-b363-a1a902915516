import PropTypes from 'prop-types';
import { Calendar } from 'primereact/calendar';
import { useCallback, useEffect, useRef, useState } from 'react';

const FcCalendar = ({ value, onChange, minDate, maxDate, openToValidDate, ...props }) => {
  const calendarRef = useRef(null);

  const clampDate = (date, min, max) => (max && date > max ? max : min && date < min ? min : date);

  const clampToday = useCallback(() => {
    const today = new Date();
    return clampDate(today, minDate, maxDate);
  }, [minDate, maxDate]);

  const [viewDate, setViewDate] = useState(() => (openToValidDate ? value ?? clampToday() : null));

  useEffect(() => {
    if (openToValidDate) {
      const nextDate = value ?? clampToday();
      setViewDate(nextDate);
      calendarRef.current?.updateViewDate(null, nextDate);
    }
  }, [value, clampToday, openToValidDate, calendarRef]);

  useEffect(() => {
    if (calendarRef.current) {
      const inputElement = calendarRef.current.getElement().getElementsByClassName('p-inputtext')[0];
      const handleInputChange = () => {
        inputElement?.value === '' && onChange({ target: { value: null } });
      };
      inputElement?.addEventListener('focusout', handleInputChange);
      return () => inputElement?.removeEventListener('focusout', handleInputChange);
    }
  }, []);

  const handleOnShow = () => {
    if (openToValidDate) {
      calendarRef.current?.updateViewDate(null, viewDate);
    }
  };

  return (
    <Calendar
      {...props}
      ref={calendarRef}
      value={value}
      onChange={(e) => e.value && onChange(e)}
      minDate={minDate}
      maxDate={maxDate}
      {...(openToValidDate && {
        viewDate,
        onViewDateChange: (e) => setViewDate(e.value),
        onShow: handleOnShow,
      })}
    />
  );
};

FcCalendar.defaultProps = {
  openToValidDate: false,
};

FcCalendar.propTypes = {
  value: PropTypes.instanceOf(Date),
  onChange: PropTypes.func,
  minDate: PropTypes.instanceOf(Date),
  maxDate: PropTypes.instanceOf(Date),
  openToValidDate: PropTypes.bool,
};

export default FcCalendar;
