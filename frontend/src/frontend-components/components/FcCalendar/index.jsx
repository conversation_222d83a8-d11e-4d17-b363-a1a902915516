import { PropTypes } from 'mobx-react';
import { Calendar } from 'primereact/calendar';
import { useEffect, useRef } from 'react';

const FcCalendar = ({ onChange, ...props }) => {
  const calendarRef = useRef(null);

  useEffect(() => {
    if (calendarRef.current) {
      const inputElement = calendarRef.current.getElement().getElementsByClassName('p-inputtext')[0];
      const handleInputChange = () => {
        inputElement?.value === '' && onChange({ target: { value: null } });
      };
      inputElement?.addEventListener('focusout', handleInputChange);
      return () => inputElement?.removeEventListener('focusout', handleInputChange);
    }
  }, []);

  return (
    <Calendar
      {...props}
      ref={calendarRef}
      onChange={(e) => {
        e.value && onChange(e);
      }}
    />
  );
};

FcCalendar.propTypes = {
  onChange: PropTypes.func,
};

export default FcCalendar;
