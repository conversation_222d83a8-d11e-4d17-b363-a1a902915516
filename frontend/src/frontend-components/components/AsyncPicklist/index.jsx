import React from 'react';
import PropTypes from 'prop-types';
import { observer } from 'mobx-react';
import { PickList } from 'primereact/picklist';
import { Skeleton } from 'primereact/skeleton';
import { InputText } from 'primereact/inputtext';
import './styles.scss';
import { getNumberUnitThousands } from 'fc/utils/utils';
@observer
class AsyncPickList extends React.Component {
  store;
  constructor(props) {
    super(props);
    this.store = props.store;

    this.state = {
      filterTimeout: null,
    };

    this._innerOnChange = this._innerOnChange.bind(this);
    this.renderTemplate = this.renderTemplate.bind(this);
  }

  componentDidMount() {
    this.store.initialize(this.props.value, () => this.forceUpdate());
  }

  _innerOnChange(e) {
    if (!this.store.loading) {
      const sourceList = this.store.getInvisibleOptionsSourceList().concat(e.source);
      const targetList = this.store.getInvisibleOptionsTargetList().concat(e.target);
      this.store.updateLists(sourceList, targetList);
      this.props.onChange(targetList);
    }
  }

  renderTemplate(item) {
    return !this.props.store.loading ? (
      <div>
        <p>{item?.[this.store.label]}</p>
      </div>
    ) : (
      <div className="p-d-flex p-ai-center p-p-2" style={{ height: '20px' }}>
        <Skeleton width={'100%'} height="1rem" />
      </div>
    );
  }

  render() {
    const targetList = this.store.getVisibleOptionsTargetList();
    const sourceList = this.store.getVisibleOptionsSourceList();
    const quantitySource = this.store.quantitySourceList();
    const quantityTarget = this.store.quantityTargetList();
    return (
      <>
        {this.store.searchOptions.filter && (
          <div className="p-fluid p-formgrid p-grid">
            <span className="p-field p-col-6" style={{ paddingRight: '30px' }}>
              <span className="p-input-icon-left">
                <i className="pi pi-search" />
                <InputText
                  value={this.store.filterSourceValue}
                  onChange={(e) => this.store.updateFilterSourceValue(e.target.value, () => this.forceUpdate())}
                />
              </span>
            </span>
            <span className="p-field p-col-6" style={{ paddingLeft: '30px' }}>
              <span className="p-input-icon-left">
                <i className="pi pi-search" />
                <InputText
                  value={this.store.filterTargetValue}
                  onChange={(e) => this.store.updateFilterTargetValue(e.target.value)}
                />
              </span>
            </span>
          </div>
        )}
        <div className="relative">
          <PickList
            {...this.props}
            source={sourceList}
            target={targetList}
            onChange={this._innerOnChange}
            showSourceControls={false}
            showTargetControls={false}
            sourceStyle={{ height: '342px' }}
            targetStyle={{ height: '342px' }}
            itemTemplate={this.renderTemplate}
            dataKey={this.store.value}
          />
          <div className="source">Quantidade de registros: {getNumberUnitThousands(quantitySource)}</div>
          <div className="target">Quantidade de registros: {getNumberUnitThousands(quantityTarget)}</div>
        </div>
      </>
    );
  }
}

AsyncPickList.propTypes = {
  id: PropTypes.string,
  value: PropTypes.any,
  onChange: PropTypes.func,
  store: PropTypes.any.isRequired,
};

export default AsyncPickList;
