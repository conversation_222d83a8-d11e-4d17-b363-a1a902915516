# AsyncDropdown

Componente de encapsulamento da PickList para chamadas à outras entidades. Deve possuir um store com instância de `AsyncPicklistStore` sendo passado.

## Props

| Propriedade | <PERSON><PERSON> | Descrição                                                                                              |
| ----------- | ------------ | ------------------------------------------------------------------------------------------------------ |
| id          | -            | Identificador do formulário (atributo). _OBS: Passado automático ao ser envolvido por um `FormField`._ |
| value       | -            | Valor da chave da entidade a ser exibida pela `PickList`                                               |
| onChange    | -            | <PERSON><PERSON> a ser chamada ao haver alteração de seleção.                                                    |
| store       | -            | Instância do `AsyncPicklistStore` que armazena os dados e a listagem exibida na `Picklist`.            |
