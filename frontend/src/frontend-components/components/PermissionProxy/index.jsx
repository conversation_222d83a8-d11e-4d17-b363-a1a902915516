import React from 'react';
import PropTypes from 'prop-types';
import AppStore from '../../stores/AppStore';
import { cloneChildrenRecursive } from '../../utils/utils';
import Access from '../../pages/Access';
import { Link } from 'react-router-dom';

class PermissionProxy extends React.Component {
  constructor(props) {
    super(props);
  }

  getChildrenHandleLink() {
    const firstChild = React.Children.toArray(this.props.children)[0];

    if (React.isValidElement(firstChild) && firstChild.type === Link) {
      const { children: linkChildren } = firstChild.props;
      return <>{linkChildren}</>;
    }

    return this.props.children;
  }

  render() {
    const resourcePermissions = Array.isArray(this.props.resourcePermissions)
      ? this.props.resourcePermissions
      : [this.props.resourcePermissions];
    if (AppStore.hasPermission(resourcePermissions)) {
      return this.props.rule ? this.props.rule() ? this.props.children : <Access /> : this.props.children;
    }
    if (this.props.blockOnFail) return <Access />;
    if (this.props.hideOnFail) return null;

    return cloneChildrenRecursive(this.getChildrenHandleLink(), { disabled: true });
  }
}

PermissionProxy.displayName = 'PermissionProxy';

PermissionProxy.propTypes = {
  children: PropTypes.any.isRequired,
  resourcePermissions: PropTypes.any.isRequired,
  blockOnFail: PropTypes.bool,
  hideOnFail: PropTypes.bool,
  rule: PropTypes.func,
};

PermissionProxy.contextTypes = {
  router: PropTypes.object,
};

export default PermissionProxy;
