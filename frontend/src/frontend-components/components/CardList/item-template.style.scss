.info-item {
  display: flex;
}

.data-card {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  border: 1px solid RGB(220, 220, 220);
  border-radius: 0.75rem;
  position: relative;
  height: 100%;
  background: white;
}

.scale-on-hover {
  transition: transform 0.2s ease-in-out, all 0.2s ease-in-out;

  &:hover {
    transform: scale(1.02);
    text-decoration: underline;
  }
}

.data-card-title {
  color: #24292f;
  font-size: 1.2rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.data-card-subtitle {
  color: RGB(90, 90, 90);
  font-size: 1rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.data-card-header {
  display: flex;
  flex-direction: column;
}

.ellipsis-button {
  transform: translate(25%, -25%);
  max-height: 2rem;
  max-width: 2rem;
}

.main-content {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 6;
  -webkit-box-orient: vertical;
  font-size: 0.8rem;
}
