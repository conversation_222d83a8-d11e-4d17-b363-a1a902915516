import React, { useEffect, useRef, useState } from 'react';
import { DataView } from 'primereact/dataview';
import FcButton from '../FcButton';
import PropTypes from 'prop-types';
import { screenBreakpoints } from '../../constants/screenBreakpoints';
import LoadingThreeDots from './loading';
import { Divider } from 'primereact/divider';
import { observer } from 'mobx-react';
import ItemTemplate from './itemTemplate';
import './style.scss';

const CardList = observer(
  ({ store, fields, header, labelsLimit, parentId, parentIsDialog, onTitleClick, emptyMessage, footer }) => {
    const [containerWidth, setContainerWidth] = useState(0);
    const [endOfData, setEndOfData] = useState(false);
    const searchPage = useRef({ index: 2, size: 10 });
    const parentRef = parentId ? document.getElementById(parentId + (parentIsDialog ? '_content' : '')) : window;
    const parentElement = parentId ? parentRef : document.documentElement;
    const [activeCardId, setActiveCardId] = useState(null);

    const handleMenuToggle = (cardId) => {
      setActiveCardId(activeCardId === cardId ? null : cardId);
    };

    const checkHasScroll = () => {
      return parentElement?.scrollHeight > parentElement?.clientHeight;
    };

    useEffect(() => {
      if (store.resetEndPageScroll) {
        setEndOfData(false);
        searchPage.current = { index: 2, size: 10 };
        store.resetPageScroll();
      }
    }, [store.resetEndPageScroll]);

    const containerRef = useRef(null);

    useEffect(() => {
      if (!checkHasScroll() && !endOfData && store.listKey.length > 0) {
        store.load(
          { ...store.advancedSearchParams, page: searchPage.current },
          (response) => {
            if (response.data.items.length < searchPage.current.size) {
              setEndOfData(true);
            }
          },
          true
        );
        searchPage.current = { index: searchPage.current.index + 1, size: searchPage.current.size };
      }
    }, [store.listKey]);

    useEffect(() => {
      const handleResize = () => {
        setContainerWidth(containerRef.current.clientWidth);
      };

      handleResize();

      parentRef?.addEventListener('resize', handleResize);

      return () => {
        parentRef?.removeEventListener('resize', handleResize);
      };
    }, []);

    useEffect(() => {
      function handleScroll() {
        const scrollTop = parentElement?.scrollTop;
        const windowHeight = parentRef?.innerHeight ?? parentRef?.clientHeight;
        const scrollHeight = parentElement?.scrollHeight;

        const isAtEnd = scrollTop + windowHeight >= scrollHeight - 100;
        if (checkHasScroll() && isAtEnd && !store.loading && !endOfData) {
          store.load(
            { ...store.advancedSearchParams, page: searchPage.current },
            (response) => {
              if (response.data.items.length < searchPage.current.size) {
                setEndOfData(true);
              }
            },
            true
          );

          searchPage.current = { index: searchPage.current.index + 1, size: searchPage.current.size };
        }
      }
      parentRef?.addEventListener('scroll', handleScroll);

      return () => parentRef?.removeEventListener('scroll', handleScroll);
    }, [endOfData, parentRef]);

    function getCardWidth() {
      if (containerWidth < screenBreakpoints.sm) {
        return 'col-12';
      } else if (containerWidth < screenBreakpoints.lg) {
        return 'col-6';
      } else {
        return 'col-4';
      }
    }

    function itemTemplate(cardItem) {
      return (
        <ItemTemplate
          cardItem={cardItem}
          cardWidth={getCardWidth()}
          labelsLimit={labelsLimit}
          activeCardId={activeCardId}
          onMenuToggle={handleMenuToggle}
        />
      );
    }

    function addItemCard(cardItemObject, fieldItem, listKeyValue, listItem) {
      if (fieldItem.value === 'title') {
        cardItemObject.title = listKeyValue;
      } else if (fieldItem.value === 'mainContent') {
        cardItemObject.mainContent = listKeyValue;
      } else if (fieldItem.value === 'iconLabel') {
        const toolTip = typeof fieldItem.label === 'function' ? fieldItem.label(listItem) : fieldItem.label;
        if (cardItemObject.iconLabels) {
          cardItemObject.iconLabels.push({
            icon: fieldItem.icon,
            value: listKeyValue,
            toolTip,
            color: fieldItem.color,
          });
        } else {
          cardItemObject.iconLabels = [
            {
              icon: fieldItem.icon,
              value: listKeyValue,
              toolTip,
              color: fieldItem.color,
            },
          ];
        }
      } else if (fieldItem.value === 'bottomBar') {
        cardItemObject.bottomBar = listKeyValue;
      } else if (fieldItem.value === 'subtitle') {
        cardItemObject.subtitle = listKeyValue;
      } else if (fieldItem.value === 'secondSubtitle') {
        cardItemObject.secondSubtitle = listKeyValue;
      } else if (fieldItem.value === 'ellipsisItems') {
        cardItemObject.ellipsisItems = listKeyValue;
      } else if (fieldItem.value === 'hasModificationRequest') {
        cardItemObject.hasModificationRequest = listKeyValue;
      }
    }

    function getCardListDataShape(fields, listKey) {
      const cardItemsPropArray = [];
      listKey.length > 0 &&
        listKey.forEach((listItem) => {
          const cardItemObject = {};

          if (onTitleClick && onTitleClick(listItem)) {
            cardItemObject.onTitleClick = () => onTitleClick(listItem).command(listItem);
          }

          fields.forEach((fieldItem) => {
            let listKeyValue = '';

            if (fieldItem.combinedColumns) {
              let listKeyValues = [];

              fieldItem.fields.map((field) => {
                listKeyValues.push(listItem[field]);
              });

              listKeyValue = listKeyValues;
              fieldItem.body && (listKeyValue = fieldItem.body(listKeyValue));
            } else {
              listKeyValue = listItem[fieldItem.field];
              fieldItem.body && (listKeyValue = fieldItem.body(listItem));
            }

            if (fieldItem.showIfExists) {
              if (listKeyValue != '-') {
                addItemCard(cardItemObject, fieldItem, listKeyValue, listItem);
              }
            } else {
              addItemCard(cardItemObject, fieldItem, listKeyValue, listItem);
            }
          });

          cardItemsPropArray.push(cardItemObject);
        });
      return cardItemsPropArray;
    }

    function renderFilters() {
      const filters = [];

      fields.forEach((field) => {
        const [sortAction, setSortAction] = useState(null);

        function renderFilterIcon() {
          if (store.advancedSearchParams.sort.by === field.field) {
            if (store.advancedSearchParams.sort.order === 'asc') {
              return 'pi pi-sort-amount-up';
            } else if (store.advancedSearchParams.sort.order === 'desc') {
              return ' pi pi-sort-amount-down-alt';
            }

            return 'pi pi-sort-alt';
          }

          return 'pi pi-sort-alt';
        }

        function getSearchOrder() {
          if (sortAction === null || sortAction === 'desc') {
            return 'asc';
          }

          return 'desc';
        }

        const alreadyExistsInFilters = filters.some((filterButton) => filterButton.props.label === field.label);
        if (field.sortable && !alreadyExistsInFilters) {
          filters.push(
            <FcButton
              icon={renderFilterIcon()}
              className={`${
                field.field === store.advancedSearchParams.sort.by ? 'selected-filters-button' : 'p-button-outlined'
              } filters-button p-button-sm`}
              label={field.label}
              onClick={() => {
                const order = getSearchOrder();

                searchPage.current = { index: 2, size: 10 };

                setEndOfData(false);

                store.load({
                  ...store.advancedSearchParams,
                  page: { index: 1, size: 10 },
                  sort: { by: field.field, order: order },
                });
                setSortAction(order);
              }}
              disabled={store.loading}
            />
          );
        }
      });

      return <div className="flex gap-2 flex-wrap">{filters.map((filter) => filter)}</div>;
    }

    const renderCardListFooter = () => {
      if (endOfData) {
        return (
          <div className="flex justify-content-center">
            <span>Você chegou ao final da página!</span>
          </div>
        );
      } else if (store.loading) {
        return (
          <>
            <Divider />
            <LoadingThreeDots label="Carregando mais" size="sm" id="sentinela" />
          </>
        );
      }
    };

    function renderDataView() {
      return (
        <div>
          <DataView
            value={getCardListDataShape(fields, store.listKey)}
            layout="grid"
            itemTemplate={itemTemplate}
            emptyMessage={emptyMessage}
          />

          {footer ? footer() : renderCardListFooter()}
        </div>
      );
    }

    return (
      <div ref={containerRef}>
        {header}
        {header && <Divider className="mb-3 mt-0" />}
        {renderFilters()}
        {store.loading && !store.loadingNewPage ? (
          <div className="flex justify-content-center align-items-center mt-4">
            <LoadingThreeDots size="sm" label="Carregando dados" />
          </div>
        ) : (
          <>{renderDataView()}</>
        )}
      </div>
    );
  }
);

CardList.defaultProps = {
  emptyMessage: 'Nenhum dado encontrado.',
};

CardList.propTypes = {
  store: PropTypes.object.isRequired,
  fields: PropTypes.array.isRequired,
  header: PropTypes.func,
  parentId: PropTypes.string,
  parentIsDialog: PropTypes.bool,
  onTitleClick: PropTypes.func,
  emptyMessage: PropTypes.string,
  footer: PropTypes.func,
};

export default CardList;
