import React, { useRef, useState } from 'react';
import { Divider } from 'primereact/divider';
import PropTypes from 'prop-types';
import { Menu } from 'primereact/menu';
import { Tag } from 'primereact/tag';
import Tooltip from '../Tooltip';
import FcButton from '../FcButton';
import { getLightenColor } from '../../utils/utils';
import { classNames } from 'primereact/utils';
import './item-template.style.scss';

const ItemTemplate = ({ cardItem, cardWidth, labelsLimit = 3, activeCardId, onMenuToggle }) => {
  const [showAllLabels, setShowAllLabels] = useState(false);
  const menu = useRef(null);

  const totalTags = cardItem.iconLabels?.reduce((prev, elem) => {
    if (Array.isArray(elem.value)) {
      const filtered = elem.value.filter((item) => item?.value);

      return prev + filtered.length;
    }

    if (elem.value) {
      return prev + 1;
    }

    return prev;
  }, 0);

  function renderIconLabel() {
    let showingTags = 0;

    function renderTag(item) {
      if (item?.value) {
        const tag = (
          <Tooltip value={item?.toolTip} sideOffset={0} key={item?.value}>
            <div style={{ cursor: 'default' }}>
              <Tag
                icon={item?.icon}
                className="info-item"
                style={{
                  backgroundColor: getLightenColor(item?.color, 0.7),
                  color: item?.color,
                  border: `1px solid ${item?.color}`,
                }}
              >
                <span>{item?.value}</span>
              </Tag>
            </div>
          </Tooltip>
        );

        if (showingTags < labelsLimit && !showAllLabels) {
          showingTags = showingTags + 1;
          return tag;
        } else if (showAllLabels) {
          return tag;
        }
      }
    }

    return cardItem.iconLabels?.map((item) => {
      if (Array.isArray(item.value)) {
        return item?.value?.map(renderTag);
      }

      return renderTag(item);
    });
  }

  return (
    <div className={classNames('p-1', cardWidth ?? 'col-12')}>
      <div className={classNames('data-card p-4')}>
        <div className="data-card-header gap-0">
          <div className="flex justify-content-between">
            <div>
              <h1
                className={classNames('data-card-title', cardItem.onTitleClick && 'scale-on-hover cursor-pointer')}
                onClick={cardItem.onTitleClick}
              >
                {cardItem.title}
              </h1>
              <h3 className="data-card-subtitle">{cardItem?.subtitle}</h3>
            </div>

            {cardItem?.ellipsisItems && cardItem?.ellipsisItems.length > 0 && (
              <>
                <Menu model={cardItem?.ellipsisItems} popup ref={menu} id="popup_menu" />
                <FcButton
                  tooltip="Mais opções"
                  tooltipOptions={{ position: 'top', style: { marginLeft: '7px', marginTop: '-10px' } }}
                  icon="pi pi-list"
                  className="p-button ellipsis-button"
                  onClick={(event) => {
                    onMenuToggle(cardItem.id);
                    if (activeCardId === cardItem.id) {
                      menu.current.hide(event);
                      menu.current.toggle(event);
                    } else {
                      menu.current.toggle(event);
                    }
                  }}
                />
              </>
            )}
          </div>
        </div>

        <div className={classNames('text-justify main-content', { 'mb-3': cardItem.iconLabels?.length })}>
          {cardItem?.mainContent}
        </div>

        <div className="flex gap-1 flex-wrap">{renderIconLabel()}</div>

        <div className="flex justify-content-end">
          {totalTags > labelsLimit && (
            <FcButton
              label={showAllLabels ? 'Ver menos' : 'Ver mais'}
              className="p-button-link"
              onClick={(event) => {
                event.stopPropagation();
                setShowAllLabels((prev) => !prev);
              }}
            />
          )}
        </div>

        {cardItem.bottomBar && (
          <>
            <Divider />
            {cardItem.bottomBar}
          </>
        )}
      </div>
    </div>
  );
};

ItemTemplate.propTypes = {
  cardItem: PropTypes.shape({
    id: PropTypes.string.isRequired,
    onTitleClick: PropTypes.func,
    title: PropTypes.string,
    ellipsisItems: PropTypes.arrayOf(
      PropTypes.shape({
        label: PropTypes.string,
        icon: PropTypes.string,
        command: PropTypes.func,
        url: PropTypes.string,
      })
    ),
    subtitle: PropTypes.string,
    mainContent: PropTypes.string,
    iconLabels: PropTypes.arrayOf(
      PropTypes.shape({
        icon: PropTypes.string,
        value: PropTypes.oneOfType([PropTypes.string, PropTypes.bool]),
        toolTip: PropTypes.string,
        color: PropTypes.string,
      })
    ),
    bottomBar: PropTypes.func,
  }).isRequired,
  cardWidth: PropTypes.string,
  labelsLimit: PropTypes.number,
  activeCardId: PropTypes.string,
  onMenuToggle: PropTypes.func.isRequired,
};

export default ItemTemplate;
