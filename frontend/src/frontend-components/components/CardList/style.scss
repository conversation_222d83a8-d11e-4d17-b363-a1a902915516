.container {
  gap: 1rem;
}

h1 {
  margin: 0;
}

h3 {
  margin: 0;
}

.options-button {
  position: absolute;
  right: 1rem;
  top: 0.5rem;
}

.info-item {
  display: flex;
}

.filters-button {
  color: #333333 !important;
  font-weight: 400;
  border: 1px solid RGB(220, 220, 220) !important;
}

.selected-filters-button {
  color: white !important;
  background-color: #3f51b5 !important;
  border: none !important;
}

@keyframes bounce {
  40% {
    transform: translateY(-5px);
  }
  100% {
    transform: translateY(0);
  }
}

.bouncing-balls {
  background-color: #e1e1e1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0.5rem 0.75rem;
  border-radius: 1rem;
  gap: 0.25rem;
}

.bouncing-balls span {
  display: block;
  border-radius: 50%;
  background-color: #868782;
  animation: bounce 1800ms ease infinite;
}

.bouncing-balls span:nth-child(1) {
  animation-delay: 0ms;
}

.bouncing-balls span:nth-child(2) {
  animation-delay: 400ms;
}

.bouncing-balls span:nth-child(3) {
  animation-delay: 800ms;
}

.loading-container {
  border: 1px solid RGB(220, 220, 220);
  width: 10rem;
  height: 8rem;
  border-radius: 6px;
}
