import PropTypes from 'prop-types';

const LoadingThreeDots = ({ size, label }) => {
  const defaultSizes = {
    xm: '4px',
    sm: '6px',
    md: '8px',
    lg: '16px',
    xl: '20px',
  };

  return (
    <div className="flex flex-column align-items-center justify-content-center gap-4">
      {label && (
        <span className="text-xl font-bold" style={{ color: '#868782' }}>
          {label}
        </span>
      )}
      <div className="bouncing-balls">
        <span style={{ width: defaultSizes[size], height: defaultSizes[size] }}></span>
        <span style={{ width: defaultSizes[size], height: defaultSizes[size] }}></span>
        <span style={{ width: defaultSizes[size], height: defaultSizes[size] }}></span>
      </div>
    </div>
  );
};

LoadingThreeDots.propTypes = {
  size: PropTypes.oneOf(['xm', 'sm', 'md', 'lg', 'xl']),
  label: PropTypes.string,
};

export default LoadingThreeDots;
