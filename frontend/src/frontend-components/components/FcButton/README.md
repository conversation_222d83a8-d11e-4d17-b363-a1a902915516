# FcButton

Componente `Button` do primereact com data-cy.

## Props

| Propriedade   | Valor <PERSON> | Descrição                                                                                                           |
| ------------- | ------------ | ------------------------------------------------------------------------------------------------------------------- |
| label         | -            | Text of the button.                                                                                                 |
| icon          | -            | Name of the icon or JSX.Element for icon.                                                                           |
| iconPos       | `left`       | Position of the icon, valid values are "left", "right", "top" and "bottom".                                         |
| badge         | -            | Value of the badge.                                                                                                 |
| badgeClassName| -            | Style class of the badge.                                                                                           |
| tooltip       | -            | Content of the tooltip.                                                                                             |
| tooltipOptions| -            | Configuration of the tooltip, refer to the tooltip documentation for more information.button                        |
| loading       | `false`      | Name of the loading icon or JSX.Element for loading icon.                                                           |
| loadingIcon   | -            | Name of the loading icon or JSX.Element for loading icon.                                                           |
| data-cy       | -            | Unique identifier for testing with cypress.                                                                         |