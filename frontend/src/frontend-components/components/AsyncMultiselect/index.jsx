import React, { useEffect, useRef, useState } from 'react';
import PropTypes from 'prop-types';
import { observer } from 'mobx-react';
import { Skeleton } from 'primereact/skeleton';
import { MultiSelect } from 'primereact/multiselect';
import AsyncMultiselectStore from '../../stores/AsyncMultiselectStore';
import { OverlayPanel } from 'primereact/overlaypanel';

const AsyncMultiselect = observer((props) => {
  const { value, onChange, store, showOverlay } = props;
  const refOp = useRef(null);
  const [selecting, setSelecting] = useState(false);

  const renderSelectedItems = (selectedList, attribute) => {
    if (selectedList) {
      return selectedList.map((element, idx) => {
        return (
          <div className="p-d-inline-flex gap-1 p-ai-center" key={idx}>
            <i className="pi pi-chevron-circle-right p-mr-2" style={{ color: '#2a5dac' }}></i>
            <span
              style={{
                padding: '2px',
              }}
            >
              {element[attribute]}
            </span>
          </div>
        );
      });
    }
  };

  useEffect(() => {
    store.initialize(value);
  }, []);

  const onFilter = (e) => {
    store.beforeFilter(e);
  };

  const innerOnChange = (e) => {
    const eventList = e.value;
    const fullList = e.value ? eventList.map((value) => store.getItemFromValue(value)) : eventList;
    store.setSelectedItems(fullList);
    onChange(fullList);
  };

  return (
    <>
      <MultiSelect
        {...props}
        showClear
        filter
        filterBy="label"
        optionLabel="label"
        optionValue="value"
        resetFilterOnHide={true}
        selectedItemsLabel="{} itens selecionados"
        emptyFilterMessage="Nenhum registro encontrado"
        options={store.keyedList}
        value={store.valueList(value)}
        onChange={innerOnChange}
        onFilter={onFilter}
        itemTemplate={
          store.loading
            ? () => (
                <div className="p-d-flex p-ai-center p-p-2" style={{ height: '20px' }}>
                  <Skeleton width={'100%'} height="1rem" />
                </div>
              )
            : null
        }
        onMouseOver={(e) => showOverlay && !selecting && refOp.current.toggle(e)}
        onMouseOut={(e) => showOverlay && !selecting && refOp.current.hide(e)}
        onShow={(e) => {
          setSelecting(true);
          showOverlay && refOp.current.hide(e);
        }}
        onHide={() => setSelecting(false)}
        onMouseEnter={(e) => {
          setSelecting(true);
          showOverlay && refOp.current.toggle(e);
        }}
      />
      {showOverlay && (
        <OverlayPanel ref={refOp}>
          <div style={{ overflowY: 'auto', maxHeight: '10rem' }} className="p-d-flex p-flex-column">
            {props.value?.length ? renderSelectedItems(props.value, props.label) : 'Não há itens selecionados.'}
          </div>
        </OverlayPanel>
      )}
    </>
  );
});

AsyncMultiselect.propTypes = {
  id: PropTypes.string,
  value: PropTypes.any,
  onChange: PropTypes.func,
  label: PropTypes.string,
  showOverlay: PropTypes.bool,
  store: PropTypes.objectOf(AsyncMultiselectStore).isRequired,
};

export default AsyncMultiselect;
