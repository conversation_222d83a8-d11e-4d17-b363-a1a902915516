# AsyncMultiSelect

Componente de encapsulamento do MultiSelect para chamadas à outras entidades. Deve possuir um store com instância de `AsyncMultiSelectStore` sendo passado.

## Props

| Propriedade | <PERSON><PERSON> | Descrição                                                                                              |
| ----------- | ------------ | ------------------------------------------------------------------------------------------------------ |
| id          | -            | Identificador do formulário (atributo). _OBS: Passado automático ao ser envolvido por um `FormField`._ |
| value       | -            | Valor da chave da entidade a ser exibida pelo `MultiSelect`                                            |
| onChange    | -            | Função a ser chamada sempre que houver seleção.                                                        |
| store       | -            | Instância do `AsyncMultiSelectStore` que armazena os dados e a listagem exibida no `MultiSelect`.      |
| showOverlay | -            | Quando verdadeira mostra um overlay com os itens selecionados.                                         |
