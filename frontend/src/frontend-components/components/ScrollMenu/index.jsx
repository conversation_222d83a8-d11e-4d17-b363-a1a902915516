import PropTypes from 'prop-types';
import { observer } from 'mobx-react';
import { classNames } from 'primereact/utils';
import FcButton from '../FcButton';
import { useEffect, useRef, useState } from 'react';
import Tooltip from '../Tooltip';
import useDrag from '../../hooks/useDrag';
import './style.scss';

const ScrollMenu = observer(({ links, offsetTopOnScroll = -138, layoutPosition = 'right', title, ...props }) => {
  const [observingLinks, setObservingLinks] = useState([]);
  const [collapsed, setCollapsed] = useState(false);

  const scrollMenuRef = useRef(null);

  useDrag(scrollMenuRef);

  const observerRef = useRef(null);

  useEffect(() => {
    observerRef.current = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        const positionInLinksProp = links.findIndex((link) => link.id === entry.target.id);

        if (entry.isIntersecting) {
          setObservingLinks((prevState) => {
            const newState = [...prevState];

            newState[positionInLinksProp] = entry.target.id;

            return newState;
          });
        } else {
          setObservingLinks((prevState) => {
            const newState = [...prevState];

            const position = newState.findIndex((linkId) => linkId === entry.target.id);

            newState[position] = null;

            return newState;
          });
        }
      });
    });

    const elements = links.map((element) => document.getElementById(element.id)).filter((el) => el);

    elements.forEach((element) => {
      observerRef.current.observe(element);
    });

    return () => {
      elements.forEach((element) => {
        observerRef.current.unobserve(element);
      });
    };
  }, [links]);

  const getFirstLinkIdPriority = () => {
    return observingLinks.find((link) => link);
  };

  const handleClick = (id) => {
    const element = document.getElementById(id);

    if (element) {
      setObservingLinks((prevState) => {
        const newState = [...prevState];

        newState[0] = id;

        return newState;
      });

      const offset = element.offsetTop - offsetTopOnScroll;
      window.scrollTo({
        top: offset,
        behavior: 'smooth',
      });
    }
  };

  return (
    <div
      ref={scrollMenuRef}
      className={classNames(
        'scroll-menu-wrapper flex flex-column p-2',
        layoutPosition === 'left' && 'justify-content-start align-items-start',
        layoutPosition === 'right' && 'justify-content-end align-items-end',
        collapsed && 'scroll-menu-border'
      )}
      {...props}
    >
      {collapsed ? (
        <Tooltip value="Fechar" delayDuration={0}>
          <div>
            <FcButton
              icon="pi pi-times"
              className="p-button-text p-button-danger menu-action-button closer"
              onClick={() => setCollapsed(false)}
            />
          </div>
        </Tooltip>
      ) : (
        <Tooltip value={title ?? 'Abrir'} delayDuration={0}>
          <div>
            <FcButton
              icon="pi pi-list"
              className="p-button-text p-button-success menu-action-button trigger"
              onClick={() => setCollapsed(true)}
            />
          </div>
        </Tooltip>
      )}

      {collapsed && title && (
        <div className="w-full align-items-center justify-content-center flex">
          <span className="menu-title">{title}</span>
        </div>
      )}

      {collapsed && (
        <div
          className={classNames(
            'links-container flex flex-column',
            layoutPosition === 'left' && 'justify-content-start align-items-start',
            layoutPosition === 'right' && 'justify-content-end align-items-end'
          )}
        >
          {links.map((link, index) => {
            return (
              <div className="flex align-items-center justify-content-center link-item">
                <button
                  className={classNames(
                    'flex align-items-center gap-2 cursor-pointer text-lg link-button',
                    layoutPosition === 'right' && 'flex-row-reverse'
                  )}
                  key={`${index}-${link.id}`}
                  onClick={() => handleClick(link.id)}
                >
                  <div className={classNames(getFirstLinkIdPriority() === link.id ? 'ball-active' : 'ball')} />
                  <strong className={classNames(getFirstLinkIdPriority() === link.id ? 'label-active' : 'label')}>
                    {link.label}
                  </strong>
                </button>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
});

ScrollMenu.propTypes = {
  links: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string.isRequired,
      label: PropTypes.string.isRequired,
    })
  ).isRequired,
  offsetTopOnScroll: PropTypes.number,
  layoutPosition: PropTypes.oneOf(['left', 'right']),
  title: PropTypes.string,
};

export default ScrollMenu;
