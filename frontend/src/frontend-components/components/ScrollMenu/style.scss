.scroll-menu-wrapper {
  background-color: white;
  border-radius: 6px;
  &:active {
    cursor: grab;
  }
}

.glass {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
  backdrop-filter: blur(2px);
  -webkit-backdrop-filter: blur(2px);
}

.scroll-menu-border {
  border: 1px solid #e0e0e0;
}

.link-item {
  &:hover {
    .close-button {
      display: flex;
    }
  }
}

.link-button {
  background: transparent;
  border: 0px solid transparent;
  transition: all 0.2s;
  border-radius: 6px;
  color: #bdbdbd;
}

.ball {
  height: 6px;
  width: 6px;
  border: 1px solid #616161;
  border-radius: 50%;
}

.ball-active {
  height: 6px;
  width: 6px;
  border-radius: 50%;
  background-color: #606ec1;
}

.label {
  color: #424242;

  &:hover {
    color: #606ec1;
    text-decoration: underline;
  }
}

.label-active {
  color: #606ec1;

  &:hover {
    text-decoration: underline;
  }
}

.menu-title {
  text-transform: uppercase;
  letter-spacing: 2px;
  color: #424242;
  font-weight: 500;
}

.close-button {
  cursor: pointer;
  display: none;
  background-color: transparent;
  color: #bdbdbd;
  border: 0px solid transparent;
}

.menu-action-button {
  &.trigger {
    color: #22c55e !important;
  }

  &.closer {
    color: #ff3d32 !important;
  }
}

.links-container {
  /* background-color: rgba(33, 33, 33, 0.05); */
  border-radius: 6px;
  transition: background-color 1s;
  padding: 8px;
}
