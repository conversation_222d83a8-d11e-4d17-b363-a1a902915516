import React from 'react';
import { observer } from 'mobx-react';
import PropTypes from 'prop-types';
import FcButton from 'fc/components/FcButton';
import { Dialog } from 'primereact/dialog';
import { InputTextarea } from 'primereact/inputtextarea';
import ReporteDialogStore from '../../stores/ReporteDialogStore';
import <PERSON><PERSON>aptcha from 'react-google-recaptcha';

@observer
class DialogReport extends React.Component {
  store;
  REPORT_CAPTCHA_SITE_KEY = window._env_.REPORT_CAPTCHA_SITE_KEY;

  constructor(props) {
    super(props);

    this.state = { submitted: false, verified: false };
    this.store = this.props.store;

    this.handleCaptchaChange = this.handleCaptchaChange.bind(this);
    this.handleCaptchaExpired = this.handleCaptchaExpired.bind(this);
  }

  componentDidMount() {
    this.store.initialize();
    this.store.setRefCaptcha(React.createRef());
  }

  clean() {
    this.setState({ submitted: false, verified: false });
    this.store.initialize();
    this.store.setRefCaptcha(React.createRef());
  }

  renderfooter() {
    return (
      <FcButton
        label="Enviar"
        onClick={() => {
          !this.submitted && this.setState({ submitted: true });
          this.store.save(() => {
            this.props.onHide();
            this.clean();
          });
        }}
      />
    );
  }

  handleCaptchaChange(value) {
    if (value !== null) {
      this.setState({ verified: true });
    }
  }

  handleCaptchaExpired() {
    this.setState({ verified: false });
  }

  render() {
    let content;

    if (this.store.object) {
      content = (
        <div className="p-fluid p-formgrid p-grid">
          <div className="p-col-12">Notou algum problema durante a utilização do site? Informe abaixo o ocorrido:</div>
          <div className="p-col-12" style={{ marginTop: '1rem' }}>
            <InputTextarea
              className={this.state.submitted && !this.store.object.conteudo && 'p-invalid p-error'}
              autoResize
              style={{ maxHeight: 200, minHeight: 100 }}
              placeholder="Descreva aqui o problema encontrado..."
              value={this.store.object.conteudo}
              onChange={(e) => this.store.setConteudo(e)}
            />
            {this.state.submitted && !this.store.object.conteudo && (
              <small className="p-error">Descreva o problema.</small>
            )}
            <div className="p-d-flex p-jc-center p-mt-1">
              <ReCaptcha
                sitekey={this.REPORT_CAPTCHA_SITE_KEY}
                onChange={this.handleCaptchaChange}
                ref={this.store.refCaptcha}
                onExpired={this.handleCaptchaExpired}
              />
            </div>
            <div className="p-d-flex p-jc-center">
              {this.state.submitted && !this.state.verified && (
                <small className="p-error">Confirme que você não é um robô.</small>
              )}
            </div>
          </div>
        </div>
      );
    } else {
      content = (
        <i
          className="pi pi-spin pi-spinner"
          style={{
            marginTop: '20px',
            marginLeft: 'calc(50% - 20px)',
            fontSize: '2em',
          }}
        ></i>
      );
    }

    return (
      <Dialog
        {...this.props}
        header="Reportar problema"
        style={{ width: '50vw' }}
        footer={this.renderfooter()}
        dismissableMask={true}
        draggable={false}
        onHide={() => {
          this.clean();
          this.props.onHide();
        }}
      >
        {content}
      </Dialog>
    );
  }
}

DialogReport.propTypes = {
  store: PropTypes.objectOf(ReporteDialogStore).isRequired,
  onHide: PropTypes.func,
};

export default DialogReport;
