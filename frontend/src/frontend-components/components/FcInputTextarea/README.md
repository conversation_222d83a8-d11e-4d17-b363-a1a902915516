# FcInputTextarea

Componente `InputTextArea` do primereact com exibição inferior da contagem de caracteres limitado até dada quantidade.

## Props

| Propriedade       | Valor <PERSON> | Descrição                                                                              |
| ----------------- | ------------ | -------------------------------------------------------------------------------------- |
| autoResize        | false        | When present, height of textarea changes as being typed.                               |
| tooltip           | -            | Content of the tooltip.                                                                |
| tooltipOptions    | -            | Configuration of the tooltip, refer to the tooltip documentation for more information. |
| hideRemaningChars | -            | When present, character count is now shown to user.                                    |

Documentação do [InputTextarea] (https://www.primefaces.org/primereact/inputtextarea/).
