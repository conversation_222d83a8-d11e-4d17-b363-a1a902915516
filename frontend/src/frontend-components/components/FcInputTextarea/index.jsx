import './style.scss';
import React from 'react';
import PropTypes from 'prop-types';
import { observer } from 'mobx-react';
import { InputTextarea } from 'primereact/inputtextarea';
import { getNumberUnitThousands } from 'fc/utils/utils';

@observer
class FcInputTextarea extends React.Component {
  constructor(props) {
    super(props);
  }

  render() {
    const copyProps = { ...this.props };
    const textoLimiteCaracteres = ' caracteres restantes';

    if (!this.props.hideRemaningChars) {
      const ruleMaxLength = copyProps.rules?.rules?.filter((r) => {
        if (r.rule === 'isMaxLength') {
          return r;
        }
      });
      const limit = ruleMaxLength?.length && ruleMaxLength[0]?.maxLength;
      return (
        <div>
          <InputTextarea autoResize {...copyProps} maxlength={limit} className="fc-text-area-default" />
          <p>
            {copyProps.value ? getNumberUnitThousands(limit - copyProps.value.length) : getNumberUnitThousands(limit)}
            {textoLimiteCaracteres}
          </p>
        </div>
      );
    } else {
      return <InputTextarea {...copyProps} />;
    }
  }
}

FcInputTextarea.propTypes = {
  value: PropTypes.any,
  hideRemaningChars: PropTypes.bool,
};

export default FcInputTextarea;
