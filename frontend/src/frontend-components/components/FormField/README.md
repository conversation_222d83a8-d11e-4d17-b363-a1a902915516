# FormField

Componente de encapsulamento de campos de formulário, já aplicando lógicas de validação.

## Props

| Propriedade | Val<PERSON> | Descrição                                                                                                           |
| ----------- | ------------ | ------------------------------------------------------------------------------------------------------------------- |
| attribute   | -            | Atributo do `object` a ser acessado pelo formulário.                                                                |
| label       | -            | Título do campo de formulário.                                                                                      |
| rule        | `{}`         | Objeto que contém a regra do campo de formulário.                                                                   |
| submitted   | `false`      | Estado de submissão fomulário (já houve tentativa de sumissão ou não - utilizado para exibir a validação da regra). |
| checkbox    | `false`      | Informa se o componente de formulário em questão é um checkbox.                                                     |
| columns     | `12`         | A propriedade para o número de colunas do grid no formulário pode ser configurada de duas formas: como um valor inteiro de 1 a 12, que fixa o número de colunas para todos os tamanhos de tela, ou como um objeto no formato `{sm: 12, md: 12, lg: 12, xl: 12}` para tornar o layout responsivo. As medidas de referência (min-width) são: `sm`: 576px, `md`: 768px, `lg`: 992px e `xl`: 1200px.                                                                           |                                                       
