import { observer } from 'mobx-react';
import PropTypes from 'prop-types';
import React from 'react';
import { FileUpload } from 'primereact/fileupload';
import FileUploadStore from '../../stores/MultipleFileUploaderStore';
import { Dropdown } from 'primereact/dropdown';
import './style.scss';
import { ConfirmDialog } from 'primereact/confirmdialog';
import moment from 'moment';
import 'moment/locale/pt-br';
import Fc<PERSON>utton from '../FcButton';
import { Dialog } from 'primereact/dialog';
import HelpIcon from './HelpIcon';
import { DataView, DataViewLayoutOptions } from 'primereact/dataview';
import { Card } from 'primereact/card';
import { formatBytes, getLightenColor, getOriginUrl, getValueByKey, showNotification } from '../../utils/utils';
import { InputTextarea } from 'primereact/inputtextarea';
import { classNames } from 'primereact/utils';
import { Image } from 'primereact/image';
import { screenBreakpoints } from '../../constants/screenBreakpoints';
import { Tag } from 'primereact/tag';
import Tooltip from '../Tooltip';

@observer
class MultipleFileUploader extends React.Component {
  constructor(props) {
    super(props);
    this.ref = React.createRef();
    this.store = props.store;

    this.state = {
      visibleDialogRemove: false,
      rowDataRemove: null,
      viewFileUrl: undefined,
      showVisualizationDialog: false,
      arquivos: [],
      showDialogDescription: false,
      selectedType: null,
      descricaoArq: '',
      currentFile: null,
      descricoesArquivosMap: {},
      selectedTypes: {},
      windowWidth: window.innerWidth,
      originalOrder: this._storeOriginalOrder(this.store.tipoArquivoEnum),
      currentPage: 0,
    };

    this._renderButtons = this._renderButtons.bind(this);
    this._onUploadFile = this._onUploadFile.bind(this);
    this._renderGridItem = this._renderGridItem.bind(this);
    this._itemTemplate = this._itemTemplate.bind(this);
    this._renderListItem = this._renderListItem.bind(this);
    this._renderHeader = this._renderHeader.bind(this);
    this._renderCardItem = this._renderCardItem.bind(this);
    this._handleResize = this._handleResize.bind(this);
    this._onPageChange = this._onPageChange.bind(this);
    this._renderRequiredFilesIcon = this._renderRequiredFilesIcon.bind(this);
  }

  componentDidMount() {
    this.store.removeFilesState = () => this.setState({ arquivos: [] });
    this.store.setFilesState = (files) => this.setArquivos(files);
    const { files } = this.props;

    files && this.store.initialize(this.props.files);
    this.setArquivos(this.store.keyedUploadedFiles);
    window.addEventListener('resize', this._handleResize);
  }

  componentWillUnmount() {
    window.removeEventListener('resize', this._handleResize);
  }

  _handleResize = () => {
    this.setState({
      windowWidth: window.innerWidth,
    });
  };

  setArquivos(arquivos) {
    const filesToUpdateKey = [];
    const uploadFiles = arquivos.map((file, idx) => {
      const keyValue = moment().toISOString() + idx;
      const keydFile = { ...file, key: keyValue };
      filesToUpdateKey.push(keydFile);
      const arquivo = {
        ...file,
        key: keyValue,
        arquivo: file.arquivo?.nomeOriginal,
        dataEnvio: file.dataEnvio,
        descricao: file.descricao,
        tipo: file.tipo,
        tamanho: file.size,
      };

      if (file?.fase) arquivo.fase = file?.fase;

      return arquivo;
    });
    this.store.updateUploadedFilesKeys(filesToUpdateKey);
    const selectedTypes = {};
    uploadFiles.forEach((file) => {
      selectedTypes[file.key] = file.tipo;
    });
    this.setState({ arquivos: uploadFiles, selectedTypes });
  }

  _findByKey(list, key) {
    return list.find((element) => element.key === key);
  }

  _onUploadFile(arquivo, callbackSuccess, callbackFail) {
    const callback = (fileList) => {
      this._onChangeFiles(fileList);
      callbackSuccess();
    };
    this.store.uploadFile(
      { arquivo: arquivo.file, descricao: arquivo.descricao, tipoArquivo: arquivo.tipo, key: arquivo.key },
      callback,
      callbackFail
    );

    this.forceUpdate();
  }

  _onPageChange(event) {
    this.setState({ currentPage: event.first / event.rows });
  }

  getCardTags(fileData, tags) {
    return tags?.map((fieldItem, index) => {
      let listKeyValue = fileData[fieldItem.field];
      fieldItem.body && (listKeyValue = fieldItem.body(fileData));
      return (
        <Tooltip key={index} value={fieldItem.tooltip} sideOffset={0}>
          <div style={{ cursor: 'default' }}>
            <Tag
              icon={fieldItem.icon}
              style={{
                color: fieldItem.color,
                backgroundColor: getLightenColor(fieldItem.color, 0.7),
                border: `1px solid ${fieldItem.color}`,
              }}
            >
              <span> {listKeyValue}</span>
            </Tag>
          </div>
        </Tooltip>
      );
    });
  }

  _getFilteredList() {
    const { filterTypes } = this.props;
    return filterTypes
      ? this.state.arquivos.filter(
          (f) =>
            ((!filterTypes.included || filterTypes.included.includes(f.tipo)) &&
              (!filterTypes.excluded || !filterTypes.excluded.includes(f.tipo)) &&
              (!f.fase || !filterTypes.filter || filterTypes.filter.values.includes(f[filterTypes.filter.column]))) ||
            !f.tipo
        )
      : this.state.arquivos;
  }

  _onChangeFiles(fileList) {
    const { onChangeFiles } = this.props;
    onChangeFiles(fileList);
  }

  _renderRemoveFileDialog() {
    const message = `${
      this.state.rowDataRemove.idArquivo ? 'Este arquivo está persistido e será removido permanentemente. ' : ''
    }Você realmente deseja remover este arquivo?`;

    let arquivos = [...this.state.arquivos];
    const arquivo = this._findByKey(this.store.keyedUploadedFiles, this.state.rowDataRemove?.key);
    arquivos = arquivos.filter((arquivo) => arquivo.key !== this.state.rowDataRemove?.key);

    return (
      <ConfirmDialog
        visible={this.state.visibleDialogRemove}
        message={message}
        header="Excluir Arquivo"
        icon="pi pi-exclamation-triangle"
        acceptClassName="p-button-danger"
        accept={() => {
          this.store.removeFile(arquivo, (fileList) => this._onChangeFiles(fileList));
          const updatedSelectedTypes = { ...this.state.selectedTypes };
          delete updatedSelectedTypes[this.state.rowDataRemove?.key];

          this.setState({
            visibleDialogRemove: false,
            rowDataRemove: null,
            arquivos,
            selectedTypes: updatedSelectedTypes,
          });
          this.props.ShowSaveRemoveNotifications && showNotification('info', null, 'Arquivo removido com sucesso!');
        }}
        onHide={() => this.setState({ visibleDialogRemove: false, rowDataRemove: null })}
      />
    );
  }

  _renderDescriptionDialog(fileData) {
    const currentDescription = this.state.descricoesArquivosMap[fileData.key] ?? '';
    return (
      <Dialog
        header="Descrição"
        className="sm:w-26rem lg:w-17rem xl:w-30rem"
        visible={this.state.showDialogDescription}
        onHide={() => this.setState({ showDialogDescription: false })}
        footer={
          <div>
            <FcButton
              label="Cancelar"
              icon="pi pi-times"
              className="p-button-danger"
              onClick={() => this.setState({ showDialogDescription: false })}
            />
            <FcButton
              label="Salvar"
              icon="pi pi-check"
              onClick={() => {
                this.setState({
                  descricoesArquivosMap: { ...this.state.descricoesArquivosMap, [fileData.key]: currentDescription },
                  showDialogDescription: false,
                });
                this.store.updateFiles(
                  fileData.key,
                  'descricao',
                  currentDescription,
                  this._findByKey(this.state.arquivos, fileData.key),
                  (fileList) => this._onChangeFiles(fileList)
                );
              }}
            />
          </div>
        }
      >
        <InputTextarea
          type="text"
          value={currentDescription}
          rows={5}
          cols={30}
          style={{ width: '100%' }}
          placeholder="Informe a descrição"
          disabled={this.props.downloadOnly}
          onChange={(e) => {
            this.setState({
              descricoesArquivosMap: { ...this.state.descricoesArquivosMap, [fileData.key]: e.target.value },
            });
          }}
        />
      </Dialog>
    );
  }

  _renderButtons(rowData) {
    const { downloadOnly, countDownloadRequest } = this.props;

    if (downloadOnly) {
      return (
        <div className="flex justify-content-end">
          <FcButton
            type="button"
            className="p-button-text toggle-button"
            tooltipOptions={{ position: 'top' }}
            tooltip="Download"
            icon="pi pi-download"
            onClick={() => {
              const arquivo = this._findByKey(this.store.keyedUploadedFiles, rowData.key);
              this.store.downloadFile(arquivo.arquivo, countDownloadRequest);
            }}
          />
        </div>
      );
    } else {
      return (
        <div className="flex justify-content-end">
          {
            <FcButton
              type="button"
              tooltip="Descrição"
              tooltipOptions={{ position: 'top' }}
              icon="pi pi-comment"
              className="p-button-info p-button-rounded p-button-text"
              onClick={() => {
                this.setState({
                  showDialogDescription: true,
                  currentFile: rowData,
                  descricaoArq: this.state.descricoesArquivosMap[rowData.key] ?? rowData.descricao ?? '',
                });
              }}
            />
          }
          <FcButton
            type="button"
            className="p-button-text toggle-button"
            tooltip="Download"
            icon="pi pi-download"
            tooltipOptions={{ position: 'top' }}
            onClick={() => {
              const arquivo = this._findByKey(this.store.keyedUploadedFiles, rowData.key);
              this.store.downloadFile(arquivo.arquivo, countDownloadRequest);
            }}
          />
          <FcButton
            type="button"
            tooltip="Remover"
            icon="pi pi-trash"
            tooltipOptions={{ position: 'top' }}
            className="p-button-rounded p-button-text p-button-danger"
            onClick={() => this.setState({ visibleDialogRemove: true, rowDataRemove: rowData })}
          />
        </div>
      );
    }
  }

  viewDoc(fileData) {
    const { countDownloadRequest } = this.props;
    const arquivo = this._findByKey(this.store.keyedUploadedFiles, fileData.key);
    this.store.downloadFile(arquivo.arquivo, countDownloadRequest, (viewFileUrl) =>
      this.setState({ showVisualizationDialog: true, viewFileUrl })
    );
  }

  checkTypeDoc(fileData) {
    const arquivo = this._findByKey(this.store.keyedUploadedFiles, fileData.key);
    const nomeOriginal = arquivo?.arquivo?.nomeOriginal;
    return (
      nomeOriginal?.endsWith('.pdf') ||
      nomeOriginal?.endsWith('.png') ||
      nomeOriginal?.endsWith('.jpeg') ||
      nomeOriginal?.endsWith('.jpg')
    );
  }

  _renderCardItem(fileData, columnSize) {
    const { showFileType, downloadOnly, tags } = this.props;
    const originUrl = getOriginUrl();
    const fileName = fileData.arquivo.toLowerCase();
    const fileType = this._getFileType(fileName);
    const imageUrl = this._getFileTypeImage(originUrl, fileType);
    const valueType =
      this._findByKey(this.store.uploadedFiles, fileData.key)?.tipo ??
      this._findByKey(this.state.arquivos, fileData.key)?.tipo ??
      fileData?.tipo;
    const formattedSize = this._getFormattedSize(showFileType, downloadOnly, fileData);

    return (
      <div className={`card-wrap ${columnSize}`}>
        <Card className="card-grid">
          <div className="flex align-items-center justify-content-between">
            <div className="flex w-full align-items-center justify-content-between">
              <Image
                imageClassName="cursor-pointer"
                imageStyle={{
                  paddingTop: '0.5rem',
                  height: '6rem',
                  padding: '0.5rem',
                }}
                src={imageUrl}
                onClick={() => {
                  this.checkTypeDoc(fileData) && this.viewDoc(fileData);
                }}
              />
              <div className="w-full">
                <div
                  className="flex card-title cursor-pointer"
                  onClick={() => {
                    this.checkTypeDoc(fileData) && this.viewDoc(fileData);
                  }}
                >
                  <div className="flex flex-column w-full">
                    <div>{fileData.arquivo}</div>
                    <div>{this._renderDate(fileData)}</div>
                  </div>
                </div>
                <div className="card-cont-desc">
                  {formattedSize}
                  {this._renderDescription(fileData)}
                </div>
                {this.props.fileTypes.length > 0 && (
                  <div className="flex card-cont-tipo">
                    {this._renderDocumentTypeDropdown(showFileType, downloadOnly, valueType, fileData)}
                  </div>
                )}
              </div>
            </div>
            {tags ? <div className="pt-5">{this._renderButtons(fileData)}</div> : this._renderButtons(fileData)}
          </div>
          {tags && <div className="pl-2 flex gap-1 flex-wrap">{this.getCardTags(fileData, tags)}</div>}
        </Card>
      </div>
    );
  }

  _handleTypeChange = (fileData, e) => {
    const { value } = e;
    const { selectedTypes } = this.state;

    const newSelectedTypes = { ...selectedTypes, [fileData.key]: value };

    this.setState(
      {
        selectedTypes: newSelectedTypes,
      },
      () => {
        this.store.updateFiles(
          fileData.key,
          'tipo',
          value,
          this._findByKey(this.state.arquivos, fileData.key),
          (fileList) => {
            this._onChangeFiles(fileList);
            this.forceUpdate();
          }
        );
      }
    );
  };

  _storeOriginalOrder(files) {
    return files.reduce((acc, file, index) => {
      acc[file.value] = index;
      return acc;
    }, {});
  }

  _renderDocumentTypeDropdown(showFileType, downloadOnly, valueType, fileData) {
    const { selectedTypes } = this.state;

    const options = this._sortRequiredFiles(this.store.tipoArquivoEnum, selectedTypes).map((option) => ({
      ...option,
      className: classNames({
        'selected-option': Object.values(selectedTypes).includes(option.value),
      }),
    }));

    if (!downloadOnly && showFileType) {
      return (
        <>
          <div className="space-type">Tipo do documento:</div>
          <div className="dropdown-bord">
            <Dropdown
              value={selectedTypes[fileData.key] || valueType}
              className="sm:w-5rem lg:w-10rem xl:w-15rem"
              options={options}
              optionLabel="text"
              optionValue="value"
              onChange={(e) => this._handleTypeChange(fileData, e)}
              placeholder="Selecione um tipo"
            />
          </div>
        </>
      );
    } else {
      return (
        <div>
          {`Tipo do Documento: ${getValueByKey(
            this._findByKey(this.state.arquivos, fileData?.key)?.tipo ?? fileData.tipo,
            this.props.fileTypes
          )}`}
        </div>
      );
    }
  }

  _renderDate({ dataEnvio }) {
    if (!dataEnvio || !moment(dataEnvio).isValid()) {
      return null;
    }

    const formattedDate = moment(dataEnvio).format('DD/MM/YYYY HH:mm');
    return <div className="gray-text ml-0">{`Data de Envio: ${formattedDate}`}</div>;
  }

  _renderDescription(fileData) {
    const descricao = this.state.descricoesArquivosMap[fileData.key] || fileData.descricao;
    return descricao ? (
      <p className="limited-text">{descricao}</p>
    ) : this.state.selectedTypes[fileData.key] === 'OUTROS_DOCUMENTOS' ? (
      <i className="pi pi-exclamation-triangle ml-2 red-text space-type">Descrição não informada</i>
    ) : (
      <div className="gray-text ml-0">Descrição não informada (opcional)</div>
    );
  }

  _getFormattedSize(showFileType, downloadOnly, fileData) {
    if (downloadOnly) {
      return fileData?.tamanho && <div className="mr-1">{formatBytes(fileData.tamanho)}</div>;
    } else if (!downloadOnly && showFileType) {
      const size = fileData?.file?.size || fileData?.tamanho;
      return size && <div className="mr-1">{formatBytes(size)}</div>;
    }
  }

  _getFileTypeImage(originUrl, fileType) {
    const fileTypesImages = {
      xls: `${originUrl}/assets/images/xls.png`,
      doc: `${originUrl}/assets/images/doc.png`,
      pdf: `${originUrl}/assets/images/pdf.png`,
      xlsx: `${originUrl}/assets/images/xlsx.png`,
      docx: `${originUrl}/assets/images/docx.png`,
      csv: `${originUrl}/assets/images/csv.png`,
      jpg: `${originUrl}/assets/images/jpg.png`,
      png: `${originUrl}/assets/images/pngImage.png`,
    };
    return fileTypesImages[fileType];
  }

  _getFileType(fileName) {
    return fileName.slice(-4) === 'xlsx' || fileName.slice(-4) === 'docx' ? fileName.slice(-4) : fileName.slice(-3);
  }

  _renderListItem(fileData) {
    return this._renderCardItem(fileData, 'p-col-12');
  }

  _renderGridItem(fileData) {
    return this._renderCardItem(fileData, 'p-col-6');
  }

  _itemTemplate(arquivo, layout) {
    if (!arquivo) return;

    if (layout === 'grid') return this._renderGridItem(arquivo);
    else if (layout === 'list') return this._renderListItem(arquivo);
  }

  _renderHeader() {
    const { downloadOnly, accept, multi } = this.props;
    const disabledUpload = downloadOnly || (!multi && this.state.arquivos?.length > 0);
    if (this.state.windowWidth > screenBreakpoints.sm) {
      return (
        <div className="flex justify-content-between">
          {!disabledUpload && (
            <div className="p-col-3 pl-0">
              <div className="flex align-items-center gap-1">
                <FileUpload
                  ref={this.ref}
                  chooseLabel={`Adicionar Arquivo${multi ? 's' : ''}`}
                  name={`file-uploader-${new Date().toString()}`}
                  accept={accept}
                  maxFileSize={30000000}
                  mode="basic"
                  auto
                  uploadHandler={(event) => this._uploadHandler(event)}
                  customUpload
                  multiple
                />
                <HelpIcon documentos={this._sortRequiredFiles(this.store.tipoArquivoEnum)} />
                {this._renderRequiredFilesIcon()}
              </div>
            </div>
          )}
          <DataViewLayoutOptions layout={this.store.selectedLayout} onChange={(e) => this.store.setlayout(e.value)} />
        </div>
      );
    } else {
      if (this.store.selectedLayout !== 'list') this.store.setlayout({ layout: 'list' });
      return (
        <div className="flex justify-content-between">
          {!disabledUpload && (
            <div className="p-col-3 pl-0">
              <div className="flex align-items-center gap-1">
                <FileUpload
                  ref={this.ref}
                  chooseLabel={`Adicionar Arquivo${multi ? 's' : ''}`}
                  name={`file-uploader-${new Date().toString()}`}
                  accept={accept}
                  maxFileSize={30000000}
                  mode="basic"
                  auto
                  uploadHandler={(event) => this._uploadHandler(event)}
                  customUpload
                  multiple
                />
                <HelpIcon documentos={this._sortRequiredFiles(this.store.tipoArquivoEnum)} />
                {this._renderRequiredFilesIcon()}
              </div>
            </div>
          )}
        </div>
      );
    }
  }

  _renderRequiredFilesIcon() {
    const hasAllRequiredFiles = this.store.hasAllRequiredFiles;

    if (hasAllRequiredFiles === null) {
      return null;
    }
    return hasAllRequiredFiles ? (
      <Tooltip value="Todos os arquivos obrigatórios foram adicionados">
        <i className="pi pi-check-circle" style={{ color: 'green' }}></i>
      </Tooltip>
    ) : (
      <Tooltip value="Ainda existem arquivos obrigatórios que não foram adicionados">
        <i className="pi pi-exclamation-triangle" style={{ color: 'red' }}></i>
      </Tooltip>
    );
  }

  _renderCards() {
    const filteredFilesList = this._getFilteredList();

    return (
      <DataView
        key={JSON.stringify(this.store.uploadedFiles)}
        header={this._renderHeader()}
        value={filteredFilesList}
        layout={this.store.selectedLayout}
        itemTemplate={this._itemTemplate}
        paginator
        emptyMessage="Nenhum arquivo adicionado."
        rows={20}
        onPage={this._onPageChange}
        first={this.state.currentPage * 20}
      />
    );
  }

  _renderVisualizationDialog() {
    return (
      <Dialog
        visible={this.state.showVisualizationDialog}
        onHide={() => this.setState({ showVisualizationDialog: false, showFileUrl: undefined })}
        style={{ width: '90vw', height: '90vh' }}
        className="show-file-overflow-fix"
        draggable={false}
      >
        {this.state.viewFileUrl && this._renderVisualizationContent()}
      </Dialog>
    );
  }

  _renderVisualizationContent() {
    return <iframe frameBorder="0" style={{ width: '100%', height: '100%' }} src={this.state.viewFileUrl}></iframe>;
  }

  _uploadHandler(event) {
    const arquivos = [...this.state.arquivos];

    Object.keys(event.files).forEach((key) => {
      const arq = {
        key: moment().toISOString() + key,
        arquivo: event.files[key]?.name,
        dataEnvio: moment(),
        file: event.files[key],
        tipo: '',
        descricao: '',
        tamanho: event.files[key]?.size,
      };
      this.setState({ currentFile: arq });
      this._onUploadFile(arq, () => {
        arquivos.push(arq);
        this.setState({ arquivos: arquivos }, () => {
          this.forceUpdate();
        });
      });
    });

    event.options?.clear();

    if (this.props.ShowSaveRemoveNotifications) {
      const message = event.files.length > 1 ? 'Arquivos adicionados com sucesso!' : 'Arquivo adicionado com sucesso!';
      showNotification('info', null, message);
    }
  }

  _sortRequiredFiles(files, selectedTypes = {}) {
    const originalOrder = this.state.originalOrder;

    const selectedValues = Object.values(selectedTypes);
    const filteredFiles = files.filter((f) => !selectedValues.includes(f.value));

    const sortedFiles = filteredFiles.sort((f1, f2) => {
      const requiredF1 = f1?.text?.startsWith('*');
      const requiredF2 = f2?.text?.startsWith('*');

      if (requiredF1 && !requiredF2) {
        return -1;
      } else if (!requiredF1 && requiredF2) {
        return 1;
      } else {
        return originalOrder[f1.value] - originalOrder[f2.value];
      }
    });

    selectedValues.forEach((selectedType) => {
      const selectedFile = files.find((f) => f.value === selectedType);
      if (selectedFile) {
        sortedFiles.push(selectedFile);
      }
    });

    return sortedFiles;
  }

  render() {
    const { downloadOnly } = this.props;

    if (!this.props.fileTypes?.length || this.store.tipoArquivoEnum?.length > 0 || downloadOnly) {
      return (
        <div className="p-fluid p-formgrid p-grid">
          <div className="p-col-12">{this._renderCards()}</div>
          {this._renderVisualizationDialog()}
          {this.state.showDialogDescription && this._renderDescriptionDialog(this.state.currentFile)}
          {this.state.visibleDialogRemove && this._renderRemoveFileDialog()}
        </div>
      );
    } else {
      return <></>;
    }
  }
}

MultipleFileUploader.defaultProps = {
  downloadOnly: false,
  fileTypes: [],
  showFileType: true,
  accept: '.pdf',
  view: true,
  multi: true,
  ShowSaveRemoveNotifications: false,
};

MultipleFileUploader.propTypes = {
  tags: PropTypes.array,
  store: PropTypes.instanceOf(FileUploadStore).isRequired,
  onChangeFiles: PropTypes.func,
  downloadOnly: PropTypes.bool,
  files: PropTypes.any,
  filterTypes: PropTypes.object,
  fileTypes: PropTypes.array,
  showFileType: PropTypes.bool,
  countDownloadRequest: PropTypes.bool,
  accept: PropTypes.string,
  view: PropTypes.bool,
  multi: PropTypes.bool,
  ShowSaveRemoveNotifications: PropTypes.bool,
};

export default MultipleFileUploader;
