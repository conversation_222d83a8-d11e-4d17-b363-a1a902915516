.show-file-overflow-fix {
  .p-dialog-content {
    overflow: hidden !important;
  }
}

.datatable-editing-demo .editable-cells-table td.p-cell-editing {
  padding-top: 0;
  padding-bottom: 0;
}

.card-wrap {
  padding: 0.25rem !important;
}

.card-grid {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  border: 1px solid RGB(220, 220, 220);
  border-radius: 6px;
  position: relative;
  height: 102%;
  background: #fafafa;
  padding: 0;
}

.p-card-content {
  padding: 0 !important;
}

.card-cont-desc {
  overflow: hidden;
  text-overflow: ellipsis;
  flex-direction: row;
  display: flex;
  font-size: 0.9rem;
  color: #a1a1aa;
}

.red-text {
  color: red;
}

.gray-text {
  color: #a1a1aa;
}

.space-type {
  margin-right: 1rem;
}

.selected-option {
  color: #22c55e !important;
}

.card-cont-tipo {
  overflow: hidden;
  text-overflow: ellipsis;
  display: flex;
  align-items: center;
  flex-direction: row;
  flex-wrap: wrap;
  font-size: 0.8rem;
  padding-top: 0.2rem;
}

.card-title {
  color: #24292f;
  font-size: 0.9rem;
  font-weight: 500;
  padding-top: 0.5rem;
  word-break: break-all;
}

.dropdown-bord .p-dropdown {
  border: 0;
  height: 2.5rem !important;
}

.dropdown-bord .p-dropdown-label {
  font-size: 0.9rem;
}

.dropdown-bord .p-component .p-inputwrapper {
  max-width: 0%;
}

.limited-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

.limited-text:hover {
  white-space: normal;
  overflow: visible;
  text-overflow: inherit;
}

.p-dataview.p-dataview-list .p-dataview-content > .p-grid > div {
  border: none;
}
