# MulipleFileUploader

Componente que auxilia no gerenciamento de upload e download de arquivos, além de prover também a listagem dos mesmos com pré-visualização em `Dialog`.

## Props

| Propriedade          | <PERSON><PERSON> | Descrição                                                                                                                                                                                                                                                                                                                   |
| -------------------- | ------------ | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| tags                 | -            | tags a serem exibidas com informações a mais que os dados básicos de um arquivo. Deverá ser um array contendo objetos seguindo o mesmo padrão passado aos componentes `DataTable` e `IndexDataTable`.                                                                                                                       |
| store                | -            | Deverá ser passada uma instância do `FileUploaderStore`, criada com os parâmetros significativos ao contexto do componente, de acordo com o seu construtor.                                                                                                                                                                 |
| onChangeFiles        | -            | Função de callback a ser chamada a cada alteração nos arquivos presentes na listagem do componente (normalmente, função que atualiza o objeto que irá mandar os arquivos para upload definitivo). Para esta função será passada uma lista, caso o valor da prop `multiple` seja `true`, ou um objeto único, caso contrário. |
| downloadOnly         | `false`      | Indica se o componente deverá funcionar no modo de apenas download/visualização, sem haver possibilidade de fazer upload de novos arquivos ou remover uploads existentes.                                                                                                                                                   |
| files                | `[]`         | Inicialização de arquivos já persistidos, no caso de haver edição e já haverem arquivos persistidos.                                                                                                                                                                                                                        |
| fileTypes            | `[]`         | Array com objetos utilizados na exibição dos valores nominais dos tipos dos arquivos                                                                                                                                                                                                                                        |
| filterTypes          |              | Objeto com utilizado para filtrar lista de tipos de arquivos de acordo com a fase da licitação sendo criada/editada                                                                                                                                                                                                         |
| showFileType         | `true`       | Boolean que indica se o tipo do arquivo será editável                                                                                                                                                                                                                                                                       |
| countDownloadRequest | `true`       | Contador de donwloads                                                                                                                                                                                                                                                                                                       |
| accept               | -            | Tipos de arquivos aceitos                                                                                                                                                                                                                                                                                                   |
| multi                | `True`       | Usado para selecionar vários arquivos de uma vez na caixa de diálogo de arquivos.                                                                                                                                                                                                                                           | chooseLabel          | `Adicionar Arquivo` ou `Adicionar Arquivos`(se for multi)       | Texto exibido no botão de adicionar arquivos.
| emptyMessage         | `Nenhum arquivo adicionado.`       | Texto exibido no componente quando nenhum arquivo foi adicionado.
| max                  | `Number.MAX_SAFE_INTEGER`       | Número para limitar a quantidade de uploads de arquivos.
| isObra               | `False`       | Usado quando o componente estiver sendo usado em uma tela do GEOOBRAS
| faseObra             | -       | Indica a fase a que o arquivo pertence no GEOOBRAS 
| chooseLabel             | `Adicionar Arquivo` ou `Adicionar Arquivos` | Indica o texto exibido no botão do componente