import React, { useRef, useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { observer } from 'mobx-react';
import { MultiSelect } from 'primereact/multiselect';
import { OverlayPanel } from 'primereact/overlaypanel';
import { getValueByKey } from '../../utils/utils';

const FcMultiSelect = observer((props) => {
  const { showOverlay, optionValue, optionLabel, value, options, disabled, sortable } = props;
  const refOp = useRef(null);
  const containerRef = useRef(null);
  const [selecting, setSelecting] = useState(false);
  const [dropdownWidth, setDropdownWidth] = useState('100%');

  const optionsSorted = sortable
    ? options.slice().sort((a, b) => a[optionLabel].localeCompare(b[optionLabel]))
    : options;

  useEffect(() => {
    const updateWidth = () => {
      if (containerRef.current) {
        const width = containerRef.current.getBoundingClientRect().width;
        setDropdownWidth(`${width}px`);
      }
    };
    updateWidth();
    window.addEventListener('resize', updateWidth);
    return () => window.removeEventListener('resize', updateWidth);
  }, []);

  const renderSelectedItems = () => {
    if (value) {
      return value.map((element, idx) => {
        return (
          <div className="p-d-inline-flex gap-1 p-ai-center" key={idx}>
            <i className="pi pi-chevron-circle-right p-mr-2" style={{ color: '#2a5dac' }} />
            <span
              style={{
                padding: '2px',
              }}
            >
              {optionLabel && optionValue
                ? getValueByKey(element, options, optionValue, optionLabel)
                : optionLabel
                ? element[optionLabel]
                : element}
            </span>
          </div>
        );
      });
    }
  };

  const itemTemplate = (option) => {
    const label = optionLabel ? option[optionLabel] : option;
    return (
      <div
        className="p-multiselect-item"
        title={label}
        style={{
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap',
          padding: '0px',
          cursor: 'pointer',
        }}
      >
        {label}
      </div>
    );
  };

  return (
    <div ref={containerRef} style={{ width: '100%', position: 'relative' }}>
      <MultiSelect
        {...props}
        onMouseOver={(e) => showOverlay && !selecting && refOp.current.toggle(e)}
        onMouseOut={(e) => showOverlay && !selecting && refOp.current.hide(e)}
        onShow={(e) => {
          setSelecting(true);
          showOverlay && refOp.current.hide(e);
        }}
        onHide={() => setSelecting(false)}
        onMouseEnter={(e) => {
          setSelecting(true);
          showOverlay && refOp.current.toggle(e);
        }}
        options={optionsSorted}
        disabled={disabled}
        emptyFilterMessage="Nenhum registro encontrado"
        panelStyle={{
          width: dropdownWidth,
          minWidth: dropdownWidth,
          maxWidth: dropdownWidth,
        }}
        appendTo="self"
        itemTemplate={itemTemplate}
      />
      {showOverlay && (
        <OverlayPanel ref={refOp}>
          <div style={{ overflowY: 'auto', maxHeight: '10rem' }} className="p-d-flex p-flex-column">
            {props.value?.length ? renderSelectedItems(props.value, props.label) : 'Não há itens selecionados.'}
          </div>
        </OverlayPanel>
      )}
    </div>
  );
});

FcMultiSelect.propTypes = {
  id: PropTypes.string,
  optionValue: PropTypes.string,
  optionLabel: PropTypes.string,
  showOverlay: PropTypes.bool,
  value: PropTypes.array,
  options: PropTypes.array,
  disabled: PropTypes.bool,
  sortable: PropTypes.bool,
};

FcMultiSelect.defaultProps = {
  sortable: true,
};

export default FcMultiSelect;
