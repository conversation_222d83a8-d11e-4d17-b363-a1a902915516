# IndexDataTable

Componente de tabela especialmente criado para componentes de index.

## Props

| Propriedade          | Valor <PERSON> | Descrição                                                                                    |
|----------------------|--------------|----------------------------------------------------------------------------------------------|
| disableColumnToggle  | `false`      | Flag para habilitar/desabilitar componente de seleção de colunas a serem exibidas na tabela. |
| highlightOnSelection | `false`      | Flag para habilitar/desabilitar o destaque para uma linha selecionada                        |
| selected             | `null`       | Representa o objeto correspondente à linha selecionada.                                      |

_OBS: <PERSON><PERSON><PERSON> propriedades são tais quais as presentes na documentação do DataTable do PrimeFaces._
