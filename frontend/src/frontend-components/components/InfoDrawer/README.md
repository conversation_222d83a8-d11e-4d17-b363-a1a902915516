# InfoDrawer

Componente de exibição estruturada para detalhamento de dados de listagem num drawer.

## Props

| Propriedade   | <PERSON><PERSON> | Descrição                                                                                                    |
| ------------- | ------------ | ------------------------------------------------------------------------------------------------------------ |
| visible       | false        | Altera o estado de exibição do drawer contendo os dados.                                                     |
| onHide        | -            | Função de callback a ser chamada quando o drawer for "escondido".                                            |
| list          | -            | Lista de dados a serem exibidos pelo componente. Devem obedecer o formato definido conforme descrito abaixo. |
| actionButtons | -            | Lista de objetos de definição de botões de ação. Devem obdecer o formato defindo conforme descrito abaixo.   |

## Formato dos objetos passados na prop `list`

A list deverá conter objetos seguindo o seguinte padrão:

```js
{
    title: 'Detalhes do Contrato',
    children: [
        {
            col: 6,
            label: 'Título',
            value: 'Valor',
        }
    ]
}
```

- title: Título para cada parte de conteúdo no drawer.
- children: Lista de objetos para o conteúdo.

- col: número de colunas do sistema de grid que serão destinadas à exibição do respectivo valor (valor de 1 à 12, por default é 6);
- label: título associado ao valor exibido no drawer;
- value: valor a ser exibido no drawer.

## Formato dos objetos passados na prop `actionButtons`

A prop `actionButtons` deverá consistir numa lista contendo, em formato chave-valor, as props do componente `Button` do próprio primeReact. Consultar documentação do [Button](https://www.primefaces.org/primereact/button/) para mais detalhes.
