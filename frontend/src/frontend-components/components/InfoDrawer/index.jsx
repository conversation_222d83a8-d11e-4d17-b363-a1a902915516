import './style.scss';
import React from 'react';
import PropTypes from 'prop-types';
import { Divider } from 'primereact/divider';
import { Sidebar } from 'primereact/sidebar';
import FcButton from 'fc/components/FcButton';
import PermissionProxy from '../PermissionProxy';

class InfoDrawer extends React.Component {
  constructor(props) {
    super(props);
  }

  renderTitleChildren(item, idx) {
    const style = item.style ?? {};
    return (
      <div key={idx}>
        <h5 style={{ ...style, fontWeight: 'bold', marginTop: '10px' }}>{item.title}</h5>
        <div>{item.children.map((item, idx) => this.renderValue(item, idx))}</div>
      </div>
    );
  }

  renderValue(item, idx) {
    const col = item.col ?? 6;
    const styleMargin = item.label ? '' : '-mt-5';
    return (
      <div key={idx} className={`p-col-${col}`}>
        <div className={`p-col-${col} drawer-content-label ${styleMargin}`}>{item.label}</div>
        <div className={`p-col-${col}`}>{item.value ?? '-'}</div>
      </div>
    );
  }

  render() {
    return (
      <Sidebar
        position="right"
        style={this.props.style}
        className="info-drawer"
        visible={this.props.visible}
        onHide={this.props.onHide}
      >
        <div
          style={{
            float: 'right',
            marginBottom: '1.23rem',
          }}
        >
          {this.props.actionButtons && this.props.actionButtons.length > 0
            ? this.props.actionButtons.map((propsButton) => {
                const button = (
                  <FcButton style={{ marginLeft: '3px' }} tooltipOptions={{ position: 'bottom' }} {...propsButton} />
                );
                if (propsButton.permissionsAttributes)
                  return (
                    <PermissionProxy resourcePermissions={propsButton.permissionsAttributes}>{button}</PermissionProxy>
                  );
                else return button;
              })
            : ''}
        </div>
        <div>
          <Divider />
          <div className="p-grid">{this.props.list.map((item, idx) => this.renderTitleChildren(item, idx))}</div>
          <Divider />
        </div>
      </Sidebar>
    );
  }
}

InfoDrawer.defaultProps = {
  style: { width: '30%' },
};

InfoDrawer.propTypes = {
  visible: PropTypes.bool.isRequired,
  onHide: PropTypes.func.isRequired,
  list: PropTypes.array.isRequired,
  actionButtons: PropTypes.array,
  style: PropTypes.any,
};

export default InfoDrawer;
