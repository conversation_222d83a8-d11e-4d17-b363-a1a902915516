import classNames from 'classnames';
import PropTypes from 'prop-types';
import { useState } from 'react';
import './style.scss';

const ModalImages = (props) => {
  const { images, visible, onHide } = props;

  const [selectedImageIndex, setSelectedImageIndex] = useState(0);

  const nextImage = () => {
    setSelectedImageIndex((prevIndex) => {
      return prevIndex === images.length - 1 ? 0 : prevIndex + 1;
    });
  };

  const prevImage = () => {
    setSelectedImageIndex((prevIndex) => {
      return prevIndex === 0 ? images.length - 1 : prevIndex - 1;
    });
  };

  const onClose = () => {
    onHide && onHide();
  };

  const renderImages = () =>
    images.map((image, index) => (
      <img
        src={image}
        key={`foto-modal-${index}`}
        className={classNames('image', { 'image-visible': selectedImageIndex === index })}
        alt={`Imagem ${index + 1}`}
      />
    ));
  return (
    images?.length > 0 && (
      <div className={classNames('modal', { 'modal-visible': visible })}>
        <div className="modal-content">
          {renderImages()}
          {visible && (
            <>
              {images.length > 1 && (
                <>
                  <span className="pi pi-angle-left button modal-button modal-prev-button" onClick={prevImage} />
                  <span className="pi pi-angle-right button modal-button modal-next-button" onClick={nextImage} />
                </>
              )}
              <span className="pi pi-times button modal-button modal-close-button" onClick={onClose} />
            </>
          )}
        </div>
      </div>
    )
  );
};

ModalImages.propTypes = {
  images: PropTypes.array,
  visible: PropTypes.bool,
  onHide: PropTypes.func,
};

export default ModalImages;
