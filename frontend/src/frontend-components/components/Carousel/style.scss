.carousel-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.carousel-image {
  max-width: 100%;
  max-height: 100%;
}

.modal {
  position: fixed;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.6s;
  transform: translateY(-50%), translateX(-50%);
  z-index: 1;
}

.modal.modal-visible {
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}

.modal-content {
  width: 70%;
  height: auto;
  display: flex;
  justify-content: center;
  align-items: center;
}

.image {
  height: 0px;
  width: 0px;
  opacity: 0.5;
  transition: opacity 1s;
}

.image-visible {
  opacity: 1;
  height: auto;
  width: 80%;
}

.button {
  margin-inline: 3px;
  padding: 10px;
  border-radius: 50px;
}

.modal-button {
  background-color: transparent;
  border: none;
  font-size: 2rem;
  color: #fff;
  cursor: pointer;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}

.button:hover {
  background-color: rgba(179, 177, 177, 0.3);
}

.modal-prev-button {
  left: 20px;
}

.modal-next-button {
  right: 20px;
}

.modal-close-button {
  top: 40px;
  right: 20px;
}
