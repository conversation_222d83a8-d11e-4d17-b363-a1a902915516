import classNames from 'classnames';
import { useState } from 'react';
import ModalImages from './ModalImages';
import PropTypes from 'prop-types';
import './style.scss';

const Carousel = (props) => {
  const { images } = props;

  const [showModal, setShowModal] = useState(false);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);

  const openModal = (index) => {
    setSelectedImageIndex(index);
    setShowModal(true);
  };

  const closeModal = () => {
    setShowModal(false);
  };

  const nextImage = () => {
    setSelectedImageIndex((prevIndex) => {
      return prevIndex === images.length - 1 ? 0 : prevIndex + 1;
    });
  };

  const prevImage = () => {
    setSelectedImageIndex((prevIndex) => {
      return prevIndex === 0 ? images.length - 1 : prevIndex - 1;
    });
  };

  const renderImages = () =>
    images.map((image, index) => (
      <img
        src={image}
        key={`foto-${index}`}
        className={classNames('image', { 'image-visible': selectedImageIndex === index })}
        onClick={() => openModal(index)}
        alt={`Imagem ${index + 1}`}
      />
    ));
  return (
    images?.length > 0 && (
      <div className="carousel-container">
        {images.length > 1 && <span className="pi pi-angle-left button modal-prev-button" onClick={prevImage} />}
        {renderImages()}
        {images.length > 1 && <span className="pi pi-angle-right button modal-next-button" onClick={nextImage} />}
        <ModalImages images={images} visible={showModal} onHide={closeModal} />
      </div>
    )
  );
};

Carousel.propTypes = {
  images: PropTypes.array,
};

export default Carousel;
