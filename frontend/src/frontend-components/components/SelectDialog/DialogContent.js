import React from 'react';
import { observer } from 'mobx-react';
import { DataTable } from 'primereact/datatable';
import GenericIndexPage from 'fc/pages/GenericIndexPage';
import AdvancedSearch from 'fc/components/AdvancedSearch';
import PropTypes from 'prop-types';
import { Column } from 'primereact/column';

@observer
class DialogContent extends GenericIndexPage {
  constructor(props) {
    super(props);

    this.store = this.props.indexStore;
    this._innerOnChange = this._innerOnChange.bind(this);
  }

  _innerOnChange(e) {
    this.props.onSelectRow(e);
    !this.props.isMultiple && this.props.dismiss();
  }

  render() {
    const { listKey, loading } = this.store;
    const { getDefaultTableProps } = this;

    return (
      <>
        <div className="m-1 page index-table">
          <AdvancedSearch
            searchParams={this.store.getAdvancedSearchParams()}
            store={this.store}
            searchFields={this.props.searchFields}
            filterSuggest={this.props.filterSuggest}
            useOr={true}
            alwaysDrawer
          />

          <DataTable
            rowHover
            value={listKey}
            selectionMode={this.props.isMultiple ? 'multiple' : 'single'}
            selection={this.props.selectedElements}
            onSelectionChange={this._innerOnChange}
            loading={loading}
            dataKey="id"
            {...getDefaultTableProps()}
          >
            {this.props.isMultiple && <Column selectionMode="multiple"></Column>}
            {!this.props.isMultiple && this.props.radioMode && (
              <Column selectionMode="single" headerStyle={{ width: '3em' }}></Column>
            )}
            {this.renderColumns(this.props.columns)}
          </DataTable>
        </div>
      </>
    );
  }
}

DialogContent.propTypes = {
  onSelectRow: PropTypes.func,
  isMultiple: PropTypes.bool,
  radioMode: PropTypes.bool,
  selectedElements: PropTypes.array,
};

export default DialogContent;
