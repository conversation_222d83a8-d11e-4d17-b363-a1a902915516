# SelectDialog

Componente composto por três partes, um `InputText` que exibe o valor escolhido, um `Button` com icone de pesquisa e um `Dialog` que exibe uma listagem de itens para o usuario. Deve possuir um store com instância de `IndexBase` sendo passado.

## Props

| Propriedade              | Valor <PERSON> | Descrição                                                                                                     |
| ------------------------ | ------------ | ------------------------------------------------------------------------------------------------------------- |
| id                       | -            | Identificador do formulário (atributo). _OBS: Passado automático ao ser envolvido por um `FormField`._        |
| value                    | -            | Valor da chave da entidade a ser exibida pelo `InputText`                                                     |
| label                    | -            | Atributo da entidade que será exibido no `InputText`.                                                         |
| onChange                 | -            | Função a ser chamada ao haver alteração de seleção.                                                           |
| Indexstore               | -            | Instância do `IndexBase` utilizada no `Dialog` para exibir a listagem de itens.                               |
| headerDialog             | -            | Texto exibido como header do `Dialog`.                                                                        |
| dialogColumns            | -            | Colunas que serão renderizadas na `DataTable` interna do `Dialog`                                             |
| searchFields             | -            | Campos que poderão ser utilizados na filtragem do `AdvancedSearch` interno do `Dialog`                        |
| filterSuggest            | -            | Valores padrões aplicados nos filtros especificos do `AdvancedSearch` interno do `Dialog`.                    |
| emptyMessage             | -            | Mensagem exibida no `InputText` quando nenhuma entidade for selecionada.                                      |
| nullMessage              | -            | Mensagem exibida no `InputText` quando a entidade selecionada não possuir valor no campo definido como label. |
| canCreate                | false        | Define se é possivel cadastrar entidades dentro do `Dialog`.                                                  |
| formPage                 | -            | Instância do `FormBase` utilizada no `Dialog` caso seja possivel cadastrar novas entidades.                   |
| isMultiple               | false        | Booleano que indica se o tipo de seleção do `Dialog` é múltiplo.                                              |
| selectedElements         | -            | Array com os elementos selecionados no `Dialog`.                                                              |
| onSelectMultiple         | undefined    | Função de confirmação de seleção de registros, chamada após a seleção dos registros no `Dialog`.              |
| labelButtonCreate        | -            | Label para o botão que dispara o `Dialog` com seleção múltipla.                                               |
| selectedKeydElementsList | -            | Lista com os elementos selecionados utilizada na tabela externa ao `Dialog`.                                  |
| paginator                | true         | Paginador para tabela com os elementos selecionados na seleção múltipla.                                      |
| rows                     | 5            | Número de linhas exibidas na tabela com os registros selecionados na selecão múltipla.                        |
| columnsSelectMultiple    | -            | Colunas exibidas na tabela com os registros selecionados na seleção múltipla                                  |
| showInput                | true         | Define se o input deve ser exibido                                                                            |
| labelButtonDialog        | -            | Define uma label do botão de abrir o Dialog                                                                   |
| iconButtonDialog         | pi pi-search | Define o icone do botão de abrir o Dialog                                                                     |
