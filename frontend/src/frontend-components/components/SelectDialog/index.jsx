import React from 'react';
import PropTypes from 'prop-types';
import { observer } from 'mobx-react';
import { Dialog } from 'primereact/dialog';
import DialogContent from './DialogContent';
import { InputText } from 'primereact/inputtext';
import FcButton from 'fc/components/FcButton';
import IndexBase from 'fc/stores/IndexBase';
import GenericFormPage from 'fc/pages/GenericFormPage';
import { PrimeIcons } from 'primereact/api';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { showNotification } from '../../utils/utils';
import { ConfirmDialog } from 'primereact/confirmdialog';

@observer
class SelectDialog extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      visibleModal: false,
      visibleModalCreate: false,
      selectedElements: [],
      visiblePopup: false,
    };

    this._onChange = this._onChange.bind(this);
    this._renderDialogModal = this._renderDialogModal.bind(this);
    this._showDialogModal = this._showDialogModal.bind(this);
    this.renderColumns = this.renderColumns.bind(this);
    this._onSelectMultiple = this._onSelectMultiple.bind(this);
  }

  _renderFooter() {
    if (this.props.canCreate) {
      return (
        <div className="p-mt-10 form-actions-dialog">
          <span className="p-d-flex">
            <FcButton
              label="Cancelar"
              type="button"
              className="p-ml-auto p-button-secondary p-mr-2"
              onClick={() => this._showDialogModal(false)}
            />
            {this.props.radioMode && <FcButton label="Selecionar" onClick={() => this._onSelect()} />}
            {this.props.isMultiple && <FcButton label="Selecionar" onClick={() => this._onSelectMultiple()} />}
            {!this.props.disabledCreate && (
              <FcButton className="p-button-info" label="Cadastrar" onClick={() => this._showDialogModalCreate(true)} />
            )}
          </span>
        </div>
      );
    }
  }

  renderColumns(columns) {
    return columns.map((col) => <Column key={`col-${col.field}`} {...col} />);
  }

  renderConfirmPopup(callback) {
    this.onChangeCallback = callback;
    this.setState({ visiblePopup: true });
  }

  closeModal() {
    if (this.props.radioMode) {
      return { dismiss: () => {} };
    } else {
      return { dismiss: () => this._showDialogModal(false) };
    }
  }

  _renderDialogModal() {
    const dismissProp = this.closeModal();
    const onSelectRow = (e) => {
      if (this.props.isMultiple || this.props.radioMode) {
        this._onChange(e);
      } else if (this.props.confirmChange) {
        if (this.props.value.id !== e.value.id) {
          this.renderConfirmPopup(this.props.onChange(e));
        }
      } else {
        this.props.onChange(e);
      }
    };
    return (
      <Dialog
        header={this.props.headerDialog}
        visible={this.state.visibleModal}
        modal
        footer={this._renderFooter()}
        style={{ width: '80vw' }}
        onHide={() => this._showDialogModal(false)}
        draggable={false}
        resizable={false}
      >
        <DialogContent
          indexStore={this.props.indexStore}
          columns={this.props.dialogColumns}
          onSelectRow={onSelectRow}
          searchFields={this.props.searchFields}
          filterSuggest={this.props.filterSuggest}
          isMultiple={this.props.isMultiple}
          selectedElements={
            this.props.radioMode
              ? !Array.isArray(this.state.selectedElements) && this.state.selectedElements
                ? this.state.selectedElements
                : this.props.value
              : this.state.selectedElements
          }
          radioMode={this.props.radioMode}
          {...dismissProp}
        />
      </Dialog>
    );
  }

  _finalizeDialog(entity) {
    this._showDialogModalCreate(false);
    if (entity && !this.props.isMultiple) {
      this._showDialogModal(false);
      this.props.onChange(entity);
    } else if (entity && this.props.isMultiple) {
      this._showDialogModal(false);
      this._showDialogModal(true);
    }
  }

  _renderDialogModalCreate() {
    const FormPage = this.props.formPage;
    return (
      <Dialog
        header={'Cadastro de ' + this.props.headerDialog}
        visible={this.state.visibleModalCreate}
        modal
        style={{ width: '80vw', maxWidth: '750px' }}
        onHide={() => this._showDialogModalCreate(false)}
        draggable={false}
        resizable={false}
      >
        <FormPage
          closeMethod={(entity) => {
            this._finalizeDialog(entity);
            this.props.indexStore.reloadTableData();
          }}
        />
      </Dialog>
    );
  }

  _renderConfirmDialog() {
    const { confirmChange } = this.props;
    return (
      <ConfirmDialog
        visible={this.state.visiblePopup}
        onHide={() => this.setState({ visiblePopup: false })}
        accept={() => {
          this.onChangeCallback && this.onChangeCallback();
          confirmChange.callback && confirmChange.callback();
        }}
        message={confirmChange?.message ?? ''}
        icon="pi pi-exclamation-triangle"
      />
    );
  }

  _showDialogModal(boolean) {
    this.setState({ visibleModal: boolean, selectedElements: this.props.selectedElements ?? [] });
  }

  _showDialogModalCreate(boolean) {
    this.setState({ visibleModalCreate: boolean });
  }

  _onSelect() {
    this.props.onChange(this.state.selectedElements);
    this.setState({ visibleModal: false });
  }

  _onSelectMultiple() {
    this.props.onSelectMultiple(this.state.selectedElements);
    this.setState({ visibleModal: false });
  }

  _onChange(event) {
    let selectedElementsEvent = [];

    if (event && event.value) {
      selectedElementsEvent = event.value;
    } else if (event && event.target) {
      selectedElementsEvent = event.target.value;
    } else if (event) {
      selectedElementsEvent = event;
    }

    if (this.state.selectedElements.length <= selectedElementsEvent.length) {
      this.setState({ selectedElements: selectedElementsEvent });
    } else {
      if (this.props.onDesselect) {
        const elementToDesselect = this.state.selectedElements.filter(
          (object) => !selectedElementsEvent.includes(object)
        )[0];

        if (this.props.onDesselect(elementToDesselect)) {
          this.setState({ selectedElements: selectedElementsEvent });
        } else {
          showNotification('error', null, this.props.errorMessageToDesselect);
        }
      } else {
        this.setState({ selectedElements: selectedElementsEvent });
      }
    }
  }

  render() {
    return (
      <div className="formgrid p-grid align-center">
        {this.props.isMultiple ? (
          <>
            {!this.props.readOnly && (
              <div className="col-3" style={{ marginBottom: '30px', marginTop: '15px' }}>
                <FcButton
                  label={this.props.labelButtonCreate}
                  type="button"
                  icon={PrimeIcons.PLUS}
                  onClick={() => this._showDialogModal(true)}
                  disabled={this.props.disabled}
                />
              </div>
            )}
            <div className="col-12">
              <DataTable
                rowHover
                value={this.props.selectedKeydElementsList}
                emptyMessage={this.props.emptyMessage}
                paginator={this.props.paginator}
                rows={this.props.rows}
                disabled
              >
                {this.renderColumns(this.props.columnsSelectMultiple)}
              </DataTable>
            </div>
          </>
        ) : (
          <>
            {this.props.showInput && (
              <div className="col-10">
                <InputText
                  value={
                    this.props.value
                      ? this.props.value[this.props.label]
                        ? this.props.value[this.props.label]
                        : this.props.nullMessage
                      : this.props.emptyMessage
                  }
                  disabled
                />
              </div>
            )}
            <div className={this.props.showInput ? 'col-2' : 'col-12'}>
              <FcButton
                id="search-btn"
                type="button"
                style={{ width: '100%', height: '100%' }}
                icon={this.props.iconButtonDialog}
                label={this.props.labelButtonDialog}
                onClick={() => this._showDialogModal(true)}
                disabled={this.props.disabledComponent}
              />
            </div>
          </>
        )}
        {this.state.visibleModal && this._renderDialogModal()}
        {this._renderConfirmDialog()}
        {this.state.visibleModalCreate && this._renderDialogModalCreate()}
      </div>
    );
  }
}

SelectDialog.defaultProps = {
  isMultiple: false,
  selectedElements: null,
  onSelectMultiple: undefined,
  onDesselect: undefined,
  paginator: true,
  rows: 5,
  errorMessageToDesselect: 'Elemento não pode ser desselecionado',
  emptyMessage: 'Nenhum registro selecionado',
  labelButtonCreate: '',
  disabled: false,
  disabledComponent: false,
  onChange: undefined,
  disabledCreate: false,
  showInput: true,
  iconButtonDialog: 'pi pi-search',
  labelButtonDialog: '',
  radioMode: false,
  readOnly: false,
};

SelectDialog.propTypes = {
  id: PropTypes.string,
  value: PropTypes.any,
  label: PropTypes.string,
  onChange: PropTypes.func,
  headerDialog: PropTypes.string,
  indexStore: PropTypes.objectOf(IndexBase).isRequired,
  dialogColumns: PropTypes.array,
  searchFields: PropTypes.array,
  filterSuggest: PropTypes.array,
  emptyMessage: PropTypes.string,
  nullMessage: PropTypes.string,
  canCreate: PropTypes.bool,
  formPage: PropTypes.objectOf(GenericFormPage),
  isMultiple: PropTypes.string,
  selectedElements: PropTypes.array,
  onSelectMultiple: PropTypes.func,
  onDesselect: PropTypes.func,
  errorMessageToDesselect: PropTypes.string,
  labelButtonCreate: PropTypes.string,
  selectedKeydElementsList: PropTypes.array,
  paginator: PropTypes.bool,
  rows: PropTypes.number,
  columnsSelectMultiple: PropTypes.array,
  disabled: PropTypes.bool,
  disabledComponent: PropTypes.bool,
  disabledCreate: PropTypes.bool,
  confirmChange: PropTypes.object,
  showInput: PropTypes.bool,
  iconButtonDialog: PropTypes.string,
  labelButtonDialog: PropTypes.string,
  radioMode: PropTypes.bool,
  readOnly: PropTypes.bool,
};

export default SelectDialog;
