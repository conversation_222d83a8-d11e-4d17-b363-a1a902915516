import { action, computed, makeObservable, observable, runInAction } from 'mobx';
import { showErrorNotification, showNotification } from '../utils/utils';
import moment from 'moment';
import { DATE_FORMAT_REPORT, DATE_PARSE_FORMAT_WITH_HOURS_AND_SECONDS } from '../utils/date';

class FileUploaderStore {
  @observable uploadedFiles = [];
  @observable loading = false;
  tipoArquivoEnum = [];
  uploadFunction;
  downloadFunction;
  removeFunction;
  updateFunction;

  constructor(tipoArquivoEnum, uploadFunction, downloadFunction, removeFunction, updateFunction) {
    this.tipoArquivoEnum = tipoArquivoEnum;
    this.uploadFunction = uploadFunction;
    this.downloadFunction = downloadFunction;
    this.removeFunction = removeFunction;
    this.updateFunction = updateFunction;
    makeObservable(this);

    this.uploadFile = this.uploadFile.bind(this);
    this.removeFile = this.removeFile.bind(this);
    this.editFile = this.editFile.bind(this);
  }

  @action
  initialize(uploadedFiles = []) {
    this.loading = true;
    this.uploadedFiles = uploadedFiles;
    this.loading = false;
  }

  @action
  editFile(fieldData = {}, editingKey, hasFileChanged, callback) {
    const encapsulatedCallback = () => {
      callback && callback(this.uploadedFiles);
    };

    const fileToUpdate = {
      ...fieldData,
      tipo: fieldData.tipoArquivo,
      dataEnvio: moment().format(DATE_PARSE_FORMAT_WITH_HOURS_AND_SECONDS),
    };

    if (hasFileChanged) {
      this.loading = true;
      this.uploadFunction(fieldData.arquivo)
        .then((response) =>
          runInAction(() => {
            fileToUpdate.arquivo = response.data;
            this.loading = false;
            this.overrideFile(editingKey, fileToUpdate);
            encapsulatedCallback();
          })
        )
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
            this.loading = false;
          })
        );
    } else {
      this.overrideFile(editingKey, fileToUpdate);
      encapsulatedCallback();
    }
  }

  @action
  updatePersistedFile(fileData, callback) {
    this.loading = true;
    this.updateFunction(fileData.idArquivo, fileData)
      .then(() =>
        runInAction(() => {
          showNotification('success', null, 'Arquivo atualizado com sucesso!');
          this.loading = false;
          callback && callback();
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
          this.loading = false;
        })
      );
  }

  @action
  uploadFile(fieldData = {}, callback) {
    this.loading = true;
    this.uploadFunction(fieldData.arquivo)
      .then((response) =>
        runInAction(() => {
          const fileToAdd = {
            arquivo: response.data,
            descricao: fieldData.descricao ?? '',
            tipo: this.tipoArquivoEnum?.length === 1 ? this.tipoArquivoEnum[0].value : fieldData.tipoArquivo,
            dataEnvio: moment().format(DATE_PARSE_FORMAT_WITH_HOURS_AND_SECONDS),
            tamanho: arquivo?.size,
          };
          this.uploadedFiles.push(fileToAdd);
          this.loading = false;
          callback && callback(this.uploadedFiles);
        })
      )
      .catch((error) =>
        runInAction(() => {
          showErrorNotification(error);
          this.loading = false;
        })
      );
  }

  downloadFile(fileDTO, countDownloadRequest, visualizeCallback, optionalCallback = undefined) {
    this.downloadFunction(fileDTO, countDownloadRequest)
      .then((response) =>
        runInAction(() => {
          if (visualizeCallback) {
            visualizeCallback(URL.createObjectURL(response.data));
          } else {
            const { nomeOriginal } = fileDTO;
            const nameArray = nomeOriginal?.split('.');
            const extension = nameArray && nameArray.length > 1 ? nameArray[nameArray.length - 1] : 'pdf';
            const timestamp = moment().format(DATE_FORMAT_REPORT);
            const filename = fileDTO.nomeOriginal + '_' + timestamp + '.' + extension;
            const link = document.createElement('a');
            link.setAttribute('href', URL.createObjectURL(response.data));
            link.setAttribute('download', filename);
            link.style.display = 'none';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            optionalCallback && optionalCallback();
          }
        })
      )
      .catch((error) =>
        runInAction(() => {
          if (error?.response) {
            error.response.data.text().then((errorResponse) => {
              const errorObject = JSON.parse(errorResponse);
              showNotification(
                'error',
                null,
                errorObject?.messages.length > 0 ? errorObject.messages[0] : 'Ocorreu um erro!'
              );
            });
          } else {
            showNotification('error', null, 'Ocorreu um erro!');
          }
        })
      );
  }

  @action
  removeFile(fileToRemove, callback) {
    this.uploadedFiles.replace(this.keyedUploadedFiles.filter((file) => file.key !== fileToRemove.key));
    callback && callback(this.uploadedFiles);
  }

  @action
  overrideFile(keyToOverride, file) {
    const index = this.fileKeys.indexOf(keyToOverride);
    this.uploadedFiles[index] = file;
  }

  @computed
  get keyedUploadedFiles() {
    return this.uploadedFiles.map((file, idx) => ({ ...file, key: `file-${idx}` }));
  }

  @computed
  get fileKeys() {
    return this.keyedUploadedFiles.map((file) => file.key);
  }
}

export default FileUploaderStore;
