import { ServiceBase } from 'fc/services/ServiceBase';
import ApiEndpoints from '~/constants/ApiEndpoints';
import axios from 'axios';

class TdaInexigibilidadeService extends ServiceBase {
  constructor() {
    super(ApiEndpoints.tdaInexigibilidade);
  }

  upload(file) {
    const formData = new FormData();
    formData.append('file', file);
    return axios.post(`${this.baseUrl}/${this.endpoint}/upload`, formData);
  }

  download(fileDTO) {
    let params = `lookupId=${fileDTO.lookupId}&nomeOriginal=${fileDTO.nomeOriginal}&tipoArquivo=${fileDTO.tipoArquivo}`;
    return axios.get(`${this.baseUrl}/${this.endpoint}/download?${params}`, { responseType: 'blob' });
  }

  removerArquivo(idTdaInexigibilidade, idArquivo) {
    return axios.delete(`${this.baseUrl}/${this.endpoint}/${idTdaInexigibilidade}/arquivos/${idArquivo}`);
  }

  recuperarArquivos(idTdaInexigibilidade) {
    return axios.get(`${this.baseUrl}/${this.endpoint}/${idTdaInexigibilidade}/arquivos`);
  }

  atualizarArquivo(idTdaInexigibilidade, idArquivo, arquivoTdaInexigibilidadeDTO) {
    return axios.put(
      `${this.baseUrl}/${this.endpoint}/${idTdaInexigibilidade}/arquivos/${idArquivo}`,
      arquivoTdaInexigibilidadeDTO
    );
  }

  saveArquivos(idTdaInexigibilidade, arquivosTdaInexigibilidadeDTO) {
    return axios.post(
      `${this.baseUrl}/${this.endpoint}/${idTdaInexigibilidade}/salvar-arquivos`,
      arquivosTdaInexigibilidadeDTO
    );
  }

  tdaInexigibilidadeByIdInexigibilidade(idInexigibilidade) {
    return axios.get(`${this.baseUrl}/${this.endpoint}/${idInexigibilidade}/tda-inexigibilidade`);
  }

  arquivarProcesso(idInexigibilidade, statusArquivamento) {
    return axios.post(`${this.baseUrl}/${this.endpoint}/${idInexigibilidade}/${statusArquivamento}/arquivar-processo`);
  }
}

const instance = new TdaInexigibilidadeService();

export default instance;
