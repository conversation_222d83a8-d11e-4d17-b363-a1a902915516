import axios from 'axios';
import { ServiceBase } from 'fc/services/ServiceBase';
import ApiEndpoints from '~/constants/ApiEndpoints';

class InexigibilidadeService extends ServiceBase {
  constructor() {
    super(ApiEndpoints.inexigibilidade);
  }

  upload(file) {
    const formData = new FormData();
    formData.append('file', file);
    return axios.post(`${this.baseUrl}/${this.endpoint}/upload`, formData);
  }

  download(fileDTO, countDownloads) {
    let params = `lookupId=${fileDTO.lookupId}&nomeOriginal=${fileDTO.nomeOriginal}&tipoArquivo=${
      fileDTO.tipoArquivo
    }&countDownloads=${countDownloads ?? false}`;

    return axios.get(`${this.baseUrl}/${this.endpoint}/download?${params}`, { responseType: 'blob' });
  }

  removerArquivo(idInexigibilidade, idArquivo) {
    return axios.delete(`${this.baseUrl}/${this.endpoint}/${idInexigibilidade}/arquivos/${idArquivo}`);
  }

  recuperarArquivos(idInexigibilidade) {
    return axios.get(`${this.baseUrl}/${this.endpoint}/${idInexigibilidade}/arquivos`);
  }

  atualizarArquivo(idInexigibilidade, idArquivo, arquivoInexigibilidadeDTO) {
    return axios.put(
      `${this.baseUrl}/${this.endpoint}/${idInexigibilidade}/arquivos/${idArquivo}`,
      arquivoInexigibilidadeDTO
    );
  }

  criarInexigibilidade(inexigibilidadeDTO) {
    return axios.post(`${this.baseUrl}/${this.endpoint}/novo`, inexigibilidadeDTO);
  }

  validaArquivos(inexigibilidadeDTO) {
    return axios.post(`${this.baseUrl}/${this.endpoint}/arquivos/validacao`, inexigibilidadeDTO);
  }

  getTresCasasDecimais(idInexigibilidade) {
    return axios.get(`${this.baseUrl}/${this.endpoint}/${idInexigibilidade}/tres-casas-decimais`);
  }

  getAnosInexigibilidade() {
    return axios.get(`${this.baseUrl}/${this.endpoint}/anos-inexigibilidade`);
  }

  getAlertaInexigibilidade(idInexigibilidade) {
    return axios.get(`${this.baseUrl}/${this.endpoint}/alerta/${idInexigibilidade}`);
  }
}

const instance = new InexigibilidadeService();

export default instance;
