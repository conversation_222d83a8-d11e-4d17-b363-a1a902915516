import { ServiceBase } from 'fc/services/ServiceBase';
import ApiEndpoints from '~/constants/ApiEndpoints';
import axios from 'axios';

class OcorrenciaLicitacaoService extends ServiceBase {
  constructor() {
    super(ApiEndpoints.ocorrenciaLicitacao);
  }

  suspenderLicitacao(ocorrenciaLicitacaoDTO, idLicitacao) {
    return axios.post(`${this.baseUrl}/${this.endpoint}/${idLicitacao}/suspender`, ocorrenciaLicitacaoDTO);
  }

  prorrogarLicitacao(ocorrenciaLicitacaoDTO, idLicitacao) {
    return axios.post(`${this.baseUrl}/${this.endpoint}/${idLicitacao}/prorrogar`, ocorrenciaLicitacaoDTO);
  }

  reabrirLicitacao(ocorrenciaLicitacaoDTO, idLicitacao) {
    return axios.post(`${this.baseUrl}/${this.endpoint}/${idLicitacao}/reabrir`, ocorrenciaLicitacaoDTO);
  }

  continuarLicitacao(ocorrenciaLicitacaoDTO, idLicitacao) {
    return axios.post(`${this.baseUrl}/${this.endpoint}/${idLicitacao}/continuar`, ocorrenciaLicitacaoDTO);
  }

  finalizarLicitacao(ocorrenciaLicitacaoDTO, idLicitacao) {
    return axios.post(`${this.baseUrl}/${this.endpoint}/${idLicitacao}/finalizar`, ocorrenciaLicitacaoDTO);
  }

  upload(file) {
    const formData = new FormData();
    formData.append('file', file);
    return axios.post(`${this.baseUrl}/${this.endpoint}/upload`, formData);
  }

  download(fileDTO) {
    let params = `lookupId=${fileDTO.lookupId}&nomeOriginal=${fileDTO.nomeOriginal}&tipoArquivo=${fileDTO.tipoArquivo}`;
    return axios.get(`${this.baseUrl}/${this.endpoint}/download?${params}`, { responseType: 'blob' });
  }

  recuperarArquivos(idOcorrenciaLicitacao) {
    return axios.get(`${this.baseUrl}/${this.endpoint}/${idOcorrenciaLicitacao}/arquivos`);
  }

  getAllByLicitacao(id) {
    return axios.get(`${this.baseUrl}/${this.endpoint}/licitacao/${id}`);
  }

  removerArquivo(idOcorrenciaLicitacao, idArquivo) {
    return axios.delete(`${this.baseUrl}/${this.endpoint}/${idOcorrenciaLicitacao}/arquivos/${idArquivo}`);
  }

  atualizarArquivo(idOcorrenciaLicitacao, idArquivo, arquivoOcorrenciaLicitacaoDTO) {
    return axios.put(
      `${this.baseUrl}/${this.endpoint}/${idOcorrenciaLicitacao}/arquivos/${idArquivo}`,
      arquivoOcorrenciaLicitacaoDTO
    );
  }

  getPendenciasLicitacao(id) {
    return axios.get(`${this.baseUrl}/${this.endpoint}/pendencias/${id}`);
  }
}

const instance = new OcorrenciaLicitacaoService();

export default instance;
