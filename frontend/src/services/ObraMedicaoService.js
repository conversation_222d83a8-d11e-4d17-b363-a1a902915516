import axios from 'axios';
import { ServiceBase } from 'fc/services/ServiceBase';
import ApiEndpoints from '~/constants/ApiEndpoints';

class ObraMedicaoService extends ServiceBase {
  constructor() {
    super(ApiEndpoints.obraMedicao);
  }

  upload(file) {
    const formData = new FormData();
    formData.append('file', file);
    return axios.post(`${this.baseUrl}/${this.endpoint}/upload`, formData);
  }

  download(fileDTO) {
    let params = `lookupId=${fileDTO.lookupId}&nomeOriginal=${fileDTO.nomeOriginal}&tipoArquivo=${fileDTO.tipoArquivo}`;
    return axios.get(`${this.baseUrl}/${this.endpoint}/download?${params}`, { responseType: 'blob' });
  }

  removerArquivo(idObraMedicao, idArquivo) {
    return axios.delete(`${this.baseUrl}/${this.endpoint}/${idObraMedicao}/arquivos/${idArquivo}`);
  }

  recuperarArquivos(idObraMedicao) {
    return axios.get(`${this.baseUrl}/${this.endpoint}/${idObraMedicao}/arquivos`);
  }

  atualizarArquivo(idObraMedicao, idArquivo, arquivoObraMedicaoDTO) {
    return axios.put(`${this.baseUrl}/${this.endpoint}/${idObraMedicao}/arquivos/${idArquivo}`, arquivoObraMedicaoDTO);
  }

  listarMedicoesObra(idObra) {
    return axios.get(`${this.baseUrl}/${this.endpoint}/${idObra}/medicoes`);
  }

  cadastrarMedicaoObra(medicaoObraDTO) {
    return axios.post(`${this.baseUrl}/${this.endpoint}/cadastrar-medicao-obra`, medicaoObraDTO);
  }

  validaArquivos(obraMedicao) {
    return axios.post(`${this.baseUrl}/${this.endpoint}/arquivos/validacao`, obraMedicao);
  }
}

const instance = new ObraMedicaoService();

export default instance;
