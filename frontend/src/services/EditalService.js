import axios from 'axios';
import { ServiceBase } from 'fc/services/ServiceBase';
import ApiEndpoints from '~/constants/ApiEndpoints';

class EditalService extends ServiceBase {
  constructor() {
    super(ApiEndpoints.edital);
  }

  upload(file) {
    const formData = new FormData();
    formData.append('file', file);
    return axios.post(`${this.baseUrl}/${this.endpoint}/upload`, formData);
  }

  download(fileDTO) {
    let params = `lookupId=${fileDTO.lookupId}&nomeOriginal=${fileDTO.nomeOriginal}&tipoArquivo=${fileDTO.tipoArquivo}`;
    return axios.get(`${this.baseUrl}/${this.endpoint}/download?${params}`, { responseType: 'blob' });
  }

  removerArquivo(idEdital, idArquivo) {
    return axios.delete(`${this.baseUrl}/${this.endpoint}/${idEdital}/arquivos/${idArquivo}`);
  }

  recuperarArquivos(idEdital) {
    return axios.get(`${this.baseUrl}/${this.endpoint}/${idEdital}/arquivos`);
  }

  atualizarArquivo(idEdital, idArquivo, arquivoEditalDTO) {
    return axios.put(`${this.baseUrl}/${this.endpoint}/${idEdital}/arquivos/${idArquivo}`, arquivoEditalDTO);
  }

  analisarEdital(object, arquivosEditalDTO) {
    return axios.post(`${this.baseUrl}/${this.endpoint}/analisar-edital`, {
      edital: object,
      arquivosEditalDTO,
    });
  }
}

const instance = new EditalService();

export default instance;
