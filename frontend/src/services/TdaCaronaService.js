import { ServiceBase } from 'fc/services/ServiceBase';
import ApiEndpoints from '~/constants/ApiEndpoints';
import axios from 'axios';

class TdaCaronaService extends ServiceBase {
  constructor() {
    super(ApiEndpoints.tdaCarona);
  }

  upload(file) {
    const formData = new FormData();
    formData.append('file', file);
    return axios.post(`${this.baseUrl}/${this.endpoint}/upload`, formData);
  }

  download(fileDTO) {
    let params = `lookupId=${fileDTO.lookupId}&nomeOriginal=${fileDTO.nomeOriginal}&tipoArquivo=${fileDTO.tipoArquivo}`;
    return axios.get(`${this.baseUrl}/${this.endpoint}/download?${params}`, { responseType: 'blob' });
  }

  removerArquivo(idTdaCarona, idArquivo) {
    return axios.delete(`${this.baseUrl}/${this.endpoint}/${idTdaCarona}/arquivos/${idArquivo}`);
  }

  recuperarArquivos(idTdaCarona) {
    return axios.get(`${this.baseUrl}/${this.endpoint}/${idTdaCarona}/arquivos`);
  }

  atualizarArquivo(idTdaCarona, idArquivo, arquivoTdaCaronaDTO) {
    return axios.put(`${this.baseUrl}/${this.endpoint}/${idTdaCarona}/arquivos/${idArquivo}`, arquivoTdaCaronaDTO);
  }

  saveArquivos(idTdaCarona, arquivosTdaCaronaDTO) {
    return axios.post(`${this.baseUrl}/${this.endpoint}/${idTdaCarona}/salvar-arquivos`, arquivosTdaCaronaDTO);
  }

  tdaCaronaByIdCarona(idCarona) {
    return axios.get(`${this.baseUrl}/${this.endpoint}/${idCarona}/tda-carona`);
  }

  arquivarProcesso(idCarona, statusArquivamento) {
    return axios.post(`${this.baseUrl}/${this.endpoint}/${idCarona}/${statusArquivamento}/arquivar-processo`);
  }
}

const instance = new TdaCaronaService();

export default instance;
