import axios from 'axios';
import { ServiceBase } from 'fc/services/ServiceBase';
import ApiEndpoints from '~/constants/ApiEndpoints';

class CredenciamentoService extends ServiceBase {
  constructor() {
    super(ApiEndpoints.credenciamento);
  }

  salvar(credenciamentoDTO) {
    return axios.post(`${this.baseUrl}/${this.endpoint}/salvar`, credenciamentoDTO);
  }

  upload(file) {
    const formData = new FormData();
    formData.append('file', file);
    return axios.post(`${this.baseUrl}/${this.endpoint}/upload`, formData);
  }

  download(fileDTO) {
    let params = `lookupId=${fileDTO.lookupId}&nomeOriginal=${fileDTO.nomeOriginal}&tipoArquivo=${fileDTO.tipoArquivo}`;
    return axios.get(`${this.baseUrl}/${this.endpoint}/download?${params}`, { responseType: 'blob' });
  }

  removerArquivo(idCredenciamento, idArquivo) {
    return axios.delete(`${this.baseUrl}/${this.endpoint}/${idCredenciamento}/arquivos/${idArquivo}`);
  }

  validaArquivos(credenciamentoDTO) {
    return axios.post(`${this.baseUrl}/${this.endpoint}/arquivos/validacao`, credenciamentoDTO);
  }

  atualizarArquivo(idCredenciamento, idArquivo, arquivoCredenciamentoDTO) {
    return axios.put(
      `${this.baseUrl}/${this.endpoint}/${idCredenciamento}/arquivos/${idArquivo}`,
      arquivoCredenciamentoDTO
    );
  }

  recuperarArquivos(idCredenciamento) {
    return axios.get(`${this.baseUrl}/${this.endpoint}/${idCredenciamento}/arquivos`);
  }

  getAlertaCredenciamento(idCredenciamento) {
    return axios.get(`${this.baseUrl}/${this.endpoint}/alerta/${idCredenciamento}`);
  }
}

const instance = new CredenciamentoService();

export default instance;
