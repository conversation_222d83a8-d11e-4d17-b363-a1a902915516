import axios from 'axios';
import { ServiceBase } from 'fc/services/ServiceBase';
import ApiEndpoints from '~/constants/ApiEndpoints';

class ContratoService extends ServiceBase {
  constructor() {
    super(ApiEndpoints.contrato);
  }

  createContrato(object) {
    return axios.post(`${this.baseUrl}/${this.endpoint}/novo`, object);
  }

  upload(file) {
    const formData = new FormData();
    formData.append('file', file);
    return axios.post(`${this.baseUrl}/${this.endpoint}/upload`, formData);
  }

  download(fileDTO) {
    let params = `lookupId=${fileDTO.lookupId}&nomeOriginal=${fileDTO.nomeOriginal}&tipoArquivo=${fileDTO.tipoArquivo}`;
    return axios.get(`${this.baseUrl}/${this.endpoint}/download?${params}`, { responseType: 'blob' });
  }

  removerArquivo(idContrato, idArquivo) {
    return axios.delete(`${this.baseUrl}/${this.endpoint}/${idContrato}/arquivos/${idArquivo}`);
  }

  atualizarArquivo(idContrato, idArquivo, arquivoContratoDTO) {
    return axios.put(`${this.baseUrl}/${this.endpoint}/${idContrato}/arquivos/${idArquivo}`, arquivoContratoDTO);
  }

  recuperarArquivos(idContrato) {
    return axios.get(`${this.baseUrl}/${this.endpoint}/${idContrato}/arquivos`);
  }

  salvarRescisaoContrato(object) {
    return axios.post(`${this.baseUrl}/${this.endpoint}/rescisao-contrato`, object);
  }

  getByIdWithAssociatedProcess(idContrato) {
    return axios.get(`${this.baseUrl}/${this.endpoint}/processo-associado/${idContrato}`);
  }

  getItensDisponiveis(idProcesso, tipoProcesso) {
    return axios.get(`${this.baseUrl}/${this.endpoint}/${idProcesso}/${tipoProcesso}/itens-disponiveis`);
  }

  validaArquivos(contratoDTO) {
    return axios.post(`${this.baseUrl}/${this.endpoint}/arquivos/validacao`, contratoDTO);
  }

  getAnosContrato() {
    return axios.get(`${this.baseUrl}/${this.endpoint}/anos-contrato`);
  }

  getQuantidadeAditivosContrato(idContrato) {
    return axios.get(`${this.baseUrl}/${this.endpoint}/${idContrato}/quantidade-aditivos`);
  }

  getPessoasResponsaveis(idEntidade) {
    return axios.get(`${this.baseUrl}/${this.endpoint}/${idEntidade}/pessoas-responsaveis`);
  }
}

const instance = new ContratoService();

export default instance;
