import { ServiceBase } from 'fc/services/ServiceBase';
import ApiEndpoints from '~/constants/ApiEndpoints';
import axios from 'axios';

class TdaCredenciamentoService extends ServiceBase {
  constructor() {
    super(ApiEndpoints.tdaCredenciamento);
  }

  upload(file) {
    const formData = new FormData();
    formData.append('file', file);
    return axios.post(`${this.baseUrl}/${this.endpoint}/upload`, formData);
  }

  download(fileDTO) {
    let params = `lookupId=${fileDTO.lookupId}&nomeOriginal=${fileDTO.nomeOriginal}&tipoArquivo=${fileDTO.tipoArquivo}`;
    return axios.get(`${this.baseUrl}/${this.endpoint}/download?${params}`, { responseType: 'blob' });
  }

  removerArquivo(idTdaCredenciamento, idArquivo) {
    return axios.delete(`${this.baseUrl}/${this.endpoint}/${idTdaCredenciamento}/arquivos/${idArquivo}`);
  }

  recuperarArquivos(idTdaCredenciamento) {
    return axios.get(`${this.baseUrl}/${this.endpoint}/${idTdaCredenciamento}/arquivos`);
  }

  atualizarArquivo(idTdaCredenciamento, idArquivo, arquivoTdaCredenciamentoDTO) {
    return axios.put(
      `${this.baseUrl}/${this.endpoint}/${idTdaCredenciamento}/arquivos/${idArquivo}`,
      arquivoTdaCredenciamentoDTO
    );
  }

  saveArquivos(idTdaCredenciamento, arquivosTdaCredenciamentoDTO) {
    return axios.post(
      `${this.baseUrl}/${this.endpoint}/${idTdaCredenciamento}/salvar-arquivos`,
      arquivosTdaCredenciamentoDTO
    );
  }

  tdaCredenciamentoByIdCredenciamento(idCredenciamento) {
    return axios.get(`${this.baseUrl}/${this.endpoint}/${idCredenciamento}/tda-credenciamento`);
  }

  arquivarProcesso(idCredenciamento, statusArquivamento) {
    return axios.post(`${this.baseUrl}/${this.endpoint}/${idCredenciamento}/${statusArquivamento}/arquivar-processo`);
  }
}

const instance = new TdaCredenciamentoService();

export default instance;
