import axios from 'axios';
import { ServiceBase } from 'fc/services/ServiceBase';
import ApiEndpoints from '../constants/ApiEndpoints';

class TermoReferenciaService extends ServiceBase {
  constructor() {
    super(ApiEndpoints.termoReferencia);
  }

  cloneById(id, identificadorProcesso) {
    return axios.post(`${this.baseUrl}/${this.endpoint}/gerar-clone/${id}`, { identificador: identificadorProcesso });
  }

  upload(file) {
    const formData = new FormData();
    formData.append('file', file);
    return axios.post(`${this.baseUrl}/${this.endpoint}/upload`, formData);
  }

  download(fileDTO) {
    let params = `lookupId=${fileDTO.lookupId}&nomeOriginal=${fileDTO.nomeOriginal}&tipoArquivo=${fileDTO.tipoArquivo}`;
    return axios.get(`${this.baseUrl}/${this.endpoint}/download?${params}`, { responseType: 'blob' });
  }

  removerArquivo(idTermoReferencia, idArquivo) {
    return axios.delete(`${this.baseUrl}/${this.endpoint}/${idTermoReferencia}/arquivos/${idArquivo}`);
  }

  recuperarArquivos(idTermoReferencia) {
    return axios.get(`${this.baseUrl}/${this.endpoint}/${idTermoReferencia}/arquivos`);
  }

  atualizarArquivo(idTermoReferencia, idArquivo, arquivoTermoReferenciaDTO) {
    return axios.put(
      `${this.baseUrl}/${this.endpoint}/${idTermoReferencia}/arquivos/${idArquivo}`,
      arquivoTermoReferenciaDTO
    );
  }

  processoAssociado(idTermoReferencia) {
    return axios.get(`${this.baseUrl}/${this.endpoint}/${idTermoReferencia}/processo`);
  }
}

const instance = new TermoReferenciaService();

export default instance;
