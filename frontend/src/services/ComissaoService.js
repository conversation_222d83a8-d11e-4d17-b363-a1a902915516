import axios from 'axios';
import { ServiceBase } from 'fc/services/ServiceBase';
import ApiEndpoints from '~/constants/ApiEndpoints';

class ComissaoService extends ServiceBase {
  constructor() {
    super(ApiEndpoints.comissao);
  }

  upload(file) {
    const formData = new FormData();
    formData.append('file', file);
    return axios.post(`${this.baseUrl}/${this.endpoint}/upload`, formData);
  }

  download(fileDTO) {
    let params = `lookupId=${fileDTO.lookupId}&nomeOriginal=${fileDTO.nomeOriginal}&tipoArquivo=${fileDTO.tipoArquivo}`;
    return axios.get(`${this.baseUrl}/${this.endpoint}/download?${params}`, { responseType: 'blob' });
  }

  removerArquivo(idComissao, idArquivo) {
    return axios.delete(`${this.baseUrl}/${this.endpoint}/${idComissao}/arquivos/${idArquivo}`);
  }

  recuperarArquivos(idComissao) {
    return axios.get(`${this.baseUrl}/${this.endpoint}/${idComissao}/arquivos`);
  }

  atualizarArquivo(idComissao, idArquivo, arquivoComissaoDTO) {
    return axios.put(`${this.baseUrl}/${this.endpoint}/${idComissao}/arquivos/${idArquivo}`, arquivoComissaoDTO);
  }
}

const instance = new ComissaoService();

export default instance;
