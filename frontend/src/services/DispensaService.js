import axios from 'axios';
import { ServiceBase } from 'fc/services/ServiceBase';
import ApiEndpoints from '../constants/ApiEndpoints';

class DispensaService extends ServiceBase {
  constructor() {
    super(ApiEndpoints.dispensa);
  }

  upload(file) {
    const formData = new FormData();
    formData.append('file', file);
    return axios.post(`${this.baseUrl}/${this.endpoint}/upload`, formData);
  }

  criarDispensa(dispensaDTO) {
    return axios.post(`${this.baseUrl}/${this.endpoint}/novo`, dispensaDTO);
  }

  validaArquivos(dispensaDTO) {
    return axios.post(`${this.baseUrl}/${this.endpoint}/arquivos/validacao`, dispensaDTO);
  }

  download(fileDTO, countDownloads) {
    let params = `lookupId=${fileDTO.lookupId}&nomeOriginal=${fileDTO.nomeOriginal}&tipoArquivo=${
      fileDTO.tipoArquivo
    }&countDownloads=${countDownloads ?? false}`;
    return axios.get(`${this.baseUrl}/${this.endpoint}/download?${params}`, { responseType: 'blob' });
  }

  removerArquivo(idDispensa, idArquivo) {
    return axios.delete(`${this.baseUrl}/${this.endpoint}/${idDispensa}/arquivos/${idArquivo}`);
  }

  recuperarArquivos(idDispensa) {
    return axios.get(`${this.baseUrl}/${this.endpoint}/${idDispensa}/arquivos`);
  }

  atualizarArquivo(idDispensa, idArquivo, arquivoDispensaDTO, lei) {
    return axios.put(`${this.baseUrl}/${this.endpoint}/${idDispensa}/arquivos/${idArquivo}/${lei}`, arquivoDispensaDTO);
  }

  getTresCasasDecimais(idDispensa) {
    return axios.get(`${this.baseUrl}/${this.endpoint}/${idDispensa}/tres-casas-decimais`);
  }

  getAnosDispensa() {
    return axios.get(`${this.baseUrl}/${this.endpoint}/anos-dispensa`);
  }

  getAlertaDispensa(idDispensa) {
    return axios.get(`${this.baseUrl}/${this.endpoint}/alerta/${idDispensa}`);
  }
}

const instance = new DispensaService();

export default instance;
