import { ServiceBase } from 'fc/services/ServiceBase';
import ApiEndpoints from '../constants/ApiEndpoints';
import axios from 'axios';

class LicitacaoService extends ServiceBase {
  constructor() {
    super(ApiEndpoints.licitacao);
  }

  upload(file) {
    const formData = new FormData();
    formData.append('file', file);
    return axios.post(`${this.baseUrl}/${this.endpoint}/upload`, formData);
  }

  publicarLicitacao(licitacaoPublicacaoDTO, idLicitacao, type) {
    if (type === 'next')
      return axios.post(`${this.baseUrl}/${this.endpoint}/${idLicitacao}/divulgacao/publicar`, licitacaoPublicacaoDTO);

    if (type === 'edit')
      return axios.patch(`${this.baseUrl}/${this.endpoint}/${idLicitacao}/divulgacao/publicar`, licitacaoPublicacaoDTO);
  }

  salvarVencedorLicitacao(licitacao) {
    return axios.post(`${this.baseUrl}/${this.endpoint}/${licitacao.id}/apresentacao/salvar-vencedor`, licitacao);
  }

  finalizarLicitacao(licitacaoFinalizacaoDTO, idLicitacao, type) {
    if (type === 'next')
      return axios.post(`${this.baseUrl}/${this.endpoint}/${idLicitacao}/finalizar`, licitacaoFinalizacaoDTO);

    if (type === 'edit')
      return axios.patch(`${this.baseUrl}/${this.endpoint}/${idLicitacao}/finalizar`, licitacaoFinalizacaoDTO);
  }

  download(fileDTO, countDownloads) {
    let params = `lookupId=${fileDTO.lookupId}&nomeOriginal=${fileDTO.nomeOriginal}&tipoArquivo=${
      fileDTO.tipoArquivo
    }&countDownloads=${countDownloads ?? false}`;
    return axios.get(`${this.baseUrl}/${this.endpoint}/download?${params}`, { responseType: 'blob' });
  }

  cadastrarLicitacao(cadastroLicitacaoDTO) {
    return axios.post(`${this.baseUrl}/${this.endpoint}/preparatoria/cadastrar`, cadastroLicitacaoDTO);
  }

  removerArquivo(idLicitacao, idArquivo) {
    return axios.delete(`${this.baseUrl}/${this.endpoint}/${idLicitacao}/arquivos/${idArquivo}`);
  }

  recuperarArquivos(idLicitacao) {
    return axios.get(`${this.baseUrl}/${this.endpoint}/${idLicitacao}/arquivos`);
  }

  atualizarArquivo(idLicitacao, idArquivo, arquivoLicitacaoDTO) {
    return axios.put(`${this.baseUrl}/${this.endpoint}/${idLicitacao}/arquivos/${idArquivo}`, arquivoLicitacaoDTO);
  }

  getFontesRecurso(idLicitacao) {
    return axios.get(`${this.baseUrl}/${this.endpoint}/${idLicitacao}/fontes-recursos`);
  }

  getNaturezasObjeto(idLicitacao) {
    return axios.get(`${this.baseUrl}/${this.endpoint}/${idLicitacao}/naturezas-objeto`);
  }

  getTresCasasDecimais(idLicitacao) {
    return axios.get(`${this.baseUrl}/${this.endpoint}/${idLicitacao}/tres-casas-decimais`);
  }

  getAnosLicitacao() {
    return axios.get(`${this.baseUrl}/${this.endpoint}/anos-licitacao`);
  }

  removerLicitacao(idLicitacao) {
    return axios.delete(`${this.baseUrl}/${this.endpoint}/${idLicitacao}/remove`);
  }

  getAlertaLicitacao(idLicitacao) {
    return axios.get(`${this.baseUrl}/${this.endpoint}/alerta/${idLicitacao}`);
  }
}

const instance = new LicitacaoService();

export default instance;
