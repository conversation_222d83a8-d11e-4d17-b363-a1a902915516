import { getValueByKey, getValueDate, getValueMoney, getValueMoneyIfNotFormatted } from 'fc/utils/utils';

const DadosEstaticosService = {
  getTipoComissao() {
    return [
      { value: 'PERMANENTE', text: 'Permanente', cod: 'CPL', desc: 'Comissão Permanente de Licitação' },
      { value: 'ESPECIAL', text: 'Especial', cod: 'CEL', desc: 'Comissão Especial de Licitação' },
    ];
  },

  getTipoConjuntoComissao() {
    return [
      { value: 'CONTRATACAO', text: 'Comissão de Contratação', cod: 'CCT', desc: 'Comissão Permanente de Licitação' },
      {
        value: 'APOIO',
        text: 'Agente de Contratação/Pregoeiro(a) com Equipe de Apoio',
        cod: 'ACP',
        desc: 'Agente de Contratação/Pregoeiro(a) com equipe de apoio',
      },
    ];
  },

  getTipoVariavelAmbiente() {
    return [
      { value: 'TEXTO', text: 'Texto' },
      { value: 'NUMERICO', text: 'Numérico' },
      { value: 'BOOLEAN', text: 'Boolean' },
    ];
  },

  getTiposObjetos() {
    return [
      { value: 'LICITACAO', text: 'Licitação' },
      { value: 'CARONA', text: 'Adesão (Carona)' },
      { value: 'DISPENSA', text: 'Dispensa' },
      { value: 'INEXIGIBILIDADE', text: 'Inexigibilidade' },
      { value: 'CONTRATO', text: 'Contrato' },
      { value: 'ADITIVO', text: 'Aditivo' },
      { value: 'EMPENHO', text: 'Empenho' },
      { value: 'EMPENHO_LICITACAO', text: 'Empenho / Ata de Registro de Preço' },
      { value: 'RESCISAO_CONTRATUAL', text: 'Rescisão Contratual' },
      { value: 'TERMO_REFERENCIA', text: 'Termo de Referência' },
      { value: 'MEDICAO', text: 'Medição de Obra' },
      { value: 'CREDENCIAMENTO', text: 'Credenciamento' },
      { value: 'CREDENCIADO', text: 'Credenciado' },
      { value: 'ANULACAO_REVOGACAO', text: 'Anulação/Revogação' },
    ];
  },

  getModalidadesLicitacao() {
    return [
      { value: 'Convite', text: 'Convite', key: 'CARTA_CONVITE' },
      { value: 'Concorrência', text: 'Concorrência', key: 'CONCORRENCIA' },
      { value: 'Pregão Presencial', text: 'Pregão Presencial', key: 'PREGAO_PRESENCIAL' },
      { value: 'Pregão Eletrônico', text: 'Pregão Eletrônico', key: 'PREGAO_ELETRONICO' },
      { value: 'Tomada de Preços', text: 'Tomada de Preços', key: 'TOMADA_DE_PRECO' },
      { value: 'Leilão', text: 'Leilão', key: 'LEILAO' },
      {
        value: 'RDC - Regime Diferenciado de Contratação',
        text: 'RDC - Regime Diferenciado de Contratação',
        key: 'RDC',
      },
      { value: 'LPN - Licitação Pública Nacional', text: 'LPN - Licitação Pública Nacional', key: 'LPN' },
      {
        value: 'LPNI - Licitação Pública Nacional e Internacional',
        text: 'LPNI - Licitação Pública Nacional e Internacional',
        key: 'LPNI',
      },
      { value: 'Shopping', text: 'Shopping', key: 'SHOPPING' },
      { value: 'Seleção de Consultores', text: 'Seleção de Consultores', key: 'SELECAO' },
      { value: 'Chamamento Público', text: 'Chamamento Público', key: 'CHAMAMENTO' },
    ];
  },

  getModalidadeCarona() {
    return [
      { value: 'CARTA_CONVITE', text: 'Convite' },
      { value: 'CONCORRENCIA', text: 'Concorrência' },
      { value: 'PREGAO_PRESENCIAL', text: 'Pregão Presencial' },
      { value: 'PREGAO_ELETRONICO', text: 'Pregão Eletrônico' },
      { value: 'TOMADA_DE_PRECO', text: 'Tomada de Preços' },
      { value: 'LEILAO', text: 'Leilão' },
      { value: 'RDC', text: 'RDC - Regime Diferenciado de Contratação' },
      { value: 'LPN', text: 'LPN - Licitação Pública Nacional' },
      { value: 'LPNI', text: 'LPNI - Licitação Pública Nacional e Internacional' },
      { value: 'SHOPPING', text: 'Shopping' },
      { value: 'SELECAO', text: 'Seleção de Consultores' },
      { value: 'CHAMAMENTO', text: 'Chamamento Público' },
      { value: 'PREGAO', text: 'Pregão' },
    ];
  },

  getModalidadesCarona() {
    return [
      { value: 'Convite', text: 'Convite' },
      { value: 'Concorrência', text: 'Concorrência' },
      { value: 'Pregão Presencial', text: 'Pregão Presencial' },
      { value: 'Pregão Eletrônico', text: 'Pregão Eletrônico' },
      { value: 'Tomada de Preços', text: 'Tomada de Preços' },
      { value: 'Leilão', text: 'Leilão' },
      { value: 'RDC - Regime Diferenciado de Contratação', text: 'RDC - Regime Diferenciado de Contratação' },
      { value: 'LPN - Licitação Pública Nacional', text: 'LPN - Licitação Pública Nacional' },
      {
        value: 'LPNI - Licitação Pública Nacional e Internacional',
        text: 'LPNI - Licitação Pública Nacional e Internacional',
      },
      { value: 'Shopping', text: 'Shopping' },
      { value: 'Seleção de Consultores', text: 'Seleção de Consultores' },
      { value: 'Chamamento Público', text: 'Chamamento Público' },
      { value: 'Pregão', text: 'Pregão' },
    ];
  },

  getTipoFeriado() {
    return [
      { value: 'MUNICIPAL', text: 'Municipal' },
      { value: 'ESTADUAL', text: 'Estadual' },
      { value: 'FEDERAL', text: 'Federal' },
    ];
  },

  getTipoPessoa() {
    return [
      { value: true, text: 'Física' },
      { value: false, text: 'Jurídica' },
    ];
  },

  getTipoFundamentacaoLegal() {
    return [
      { value: true, text: 'Dispensa' },
      { value: false, text: 'Inexigibilidade' },
    ];
  },

  getSimNao() {
    return [
      { bit: '1', value: true, text: 'Sim' },
      { bit: '0', value: false, text: 'Não' },
    ];
  },

  getSimNaoCatalogo() {
    return [
      { value: 'S', text: 'Sim' },
      { value: 'N', text: 'Não' },
    ];
  },

  getTipoMaterial() {
    return [
      { value: 'M', text: 'Materiais', name: 'materiais', singularText: 'Material' },
      { value: 'S', text: 'Serviços', name: 'servicos', singularText: 'Serviço' },
    ];
  },

  getSimNaoInativo() {
    return [
      { value: true, text: 'Sim' },
      { value: false, text: 'Não' },
    ];
  },

  getVerdadeiroFalso() {
    return [
      { value: true, text: 'Verdadeiro' },
      { value: false, text: 'Falso' },
    ];
  },

  getVerdadeiroFalsoVariavelControle() {
    return [
      { value: '1', text: 'Verdadeiro' },
      { value: '0', text: 'Falso' },
    ];
  },

  getModalidadeLicitacao() {
    return [
      { value: 'CARTA_CONVITE', text: 'Convite' },
      { value: 'CONCORRENCIA', text: 'Concorrência' },
      { value: 'PREGAO_PRESENCIAL', text: 'Pregão Presencial' },
      { value: 'PREGAO_ELETRONICO', text: 'Pregão Eletrônico' },
      { value: 'TOMADA_DE_PRECO', text: 'Tomada de Preços' },
      { value: 'LEILAO', text: 'Leilão' },
      { value: 'RDC', text: 'RDC - Regime Diferenciado de Contratação' },
      { value: 'LPN', text: 'LPN - Licitação Pública Nacional' },
      { value: 'LPNI', text: 'LPNI - Licitação Pública Nacional e Internacional' },
      { value: 'SHOPPING', text: 'Shopping' },
      { value: 'SELECAO', text: 'Seleção de Consultores' },
      { value: 'CHAMAMENTO', text: 'Chamamento Público' },
    ];
  },

  getStatusAuditoriaLicitacao() {
    return [
      { value: 'ANALISE', text: 'Em análise' },
      { value: 'CONSISTENTE', text: 'Consistente' },
      { value: 'INCONSISTENTE', text: 'Inconsistente' },
      { value: 'INCONSISTENCIA_RESOLVIDA', text: 'Inconsistência resolvida' },
      { value: 'INCONSISTENCIA_NAO_RESOLVIDA', text: 'Inconsistência não resolvida' },
    ];
  },

  getStatusLicitacao() {
    return [
      { value: 'NAO_PUBLICADA', text: 'Não publicada' },
      { value: 'PUBLICADA', text: 'Publicada' },
      { value: 'REMOVIDA', text: 'Removida' },
    ];
  },

  getStatusOcorrenciaLicitacao() {
    return [
      { value: 'CANCELADA', text: 'Cancelada' },
      { value: 'PRORROGADA', text: 'Prorrogada' },
      { value: 'SUSPENSA', text: 'Suspensa' },
      { value: 'REVOGADA', text: 'Revogada' },
      { value: 'ANULADA', text: 'Anulada' },
      { value: 'SUSPENSA_DETERMINACAO_JUDICIAL', text: 'Suspensa por determinação judicial' },
      { value: 'EDITAL_IMPUGNADO_ANALISE', text: 'Edital impugnado em análise' },
      { value: 'SUSTADA_MEDIDA_CAUTELAR', text: 'Sustada por medida cautelar' },
      { value: 'ADJUDICADA', text: 'Adjudicada' },
      { value: 'HOMOLOGADA', text: 'Homologada' },
      { value: 'DESERTA', text: 'Deserta' },
      { value: 'FRACASSADA', text: 'Fracassada' },
      { value: 'RETIFICACAO_EDITAL', text: 'Retificação do Edital' },
      { value: 'REABRIR', text: 'Reabertura' },
      { value: 'REABRIR_DETERMINACAO_JUDICIAL', text: 'Reaberta por determinação judicial' },
      { value: 'ATA_REGISTRO_PRECOS', text: 'Ata de registro de preço' },
      { value: 'REEDICAO', text: 'Reedição (Em casos de deserta ou fracassada)' },
      { value: 'OUTRA', text: 'Outra' },
      { value: 'OUTROS_PRORROGAR', text: 'Outros' },
      { value: 'ALERTA_TCE', text: 'Alerta TCE' },
      { value: 'CONTINUAR', text: 'Não houve alteração nos dados da Licitação' },
      { value: 'PARA_RETIFICACAO_EDITAL', text: 'Para retificação do edital' },
      { value: 'OUTROS_MOTIVOS', text: 'Outros motivos' },
      { value: 'ALERTA_TCE_PRORROGACAO', text: 'Alerta TCE' },
      { value: 'EDITAL_IMPUGNADO_ANALISE_PRORROGACAO', text: 'Edital impugnado em análise' },
      { value: 'PRORROGADA_DETERMINACAO_JUDICIAL', text: 'Prorrogada por determinação judicial' },
      { value: 'PRORROGADA_MEDIDA_CAUTELAR', text: 'Prorrogada por medida cautelar' },
      { value: 'PARA_RETIFICACAO_EDITAL_PRORROGACAO', text: 'Para retificação do edital' },
    ];
  },

  getMotivosOcorrenciaFilter() {
    return ['Anulada', 'Fracassada', 'Deserta', 'Revogada'];
  },

  getTipoOcorrencia() {
    return [
      { value: 'CANCELAR', text: 'Cancelada' },
      { value: 'FINALIZAR', text: 'Finalizada' },
      { value: 'SUSPENDER', text: 'Suspensa' },
      { value: 'PRORROGAR', text: 'Prorrogada' },
      { value: 'REABRIR', text: 'Reaberta' },
      { value: 'CONTINUAR', text: 'Continuada' },
    ];
  },

  getTipoItem() {
    return [
      { value: 'NUMERO', text: 'Númerico' },
      { value: 'TEXTUAL', text: 'Textual' },
      { value: 'BOOLEAN', text: 'Boolean' },
      { value: 'TEMPORAL', text: 'Temporal' },
      { value: 'MONETARIO', text: 'Monetário' },
    ];
  },

  getSituacaoPedidoCatalogo() {
    return [
      { value: 'PENDENTE', text: 'Pendente' },
      { value: 'DEFERIDO', text: 'Deferido' },
      { value: 'INDEFERIDO', text: 'Indeferido' },
    ];
  },

  getTipoTemporal() {
    return [
      { value: 'HORAS', text: 'Horas' },
      { value: 'DIAS', text: 'Dias' },
      { value: 'DIAS_UTEIS', text: 'Dias Úteis' },
      { value: 'SEMANAS', text: 'Semanas' },
      { value: 'MESES', text: 'Meses' },
      { value: 'ANOS', text: 'Anos' },
    ];
  },
  getNaturezaObjetoLicitacao() {
    return [
      { value: 'ALIENACAO_DE_BENS', text: 'Alienação de Bens' },
      { value: 'CONCESSAO', text: 'Concessão' },
      { value: 'LOCACAO_DE_BENS', text: 'Locação de Bens' },
      { value: 'SERVICOS_DE_ENGENHARIA', text: 'Serviços comuns de engenharia' },
      { value: 'BENS_SERVICOS_ESPECIAIS', text: 'Bens/Serviços especiais' },
      { value: 'PERMISSAO', text: 'Permissão' },
      { value: 'CONSULTORIA', text: 'Consultoria' },
      { value: 'COMPRAS', text: 'Compras' },
      { value: 'OBRAS', text: 'Obras/Serviços especiais de engenharia' },
      { value: 'PRESTACAO_DE_SERVICO', text: 'Prestação de Serviços' },
      {
        value: 'PRESTACAO_DE_SERVICO_FORNECIMENTO_MATERIAL',
        text: 'Prestação de Serviços com Fornecimento de Material',
      },
      { value: 'OUTROS_SERVICOS', text: 'Outros Serviços' },
    ];
  },

  getTodosTiposArquivos() {
    let tipos = [];
    tipos.push(this.getTipoArquivoAditivo());
    tipos.push(this.getTipoArquivoCarona());
    tipos.push(this.getTipoArquivoContrato());
    tipos.push(this.getTipoArquivoDispensa());
    tipos.push(this.getTipoArquivoInexigibilidade());
    tipos.push(this.getTipoArquivoLicitacao());
    tipos.push(this.getTipoArquivoRescisaoContrato());
    tipos.push(this.getTipoArquivoTermoReferencia());
    tipos.push(this.getTipoArquivoObra());
    return [].concat.apply([], tipos);
  },

  getTipoArquivoLicitacao() {
    return [
      {
        value: 'ANALISE_RISCOS',
        text: 'Análise dos riscos que possam comprometer o sucesso da licitação e a boa execução contratual',
        fase: ['PREPARATORIA'],
      },
      {
        value: 'COMPROVANTE_PUBLICACAO_DIARIO',
        text: 'Comprovante da publicação do aviso de licitação em Diário Oficial',
        fase: ['PREPARATORIA'],
      },
      {
        value: 'COMPROVANTE_PUBLICACAO_JORNAL',
        text: 'Comprovante da publicação do aviso de licitação em jornal diário de grande circulação',
        fase: ['PREPARATORIA'],
      },
      {
        value: 'COMPROVANTE_DIVULGACAO_PNCP',
        text: 'Comprovante de divulgação e manutenção do inteiro teor do ato convocatório e de seus anexos no Portal Nacional de Contratações Públicas (PNCP)',
        fase: ['PREPARATORIA'],
      },
      {
        value: 'AUTORIZACAO_ABERTURA_LICITACAO_ORDENADOR_DESPESA',
        text: 'Justificativa para contratação',
        fase: ['PREPARATORIA'],
      },
      {
        value: 'PROJETO_BASICO',
        text: 'Termo de Referência ou Projeto básico',
        fase: ['PREPARATORIA'],
      },
      {
        value: 'ESTIMATIVA_PRECO',
        text: 'Estimativa de preço detalhada, incluindo pesquisa de preço de mercado e Mapa Comparativo de Preços',
        fase: ['PREPARATORIA'],
      },
      {
        value: 'PESQUISA_PRECO_PUBLICO',
        text: 'Pesquisa de preço praticado por Órgãos e Entidades da Administração Pública',
        fase: ['PREPARATORIA'],
      },
      {
        value: 'PESQUISA_PRECO_MERCADO',
        text: 'Pesquisa de preço de mercado',
        fase: ['PREPARATORIA'],
      },
      {
        value: 'MAPA_COMPARATIVO_PRECOS',
        text: 'Mapa Comparativo de Preços (Planilha Excel/PDF)',
        fase: ['PREPARATORIA'],
      },
      {
        value: 'MAPA_LANCES',
        text: 'Mapa de lances (Planilha Excel/PDF)',
        fase: ['PREPARATORIA'],
      },
      {
        value: 'DOCUMENTO_SOLICITACAO_FORMAL',
        text: 'Documento de solicitação formal de cotação de preços',
        fase: ['PREPARATORIA'],
      },
      {
        value: 'JUSTIFICATIVA_ESCOLHA_FORNECEDORES',
        text: 'Justificativa da escolha dos fornecedores constantes na cotação de preço de mercado',
        fase: ['PREPARATORIA'],
      },
      {
        value: 'DOCUMENTO_FORMALIZACAO_DEMANDA',
        text: 'Documento de Formalização de Demanda, com a descrição da necessidade da contratação',
        fase: ['PREPARATORIA'],
      },
      {
        value: 'ESTUDO_TECNICO_PRELIMINAR',
        text: 'Estudo Técnico Preliminar',
        fase: ['PREPARATORIA'],
      },
      {
        value: 'PARECER_JURIDICO_APROVACAO_MINUTAS',
        text: 'Parecer jurídico aprovando as minutas do edital e do contrato',
        fase: ['PREPARATORIA'],
      },
      {
        value: 'PLANO_CONTRATACAO_ANUAL',
        text: 'Plano de Contratações Anual',
        fase: ['PREPARATORIA'],
      },
      {
        value: 'PARECER_JURIDICO_ADMINISTRACAO',
        text: 'Parecer jurídico da Administração',
        fase: ['PREPARATORIA'],
      },
      {
        value: 'OUTROS_DOCUMENTOS',
        text: 'Outros documentos',
        fase: ['PREPARATORIA'],
      },
      {
        value: 'ORCAMENTO_ESTIMATIVO',
        text: 'Orçamento Estimativo - 1ª Etapa - (Planilha Excel)',
        fase: ['PREPARATORIA'],
      },
      {
        value: 'EDITAL_PROJETO_BASICO',
        text: 'Edital e Anexos',
        fase: ['PREPARATORIA'],
      },
      {
        value: 'PARECER_ASSESSORIA_JURIDICA',
        text: 'Parecer Jurídico',
        fase: ['DIVULGACAO_PUBLICACAO_LICITACAO'],
      },
      {
        value: 'COMPROVANTE_PUBLICACOES',
        text: 'Comprovante da publicação do aviso de licitação',
        fase: ['DIVULGACAO_PUBLICACAO_LICITACAO'],
      },
      {
        value: 'OUTROS_DOCUMENTOS_INTERNO',
        text: 'Outros documentos',
        fase: ['DIVULGACAO_PUBLICACAO_LICITACAO'],
      },
      {
        value: 'COMPROVANTE_DIVULGACAO_PNCP',
        text: 'Comprovante de divulgação e manutenção do inteiro teor do ato convocatório e de seus anexos no Portal Nacional de Contratações Públicas (PNCP)',
        fase: ['DIVULGACAO_PUBLICACAO_LICITACAO'],
      },
      {
        value: 'COMPROVANTE_PUBLICACAO_JORNAL',
        text: 'Comprovante da publicação do aviso de licitação em jornal diário de grande circulação',
        fase: ['DIVULGACAO_PUBLICACAO_LICITACAO'],
      },
      {
        value: 'COMPROVANTE_PUBLICACAO_DIARIO',
        text: 'Comprovante da publicação do aviso de licitação em Diário Oficial',
        fase: ['DIVULGACAO_PUBLICACAO_LICITACAO'],
      },
      {
        value: 'IMPUGNACAO_EDITAL',
        text: 'Impugnações do edital e respostas da Administração',
        fase: ['DIVULGACAO_PUBLICACAO_LICITACAO'],
      },
      {
        value: 'MAPA_LANCES',
        text: 'Mapa de lances (Planilha Excel/PDF)',
        fase: ['APRESENTACAO_PROPOSTAS_LANCES'],
      },
      {
        value: 'ATA_SESSAO',
        text: 'Ata da sessão da licitação',
        fase: ['APRESENTACAO_PROPOSTAS_LANCES'],
      },
      {
        value: 'PROPOSTAS_VENCEDORAS',
        text: 'Propostas vencedoras',
        fase: ['APRESENTACAO_PROPOSTAS_LANCES'],
      },
      {
        value: 'OUTROS_DOCUMENTOS_EXTERNO',
        text: 'Outros documentos',
        fase: ['APRESENTACAO_PROPOSTAS_LANCES', 'FINALIZACAO'],
      },
      {
        value: 'TERMO_HOMOLOGACAO',
        text: 'Termo de Homologação',
        fase: ['FINALIZACAO'],
      },
      {
        value: 'PROPOSTA_ADJUDICADA',
        text: 'Proposta Adjudicada - 2ª Etapa - (Planilha Excel)',
        fase: ['FINALIZACAO'],
      },
      {
        value: 'RECURSOS',
        text: 'Recursos interpostos e respostas da Administração',
        fase: ['FINALIZACAO'],
      },
      {
        value: 'TERMO_ADJUDICACAO',
        text: 'Termo de Adjudicação',
        fase: ['FINALIZACAO'],
      },
      {
        value: 'ATA_REGISTRO_PRECOS',
        text: 'Ata de Registro de Preços',
        fase: ['FINALIZACAO'],
      },
      {
        value: 'PROJETO_ENGENHARIA',
        text: 'Projetos de engenharia (básico, executivo e afins)',
        fase: ['PREPARATORIA'],
      },
    ];
  },

  getFormaPublicacaoPaginada() {
    return [
      {
        value: 'Diário Oficial da União (DOU)',
        text: 'DOU',
      },
      {
        value: 'Diário Oficial de Contas',
        text: 'DOC',
      },
      {
        value: 'Diário Oficial do Estado (DOE)',
        text: 'DOE',
      },
    ];
  },

  getTipoArquivoComissao() {
    return [
      { value: 'ARQUIVO_COMISSAO', text: 'Documento de designação da comissão' },
      { value: 'OUTROS_DOCUMENTOS', text: 'Outros documentos' },
    ];
  },

  getTipoArquivoTermoReferencia() {
    return [
      { value: 'ARQUIVO_TERMO_REFERENCIA', text: 'Arquivo do Termo de Referência' },
      { value: 'PROJETO_BASICO', text: 'Projeto Básico' },
      { value: 'OUTROS_DOCUMENTOS', text: 'Outros documentos' },
    ];
  },

  getTipoArquivoDispensa() {
    return [
      { value: 'JUSTIFICATIVA', text: 'Justificativa da necessidade do objeto' },
      {
        value: 'SITUACAO_EMERGENCIAL',
        text: 'Justificativa da situação de dispensa com os elementos necessários à sua caracterização',
      },
      { value: 'JUSTIFICATIVA_FORNECEDOR', text: 'Razões da escolha do fornecedor ou executante' },
      {
        value: 'JUSTIFICATIVA_PRECO',
        text: 'Justificativa do preço do fornecedor ou executante, incluindo pesquisa de preço de mercado, pesquisa de preço praticado por Órgãos e Entidades da Administração Pública e Mapa Comparativo de Preços',
      },
      { value: 'PROPOSTA_PRECOS', text: 'Proposta formal oferecida pelo contratado' },
      { value: 'PROJETO_BASICO_ORCAMENTO', text: 'Termo de Referência ou Projeto Básico' },
      {
        value: 'PROVA_DOCUMENTOS_EXAMINADOS',
        text: 'Parecer jurídico demonstrando o atendimento dos requisitos exigidos para a dispensa de licitação',
      },
      { value: 'TERMO_RATIFICACAO', text: 'Publicação do termo ou ato de ratificação da dispensa de licitação' },
      { value: 'DOCUMENTOS_FORNECEDOR', text: 'Documentos de habilitação e qualificação do fornecedor ou executante' },
      { value: 'MINUTA_CONTRATO', text: 'Minuta do contrato' },
      { value: 'AUTORIZACAO_COMPETENTE', text: 'Autorização da autoridade competente' },
      {
        value: 'COMPROVANTE_DIVULGACAO',
        text: 'Comprovante da divulgação, em sítio eletrônico oficial, do ato que autoriza a contratação direta ou o extrato decorrente do contrato',
      },
      {
        value: 'DEMONSTRACAO_COMPATIBILIDADE',
        text: 'Demonstração da compatibilidade da previsão de recursos orçamentários com o compromisso a ser assumido',
      },
      { value: 'DOCUMENTO_DEMANDA', text: 'Documento de formalização de demanda' },
      { value: 'DOCUMENTO_QUALIFICACAO_CONTRATO', text: 'Documentos de habilitação e qualificação do contratado' },
      { value: 'JUSTIFICATIVA_PRECO_CONTRATADO', text: 'Justificativa do preço do contratado' },
      { value: 'MAPA_PRECOS', text: 'Mapa Comparativo de Preços' },
      { value: 'PESQUISA_PRECO_MERCADO', text: 'Pesquisa de preço de mercado' },
      {
        value: 'PESQUISA_PRECO_ORGAO',
        text: 'Pesquisa de preço praticado por Órgãos e Entidades da Administração Pública',
      },
      { value: 'RAZAO_ESCOLHA_CONTRATADO', text: 'Razões da escolha do contratado' },
      { value: 'ANALISE_RISCOS', text: 'Análise de riscos' },
      { value: 'ESTUDO_TECNICO', text: 'Estudo Técnico Preliminar' },
      {
        value: 'PARECER_TECNICO',
        text: 'Pareceres técnicos demonstrando o atendimento dos requisitos exigidos para a contratação',
      },
      { value: 'JUSTIFICATIVA_PRECO_FORNECEDOR', text: 'Justificativa do preço do fornecedor ou executante' },
      {
        value: 'PROJETO_ENGENHARIA',
        text: 'Projetos de engenharia (básico, executivo e afins)',
      },
      { value: 'OUTROS_DOCUMENTOS', text: 'Outros documentos' },
    ];
  },

  getTipoArquivoInexigibilidade() {
    return [
      { value: 'AUTORIZACAO_COMPETENTE', text: 'Autorização da autoridade competente' },
      {
        value: 'COMPROVANTE_DIVULGACAO',
        text: 'Comprovante da divulgação, em sítio eletrônico oficial, do ato que autoriza a contratação direta ou extrato decorrente do contrato',
      },
      {
        value: 'DEMONSTRACAO_COMPATIBILIDADE',
        text: 'Demonstração da compatibilidade da previsão de recursos orçamentários com o compromisso a ser assumido',
      },
      { value: 'FORMALIZACAO_DEMANDA', text: 'Documento de formalização de demanda' },
      { value: 'HABILITACAO_QUALIFICACAO_CONTRATADO', text: 'Documentos de habilitação e qualificação do contratado' },
      { value: 'JUSTIFICATIVA_DO_PRECO', text: 'Justificativa do preço do contratado' },
      { value: 'MAPA_COMPARATIVO_PRECO', text: 'Mapa Comparativo de Preços' },
      {
        value: 'PROVA_DOCUMENTOS_EXAMINADOS',
        text: 'Parecer jurídico demonstrando o atendimento dos requisitos exigidos para a inexigibilidade de licitação',
      },
      { value: 'PESQUISA_PRECO_MERCADO', text: 'Pesquisa de preço de mercado' },
      {
        value: 'PESQUISA_PRECO_PUBLICO',
        text: 'Pesquisa de preço praticado por Órgãos e Entidades da Administração Pública',
      },
      { value: 'PROPOSTA_PRECOS', text: 'Proposta formal oferecida pelo contratado' },
      { value: 'RAZOES_CONTRATADO', text: 'Razões da escolha do contratado' },
      { value: 'ANALISE_RISCOS', text: 'Análise de riscos' },
      {
        value: 'HABILITACAO_FORNECEDOR',
        text: 'Documentos de habilitação e qualificação do fornecedor ou executante',
      },
      { value: 'JUSTIFICATIVA', text: 'Justificativa da necessidade do objeto' },
      {
        value: 'SITUACAO_EMERGENCIAL',
        text: 'Justificativa da situação de inexigibilidade com os elementos necessários à sua caracterização',
      },
      { value: 'JUSTIFICATIVA_PRECO', text: 'Justificativa do preço do fornecedor ou executante' },
      { value: 'TERMO_RATIFICACAO', text: 'Publicação do termo ou ato de ratificação da inexigibilidade de licitação' },
      { value: 'ESTUDO_TÉCNICO', text: 'Estudo Técnico Preliminar' },
      {
        value: 'PARECERES_TECNICOS',
        text: 'Pareceres técnicos demonstrando o atendimento dos requisitos exigidos para a contratação',
      },
      { value: 'TERMO_REFERENCIA', text: 'Termo de Referência ou Projeto Básico' },
      { value: 'JUSTIFICATIVA_FORNECEDOR', text: 'Razões da escolha do fornecedor ou executante' },
      { value: 'COMPROVANTE', text: 'Documento comprovante da inexigibilidade' },
      { value: 'MINUTA_CONTRATO', text: 'Minuta do contrato' },
      { value: 'OUTROS_DOCUMENTOS', text: 'Outros documentos' },
      { value: 'PROJETO_ENGENHARIA', text: 'Projetos de engenharia (básico, executivo e afins)' },
    ];
  },

  getTipoLicitacaoLegislacao() {
    return [
      { value: 'LEI_N_14133', text: 'Lei Nº 14.133' },
      { value: 'LEI_N_8666', text: 'Lei Nº 8666' },
      { value: 'OUTRA', text: 'Outra' },
    ];
  },

  getTipoLicitacaoLei14133() {
    return [
      { value: 'LEI_N_14133', text: 'Lei Nº 14.133' },
      { value: 'LEI_N_8666', text: 'Lei Nº 8.666' },
      { value: 'OUTRA', text: 'Outra' },
    ];
  },

  getTipoLicitacaoSomenteLei14133() {
    return this.getTipoLicitacaoLegislacao().filter((lei) => !['LEI_N_8666'].includes(lei.value));
  },

  getSituacaoParecer() {
    return [
      { value: 'SIM', text: 'Sim' },
      { value: 'NAO', text: 'Não' },
      { value: 'PARCIALMENTE', text: 'Parcialmente' },
      { value: 'NAO_APLICA', text: 'N/A' },
    ];
  },

  getTipoUF() {
    return [
      { value: 'AC', text: 'ACRE' },
      { value: 'AL', text: 'ALAGOAS' },
      { value: 'AP', text: 'AMAPA' },
      { value: 'AM', text: 'AMAZONAS' },
      { value: 'BA', text: 'BAHIA' },
      { value: 'CE', text: 'CEARA' },
      { value: 'DF', text: 'DISTRITO_FEDERAL' },
      { value: 'ES', text: 'ESPIRITO_SANTO' },
      { value: 'GO', text: 'GOIAS' },
      { value: 'MA', text: 'MARANHAO' },
      { value: 'MT', text: 'MATO_GROSSO' },
      { value: 'MS', text: 'MATO_GROSSO_DO_SUL' },
      { value: 'MG', text: 'MINAS_GERAIS' },
      { value: 'PA', text: 'PARA' },
      { value: 'PB', text: 'PARAIBA' },
      { value: 'PR', text: 'PARANA' },
      { value: 'PE', text: 'PERNAMBUCO' },
      { value: 'PI', text: 'PIAUI' },
      { value: 'RJ', text: 'RIO_DE_JANEIRO' },
      { value: 'RN', text: 'RIO_GRANDE_DO_NORTE' },
      { value: 'RS', text: 'RIO_GRANDE_DO_SUL' },
      { value: 'RO', text: 'RONDONIA' },
      { value: 'RR', text: 'RORAIMA' },
      { value: 'SC', text: 'SANTA_CATARINA' },
      { value: 'SP', text: 'SAO_PAULO' },
      { value: 'SE', text: 'SERGIPE' },
      { value: 'TO', text: 'TOCANTINS' },
    ];
  },

  getTipoArquivoCarona() {
    return [
      { value: 'JUSTIFICATIVA', text: 'Justificativa para contratação' },
      {
        value: 'OFICIO_GERENCIADOR',
        text: 'Ofício ao Órgão/Entidade gerenciador da ata, pedindo autorização para adesão',
      },
      {
        value: 'OFICIO_PRESTADOR_ATA',
        text: 'Ofício ao fornecedor/prestador da ata, consultando a disponibilidade em fornecer produtos/prestar serviços',
      },
      { value: 'PARECER_JURIDICO', text: 'Parecer jurídico do Órgão/Entidade aderente (carona)' },
      { value: 'PESQUISA_MAPA', text: 'Pesquisa de preço de mercado e Mapa Comparativo de Preços' },
      {
        value: 'OFICIO_RESPOSTAS_GERENCIADOR',
        text: 'Ofício com a resposta do Órgão/Entidade gerenciador da ata de registro de preços',
      },
      { value: 'EDITAL', text: 'Edital e Anexos' },
      { value: 'PARECER_GERENCIADOR', text: 'Parecer da assessoria jurídica' },
      { value: 'PROPOSTAS_VENCEDORAS', text: 'Propostas vencedoras' },
      { value: 'ATA_RP', text: 'Ata de Registro de Preços' },
      { value: 'HOMOLOGACAO', text: 'Termo de Homologação' },
      {
        value: 'OFICIO_RESPOSTAS_PRESTADOR_ATA',
        text: 'Ofício com a resposta do fornecedor/prestador da ata de registro de preços',
      },
      { value: 'TERMO_ADESAO', text: 'Publicação do Termo de Adesão a Ata de Registro de Preços' },
      {
        value: 'FORMALIZACAO_DEMANDA',
        text: 'Documento de Formalização de Demanda, com a descrição da necessidade da contratação',
      },
      {
        value: 'SOLICITACAO_FORMAL_ADERENTE',
        text: 'Documento de solicitação formal de cotação de preços do Órgão/Entidade aderente (carona)',
      },
      {
        value: 'ESTUDO_TEC_PRELIMINAR_ADERENTE',
        text: 'Estudo Técnico Preliminar do Órgão/Entidade aderente (carona)',
      },
      {
        value: 'JUSTIFICATIVA_ESCOLHA',
        text: 'Justificativa da escolha dos fornecedores constantes na cotação de preço de mercado',
      },
      {
        value: 'JUSTIFICATIVA_ESCOLHA_ADERENTE',
        text: 'Justificativa da escolha dos fornecedores constantes na cotação de preço de mercado feita pelo Órgão/Entidade aderente (carona)',
      },
      { value: 'JUSTIFICATIVA_VANTAGEM', text: 'Justificativa da vantagem da adesão' },
      { value: 'PESQUISA_PRECO', text: 'Pesquisa de preço de mercado feita pelo Órgão/Entidade gerenciador' },
      {
        value: 'PESQUISA_PRECO_MERCADO',
        text: 'Pesquisa de preço de mercado feita pelo Órgão/Entidade aderente (carona)',
      },
      {
        value: 'PESQUISA_PRECO_PUBLICA',
        text: 'Pesquisa de preço praticado por Órgãos e Entidades da Administração Pública feita pelo Órgão/Entidade gerenciador',
      },
      {
        value: 'PESQUISA_PRECO_ADERENTE',
        text: 'Pesquisa de preço praticado por Órgãos e Entidades da Administração Pública feita pelo Órgão/Entidade aderente (carona)',
      },
      { value: 'PUBLICACAO_EXTRATO', text: 'Publicação do extrato de adesão à ata de registro de preços' },
      { value: 'PLANO_CONTRATACAO_ADERENTE', text: 'Plano de Contratações Anual do Órgão/Entidade aderente (carona)' },
      { value: 'ATA_SESSAO_LICITACAO', text: 'Ata da sessão de licitação' },
      { value: 'MAPA_COMPARATIVO_ADERENTE', text: 'Mapa Comparativo de Preços do Órgão/Entidade aderente (carona)' },
      { value: 'MAPA_LANCES', text: 'Mapa de lances da licitação' },
      { value: 'TERMO_ADJUDIACAO', text: 'Termo de Adjudicação' },
      { value: 'SOLICITACAO_FORMAL', text: 'Documento de solicitação formal de cotação de preços' },
      { value: 'ESTUDO_TEC_PRELIMINAR', text: 'Estudo Técnico Preliminar' },
      { value: 'PLANO_CONTRATACAO', text: 'Plano de Contratações Anual' },
      {
        value: 'AUTORIZACAO_AUTORIDADE_COMPETENTE',
        text: 'Autorização da autoridade competente, do Órgão/Entidade aderente (carona)',
      },
      { value: 'OUTROS_DOCUMENTOS', text: 'Outros documentos' },
      { value: 'PROJETO_ENGENHARIA', text: 'Projetos de engenharia (básico, executivo e afins)' },
      { value: 'MAPA_COMPARATIVO_GERENCIADOR', text: 'Mapa comparativo de preços do Órgão/Entidade gerenciador' },
    ];
  },

  getTipoArquivoFinalizar() {
    return [
      {
        value: 'MAPA_LANCES',
        text: 'Mapa de Lances',
      },
      {
        value: 'ATA_SESSAO',
        text: 'Ata da sessão de licitação',
      },
      {
        value: 'TERMO_REVOGACAO',
        text: 'Termo de Revogação',
      },
      {
        value: 'TERMO_ANULACAO',
        text: 'Termo de Anulação',
      },
      {
        value: 'SENTENCA_JUDICIAL',
        text: 'Sentença judicial',
      },
      { value: 'PROPOSTAS_VENCEDORAS', text: 'Propostas vencedoras' },
      {
        value: 'ATA_SESSAO_DESERTA_OU_FRACASSADA',
        text: 'Ata da sessão de licitação (deserta ou fracassada)',
      },
      {
        value: 'JUSTIFICATIVA_REABERTURA',
        text: 'Documento de Justificativa da Reabertura',
      },
      { value: 'OUTROS_DOCUMENTOS', text: 'Outros documentos' },
      { value: 'EDITAL_ANEXOS_RETIFICADOS', text: 'Edital e Anexos retificados' },
      { value: 'AVISO_REABERTURA', text: 'Publicação do aviso de reabertura' },
      { value: 'IMPUGNACAO', text: 'Impugnação' },
      { value: 'MEDIDA_CAUTELAR', text: 'Medida Cautelar' },
      { value: 'PUBLICACAO_AVISO_SUSPENSAO', text: 'Publicação do aviso de suspensão' },
      { value: 'PUBLICACAO_AVISO_REVOGACAO', text: 'Publicação do aviso de revogação' },
      { value: 'PUBLICACAO_AVISO_ANULACAO', text: 'Publicação do aviso de anulação' },
      { value: 'PUBLICACAO_AVISO_PRORROGACAO', text: 'Publicação do aviso de prorrogação' },
    ];
  },

  getFasesLicitacao() {
    return [
      {
        value: 'PREPARATORIA',
        text: 'Preparatória',
        phaseKey: 'preparatoria',
        dateField: 'dataCadastroPreparatoria',
        order: 0,
      },
      {
        value: 'DIVULGACAO_PUBLICACAO_LICITACAO',
        text: 'Divulgação e Publicação da Licitação',
        phaseKey: 'divulgacaoEditalLicitacao',
        dateField: 'dataCadastro',
        order: 1,
      },
      {
        value: 'APRESENTACAO_PROPOSTAS_LANCES',
        text: 'Apresentação de Propostas e Lances',
        phaseKey: 'apresentacao',
        dateField: 'dataCadastroVencedores',
        order: 2,
      },
      {
        value: 'FINALIZACAO',
        text: 'Finalizados',
        phaseKey: 'finalizacao',
        dateField: 'dataUltimaOcorrencia',
        order: 3,
      },
    ];
  },

  getLabelsAnaliseProcesso() {
    return [
      { value: 'CADASTRADO', text: 'Cadastrados', phaseKey: 'cadastrados' },
      {
        value: 'AUDITORIA',
        text: 'Selecionados para Auditoria',
        phaseKey: 'auditoria',
      },
      { value: 'EM_ANALISE', text: 'Em Análise', phaseKey: 'em_analise' },
      { value: 'ALERTA', text: 'Alertas', phaseKey: 'alertas' },
      { value: 'FINALIZADO', text: 'Concluídos', phaseKey: 'concluidos' },
    ];
  },

  getTipoProcesso() {
    return [
      {
        value: 'L',
        text: 'Licitação',
        urlLabel: 'licitacao',
        licitantes: 'vencedores',
        singularLicitante: 'vencedor',
        licitante: 'Contratado(a)',
      },
      {
        value: 'I',
        text: 'Inexigibilidade',
        urlLabel: 'inexigibilidade',
        licitantes: 'fornecedores',
        singularLicitante: 'fornecedor',
        licitante: 'Fornecedor(a)',
      },
      {
        value: 'C',
        text: 'Adesão/Carona',
        urlLabel: 'carona',
        licitantes: 'detentores',
        singularLicitante: 'detentor',
        licitante: 'Contratado(a)',
      },
      {
        value: 'D',
        text: 'Dispensa',
        urlLabel: 'dispensa',
        licitantes: 'fornecedores',
        singularLicitante: 'fornecedor',
        licitante: 'Fornecedor(a)',
      },
      {
        value: 'CR',
        text: 'Credenciamento',
        urlLabel: 'credenciamento',
        licitantes: 'credenciados',
        singularLicitante: 'credenciado',
        licitante: 'Contratado(a)',
      },
    ];
  },

  getStatusContrato() {
    return [
      { value: 'NAO_PUBLICADA', text: 'Não Publicada' },
      { value: 'PUBLICADA', text: 'Publicada' },
      { value: 'REMOVIDA', text: 'Removida' },
    ];
  },

  getFormaContrato() {
    return [
      { value: 'CONTRATO', text: 'Contrato', label: 'Contrato' },
      { value: 'EQUIVALENTE_EMPENHO', text: 'Equivalente de Contrato: Empenho', label: 'Empenho' },
      {
        value: 'EQUIVALENTE_SERVICO',
        text: 'Equivalente de Contrato: Ordem de Serviço/Entrega',
        label: 'Ordem de Serviço/Entrega',
      },
      { value: 'CARTA_CONTRATO', text: 'Equivalente de Contrato: Carta-contrato', label: 'Carta-contrato' },
    ];
  },

  getFormaPreenchimentoSecaoTermoReferencia() {
    return [
      { value: 'PREENCHIMENTO_MANUAL', text: 'Preenchimento manual' },
      { value: 'IMPORTACAO_ARQUIVO', text: 'Preenchimento por importação de arquivo' },
    ];
  },

  getTipoArquivoContrato() {
    return [
      { value: 'ACEITE_PARTICIPACAO', text: 'Aceite de participação, pelo Órgão/Entidade gerenciador' },
      {
        value: 'ANALISE_RISCOS',
        text: 'Análise dos riscos que possam comprometer o sucesso da licitação e a boa execução contratual, feita pelo Órgão/Entidade participante',
      },
      { value: 'ATA_SESSAO_LICITACAO', text: 'Ata da sessão de licitação' },
      { value: 'ATA', text: 'Ata de Registro de Preços' },
      { value: 'DEBITOS_TRABALHISTAS', text: 'Certidão negativa de débitos trabalhistas' },
      { value: 'IMPEDIMENTO', text: 'Certidão negativa de impedimento (CNEP)' },
      { value: 'INIDONEIDADE', text: 'Certidão negativa de inidoneidade (CEIS)' },
      {
        value: 'COMPROVANTE_CONTRATO_PNCP',
        text: 'Comprovante da divulgação do contrato no Portal Nacional de Contratações Públicas (PNCP)',
      },
      {
        value: 'COMPROVANTE_EQUIVALENTE_PNCP',
        text: 'Comprovante da divulgação do equivalente de contrato no Portal Nacional de Contratações Públicas (PNCP)',
      },
      { value: 'COMPROVANTE_REGULARIDADE_FISCAL', text: 'Comprovante da regularidade fiscal do contratado' },
      { value: 'DESIGNACAO', text: 'Designação do gestor e fiscal' },
      {
        value: 'FORMALIZACAO',
        text: 'Documento de Formalização de Demanda, com a descrição da necessidade da contratação, do Órgão/Entidade participante',
      },
      {
        value: 'SOLICITACAO_COTACAO_PRECO',
        text: 'Documento de solicitação formal de cotação de preços, do Órgão/Entidade participante',
      },
      { value: 'EDITAL_ANEXOS', text: 'Edital e Anexos' },
      { value: 'ESTUDO', text: 'Estudo Técnico Preliminar do Órgão/Entidade participante' },
      { value: 'GARANTIA', text: 'Garantia contratual' },
      {
        value: 'JUSTIFICATIVA_ESCOLHA',
        text: 'Justificativa da escolha dos fornecedores constantes na cotação de preço de mercado feita pelo Órgão/Entidade participante',
      },
      { value: 'LICENCA', text: 'Licença ambiental' },
      { value: 'MAPA_PRECOS', text: 'Mapa Comparativo de Preços, do Órgão/Entidade gerenciador' },
      { value: 'MANIFESTACAO', text: 'Ofício enviado ao Órgão/Entidade gerenciador, com a manifestação de interesse' },
      { value: 'ORDEM', text: 'Ordem de serviço/entrega' },
      { value: 'OUTROS_DOCUMENTOS', text: 'Outros documentos' },
      { value: 'PARECER_JURIDICO', text: 'Parecer Jurídico do Órgão/Entidade participante' },
      { value: 'PESQUISA_PRECO', text: 'Pesquisa de preço de mercado feita pelo Órgão/Entidade participante' },
      {
        value: 'PESQUISA_PRECO_ADMINISTRACAO',
        text: 'Pesquisa de preço praticado por Órgãos e Entidades da Administração Pública, feita pelo Órgão/Entidade participante',
      },
      { value: 'PLANO_CONTRATACOES', text: 'Plano de Contratações Anual do Órgão/Entidade participante' },
      { value: 'PROPOSTA', text: 'Propostas vencedoras' },
      { value: 'ADJUDICACAO', text: 'Termo de Adjudicação' },
      { value: 'HOMOLOGACAO', text: 'Termo de Homologação' },
      { value: 'COPIA_CONTRATO', text: 'Contrato' },
      { value: 'CARTA_CONTRATO', text: 'Equivalente de contrato: Carta-contrato' },
      { value: 'EMPENHO', text: 'Equivalente de contrato: Empenho' },
      { value: 'SERVICO_ENTREGA', text: 'Equivalente de contrato: Ordem de Serviço/Entrega' },
      { value: 'RESUMO', text: 'Publicação do resumo do contrato' },
      { value: 'NOTA_EMPENHO', text: 'Nota de empenho' },
      { value: 'RESUMO_EQUIVALENTE', text: 'Publicação do resumo do equivalente de contrato' },
      { value: 'JUSTIFICATIVA', text: 'Justificativa para contratação' },
      { value: 'RESUMO_DIARIO_CONTRATO', text: 'Publicação do resumo do contrato em Diário Oficial' },
      { value: 'RESUMO_DIARIO_EQUIVALENTE', text: 'Publicação do resumo do equivalente de contrato em Diário Oficial' },
      { value: 'AUTORIZACAO_AUTORIDADE', text: 'Autorização da autoridade competente' },
      {
        value: 'FORMALIZACAO_DEMANDA',
        text: 'Documento de Formalização de Demanda, com a descrição da necessidade da contratação',
      },
    ];
  },

  getTipoArquivoRescisaoContrato() {
    return [
      {
        value: 'RESCISAO',
        text: 'Rescisão Contratual',
      },
    ];
  },
  getMotivosRescisaoContrato() {
    return [
      { value: 'RESCISAO', text: 'Rescisão' },
      { value: 'NAO_CUMPRIMENTO', text: 'Não cumprimento ou cumprimento irregular' },
      { value: 'DESATENDIMENTO', text: 'Desatendimento das determinações' },
      { value: 'ALTERACAO_SOCIAL', text: 'Alteração social ou modificação da finalidade' },
      { value: 'FALENCIA', text: 'Falência, insolvência civil, dissolução da sociedade ou falecimento' },
      { value: 'FORTUITO', text: 'Caso fortuito ou de força maior' },
      {
        value: 'LICENCA_AMBIENTAL',
        text: 'Atraso ou impossibilidade de licença ambiental/alteração significativa do anteprojeto em decorrência da licença',
      },
      { value: 'ATRASO_LIBERACAO', text: 'Atraso na liberação das áreas (desapropriação, servidão, desocupação)' },
      { value: 'INTERESSE_PUBLICO', text: 'Razões de interesse público' },
      {
        value: 'RESERVA_CARGOS',
        text: 'Não cumprimento de reserva de cargos (pessoa com deficiência, reabilitado da previdência ou aprendiz)',
      },
      { value: 'OUTROS', text: 'Outros' },
    ];
  },
  getFormasExtincaoContrato() {
    return [
      { value: 'UNILATERAL_POR_ESCRITO', text: 'Ato unilateral da administração: por escrito' },
      {
        value: 'UNILATERAL_CONDUTA_ADMINISTRACAO',
        text: 'Ato unilateral da administração: não pode decorrer de conduta da administração',
      },
      { value: 'CONSENSUAL_CONCILIACAO', text: 'Consensual: conciliação' },
      { value: 'CONSENSUAL_MEDIACAO', text: 'Consensual: mediação' },
      { value: 'CONSENSUAL_COMITE', text: 'Consensual: comitê de resolução de disputas' },
      { value: 'DETERMINACAO_DECISAO_ARBITRAL', text: 'Determinação: por decisão arbitral' },
      { value: 'DETERMINACAO_DECISAO_JUDICIAL', text: 'Determinação: por decisão judicial' },
    ];
  },
  getFasesLicitacaoNum() {
    return [
      { value: 1, text: 'PREPARATORIA' },
      { value: 2, text: 'DIVULGACAO_PUBLICACAO_LICITACAO' },
      { value: 3, text: 'APRESENTACAO_PROPOSTAS_LANCES' },
      { value: 4, text: 'FINALIZACAO' },
    ];
  },

  getTipoProcessoReqModificacao() {
    return [
      { value: 'LICITACAO', text: 'Licitação' },
      { value: 'CARONA', text: 'Carona' },
      { value: 'DISPENSA', text: 'Dispensa' },
      { value: 'INEXIGIBILIDADE', text: 'Inexigibilidade' },
      { value: 'CONTRATO', text: 'Contrato' },
      { value: 'RESCISAO_CONTRATUAL', text: 'Rescisão Contratual' },
      { value: 'ADITIVO', text: 'Aditivo' },
      { value: 'EMPENHO', text: 'Empenho' },
      { value: 'EMPENHO_LICITACAO', text: 'Empenho / Ata de Registro de Preço' },
      { value: 'TERMO_REFERENCIA', text: 'Termo de Referência' },
      { value: 'OBRA_MEDICAO', text: 'Medição de Obra' },
      { value: 'CREDENCIAMENTO', text: 'Credenciamento' },
      { value: 'CREDENCIADO', text: 'Credenciado' },
      { value: 'ANULACAO_REVOGACAO', text: 'Anulação/Revogação' },
    ];
  },

  getTipoProcessoReqModificacaoSemEmpenho() {
    return [
      { value: 'LICITACAO', text: 'Licitação' },
      { value: 'CARONA', text: 'Carona' },
      { value: 'DISPENSA', text: 'Dispensa' },
      { value: 'INEXIGIBILIDADE', text: 'Inexigibilidade' },
      { value: 'CONTRATO', text: 'Contrato' },
      { value: 'RESCISAO_CONTRATUAL', text: 'Rescisão Contratual' },
      { value: 'ADITIVO', text: 'Aditivo' },
      { value: 'TERMO_REFERENCIA', text: 'Termo de Referência' },
      { value: 'OBRA_MEDICAO', text: 'Medição de Obra' },
      { value: 'CREDENCIAMENTO', text: 'Credenciamento' },
    ];
  },

  getTipoRevisor() {
    return [
      { value: 'HUMANO', text: 'HUMANO' },
      { value: 'REV_HUMANO', text: 'REV_HUMANO' },
      { value: 'NER_SUBSISTENCIA', text: 'NER_SUBSISTENCIA' },
      { value: 'NER_TINTAS', text: 'NER_TINTAS' },
      { value: 'NER_MEDICAMENTOS', text: 'NER_MEDICAMENTOS' },
      { value: 'NER_GERAL', text: 'NER_GERAL' },
      { value: 'NER_VESTUARIO', text: 'NER_VESTUARIO' },
      { value: 'NER_VEICULOS', text: 'NER_VEICULOS' },
    ];
  },

  getTipoArquivoAditivo() {
    return [
      { value: 'JUSTIFIVATIVA_ADITIVO', text: 'Justificativa' },
      { value: 'PARECER_JURIDICO_ADM', text: 'Parecer Jurídico' },
      { value: 'PUBLICACAO_EXTRATO', text: 'Publicação do resumo do aditivo' },
      { value: 'OUTROS_DOCUMENTOS', text: 'Outros Documentos' },
      { value: 'CONCORDANCIA_FORNECEDOR', text: 'Concordância do Fornecedor' },
      { value: 'PESQUISA_PRECO', text: 'Pesquisa de Preço' },
      { value: 'TERMO_ADITIVO', text: 'Termo Aditivo/Apostilamento' },
    ];
  },

  getTipoArquivoEmpenho() {
    return [
      { value: 'NOTA_EMPENHO', text: 'Nota de empenho' },
      { value: 'OUTROS_DOCUMENTOS', text: 'Outros Documentos' },
    ];
  },

  getTipoAlteracaoContratual() {
    return [
      { value: 'ALTERACAO_PRAZO', text: 'Prorrogação da vigência' },
      { value: 'REDUCAO_VIGENCIA', text: 'Redução da vigência' },
      { value: 'ALTERACAO_VALOR', text: 'Aditivo de valor' },
      { value: 'SUPRESSAO_VALOR', text: 'Supressão de valor' },
      { value: 'APOSTILAMENTO', text: 'Apostilamento' },
      { value: 'OUTRA', text: 'Outra' },
    ];
  },

  getMotivoAlteracao() {
    return [
      { value: 'ORDEM_JUDICIAL', text: 'Ordem Judicial' },
      { value: 'OUTROS', text: 'Outros' },
      { value: 'DESISTENCIA_DO_VENCEDOR', text: 'Desistência do Vencedor' },
    ];
  },

  getTiposProcesso() {
    return [
      { key: 'L', value: 'LICITACAO', text: 'Licitação', lowerText: 'licitacao', textCamelCase: 'Licitacao' },
      { key: 'C', value: 'CARONA', text: 'Adesão (Carona)', lowerText: 'carona', textCamelCase: 'Carona' },
      { key: 'D', value: 'DISPENSA', text: 'Dispensa', lowerText: 'dispensa', textCamelCase: 'Dispensa' },
      {
        key: 'I',
        value: 'INEXIGIBILIDADE',
        text: 'Inexigibilidade',
        lowerText: 'inexigibilidade',
        textCamelCase: 'Inexigibilidade',
      },
      {
        key: 'CR',
        value: 'CREDENCIAMENTO',
        text: 'Credenciamento',
        lowerText: 'credenciamento',
        textCamelCase: 'Credenciamento',
      },
    ];
  },

  getTiposAnaliseAutomatica() {
    return [
      { value: 'OBJETO', text: 'Por objeto' },
      { value: 'VALOR', text: 'Por valor' },
    ];
  },

  getCategoriasNaturezaObjeto() {
    return [
      { value: 'OBRAS', text: 'Obras e Serviços de Engenharia' },
      { value: 'COMPRAS', text: 'Compras' },
      { value: 'SERVICOS', text: 'Serviços' },
    ];
  },

  getTiposEmpenho() {
    return [
      { value: 'ORDINARIO', text: 'Ordinário' },
      { value: 'ESTIMATIVO', text: 'Estimativo' },
      { value: 'GLOBAL', text: 'Global' },
    ];
  },

  getTiposLayout() {
    return [
      { value: 'compacto', text: 'Compacto' },
      { value: 'default', text: 'Confortável' },
      { value: 'extenso', text: 'Largo' },
    ];
  },

  getAtributosTipoProcesso(processo) {
    const processos = {
      licitacao: [
        {
          label: 'Tipo do Processo',
          field: '',
          valueFunc: () => getValueByKey(processo.toUpperCase(), this.getTipoProcessoReqModificacao()),
        },
        { label: 'Tipo da Licitação', field: 'tipo', valueFunc: (value) => value?.nome ?? value },
        {
          label: 'Número do Processo Administrativo',
          field: 'numeroProcessoAdm',
          valueFunc: (value) => value,
        },
        { label: 'Usuário', field: 'usuario', valueFunc: (value) => value?.nome },
        {
          label: 'Fontes de Recurso',
          field: 'fontesDeRecurso',
          valueFunc: (value) => value?.map((fonte) => fonte.nome) ?? value,
        },
        { label: 'Entidade', field: 'entidade', valueFunc: (value) => value?.nome ?? value },
        { label: 'Número', field: 'numero', valueFunc: (value) => value },
        { label: 'Ano', field: 'ano', valueFunc: (value) => value },
        { label: 'Objeto', field: 'objeto', valueFunc: (value) => value },
        {
          label: 'Naturezas do Objeto',
          field: 'naturezasDoObjeto',
          valueFunc: (value) => {
            const values = Array.isArray(value) ? value : value.split('; ').filter((e) => e !== '');
            const naturezasObjeto = values.map((v) => {
              const result = getValueByKey(v, this.getNaturezaObjetoLicitacao());
              return result === '-' ? v : result;
            });
            return naturezasObjeto?.join(', ') ?? '-';
          },
        },
        {
          label: 'Termo de Referência',
          field: 'termoReferencia',
          valueFunc: (value) => value?.identificadorProcesso ?? value,
        },
        { label: 'Regência Legal', field: 'regenciaLegal', valueFunc: (value) => value },
        { label: 'Parecerista', field: 'parecerista', valueFunc: (value) => value?.nome ?? value },
        {
          label: 'Valor Estimado',
          field: 'valorEstimado',
          valueFunc: (value) => getValueMoneyIfNotFormatted(value),
        },
        { label: 'Data', field: 'dataAbertura', valueFunc: (value) => getValueDate(value) },
        {
          label: 'Comissão',
          field: 'comissao',
          valueFunc: (value) =>
            typeof value === 'object'
              ? value.numero +
                ' - ' +
                (value.entidade ? value.entidade.nome + ' -' : '') +
                ' ' +
                getValueByKey(value.tipo, this.getTipoComissao())
              : value,
        },
      ],
      carona: [
        {
          label: 'Tipo do Processo',
          field: '',
          valueFunc: () => getValueByKey(processo.toUpperCase(), this.getTipoProcessoReqModificacao()),
        },
        { label: 'Usuário', field: 'usuario', valueFunc: (value) => value?.nome },
        { label: 'Entidade', field: 'entidade', valueFunc: (value) => value?.nome ?? value },
        {
          label: 'Termo de Referência',
          field: 'termoReferencia',
          valueFunc: (value) => value?.identificadorProcesso ?? value,
        },
        {
          label: 'Número',
          field: 'numeroProcessoAdministrativo',
          valueFunc: (value) => value,
        },
        { label: 'Objeto', field: 'objeto', valueFunc: (value) => value },
        {
          label: 'Valor Estimado',
          field: 'valor',
          valueFunc: (value) => getValueMoneyIfNotFormatted(value),
        },
        { label: 'Data', field: 'dataCadastro', valueFunc: (value) => getValueDate(value) },
        {
          label: 'Licitação',
          field: 'licitacao',
          valueFunc: (value) =>
            typeof value === 'object'
              ? `${value.entidade ? value.entidade.nome : ''} - ${value.modalidadeLicitacao ?? ''} ${value.numero}/${
                  value.ano
                } - Data de abertura: ${getValueDate(value.dataAbertura)}`
              : value,
        },
        {
          label: 'Natureza do Objeto',
          field: 'naturezasDoObjeto',
          valueFunc: (value) => {
            const values = Array.isArray(value) ? value : value.split('; ').filter((e) => e !== '');
            const naturezasObjeto = values.map((v) => {
              const result = getValueByKey(v, this.getNaturezaObjetoLicitacao());
              return result === '-' ? v : result;
            });
            return naturezasObjeto?.join(', ') ?? '-';
          },
        },
      ],
      dispensa: [
        {
          label: 'Tipo do Processo',
          field: '',
          valueFunc: () => getValueByKey(processo.toUpperCase(), this.getTipoProcessoReqModificacao()),
        },
        { label: 'Usuário', field: 'usuario', valueFunc: (value) => value?.nome },
        { label: 'Entidade', field: 'entidade', valueFunc: (value) => value?.nome ?? value },
        { label: 'Número Processo', field: 'numeroProcesso', valueFunc: (value) => value },
        {
          label: 'Termo de Referência',
          field: 'termoReferencia',
          valueFunc: (value) => value?.identificadorProcesso ?? value,
        },
        { label: 'Objeto', field: 'objeto', valueFunc: (value) => value },
        {
          label: 'Valor Estimado',
          field: 'valor',
          valueFunc: (value) => getValueMoneyIfNotFormatted(value),
        },
        { label: 'Data', field: 'dataCadastro', valueFunc: (value) => getValueDate(value) },
        {
          label: 'Naturezas do Objeto',
          field: 'naturezasDoObjeto',
          valueFunc: (value) => {
            const values = Array.isArray(value) ? value : value.split('; ').filter((e) => e !== '');
            const naturezasObjeto = values.map((v) => {
              const result = getValueByKey(v, this.getNaturezaObjetoLicitacao());
              return result === '-' ? v : result;
            });
            return naturezasObjeto?.join(', ') ?? '-';
          },
        },
      ],
      inexigibilidade: [
        {
          label: 'Tipo do Processo',
          field: '',
          valueFunc: () => getValueByKey(processo.toUpperCase(), this.getTipoProcessoReqModificacao()),
        },
        { label: 'Usuário', field: 'usuario', valueFunc: (value) => value?.nome },
        { label: 'Entidade', field: 'entidade', valueFunc: (value) => value?.nome ?? value },
        {
          label: 'Termo de Referência',
          field: 'termoReferencia',
          valueFunc: (value) => value?.identificadorProcesso ?? value,
        },
        {
          label: 'Número do Processo',
          field: 'numeroProcesso',
          valueFunc: (value) => value,
        },
        { label: 'Objeto', field: 'objeto', valueFunc: (value) => value },
        {
          label: 'Valor Estimado',
          field: 'valor',
          valueFunc: (value) => getValueMoneyIfNotFormatted(value),
        },
        {
          label: 'Data',
          field: 'dataPedido',
          valueFunc: (value) => getValueDate(value),
        },
        {
          label: 'Naturezas do Objeto',
          field: 'naturezasDoObjeto',
          valueFunc: (value) => {
            const values = Array.isArray(value) ? value : value.split('; ').filter((e) => e !== '');
            const naturezasObjeto = values.map((v) => {
              const result = getValueByKey(v, this.getNaturezaObjetoLicitacao());
              return result === '-' ? v : result;
            });
            return naturezasObjeto?.join(', ') ?? '-';
          },
        },
      ],
      contrato: [
        {
          label: 'Tipo do Processo',
          field: '',
          valueFunc: () => getValueByKey(processo.toUpperCase(), this.getTipoProcessoReqModificacao()),
        },
        { label: 'Usuário', field: 'usuario', valueFunc: (value) => value?.nome },
        {
          label: 'Modalidade',
          field: 'modalidadeLicitacao',
          valueFunc: (value) => getValueByKey(value, this.getModalidadeLicitacao()),
        },
        { label: 'Entidade', field: 'entidade', valueFunc: (value) => value?.nome ?? value },
        { label: 'Número da Licitação', field: 'numeroLicitacao', valueFunc: (value) => value },
        { label: 'Ano da Licitação', field: 'anoLicitacao', valueFunc: (value) => value },
        { label: 'Objeto', field: 'objeto', valueFunc: (value) => value },
        {
          label: 'Valor',
          field: 'valorGlobal',
          valueFunc: (value) => getValueMoneyIfNotFormatted(value),
        },
        { label: 'Data', field: 'dataPublicacao', valueFunc: (value) => getValueDate(value) },
        {
          label: 'Permite Aditivo',
          field: 'permiteAditivo',
          valueFunc: (value) => getValueByKey(typeof value === 'boolean' ? value : value === 'true', this.getSimNao()),
        },
      ],
      aditivo: [
        {
          label: 'Tipo do Processo',
          field: '',
          valueFunc: () => getValueByKey(processo.toUpperCase(), this.getTipoProcessoReqModificacao()),
        },
        { label: 'Usuário', field: 'contrato', valueFunc: (contrato) => contrato?.usuario?.nome },
        {
          label: 'Modalidade',
          field: 'contrato',
          valueFunc: (contrato) => getValueByKey(contrato?.modalidadeLicitacao, this.getModalidadeLicitacao()),
        },
        { label: 'Entidade', field: 'contrato', valueFunc: (contrato) => contrato?.entidade?.nome },
        { label: 'Número', field: 'numero', valueFunc: (value) => value },
        { label: 'Ano', field: 'contrato', valueFunc: (contrato) => contrato?.anoLicitacao },
        { label: 'Objeto', field: 'contrato', valueFunc: (contrato) => contrato?.objeto },
        {
          label: 'Valor Estimado',
          field: 'valor',
          valueFunc: (value) => getValueMoneyIfNotFormatted(value),
        },
        { label: 'Data', field: 'dataPublicacao', valueFunc: (value) => getValueDate(value) },
      ],
      rescisao_contratual: [
        {
          label: 'Tipo do Processo',
          field: '',
          valueFunc: () => getValueByKey(processo.toUpperCase(), this.getTipoProcessoReqModificacao()),
        },
        { label: 'Usuário', field: 'usuario', valueFunc: (value) => value?.nome },
        { label: 'Entidade', field: 'entidade', valueFunc: (value) => value?.nome ?? value },
        { label: 'Objeto', field: 'objeto', valueFunc: (value) => value },
        { label: 'Valor Estimado', field: 'valorGlobal', valueFunc: (value) => getValueMoney(value) },
        {
          label: 'Motivo da Rescisão',
          field: 'motivoRescisao',
          valueFunc: (value) => {
            let result = getValueByKey(value, this.getMotivosRescisaoContrato());
            if (result === '-' && value) result = value;
            return result;
          },
        },
        {
          label: 'Forma de Extinção',
          field: 'formaExtincao',
          valueFunc: (value) => {
            let result = getValueByKey(value, this.getFormasExtincaoContrato());
            if (result === '-' && value) result = value;
            return result;
          },
        },
        {
          label: 'Descrição da Rescisão',
          field: 'descricaoRescisao',
          valueFunc: (value) => value,
        },
        {
          label: 'Data da Rescisão',
          field: 'dataAvisoRescisao',
          valueFunc: (value) => getValueDate(value),
        },
      ],
      termo_referencia: [
        {
          label: 'Tipo do Processo',
          field: '',
          valueFunc: () => getValueByKey(processo.toUpperCase(), this.getTipoProcessoReqModificacao()),
        },
        { label: 'Usuário', field: 'usuario', valueFunc: (value) => value?.nome },
        { label: 'Entidade', field: 'entidade', valueFunc: (value) => value?.nome ?? value },
        { label: 'Data de Cadastro', field: 'dataCadastro', valueFunc: (value) => getValueDate(value) },
        { label: 'Identificador do Processo', field: 'identificadorProcesso', valueFunc: (value) => value },
        {
          label: 'É SRP?',
          field: 'srp',
          valueFunc: (value) => getValueByKey(typeof value === 'boolean' ? value : value === 'true', this.getSimNao()),
        },
        {
          label: 'Itens com 3 casas decimais',
          field: 'tresCasasDecimais',
          valueFunc: (value) => getValueByKey(typeof value === 'boolean' ? value : value === 'true', this.getSimNao()),
        },
        {
          label: 'Forma de Preenchimento das Seções',
          field: 'formaPreenchimentoSecao',
          valueFunc: (value) => {
            let result = getValueByKey(value, this.getFormaPreenchimentoSecaoTermoReferencia());
            if (result === '-' && value) result = value;
            return result;
          },
        },
        {
          label: 'É Obra de Engenharia / Serviço Especial de Engenharia',
          field: 'obraEngenharia',
          valueFunc: (value) => getValueByKey(typeof value === 'boolean' ? value : value === 'true', this.getSimNao()),
        },
      ],
      obra_medicao: [
        {
          label: 'Tipo do Processo',
          field: '',
          valueFunc: () => getValueByKey(processo.toUpperCase(), this.getTipoProcessoReqModificacao()),
        },
        { label: 'Usuário', field: 'usuario', valueFunc: (value) => value?.nome },
        {
          label: 'Obra',
          field: 'obra',
          valueFunc: (value) =>
            'Obra ' +
              getValueByKey(value?.status, this.getStatusObra()) +
              ', Categoria: ' +
              value?.categoria?.nome +
              ', Tipo: ' +
              value?.tipo?.nome ?? value,
        },
        { label: 'Data de Medição', field: 'dataMedicao', valueFunc: (value) => getValueDate(value) },
        { label: 'Valor Pago', field: 'valorEmpenhado', valueFunc: (value) => getValueMoneyIfNotFormatted(value) },
        { label: 'Número do Empenho', field: 'numeroEmpenho', valueFunc: (value) => value },
      ],
      credenciamento: [
        {
          label: 'Tipo do Processo',
          field: '',
          valueFunc: () => getValueByKey(processo.toUpperCase(), this.getTipoProcessoReqModificacao()),
        },
        { label: 'Usuário', field: 'usuario', valueFunc: (value) => value?.nome },
        { label: 'Entidade', field: 'entidade', valueFunc: (value) => value?.nome ?? value },
        {
          label: 'Termo de Referência',
          field: 'termoReferencia',
          valueFunc: (value) => value?.identificadorProcesso ?? value,
        },
        {
          label: 'Número do Processo',
          field: 'numeroProcesso',
          valueFunc: (value) => value,
        },
        { label: 'Objeto', field: 'objeto', valueFunc: (value) => value },
        {
          label: 'Naturezas do Objeto',
          field: 'naturezasDoObjeto',
          valueFunc: (value) => {
            const values = Array.isArray(value) ? value : value.split('; ').filter((e) => e !== '');
            const naturezasObjeto = values.map((v) => {
              const result = getValueByKey(v, this.getNaturezaObjetoLicitacao());
              return result === '-' ? v : result;
            });
            return naturezasObjeto?.join(', ') ?? '-';
          },
        },
      ],
      credenciado: [
        {
          label: 'Tipo do Processo',
          field: '',
          valueFunc: () => getValueByKey(processo.toUpperCase(), this.getTipoProcessoReqModificacao()),
        },
        {
          label: 'Data de Cadastro',
          field: 'dataCadastro',
          valueFunc: (value) => getValueDate(value),
        },
        {
          label: 'Credenciado',
          field: 'licitante',
          valueFunc: (value) => value?.nome,
        },
        {
          label: 'Início da Vigência',
          field: 'vigenciaInicial',
          valueFunc: (value) => getValueDate(value),
        },
        {
          label: 'Fim da Vigência',
          field: 'vigenciaFinal',
          valueFunc: (value) => getValueDate(value),
        },
        {
          label: 'Situação',
          field: 'situacao',
          valueFunc: (value) => getValueByKey(value, DadosEstaticosService.getSituacaoCredenciado()),
        },
      ],
      anulacao_revogacao: [
        {
          label: 'Tipo do Processo Associado',
          field: 'tipoProcessoAssociado',
          valueFunc: (value) => getValueByKey(value, this.getTipoProcessoReqModificacao()),
        },
        { label: 'Usuário Responsável', field: 'responsavel', valueFunc: (value) => value?.nome },
        {
          label: 'Tipo de Ocorrência',
          field: 'tipoOcorrencia',
          valueFunc: (value) => getValueByKey(value, this.getValueAnulacaoRevogacao()),
        },
        {
          label: 'Descrição da Anulação/Revogação',
          field: 'descricao',
          valueFunc: (value) => value,
        },
      ],
    };

    return processos[processo];
  },

  getTipoSelecaoMapa() {
    return [
      { value: 'PONTO', text: 'Ponto' },
      { value: 'LINHA', text: 'Linha' },
      { value: 'POLIGONO', text: 'Polígono' },
    ];
  },

  getSituacaoItemChecklist() {
    return [
      { value: 'SIM', text: 'Sim' },
      { value: 'NAO', text: 'Não' },
      { value: 'JUSTIFICADO', text: 'Justificado' },
      { value: 'NAO_APLICA', text: 'Não se aplica' },
    ];
  },

  getColumnDataAberturaByTipoProcesso(tipoProcesso) {
    return {
      licitacao: 'dataAbertura',
      carona: 'dataAdesao',
      inexigibilidade: 'dataPedido',
      dispensa: 'dataPedido',
      credenciamento: 'dataCadastro',
    }[tipoProcesso];
  },

  getColumnPregoeiroResponsavelByTipoProcesso(tipoProcesso) {
    return {
      licitacao: 'pregoeiro',
      carona: { responsavelAdesao: 'responsavelAdesao', responsavelAdesaoCarona: 'responsavelAdesaoCarona' },
      inexigibilidade: {
        responsavelHomologacao: 'responsavelHomologacao',
        responsavelRatificacao: 'responsavelRatificacao',
        responsavelInexigibilidade: 'responsavelInexigibilidade',
      },
      dispensa: { gestor: 'gestor', responsavelDispensa: 'responsavelDispensa' },
      credenciamento: 'responsavel',
    }[tipoProcesso];
  },

  getColumnValorEstimadoByTipoProcesso(tipoProcesso) {
    return { licitacao: 'valorEstimado', carona: 'valor', inexigibilidade: 'valor', dispensa: 'valor' }[tipoProcesso];
  },

  getColumnNumeroProcessoByTipoProcesso(tipoProcesso) {
    return {
      licitacao: 'numeroProcessoAdm',
      carona: 'numeroProcessoAdministrativo',
      inexigibilidade: 'numeroProcesso',
      dispensa: 'numeroProcesso',
      credenciamento: 'numeroProcesso',
    }[tipoProcesso];
  },

  getColumnItemLicitanteByTipoProcesso(tipoProcesso) {
    return {
      L: 'vencedor',
      C: 'detentor',
      I: 'fornecedorInexigibilidade',
      D: 'fornecedorDispensa',
      CR: 'credenciado',
    }[tipoProcesso];
  },

  getMaterialAttributes() {
    return ['descricaoDetalhamento', 'descricao', 'codigo', 'capacidadeEmbalagem'];
  },

  getMaterialAttributesEntidades() {
    return ['unidadeMedida', 'materialEmbalagem', 'tipoEmbalagem'];
  },

  getLabelRequisicaoModificacao() {
    return {
      ADICAO: { label: 'Adicionado', color: 'green', labelInf: 'Adição' },
      REMOCAO: { label: 'Removido', color: 'red', labelInf: 'Remoção' },
      EDICAO: { label: 'Editado', color: '#2196F3', labelInf: 'Edição' },
      REABERTURA: { label: 'Reaberta', color: 'rgb(4, 139, 168)', labelInf: 'Reabertura' },
    };
  },

  getLabelByAttribute(atributo, tipoProcesso) {
    const atributos = {
      fiscal: 'Fiscal',
      fundamentacaoLegalEntidade: 'Fundamentação Legal',
      gestor: 'Gestor',
      responsavelDispensa: 'Responsável pela Dispensa',
      responsavel: 'Responsável',
      licitantes: 'Licitante',
      numeroProcessoSEI: 'Número do Processo Administrativo',
      observacoes: 'Observações',
      responsavelRatificacao: 'Responsável pela Inexigibilidade',
      responsavelInexigibilidade: 'Responsável pela Inexigibilidade',
      status: 'Status',
      tipo: 'Tipo',
      fontesDeRecurso: 'Fontes de Recurso',
      numero: 'Número',
      naturezaDoObjeto: 'Natureza do Objeto',
      naturezasDoObjeto: 'Naturezas do Objeto',
      dataPedido: tipoProcesso === 'INEXIGIBILIDADE' ? 'Data da Autorização' : 'Data do Pedido',
      responsavelAdesao: 'Responsável pela Adesão',
      idResponsavelAdesaoCarona: 'Identificador Responsável pela Adesão',
      numeroProcessoGerenciadorAta: 'Número do Processo Gerenciador da Ata',
      dataAdesao: 'Data da Adesão',
      entidadeOrigem: 'Entidade de Origem',
      tipoObra: 'Tipo da Obra',
      modalidadeLicitacaoNova: 'Modalidade da Licitação',
      categoriaObra: 'Categoria da Obra',
      coordenadas: 'Coordenadas',
      pregoeiro: 'Pregoeiro',
      orgaosParticipantes: 'Órgãos Participantes',
      acatadoJustificado: 'Acatado/Justificado',
      gestorSubstituto: 'Gestor Substituto',
      dataVigenciaInicial: 'Data de Início da Vigência',
      inicioVigencia: 'Data de Início da Vigência',
      numeroContratoSei: 'Número do Contrato SEI',
      numeroDoe: 'Número DOE',
      fiscalSubstituto: 'Fiscal Substituto',
      dataVigenciaFinal: 'Data de Fim da Vigência',
      fimVigencia: 'Data de Fim da Vigência',
      gestorTitular: 'Gestor Titular',
      gestorSuplente: 'Gestor Suplente',
      justificativa: 'Justificativa',
      fiscalSuplente: 'Fiscal Suplente',
      fiscalAditivo: 'Fiscal do Aditivo',
      outroMotivoRescisao: 'Outro Motivo da Rescisão',
      garantias: 'Garantias',
      valorEstimado: 'Valor Estimado',
      valorAdjudicado: 'Valor Adjudicado',
      objeto: 'Objeto',
      ano: 'Ano',
      regenciaLegal: 'Regência Legal',
      dataAbertura: 'Data de Abertura',
      comissao: 'Comissão',
      modalidade: 'Modalidade',
      parecerista: 'Parecerista',
      obra: 'Obra',
      valor: 'Valor',
      dataValidadeAta: 'Data de Validade da Ata',
      numeroProcessoAdministrativo: 'Número do Processo Administrativo',
      numeroProcessoAdm: 'Número do Processo Administrativo',
      numeroSei: 'Número da Inexigibilidade',
      fundamentacao: 'Fundamentação',
      numeroProcesso: 'Número do Processo',
      origem: 'Origem',
      permiteAditivo: 'Permite Aditivo',
      dataPublicacao: 'Data da Publicação',
      motivoRescisao: 'Motivo da Rescisão',
      dataAvisoRescisao: 'Data de Aviso da Rescisão',
      formaExtincao: 'Forma da Extinção',
      descricaoRescisao: 'Descrição da Rescisão',
      identificadorProcesso: 'Identificador do Processo',
      srp: 'Srp',
      tresCasasDecimais: 'Termo com Três Casas Decimais',
      entidade: 'Entidade',
      descricao: 'Descrição',
      dataMedicao: 'Data de Medição',
      valorEmpenhado: 'Valor Pago',
      numEmpenho: 'Número do Empenho',
      percConclusao: 'Percentual de Conclusão',
      obraEngenharia: 'Obra de Engenharia / Serviço Especial de Engenharia',
      valorGlobal: 'Valor Estimado',
      tipoEmpenho: 'Tipo',
      nomeAutoridadeContratante: 'Autoridade Contratante',
      idAutoridadeContratante: 'Identificador Autoridade Contratante',
      dataEmpenho: 'Data de Empenho',
      fundamentacaoLegal: 'Fundamentação Legal',
      tiposLicitacao: 'Critérios de Julgamento',
      tipoAdjudicacao: 'Tipo do Critério',
      presidenteComissao: 'Presidente da Comissão',
      sitioDivulgacao: 'Sítio de Divulgação do Edital de Chamamento Público',
      tipoContratacao: 'Tipo de Contratação',
      comissaoContratacao: 'Comissão de Contratação',
      termoReferencia: 'Termo de Referência',
    };

    return atributos[atributo];
  },

  getGarantiasContrato() {
    return [
      { name: 'Caução em dinheiro ou em títulos da dívida pública', code: 'CAUCAO_DINHEIRO' },
      { name: 'Seguro garantia', code: 'SEGURO_GARANTIA' },
      { name: 'Fiança bancária', code: 'FIANCA_BANCARIA' },
    ];
  },

  getTiposConsulta() {
    return [
      { value: false, text: 'Materiais', name: 'materiais', singularText: 'Material' },
      { value: true, text: 'Serviços', name: 'servicos', singularText: 'Serviço' },
    ];
  },

  getColumnsMatrizRisco(tiposProcessos) {
    const columns = [
      { value: 'VALOR_ESTIMADO', text: 'Valor estimado', type: 'numeric', relations: ['C', 'L'] },
      { value: 'MODALIDADE_LICITACAO', text: 'Modalidade licitação', type: 'varchar', relations: ['L'] },
      { value: 'TIPOS_LICITACAO', text: 'Critério de Julgamento', type: 'list', relations: ['L'] },
      { value: 'NATUREZAS_OBJETO', text: 'Naturezas de objeto', type: 'list', relations: ['L'] },
      { value: 'PERMITE_CONSORCIO', text: 'Permite Consórcio', type: 'boolean', relations: ['L'] },
    ];

    const result = columns?.filter((column) => {
      let exist = true;
      tiposProcessos?.forEach((tipo) => {
        if (!column.relations.includes(tipo.tipoProcesso)) {
          exist = false;
          return;
        }
      });
      return exist;
    });
    return result;
  },

  getTipoComparacao() {
    return [
      { value: 'IGUAL_A', text: 'Igual a', compatible: ['numeric', 'varchar', 'boolean'] },
      { value: 'MAIOR_QUE', text: 'Maior que', compatible: ['numeric'] },
      { value: 'MENOR_QUE', text: 'Menor que', compatible: ['numeric'] },
      { value: 'DIFERENTE_DE', text: 'Diferente de', compatible: ['numeric', 'varchar'] },
      { value: 'MAIOR_OU_IGUAL_A', text: 'Maior ou igual a', compatible: ['numeric'] },
      { value: 'MENOR_OU_IGUAL_A', text: 'Menor ou igual a', compatible: ['numeric'] },
      { value: 'INTERVALO', text: 'Intervalo', compatible: ['numeric'] },
      { value: 'CONTEM', text: 'Contém', compatible: ['list'] },
    ];
  },

  getStatusAlertaAnalise() {
    return [
      { value: 'ENCAMINHADO', text: 'Encaminhado' },
      { value: 'DEVOLVIDO', text: 'Devolvido' },
      { value: 'JURISDICIONADO', text: 'Com o jurisdicionado' },
      { value: 'ESCLARECIDO', text: 'Esclarecimentos Iniciais' },
      { value: 'RESPONDIDO', text: 'Respondido' },
      { value: 'ARQUIVADO', text: 'Arquivado' },
      { value: 'INSPETOR', text: 'Com Inspetor' },
      { value: 'REJEITADO_INSPETOR', text: 'Rejeitado pelo Inspetor' },
    ];
  },

  getStatusMonitoramentoAtosDiarioOficial() {
    return [
      { value: 'EM_EXTRACAO', text: 'Diário sem atos' },
      { value: 'EM_CLASSIFICACAO', text: 'Diário em processamento' },
      { value: 'EXTRAINDO_INFORMACOES', text: 'Extraindo informações' },
      { value: 'PROCESSADO', text: 'Processado' },
    ];
  },

  getStatusProcessamentoAtosLicDiarioOficial() {
    return [
      { value: 'CLASSIFICADO', text: 'Classificado' },
      { value: 'NER_APLICADO', text: 'NER Aplicado' },
    ];
  },

  getStatusProcessamentoEditaisLicitacao() {
    return [
      { value: 'EM_EXTRACAO', text: 'Em Extração' },
      { value: 'EM_CLASSIFICACAO', text: 'Em Classificação' },
      { value: 'PROCESSADO', text: 'Processado' },
    ];
  },

  getClassificacaoSentencaEdital() {
    return [
      { value: 'OBJETO', text: 'Objeto', color: '#f6eddc' },
      { value: 'QUALIFICACAO_TECNICA', text: 'Qualificação Técnica', color: '#e3e5d7' },
      { value: 'PRAZO_DE_PAGAMENTO', text: 'Prazo de Pagamento', color: '#bdd6d2' },
      { value: 'CLAUSULA_DE_ATRASO', text: 'Cláusula de Atraso', color: '#a5c8ca' },
      { value: 'PRAZO_DE_ENTREGA', text: 'Prazo de Entrega', color: '#a1c1be' },
      { value: 'LOCAL_ENTREGA_OU_EXECUCAO', text: 'Local de Entrega ou Execução', color: '#facf7d' },
      { value: 'OUTRO', text: 'Outro', color: '#f0f0d8' },
    ];
  },

  getPercentualConclusaoObra() {
    return [
      { value: 0, text: '0%' },
      { value: 25, text: '25%' },
      { value: 50, text: '50%' },
      { value: 75, text: '75%' },
      { value: 100, text: '100%' },
    ];
  },

  getLabelByStatusAlerta() {
    return [
      {
        value: 'ALERTA_RESPONDIDO',
        text: 'Respondido',
        backgroundColor: '#f4fcf7',
        color: '#22c55e',
        border: '1px solid #22c55e',
        tooltip: 'Alerta Respondido pelo Jurisdicionado',
      },
      {
        value: 'ALERTA_ENVIADO',
        text: 'Diretor DAFO',
        backgroundColor: '#fff8f3',
        color: '#f97316',
        border: '1px solid #f97316',
        tooltip: 'Alerta Enviado ao Diretor da DAFO',
      },
      {
        value: 'ALERTA_RECEBIDO',
        text: 'Com Jurisdicionado',
        backgroundColor: '#fefbf3',
        color: '#eab308',
        border: '1px solid #eab308',
        tooltip: 'Alerta Enviado ao Jurisdicionado',
      },
      {
        value: 'ALERTA_INSPETOR',
        text: 'Inspetor',
        backgroundColor: '#fefbf3',
        color: '#422343',
        border: '1px solid #422343',
        tooltip: 'Alerta Enviado ao Inspetor',
      },
      {
        value: 'ALERTA_REJEITADO_INSPETOR',
        text: 'Rejeitado',
        backgroundColor: '#fefbf3',
        color: '#ff3d32',
        border: '1px solid #ff3d32',
        tooltip: 'Alerta Rejeitado pelo Inspetor',
      },
    ];
  },

  getStatusArquivamento() {
    return [
      {
        value: 'CONSISTENTE',
        label: 'Consistente',
        color: '#008100',
        backgroundColor: '#fefbf3',
        border: '1px solid #4da73b',
      },
      {
        value: 'INCONSISTENTE',
        label: 'Inconsistente',
        color: 'red',
        backgroundColor: '#fff5f5',
        border: '1px solid #ff3d32',
      },
      {
        value: 'INCONSISTENCIA_RESOLVIDA',
        label: 'Inconsistência resolvida',
        color: '#2F83DC',
        backgroundColor: '#e2ffff',
        border: '1px solid #2F83DC',
      },
      {
        value: 'INCONSISTENCIA_NAO_RESOLVIDA',
        label: 'Inconsistência não resolvida',
        color: '#eab308',
        backgroundColor: '#fefbf3',
        border: '1px solid #eab308',
      },
      {
        value: 'REJEITADO',
        label: 'Rejeitado',
        color: 'red',
        backgroundColor: '#fff5f5',
        border: '1px solid #ff3d32',
      },
      {
        value: 'ARQUIVADO_AUTOMATICAMENTE',
        label: 'Arquivado automaticamente',
        color: '#8f48d2',
        backgroundColor: '#fbf7ff',
        border: '1px solid #8f48d2',
      },
    ];
  },

  getTipoArquivoObraMedicao() {
    return [
      { value: 'PLANILHA_MEDICOES', text: 'Planilha de medições' },
      { value: 'IMAGEM_MEDICAO', text: 'Imagem de medição' },
      { value: 'OUTROS_DOCUMENTOS', text: 'Outros documentos' },
    ];
  },

  getStatusMatchMapeamentoAtoLicitacao() {
    return [
      { value: 'CONFIRMADO', text: 'Mapeado' },
      { value: 'NAO_CONFIRMADO', text: 'Mapeado pelo Mateiro' },
      { value: 'NAO_MAPEADO', text: 'Não Mapeado' },
    ];
  },

  getTipoTermoReferencia() {
    return [
      { value: 'ITENS', text: 'Itens' },
      { value: 'LOTES', text: 'Lotes' },
    ];
  },

  getStatusRequisicaoModificacao() {
    return [
      { value: 'ACEITA', text: 'Aceita', name: 'aceita' },
      { value: 'ENVIADA', text: 'Pendente', name: 'pendente' },
      { value: 'NEGADA', text: 'Negada', name: 'negada' },
    ];
  },

  getTipoAdjudicacaoLicitacao() {
    return [
      { value: 'ITEM', text: 'Item' },
      { value: 'LOTE', text: 'Lote' },
      { value: 'GLOBAL', text: 'Global' },
    ];
  },

  getTipoAdjudicacao() {
    return [
      { value: 'ITEM', text: 'Item' },
      { value: 'LOTE', text: 'Lote' },
    ];
  },

  getObjetoObra() {
    return [
      {
        type: 'CONSTRUCAO',
        label: 'Construção',
        icon: 'pi-building',
        description: 'Executar uma ou mais novas construções, bem como continuar obras já iniciadas.',
        subtypes: [
          {
            label: 'Nova Obra',
            type: 'NOVA_OBRA',
          },
          {
            label: 'Continuação de Obra',
            type: 'CONTINUACAO_OBRA',
          },
        ],
      },
      {
        type: 'ALTERACAO',
        label: 'Alteração',
        icon: 'pi-pencil',
        description: 'Modificações em obras já existentes, como reformas, ampliações ou recuperações',
        subtypes: [
          {
            label: 'Ampliação',
            type: 'AMPLIACAO',
          },
          {
            label: 'Reforma',
            type: 'REFORMA',
          },
          {
            label: 'Recuperação',
            type: 'RECUPERACAO',
          },
        ],
      },
      {
        type: 'SERVICO',
        label: 'Serviço',
        icon: 'pi-wrench',
        description: 'Conserto, instalação, montagem ou manutenção de rede elétrica, telefonia, etc.',
        subtypes: [],
      },
    ];
  },

  getFaseObra() {
    return [
      { value: 'CADASTRAL', text: 'Cadastral' },
      { value: 'INICIAL', text: 'Inicial' },
      { value: 'MEDICAO', text: 'Medição' },
      { value: 'FINALIZACAO', text: 'Finalização' },
      { value: 'INTERRUPCAO', text: 'Interrupção' },
      { value: 'ENTREGA', text: 'Entrega' },
    ];
  },

  getStatusObra() {
    return [
      { value: 'NAO_INICIADA', text: 'Não iniciada' },
      { value: 'EM_ANDAMENTO', text: 'Em andamento' },
      { value: 'FINALIZADA', text: 'Finalizada' },
      { value: 'INTERROMPIDA', text: 'Interrompida' },
      { value: 'PARALISADA', text: 'Paralisada' },
      { value: 'ATRASADA', text: 'Atrasada' },
    ];
  },

  getCicloObra() {
    return [
      { value: 'INICIO', text: 'Inicio' },
      { value: 'MEDICAO', text: 'Medições' },
      { value: 'FIM', text: 'Fim' },
      { value: 'ENTREGA', text: 'Entrega' },
    ];
  },

  getTipoArquivoObra() {
    return [
      { value: 'PROJETO_BASICO', text: 'Projeto Básico', fase: 'CADASTRAL' },
      { value: 'ORDEM_DE_SERVICO', text: 'Ordem de Serviço', fase: 'CADASTRAL' },
      { value: 'PLANILHA_CONTRATADA', text: 'Planilha Contratada', fase: 'CADASTRAL' },
      { value: 'OUTROS_DOCUMENTOS', text: 'Outros Documentos', fase: 'CADASTRAL' },
      { value: 'OUTROS_DOCUMENTOS', text: 'Outros Documentos', fase: 'ENTREGA' },
      { value: 'GEORREFERENCIAMENTO', text: 'Georreferenciamento', fase: 'INICIAL' },
      { value: 'FOTO', text: 'Foto', fase: 'INICIAL' },
      { value: 'DIARIO_OBRAS', text: 'Diário de Obras', fase: 'MEDICAO' },
      { value: 'GEORREFERENCIAMENTO', text: 'Georreferenciamento', fase: 'FINALIZACAO' },
      { value: 'FOTO', text: 'Foto', fase: 'FINALIZACAO' },
    ];
  },

  getOrigemProcessoCarona() {
    return [
      { value: true, text: 'Órgão/Entidade jurisdicionado do TCE/AC' },
      { value: false, text: 'Órgão/Entidade NÃO jurisdicionado do TCE/AC' },
    ];
  },

  getTipoDimensaoObra() {
    return [
      { value: 'M', text: 'm' },
      { value: 'M2', text: 'm²' },
      { value: 'KM', text: 'km' },
      { value: 'KM2', text: 'km²' },
    ];
  },

  getTipoArquivoRelatorioObra() {
    return [{ value: 'PLANILHA_ORCAMENTARIA', text: 'Planilha Orcamentaria' }];
  },

  getTipoContratacao() {
    return [
      { value: 'PARALELA_E_NAO_EXCLUDENTE', text: 'Paralela e não excludente' },
      { value: 'COM_SELECAO_E_CRITERIO_DE_TERCEIROS', text: 'Com seleção a critério de terceiros' },
      { value: 'MERCADOS_FLUIDOS', text: 'Mercados fluidos' },
    ];
  },

  getTipoArquivoCredenciado() {
    return [
      {
        value: 'HABILITACAO_QUALIFICACAO_CREDENCIADO',
        text: 'Documentos de habilitação e qualificação do credenciado',
      },
      {
        value: 'PARECERES_TECNICOS',
        text: 'Pareceres técnicos demonstrando o atendimento dos requisitos exigidos para o credenciamento',
      },
      {
        value: 'OUTROS_DOCUMENTOS',
        text: 'Outros documentos',
      },
    ];
  },

  getTipoArquivoCredenciamento() {
    return [
      {
        value: 'COMPROVANTE_PUBLICACAO_AVISO',
        text: 'Comprovante da publicação do aviso de chamamento público em Diário Oficial',
      },
      {
        value: 'EDITAL_ANEXOS',
        text: 'Edital e Anexos',
      },
      {
        value: 'JUSTIFICATIVA_ESCOLHA',
        text: 'Justificativa da escolha pela contratação por credenciamento',
      },
      {
        value: 'MAPA_COMPARATIVO_PRECO',
        text: 'Mapa Comparativo de Preços (Planilha Excel/PDF)',
      },
      {
        value: 'PARECER_JURIDICO',
        text: 'Parecer jurídico da Administração',
      },
      {
        value: 'PESQUISA_PRECO_MERCADO',
        text: 'Pesquisa de preço de mercado',
      },
      {
        value: 'PESQUISA_PRECO_PUBLICO',
        text: 'Pesquisa de preço praticado por Órgãos e Entidades da Administração Pública',
      },
      {
        value: 'ANALISE_RISCOS',
        text: 'Análise de riscos',
      },
      {
        value: 'ESTUDO_TECNICO',
        text: 'Estudo Técnico Preliminar',
      },
      {
        value: 'OUTROS_DOCUMENTOS',
        text: 'Outros documentos',
      },
      { value: 'PROJETO_ENGENHARIA', text: 'Projetos de engenharia (básico, executivo e afins)' },
    ];
  },

  getStatusCredenciamento() {
    return [
      { value: 'NAO_PUBLICADA', text: 'Não publicada' },
      { value: 'PUBLICADA', text: 'Publicada' },
      { value: 'REMOVIDA', text: 'Removida' },
    ];
  },

  getTipoArquivoMedicaoObra() {
    return [
      { value: 'NOTA_FISCAL', text: 'Nota Fiscal', accept: '.pdf' },
      {
        value: 'GEORREFERENCIAMENTO',
        label: 'Georreferenciamento',
        accept: '.kml',
      },
      { value: 'FOTO', text: 'Foto de Medição', accept: 'image/*' },
      {
        value: 'RELATORIO_TECNICO',
        text: 'Relatório Técnico',
        accept: '.pdf',
      },
      { value: 'PLANILHA_MEDICAO', text: 'Planilha de Medição', accept: '.xlsx' },

      { value: 'OUTROS_DOCUMENTOS', text: 'Outros documentos', accept: '.pdf' },
    ];
  },

  getSituacaoCredenciado() {
    return [
      { value: 'VIGENTE', text: 'Vigente' },
      { value: 'ENCERRADO', text: 'Encerrado' },
      { value: 'SUSPENSO', text: 'Suspenso' },
    ];
  },

  getMotivoInterrupcaoObra() {
    return [
      { value: 'DETERMINACAO_JUDICIAL', text: 'Determinação Judicial' },
      { value: 'RESCISAO_CONTRATUAL', text: 'Recisão Contratual' },
      { value: 'OUTRO_MOTIVO', text: 'Outro Motivo' },
    ];
  },

  getTipoArquivoDiarioObra() {
    return [
      { value: 'ARQUIVO_DIARIO', text: 'Diário de Obras' },
      { value: 'OUTROS_DOCUMENTOS', text: 'Outros documentos' },
    ];
  },

  getValueAnulacaoRevogacao() {
    return [
      { value: 'ANULAR', text: 'Anular' },
      { value: 'REVOGAR', text: 'Revogar' },
    ];
  },

  getValueAnuladoRevogado() {
    return [
      { value: 'ANULAR', text: 'Anulado' },
      { value: 'REVOGAR', text: 'Revogado' },
    ];
  },

  getTipoArquivoAnulacaoRevogacao() {
    return [
      {
        value: 'PUBLICACAO_AVISO_REVOGACAO_AR',
        text: 'Publicação do aviso de revogação',
      },
      {
        value: 'PUBLICACAO_AVISO_ANULACAO_AR',
        text: 'Publicação do aviso de anulação',
      },
      {
        value: 'TERMO_ANULACAO_AR',
        text: 'Termo de anulação',
      },
      {
        value: 'TERMO_REVOGACAO_AR',
        text: 'Termo de revogação',
      },
      {
        value: 'OUTROS_DOCUMENTOS',
        text: 'Outros Documentos',
      },
    ];
  },

  getTipoArquivoBoletimObra() {
    return [
      { value: 'ARQUIVO_BOLETIM', text: 'Boletins de Obras' },
      { value: 'OUTROS_DOCUMENTOS', text: 'Outros documentos' },
    ];
  },
};

export default DadosEstaticosService;
