import axios from 'axios';
import { ServiceBase } from 'fc/services/ServiceBase';
import ApiEndpoints from '~/constants/ApiEndpoints';

class EmpenhoContratoService extends ServiceBase {
  constructor() {
    super(ApiEndpoints.empenhoContrato);
  }

  upload(file) {
    const formData = new FormData();
    formData.append('file', file);
    return axios.post(`${this.baseUrl}/${this.endpoint}/upload`, formData);
  }

  download(fileDTO) {
    let params = `lookupId=${fileDTO.lookupId}&nomeOriginal=${fileDTO.nomeOriginal}&tipoArquivo=${fileDTO.tipoArquivo}`;
    return axios.get(`${this.baseUrl}/${this.endpoint}/download?${params}`, { responseType: 'blob' });
  }

  removerArquivo(idEmpenhoContrato, idArquivo) {
    return axios.delete(`${this.baseUrl}/${this.endpoint}/${idEmpenhoContrato}/arquivos/${idArquivo}`);
  }

  recuperarArquivos(idEmpenhoContrato) {
    return axios.get(`${this.baseUrl}/${this.endpoint}/${idEmpenhoContrato}/arquivos`);
  }

  atualizarArquivo(idEmpenhoContrato, idArquivo, arquivoEmpenhoContratoDTO) {
    return axios.put(
      `${this.baseUrl}/${this.endpoint}/${idEmpenhoContrato}/arquivos/${idArquivo}`,
      arquivoEmpenhoContratoDTO
    );
  }

  saveArquivos(idEmpenhoContrato, arquivosEmpenhoContratoDTO) {
    return axios.post(
      `${this.baseUrl}/${this.endpoint}/${idEmpenhoContrato}/salvar-arquivos`,
      arquivosEmpenhoContratoDTO
    );
  }

  criarEmpenho(empenhoDTO) {
    return axios.post(`${this.baseUrl}/${this.endpoint}/novo`, empenhoDTO);
  }
}

const instance = new EmpenhoContratoService();

export default instance;
