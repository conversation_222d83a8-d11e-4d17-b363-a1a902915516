import { ServiceBase } from 'fc/services/ServiceBase';
import ApiEndpoints from '~/constants/ApiEndpoints';
import axios from 'axios';

class RequisicaoModificacaoService extends ServiceBase {
  constructor() {
    super(ApiEndpoints.requisicaoModificacao);
  }

  requisicaoRemocaoContrato(requisicaoRemocaoDTO) {
    return axios.post(`${this.baseUrl}/${this.endpoint}/requisicao-remocao-contrato`, requisicaoRemocaoDTO);
  }

  requisicaoRemocaoAditivoContrato(requisicaoRemocaoDTO) {
    return axios.post(`${this.baseUrl}/${this.endpoint}/requisicao-remocao-aditivo-contrato`, requisicaoRemocaoDTO);
  }

  requisicaoRemocaoCarona(requisicaoRemocaoDTO) {
    return axios.post(`${this.baseUrl}/${this.endpoint}/requisicao-remocao-carona`, requisicaoRemocaoDTO);
  }

  requisicaoRemocaoDispensa(requisicaoRemocaoDTO) {
    return axios.post(`${this.baseUrl}/${this.endpoint}/requisicao-remocao-dispensa`, requisicaoRemocaoDTO);
  }

  requisicaoRemocaoInexigibilidade(requisicaoRemocaoDTO) {
    return axios.post(`${this.baseUrl}/${this.endpoint}/requisicao-remocao-inexigibilidade`, requisicaoRemocaoDTO);
  }

  requisicaoRemocaoLicitacao(requisicaoRemocaoDTO) {
    return axios.post(`${this.baseUrl}/${this.endpoint}/requisicao-remocao-licitacao`, requisicaoRemocaoDTO);
  }

  requisicaoReaberturaLicitacao(requisicaoReaberturaDTO) {
    return axios.post(`${this.baseUrl}/${this.endpoint}/requisicao-reabertura-licitacao`, requisicaoReaberturaDTO);
  }

  requisicaoRemocaoCredenciamento(requisicaoRemocaoDTO) {
    return axios.post(`${this.baseUrl}/${this.endpoint}/requisicao-remocao-credenciamento`, requisicaoRemocaoDTO);
  }

  requisicaoRemocaoCredenciado(requisicaoRemocaoDTO) {
    return axios.post(`${this.baseUrl}/${this.endpoint}/requisicao-remocao-credenciado`, requisicaoRemocaoDTO);
  }

  requisicaoModificacaoLicitacao(idLicitacao, dto) {
    return axios.post(`${this.baseUrl}/${this.endpoint}/${idLicitacao}/requisicao-modificacao-licitacao`, dto);
  }

  requisicaoModificacaoInexigibilidade(idInexigibilidade, dto, lei) {
    return axios.post(
      `${this.baseUrl}/${this.endpoint}/${idInexigibilidade}/requisicao-modificacao-inexigibilidade/${lei}`,
      dto,
      lei
    );
  }

  requisicaoModificacaoContrato(idContrato, dto) {
    return axios.post(`${this.baseUrl}/${this.endpoint}/${idContrato}/requisicao-modificacao-contrato`, dto);
  }

  requisicaoModificacaoRescisao(idContrato, dto) {
    return axios.post(`${this.baseUrl}/${this.endpoint}/${idContrato}/requisicao-modificacao-rescisao-contratual`, dto);
  }

  requisicaoModificacaoAditivoContrato(idAditivo, dto) {
    return axios.post(`${this.baseUrl}/${this.endpoint}/${idAditivo}/requisicao-modificacao-aditivo-contrato`, dto);
  }

  requisicaoModificacaoCarona(idCarona, dto) {
    return axios.post(`${this.baseUrl}/${this.endpoint}/${idCarona}/requisicao-modificacao-carona`, dto);
  }

  getModificacoes(idRequisicaoModificacao) {
    return axios.get(`${this.baseUrl}/${this.endpoint}/${idRequisicaoModificacao}/modificacoes`);
  }

  getArquivosModificados(idRequisicaoModificacao) {
    return axios.get(`${this.baseUrl}/${this.endpoint}/${idRequisicaoModificacao}/arquivos-modificados`);
  }

  requisicaoModificacaoDispensa(idDispensa, dto, lei) {
    return axios.post(`${this.baseUrl}/${this.endpoint}/${idDispensa}/requisicao-modificacao-dispensa/${lei}`, dto);
  }

  requisicaoModificacaoTermoReferencia(idTermoReferencia, dto) {
    return axios.post(
      `${this.baseUrl}/${this.endpoint}/${idTermoReferencia}/requisicao-modificacao-termo-referencia`,
      dto
    );
  }

  requisicaoModificacaoObraMedicao(idObraMedicao, dto) {
    return axios.post(`${this.baseUrl}/${this.endpoint}/${idObraMedicao}/requisicao-modificacao-obra-medicao`, dto);
  }

  requisicaoModificacaoCredenciamento(idCredenciamento, dto) {
    return axios.post(
      `${this.baseUrl}/${this.endpoint}/${idCredenciamento}/requisicao-modificacao-credenciamento`,
      dto
    );
  }

  download(idReqMod, fileDTO) {
    const params = `lookupId=${fileDTO.lookupId}&nomeOriginal=${fileDTO.nomeOriginal}&tipoArquivo=${fileDTO.tipoArquivo}`;
    return axios.get(`${this.baseUrl}/${this.endpoint}/${idReqMod}/download?${params}`, { responseType: 'blob' });
  }

  requisicaoModificacaoAnulacaoRevogacao(idProcesso, dto) {
    return axios.post(`${this.baseUrl}/${this.endpoint}/${idProcesso}/requisicao-modificacao-anulacao-revogacao`, dto);
  }
}

const instance = new RequisicaoModificacaoService();

export default instance;
