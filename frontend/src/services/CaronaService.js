import { ServiceBase } from 'fc/services/ServiceBase';
import ApiEndpoints from '~/constants/ApiEndpoints';
import axios from 'axios';

class CaronaService extends ServiceBase {
  constructor() {
    super(ApiEndpoints.carona);
  }

  upload(file) {
    const formData = new FormData();
    formData.append('file', file);
    return axios.post(`${this.baseUrl}/${this.endpoint}/upload`, formData);
  }

  download(fileDTO, countDownloads) {
    const params = `lookupId=${fileDTO.lookupId}&nomeOriginal=${fileDTO.nomeOriginal}&tipoArquivo=${
      fileDTO.tipoArquivo
    }&countDownloads=${countDownloads ?? false}`;
    return axios.get(`${this.baseUrl}/${this.endpoint}/download?${params}`, { responseType: 'blob' });
  }

  recuperarArquivos(idCarona) {
    return axios.get(`${this.baseUrl}/${this.endpoint}/${idCarona}/arquivos`);
  }

  validaArquivos(carona) {
    return axios.post(`${this.baseUrl}/${this.endpoint}/arquivos/validacao`, carona);
  }

  removerArquivo(idCarona, idArquivo) {
    return axios.delete(`${this.baseUrl}/${this.endpoint}/${idCarona}/arquivos/${idArquivo}`);
  }

  atualizarArquivo(idCarona, idArquivo, arquivoCaronaDTO) {
    return axios.put(`${this.baseUrl}/${this.endpoint}/${idCarona}/arquivos/${idArquivo}`, arquivoCaronaDTO);
  }

  criarCarona(caronaDTO) {
    return axios.post(`${this.baseUrl}/${this.endpoint}/novo`, caronaDTO);
  }

  getTresCasasDecimais(idCarona) {
    return axios.get(`${this.baseUrl}/${this.endpoint}/${idCarona}/tres-casas-decimais`);
  }

  getAnosCarona() {
    return axios.get(`${this.baseUrl}/${this.endpoint}/anos-carona`);
  }

  getPessoasResponsaveis(idEntidade) {
    return axios.get(`${this.baseUrl}/${this.endpoint}/${idEntidade}/responsaveis-carona`);
  }

  getAlertaCarona(idCarona) {
    return axios.get(`${this.baseUrl}/${this.endpoint}/alerta/${idCarona}`);
  }
}

const instance = new CaronaService();

export default instance;
