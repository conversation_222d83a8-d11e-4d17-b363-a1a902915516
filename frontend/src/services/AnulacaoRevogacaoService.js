import axios from 'axios';
import { ServiceBase } from 'fc/services/ServiceBase';
import ApiEndpoints from '~/constants/ApiEndpoints';

class AnulacaoRevogacaoService extends ServiceBase {
  constructor() {
    super(ApiEndpoints.AnulacaoRevogacao);
  }

  anularRevogarProcesso(anulacaoRevogacaoDTO, idProcesso) {
    return axios.post(`${this.baseUrl}/${this.endpoint}/${idProcesso}`, anulacaoRevogacaoDTO);
  }

  upload(file) {
    const formData = new FormData();
    formData.append('file', file);
    return axios.post(`${this.baseUrl}/${this.endpoint}/upload`, formData);
  }

  download(fileDTO) {
    const params = `lookupId=${fileDTO.lookupId}&nomeOriginal=${fileDTO.nomeOriginal}&tipoArquivo=${fileDTO.tipoArquivo}`;
    return axios.get(`${this.baseUrl}/${this.endpoint}/download?${params}`, { responseType: 'blob' });
  }

  recuperarArquivos(id) {
    return axios.get(`${this.baseUrl}/${this.endpoint}/${id}/arquivos`);
  }

  validaArquivos(anulacaoRevogacao) {
    return axios.post(`${this.baseUrl}/${this.endpoint}/arquivos/validacao`, anulacaoRevogacao);
  }

  removerArquivo(id, idArquivo) {
    return axios.delete(`${this.baseUrl}/${this.endpoint}/${id}/arquivos/${idArquivo}`);
  }

  atualizarArquivo(id, idArquivo, arquivoDTO) {
    return axios.put(`${this.baseUrl}/${this.endpoint}/${id}/arquivos/${idArquivo}`, arquivoDTO);
  }
}

const instance = new AnulacaoRevogacaoService();

export default instance;
