import { ServiceBase } from 'fc/services/ServiceBase';
import ApiEndpoints from '~/constants/ApiEndpoints';
import axios from 'axios';

class TdaDispensaService extends ServiceBase {
  constructor() {
    super(ApiEndpoints.tdaDispensa);
  }

  upload(file) {
    const formData = new FormData();
    formData.append('file', file);
    return axios.post(`${this.baseUrl}/${this.endpoint}/upload`, formData);
  }

  download(fileDTO) {
    let params = `lookupId=${fileDTO.lookupId}&nomeOriginal=${fileDTO.nomeOriginal}&tipoArquivo=${fileDTO.tipoArquivo}`;
    return axios.get(`${this.baseUrl}/${this.endpoint}/download?${params}`, { responseType: 'blob' });
  }

  removerArquivo(idTdaDispensa, idArquivo) {
    return axios.delete(`${this.baseUrl}/${this.endpoint}/${idTdaDispensa}/arquivos/${idArquivo}`);
  }

  recuperarArquivos(idTdaDispensa) {
    return axios.get(`${this.baseUrl}/${this.endpoint}/${idTdaDispensa}/arquivos`);
  }

  atualizarArquivo(idTdaDispensa, idArquivo, arquivoTdaDispensaDTO) {
    return axios.put(`${this.baseUrl}/${this.endpoint}/${idTdaDispensa}/arquivos/${idArquivo}`, arquivoTdaDispensaDTO);
  }

  saveArquivos(idTdaDispensa, arquivosTdaDispensaDTO) {
    return axios.post(`${this.baseUrl}/${this.endpoint}/${idTdaDispensa}/salvar-arquivos`, arquivosTdaDispensaDTO);
  }

  tdaDispensaByIdDispensa(idDispensa) {
    return axios.get(`${this.baseUrl}/${this.endpoint}/${idDispensa}/tda-dispensa`);
  }

  arquivarProcesso(idDispensa, statusArquivamento) {
    return axios.post(`${this.baseUrl}/${this.endpoint}/${idDispensa}/${statusArquivamento}/arquivar-processo`);
  }
}

const instance = new TdaDispensaService();

export default instance;
