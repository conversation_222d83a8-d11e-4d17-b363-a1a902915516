import { ServiceBase } from 'fc/services/ServiceBase';
import ApiEndpoints from '~/constants/ApiEndpoints';
import axios from 'axios';

class AlertaAnaliseService extends ServiceBase {
  constructor() {
    super(ApiEndpoints.alertaAnalise);
  }

  upload(file) {
    const formData = new FormData();
    formData.append('file', file);
    return axios.post(`${this.baseUrl}/${this.endpoint}/upload`, formData);
  }

  download(fileDTO) {
    let params = `lookupId=${fileDTO.lookupId}&nomeOriginal=${fileDTO.nomeOriginal}&tipoArquivo=${fileDTO.tipoArquivo}`;
    return axios.get(`${this.baseUrl}/${this.endpoint}/download?${params}`, { responseType: 'blob' });
  }

  removerArquivo(idAlertaAnalise, idArquivo) {
    return axios.delete(`${this.baseUrl}/${this.endpoint}/${idAlertaAnalise}/arquivos/${idArquivo}`);
  }

  recuperarArquivos(idAlertaAnalise) {
    return axios.get(`${this.baseUrl}/${this.endpoint}/${idAlertaAnalise}/arquivos`);
  }

  atualizarArquivo(idAlertaAnalise, idArquivo, arquivoAlertaDTO) {
    return axios.put(`${this.baseUrl}/${this.endpoint}/${idAlertaAnalise}/arquivos/${idArquivo}`, arquivoAlertaDTO);
  }

  saveArquivos(idAlertaAnalise, arquivosAlertaDTO) {
    return axios.post(`${this.baseUrl}/${this.endpoint}/${idAlertaAnalise}/salvar-arquivos`, arquivosAlertaDTO);
  }

  arquivarAlerta(idAlertaAnalise, statusArquivamento) {
    return axios.post(`${this.baseUrl}/${this.endpoint}/${idAlertaAnalise}/${statusArquivamento}/arquivar-alerta`);
  }
}

const instance = new AlertaAnaliseService();

export default instance;
