import { ServiceBase } from 'fc/services/ServiceBase';
import ApiEndpoints from '~/constants/ApiEndpoints';
import axios from 'axios';

class AlertaMensagemService extends ServiceBase {
  constructor() {
    super(ApiEndpoints.alertaMensagem);
  }

  upload(file) {
    const formData = new FormData();
    formData.append('file', file);
    return axios.post(`${this.baseUrl}/${this.endpoint}/upload`, formData);
  }

  download(fileDTO) {
    let params = `lookupId=${fileDTO.lookupId}&nomeOriginal=${fileDTO.nomeOriginal}&tipoArquivo=${fileDTO.tipoArquivo}`;
    return axios.get(`${this.baseUrl}/${this.endpoint}/download?${params}`, { responseType: 'blob' });
  }

  removerArquivo(idAlertaMensagem, idArquivo) {
    return axios.delete(`${this.baseUrl}/${this.endpoint}/${idAlertaMensagem}/arquivos/${idArquivo}`);
  }

  recuperarArquivos(idAlertaMensagem) {
    return axios.get(`${this.baseUrl}/${this.endpoint}/${idAlertaMensagem}/arquivos`);
  }

  atualizarArquivo(idAlertaMensagem, idArquivo, arquivoAlertaMensagemDTO) {
    return axios.put(
      `${this.baseUrl}/${this.endpoint}/${idAlertaMensagem}/arquivos/${idArquivo}`,
      arquivoAlertaMensagemDTO
    );
  }

  saveArquivos(idAlertaMensagem, arquivosAlertaMensagemDTO) {
    return axios.post(
      `${this.baseUrl}/${this.endpoint}/${idAlertaMensagem}/salvar-arquivos`,
      arquivosAlertaMensagemDTO
    );
  }

  cadastrarMensagem(mensagemDTO) {
    return axios.post(`${this.baseUrl}/${this.endpoint}/mensagem`, mensagemDTO);
  }

  listarMensagens(idAlertaAnalise) {
    return axios.get(`${this.baseUrl}/${this.endpoint}/${idAlertaAnalise}/mensagens`);
  }

  rejeitarMensagem(idAlertaAnalise, mensagemRejeicao) {
    return axios.post(`${this.baseUrl}/${this.endpoint}/${idAlertaAnalise}/rejeitar-mensagem`, { mensagemRejeicao });
  }

  aprovarMensagem(idMensagem, mensagem, prazoResposta) {
    return axios.post(`${this.baseUrl}/${this.endpoint}/${idMensagem}/aprovar-mensagem`, {
      mensagem,
      prazoResposta,
    });
  }
}

const instance = new AlertaMensagemService();

export default instance;
