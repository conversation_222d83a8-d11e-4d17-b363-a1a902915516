import axios from 'axios';
import { ServiceBase } from 'fc/services/ServiceBase';
import ApiEndpoints from '~/constants/ApiEndpoints';

class AditivoContratoService extends ServiceBase {
  constructor() {
    super(ApiEndpoints.aditivoContrato);
  }

  getProximoNumero(idAditivoContrato) {
    return axios.get(`${this.baseUrl}/${this.endpoint}/${idAditivoContrato}/proximo-numero/`);
  }

  createAditivoContrato(object) {
    return axios.post(`${this.baseUrl}/${this.endpoint}/novo`, object);
  }

  upload(file) {
    const formData = new FormData();
    formData.append('file', file);
    return axios.post(`${this.baseUrl}/${this.endpoint}/upload`, formData);
  }

  download(fileDTO) {
    let params = `lookupId=${fileDTO.lookupId}&nomeOriginal=${fileDTO.nomeOriginal}&tipoArquivo=${fileDTO.tipoArquivo}`;
    return axios.get(`${this.baseUrl}/${this.endpoint}/download?${params}`, { responseType: 'blob' });
  }

  removerArquivo(idAditivoContrato, idArquivo) {
    return axios.delete(`${this.baseUrl}/${this.endpoint}/${idAditivoContrato}/arquivos/${idArquivo}`);
  }

  atualizarArquivo(idAditivoContrato, idArquivo, arquivoAditivoContratoDTO) {
    return axios.put(
      `${this.baseUrl}/${this.endpoint}/${idAditivoContrato}/arquivos/${idArquivo}`,
      arquivoAditivoContratoDTO
    );
  }

  recuperarArquivos(idAditivoContrato) {
    return axios.get(`${this.baseUrl}/${this.endpoint}/${idAditivoContrato}/arquivos`);
  }
}

const instance = new AditivoContratoService();

export default instance;
