import { ServiceBase } from 'fc/services/ServiceBase';
import ApiEndpoints from '~/constants/ApiEndpoints';
import axios from 'axios';

class TdaLicitacaoService extends ServiceBase {
  constructor() {
    super(ApiEndpoints.tdaLicitacao);
  }

  upload(file) {
    const formData = new FormData();
    formData.append('file', file);
    return axios.post(`${this.baseUrl}/${this.endpoint}/upload`, formData);
  }

  download(fileDTO) {
    let params = `lookupId=${fileDTO.lookupId}&nomeOriginal=${fileDTO.nomeOriginal}&tipoArquivo=${fileDTO.tipoArquivo}`;
    return axios.get(`${this.baseUrl}/${this.endpoint}/download?${params}`, { responseType: 'blob' });
  }

  removerArquivo(idTdaLicitacao, idArquivo) {
    return axios.delete(`${this.baseUrl}/${this.endpoint}/${idTdaLicitacao}/arquivos/${idArquivo}`);
  }

  recuperarArquivos(idTdaLicitacao) {
    return axios.get(`${this.baseUrl}/${this.endpoint}/${idTdaLicitacao}/arquivos`);
  }

  atualizarArquivo(idTdaLicitacao, idArquivo, arquivoTdaLicitacaoDTO) {
    return axios.put(
      `${this.baseUrl}/${this.endpoint}/${idTdaLicitacao}/arquivos/${idArquivo}`,
      arquivoTdaLicitacaoDTO
    );
  }

  saveArquivos(idTdaLicitacao, arquivosTdaLicitacaoDTO) {
    return axios.post(`${this.baseUrl}/${this.endpoint}/${idTdaLicitacao}/salvar-arquivos`, arquivosTdaLicitacaoDTO);
  }

  tdaLicitacaoByIdLicitacao(idLicitacao) {
    return axios.get(`${this.baseUrl}/${this.endpoint}/${idLicitacao}/tda-Licitacao`);
  }

  arquivarProcesso(idLicitacao, statusArquivamento) {
    return axios.post(`${this.baseUrl}/${this.endpoint}/${idLicitacao}/${statusArquivamento}/arquivar-processo`);
  }
}

const instance = new TdaLicitacaoService();

export default instance;
