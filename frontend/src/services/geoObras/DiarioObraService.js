import axios from 'axios';
import { ServiceBase } from 'fc/services/ServiceBase';
import ApiEndpoints from '~/constants/ApiEndpoints';

class DiarioObraService extends ServiceBase {
  constructor() {
    super(ApiEndpoints.geoObras.diarios);
  }

  upload(file) {
    const formData = new FormData();
    formData.append('file', file);
    return axios.post(`${this.baseUrl}/${this.endpoint}/upload`, formData);
  }

  download(fileDTO, countDownloads) {
    const params = `lookupId=${fileDTO.lookupId}&nomeOriginal=${fileDTO.nomeOriginal}&tipoArquivo=${
      fileDTO.tipoArquivo
    }&countDownloads=${countDownloads ?? false}`;
    return axios.get(`${this.baseUrl}/${this.endpoint}/download?${params}`, { responseType: 'blob' });
  }

  recuperarArquivos(idDiarioObra) {
    return axios.get(`${this.baseUrl}/${this.endpoint}/${idDiarioObra}/arquivos`);
  }

  validaArquivos(diarioObra) {
    return axios.post(`${this.baseUrl}/${this.endpoint}/arquivos/validacao`, diarioObra);
  }

  removerArquivo(idDiarioObra, idArquivo) {
    return axios.delete(`${this.baseUrl}/${this.endpoint}/${idDiarioObra}/arquivos/${idArquivo}`);
  }

  atualizarArquivo(idDiarioObra, idArquivo, arquivoCaronaDTO) {
    return axios.put(`${this.baseUrl}/${this.endpoint}/${idDiarioObra}/arquivos/${idArquivo}`, arquivoCaronaDTO);
  }

  criarDiarioObra(diarioObra) {
    return axios.post(`${this.baseUrl}/${this.endpoint}/novo`, diarioObra);
  }
}

const instance = new DiarioObraService();

export default instance;
