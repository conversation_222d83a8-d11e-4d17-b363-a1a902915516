import axios from 'axios';
import { ServiceBase } from 'fc/services/ServiceBase';
import ApiEndpoints from '~/constants/ApiEndpoints';

class ObraService extends ServiceBase {
  constructor() {
    super(ApiEndpoints.geoObras.obra);
  }

  upload(file) {
    const formData = new FormData();
    formData.append('file', file);
    return axios.post(`${this.baseUrl}/${this.endpoint}/upload`, formData);
  }

  download(fileDTO, countDownloads) {
    const params = `lookupId=${fileDTO.lookupId}&nomeOriginal=${fileDTO.nomeOriginal}&tipoArquivo=${
      fileDTO.tipoArquivo
    }&countDownloads=${countDownloads ?? false}`;
    return axios.get(`${this.baseUrl}/${this.endpoint}/download?${params}`, { responseType: 'blob' });
  }

  recuperarArquivos(idObra) {
    return axios.get(`${this.baseUrl}/${this.endpoint}/${idObra}/arquivos`);
  }

  validaArquivos(obra) {
    return axios.post(`${this.baseUrl}/${this.endpoint}/arquivos/validacao`, obra);
  }

  removerArquivo(idObra, idArquivo) {
    return axios.delete(`${this.baseUrl}/${this.endpoint}/${idObra}/arquivos/${idArquivo}`);
  }

  atualizarArquivo(idObra, idArquivo, arquivoObraDTO) {
    return axios.put(`${this.baseUrl}/${this.endpoint}/${idObra}/arquivos/${idArquivo}`, arquivoObraDTO);
  }

  salvarObra(obraDTO) {
    return axios.post(`${this.baseUrl}/${this.endpoint}/salvar`, obraDTO);
  }

  iniciarObra(obraDTO) {
    return axios.post(`${this.baseUrl}/${this.endpoint}/iniciar`, obraDTO);
  }

  finalizarObra(obraDTO) {
    return axios.post(`${this.baseUrl}/${this.endpoint}/finalizar`, obraDTO);
  }

  entregarObra(obraDTO) {
    return axios.post(`${this.baseUrl}/${this.endpoint}/entregar`, obraDTO);
  }

  interromperObra(obraDTO) {
    return axios.post(`${this.baseUrl}/${this.endpoint}/interromper`, obraDTO);
  }

  paralisarObra(obraDTO) {
    return axios.post(`${this.baseUrl}/${this.endpoint}/paralisar`, obraDTO);
  }

  continuarObra(obraDTO) {
    return axios.post(`${this.baseUrl}/${this.endpoint}/continuar`, obraDTO);
  }
}

const instance = new ObraService();

export default instance;
