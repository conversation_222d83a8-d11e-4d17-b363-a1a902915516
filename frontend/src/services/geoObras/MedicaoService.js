import { ServiceBase } from 'fc/services/ServiceBase';
import ApiEndpoints from '~/constants/ApiEndpoints';
import axios from 'axios';

class MedicaoService extends ServiceBase {
  constructor() {
    super(ApiEndpoints.geoObras.medicao);
  }

  buildRequest(idObra) {
    return `${this.baseUrl}/${this.endpoint}`.replace('#idObra', idObra);
  }

  upload(idObra, file) {
    const formData = new FormData();
    formData.append('file', file);
    return axios.post(`${this.buildRequest(idObra)}/upload`, formData);
  }

  recuperarArquivos(idObra, idMedicao) {
    return axios.get(`${this.buildRequest(idObra)}/${idMedicao}/arquivos`);
  }

  download(idObra, fileDTO, countDownloads) {
    let params = `lookupId=${fileDTO.lookupId}&nomeOriginal=${fileDTO.nomeOriginal}&tipoArquivo=${
      fileDTO.tipoArquivo
    }&countDownloads=${countDownloads ?? false}`;
    return axios.get(`${this.buildRequest(idObra)}/download?${params}`, { responseType: 'blob' });
  }

  advancedSearch(options = {}) {
    const defaultValues = {
      page: { index: 1, size: 10 },
      andParameters: [],
      orParameters: [],
    };
    Object.assign(defaultValues, options);
    return axios.post(`${this.baseUrl}/${this.endpoint}/advanced-search`, defaultValues);
  }

  removerArquivo(idObra, idMedicao, idArquivo) {
    return axios.delete(`${this.buildRequest(idObra)}/${idMedicao}/arquivos/${idArquivo}`);
  }

  atualizarArquivo(idObra, idMedicao, idArquivo, arquivoMedicaoDTO) {
    return axios.put(`${this.buildRequest(idObra)}/${idMedicao}/arquivos/${idArquivo}`, arquivoMedicaoDTO);
  }

  criarMedicao(idObra, medicaoDTO) {
    return axios.post(`${this.buildRequest(idObra)}/novo`, medicaoDTO);
  }

  validaArquivos(idObra, medicaoDTO) {
    return axios.post(`${this.buildRequest(idObra)}/arquivos/validacao`, medicaoDTO);
  }

  carregarPorObra(idObra) {
    return axios.get(`${this.buildRequest(idObra)}/all-by-obra`);
  }

  advancedSearch(options = {}) {
    const defaultValues = {
      page: { index: 1, size: 10 },
      andParameters: [],
      orParameters: [],
    };
    Object.assign(defaultValues, options);
    const idObra = options.andParameters?.find((filtro) => filtro.field === 'obra')?.value;
    return axios.post(`${this.buildRequest(idObra)}/advanced-search`, defaultValues);
  }
}

const instance = new MedicaoService();

export default instance;
