import axios from 'axios';
import { ServiceBase } from 'fc/services/ServiceBase';
import ApiEndpoints from '~/constants/ApiEndpoints';

class BoletimObraService extends ServiceBase {
  constructor() {
    super(ApiEndpoints.geoObras.boletins);
  }

  upload(file) {
    const formData = new FormData();
    formData.append('file', file);
    return axios.post(`${this.baseUrl}/${this.endpoint}/upload`, formData);
  }

  download(fileDTO, countDownloads) {
    const params = `lookupId=${fileDTO.lookupId}&nomeOriginal=${fileDTO.nomeOriginal}&tipoArquivo=${
      fileDTO.tipoArquivo
    }&countDownloads=${countDownloads ?? false}`;
    return axios.get(`${this.baseUrl}/${this.endpoint}/download?${params}`, { responseType: 'blob' });
  }

  recuperarArquivos(idBoletimObra) {
    return axios.get(`${this.baseUrl}/${this.endpoint}/${idBoletimObra}/arquivos`);
  }

  validaArquivos(boletimObra) {
    return axios.post(`${this.baseUrl}/${this.endpoint}/arquivos/validacao`, boletimObra);
  }

  removerArquivo(idBoletimObra, idArquivo) {
    return axios.delete(`${this.baseUrl}/${this.endpoint}/${idBoletimObra}/arquivos/${idArquivo}`);
  }

  atualizarArquivo(idBoletimObra, idArquivo, arquivoCaronaDTO) {
    return axios.put(`${this.baseUrl}/${this.endpoint}/${idBoletimObra}/arquivos/${idArquivo}`, arquivoCaronaDTO);
  }

  criarBoletimObra(boletimObra) {
    return axios.post(`${this.baseUrl}/${this.endpoint}/novo`, boletimObra);
  }
}

const instance = new BoletimObraService();

export default instance;
