import axios from 'axios';
import { ServiceBase } from 'fc/services/ServiceBase';
import ApiEndpoints from '~/constants/ApiEndpoints';

class CredenciadoService extends ServiceBase {
  constructor() {
    super(ApiEndpoints.credenciado);
  }

  salvar(credenciadoDTO) {
    return axios.post(`${this.baseUrl}/${this.endpoint}/salvar`, credenciadoDTO);
  }

  upload(file) {
    const formData = new FormData();
    formData.append('file', file);
    return axios.post(`${this.baseUrl}/${this.endpoint}/upload`, formData);
  }

  download(fileDTO) {
    let params = `lookupId=${fileDTO.lookupId}&nomeOriginal=${fileDTO.nomeOriginal}&tipoArquivo=${fileDTO.tipoArquivo}`;
    return axios.get(`${this.baseUrl}/${this.endpoint}/download?${params}`, { responseType: 'blob' });
  }

  removerArquivo(idCredenciado, idArquivo) {
    return axios.delete(`${this.baseUrl}/${this.endpoint}/${idCredenciado}/arquivos/${idArquivo}`);
  }

  validaArquivos(credenciadoDTO) {
    return axios.post(`${this.baseUrl}/${this.endpoint}/arquivos/validacao`, credenciadoDTO);
  }

  atualizarArquivo(idCredenciado, idArquivo, arquivoCredenciadoDTO) {
    return axios.put(`${this.baseUrl}/${this.endpoint}/${idCredenciado}/arquivos/${idArquivo}`, arquivoCredenciadoDTO);
  }

  recuperarArquivos(idCredenciado) {
    return axios.get(`${this.baseUrl}/${this.endpoint}/${idCredenciado}/arquivos`);
  }

  getAllByCredenciamento(idCredenciamento) {
    return axios.get(`${this.baseUrl}/${this.endpoint}/credenciamento/${idCredenciamento}`);
  }

  getIdsCredenciados(idCredenciamento) {
    return axios.get(`${this.baseUrl}/${this.endpoint}/${idCredenciamento}/ids-credenciados`);
  }

  getVigenciasCredenciados(idCredenciamento) {
    return axios.get(`${this.baseUrl}/${this.endpoint}/${idCredenciamento}/vigencias`);
  }

  findAllByCredenciamentoAndLicitante(idCredenciamento, idLicitante) {
    return axios.get(`${this.baseUrl}/${this.endpoint}/${idCredenciamento}/${idLicitante}/all`);
  }

  suspender(idCredenciado) {
    return axios.patch(`${this.baseUrl}/${this.endpoint}/${idCredenciado}/suspender`);
  }
}

const instance = new CredenciadoService();

export default instance;
