import React from 'react';
import PropTypes from 'prop-types';
import { Divider } from 'primereact/divider';
import { getValue<PERSON><PERSON>, getValue<PERSON><PERSON><PERSON><PERSON>, getValueDate, getValue } from 'fc/utils/utils';
import GenericIndexPage from 'fc/pages/GenericIndexPage';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import { DataTable } from 'primereact/datatable';
import ContratoFormStore from '~/stores/contrato/formStore';
import { DATE_FORMAT, DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT, DATE_PARSE_FORMAT_WITH_HOURS } from 'fc/utils/date';
import { observer } from 'mobx-react';
import UrlRouter from '~/constants/UrlRouter';
import FcButton from 'fc/components/FcButton';
import AdvancedSearch from 'fc/components/AdvancedSearch';
import AditivoContratoIndexStore from '~/stores/contrato/aditivo/indexStore';
import SearchTypes from 'fc/components/AdvancedSearch/SearchTypes';
import { ProgressSpinner } from 'primereact/progressspinner';
import FcCloseableTabView from 'fc/components/FcCloseableTabView';
import EmpenhoContratoIndexStore from '~/stores/contrato/empenhos/indexStore';
import EmpenhoDetailDialog from 'pages/contrato/empenhos/EmpenhoDetailDialog';
import MultipleFileUploader from 'fc/components/MultipleFileUploader';

@observer
class ContratoDetailPage extends GenericIndexPage {
  constructor(props) {
    super(props);
    this.contratoStore = new ContratoFormStore();
    this.aditivoStore = new AditivoContratoIndexStore();
    this.empenhoStore = new EmpenhoContratoIndexStore();
    this.arquivoStore = this.contratoStore.fileStore;
    this.state = {
      decimalPlaces: 2,
      activeTabIndex: 0,
      selectedRow: null,
      detalhesVisibility: false,
    };
  }
  componentDidMount() {
    const id = this.props.idContrato;
    this.contratoStore.initializeWithAssociatedProcess(id, {}, () => {
      this.contratoStore.initializeFiles(id);
      this.contratoStore.carregaUltimaAlteracao(id);
      this.contratoStore.carregaDecimalPlaces((decimalPlaces) => this.setState({ decimalPlaces: decimalPlaces }));
    });
  }

  _renderValue(label, value, col = 12) {
    return (
      <div className={`p-mt-3 p-col-12 p-md-${col} p-sm-${col >= 6 ? col : 6}`}>
        <div className="p-col-12 drawer-content-label">{label}</div>
        <div className={`p-col-12 p-text-justify`}>{value ?? '-'}</div>
      </div>
    );
  }

  _getHomeUrl() {
    const splited = document.location.href.split('#');
    return splited.length >= 1 ? splited[0] : '/';
  }

  _renderOrigemContrato() {
    const { processo } = this.contratoStore.object;
    if (processo) {
      this.contratoStore.checkDataCadastro(processo.dataCadastro);
      const isReqMod = this.contratoStore.enableReqMod;
      const links = {
        L: {
          link: UrlRouter.cadastrosConsulta.licitacao.editar,
          text: 'Licitação ',
          numero: processo.numeroLicitacao ?? '',
        },
        D: {
          link: isReqMod
            ? UrlRouter.administracao.requisicaoModificacao.dispensa.requisitar
            : UrlRouter.cadastrosConsulta.dispensa.editar,
          text: 'Dispensa ',
          numero:
            processo.numeroProcesso && processo.numeroProcesso.includes('/')
              ? processo.numeroProcesso
              : `${processo.numeroProcesso}/${processo.anoDispensa}`,
        },
        I: {
          link: isReqMod
            ? UrlRouter.administracao.requisicaoModificacao.inexigibilidade.requisitar
            : UrlRouter.cadastrosConsulta.inexigibilidade.editar,
          text: 'Inexigibilidade ',
          numero:
            processo.numeroProcesso && processo.numeroProcesso.includes('/')
              ? processo.numeroProcesso
              : `${processo.numeroProcesso}/${processo.anoInexigibilidade}`,
        },
        C: {
          link: isReqMod
            ? UrlRouter.administracao.requisicaoModificacao.carona.requisitar
            : UrlRouter.cadastrosConsulta.carona.editar,
          text: 'Carona ',
          numero:
            processo.numeroProcessoGerenciadorAta && processo.numeroProcessoGerenciadorAta.includes('/')
              ? processo.numeroProcessoGerenciadorAta
              : `${processo.numeroProcessoGerenciadorAta}/${processo.anoCarona}`,
        },
        CR: {
          link: isReqMod
            ? UrlRouter.administracao.requisicaoModificacao.credenciamento.requisitar
            : UrlRouter.cadastrosConsulta.credenciamento.editar,
          text: 'Credenciamento ',
          numero:
            processo.numero && processo.numero.includes('/') ? processo.numero : `${processo.numero}/${processo?.ano}`,
        },
      };
      const typeProcess = this.contratoStore.object.tipo;
      const linkLicitacao = `${this._getHomeUrl()}#${links[typeProcess].link
        .replace(':fase', 'preparatoria')
        .replace(':id', processo.id)}`;
      const linkProcesso = (
        <a
          href={
            typeProcess === 'L'
              ? linkLicitacao
              : `${this._getHomeUrl()}#${links[typeProcess].link.replace(':id', processo.id)}`
          }
          style={{ color: '#3F51B5' }}
        >
          <u>{links[typeProcess].text + links[typeProcess].numero}</u>
        </a>
      );
      return this._renderValue('Origem', linkProcesso, 3);
    }
  }

  _renderTabs(contrato) {
    const tabs = [];
    tabs.push({ id: 0, header: 'Elementos de Despesas', content: this._renderTabElementoDeDespesa(contrato) });
    tabs.push({ id: 1, header: 'Fontes de Recurso', content: this._renderTabFontesDeRecurso(contrato) });
    tabs.push({ id: 2, header: 'Empenhos', content: this._renderTabEmpenho() });
    tabs.push({
      id: 3,
      header: 'Arquivos',
      content: (
        <MultipleFileUploader
          fileTypes={DadosEstaticosService.getTipoArquivoContrato()}
          downloadOnly
          store={this.contratoStore.fileStore}
        />
      ),
    });
    tabs.push({ id: 4, header: 'Aditivos', content: this._renderTabAditivo(contrato) });

    return (
      <FcCloseableTabView
        tabs={tabs}
        activeTabIndex={this.state.activeTabIndex}
        onChangeTab={(tab) => this.setState({ activeTabIndex: tab.id })}
      />
    );
  }

  _renderTabElementoDeDespesa(contrato) {
    const columns = [
      {
        field: 'numero',
        header: 'Número',
      },
      {
        field: 'descricao',
        header: 'Descrição',
      },
    ];

    return (
      <DataTable
        rowHover
        emptyMessage="Nenhum elemento de despesa encontrado"
        value={contrato.elementosDeDespesa}
        className="p-datatable-sm"
        paginator
        rows={5}
      >
        {this.renderColumns(columns)}
      </DataTable>
    );
  }

  _renderTabFontesDeRecurso(contrato) {
    const columns = [
      {
        field: 'nome',
        header: 'Nome',
      },
      {
        field: 'descricao',
        header: 'Descrição',
      },
    ];

    return (
      <DataTable
        rowHover
        emptyMessage={'Nenhuma fonte de recurso encontrada'}
        value={contrato.fontesDeRecurso}
        className="p-datatable-sm"
        paginator
        rows={5}
      >
        {this.renderColumns(columns)}
      </DataTable>
    );
  }

  _renderValue(label, value, col = 12) {
    return (
      <div className={`p-mt-3 p-col-12 p-md-${col} p-sm-${col >= 6 ? col : 6}`}>
        <div className="p-col-12 drawer-content-label font-bold">{label}</div>
        <div className={`p-col-12`}>{value ? value : '-'}</div>
      </div>
    );
  }

  _renderTabEmpenho() {
    const columns = [
      {
        field: 'numeroEmpenho',
        header: 'Número',
      },
      {
        field: 'valorEmpenho',
        header: 'Valor',
        body: ({ valorEmpenho }) => getValueMoney(valorEmpenho),
      },
      {
        field: 'dataEmpenho',
        header: 'Data de Empenho',
        body: ({ dataEmpenho }) => getValueDate(dataEmpenho, DATE_FORMAT, DATE_PARSE_FORMAT),
      },
      {
        field: 'tipoEmpenho',
        header: 'Tipo',
        body: ({ tipoEmpenho }) => getValueByKey(tipoEmpenho, DadosEstaticosService.getTiposEmpenho()),
      },
      {
        field: 'dataCadastro',
        header: 'Data de Cadastro',
        body: ({ dataCadastro }) => getValueDate(dataCadastro, DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS),
      },
      {
        style: { width: '40px' },
        body: (rowData) => {
          return (
            <div className="actions p-d-flex p-jc-end">
              <FcButton
                icon="pi pi-eye"
                className="p-button-sm p-mr-2"
                tooltip="Detalhes"
                onClick={() => {
                  this.empenhoStore.initializeArquivos(rowData.id);
                  this.setState({ detalhesVisibility: true, selectedRow: rowData });
                }}
              />
            </div>
          );
        },
      },
    ];
    return (
      <DataTable
        rowHover
        emptyMessage={'Nenhum Empenho Encontrado'}
        value={this.contratoStore.getEmpenhos()}
        className="p-datatable-sm"
        paginator
        rows={5}
      >
        {this.renderColumns(columns)}
      </DataTable>
    );
  }

  getVencedorField(tipo) {
    if (tipo == 'L') return 'vencedorLicitacao';
    if (tipo == 'C') return 'vencedorCarona';
    if (tipo == 'D') return 'vencedorDispensa';
    if (tipo == 'I') return 'vencedorInexigibilidade';
    if (tipo == 'CR') return 'credenciadoItem';
  }

  renderItens(contrato) {
    const { contratoLicitante, valorGlobal, tipo } = contrato;
    const vencedorField = this.getVencedorField(tipo);
    const columnsAdicionados = [
      {
        header: 'Descrição',
        style: { width: '30%' },
        body: (item) => {
          if (!item.itemGerado) {
            return `${
              item[vencedorField].itemLote?.numero ? item[vencedorField].itemLote?.numero + ' - ' : ''
            } ${getValue(item[vencedorField].itemLote?.materialDetalhamento?.pdm?.nome)}`;
          } else {
            return item.materialDetalhamento.pdm.nome;
          }
        },
      },
      {
        field: 'marcaModelo',
        header: 'Marca/Modelo',
        className: 'text-justify',
        body: (item) => (item.itemGerado ? '-' : <span>{getValue(item[vencedorField].marcaModelo)}</span>),
      },
      {
        field: 'quantidade',
        header: 'Quantidade',
        body: (item) => <span>{getValue(item.quantidade)}</span>,
      },
      {
        field: 'valorUnitario',
        header: 'Valor Unitário',
        body: (item) => <span>{getValueMoney(item.valor, this.state.decimalPlaces)}</span>,
      },
      {
        field: 'valor',
        header: 'Valor',
        body: (item) => <span>{getValueMoney(item.valor * item.quantidade, this.state.decimalPlaces)}</span>,
      },
    ];
    return (
      <DataTable
        rowHover
        value={contratoLicitante?.itens}
        responsiveLayout="scroll"
        emptyMessage="Nenhum item adicionado ao contrato."
        footer={`Valor Total: ${getValueMoney(valorGlobal, this.state.decimalPlaces)}`}
      >
        {this.renderColumns(columnsAdicionados)}
      </DataTable>
    );
  }

  _renderTabAditivo(item) {
    if (item) {
      const columns = [
        {
          field: 'numero',
          header: 'Número',
          sortable: true,
        },
        {
          field: 'dataPublicacao',
          header: 'Data da Publicação',
          body: ({ dataPublicacao }) => getValueDate(dataPublicacao, DATE_FORMAT, DATE_PARSE_FORMAT),
          sortable: true,
        },
        {
          field: 'tipoAlteracao',
          header: 'Tipo de Alteração Contratual',
          body: ({ tipoAlteracao }) => getValueByKey(tipoAlteracao, DadosEstaticosService.getTipoAlteracaoContratual()),
          sortable: true,
        },
        {
          style: { width: '40px' },
          body: (rowData) => {
            return (
              <div className="actions p-d-flex p-jc-end">
                <FcButton
                  icon="pi pi-eye"
                  className="p-button-sm p-mr-2"
                  tooltip="Detalhes"
                  onClick={() => this.props.onDetailAditivo(rowData)}
                />
              </div>
            );
          },
        },
      ];

      const { listKey, loading } = this.aditivoStore;

      const filterSuggest = [
        {
          id: '',
          field: 'contrato',
          operator: 'EQUAL_TO',
          value: item?.id,
          formatted: '',
          fixed: true,
          completeParam: {
            field: 'contrato',
            label: 'Id Contrato',
            type: SearchTypes.NUMBER,
          },
        },
      ];

      return (
        <>
          <AdvancedSearch
            searchParams={this.contratoStore.getAdvancedSearchParamsAditivo()}
            store={this.aditivoStore}
            searchFields={['numero']}
            filterSuggest={filterSuggest}
          />
          <DataTable
            rowHover
            value={listKey}
            className="p-datatable-sm"
            paginator
            emptyMessage="Nenhum Aditivo"
            rows={10}
            loading={loading}
          >
            {this.renderColumns(columns)}
          </DataTable>
        </>
      );
    }
  }

  render() {
    if (this.contratoStore.loading) {
      return (
        <div className="p-d-inline p-d-flex align-items-center justify-content-center">
          <ProgressSpinner />
        </div>
      );
    } else if (this.contratoStore.object) {
      const contrato = this.contratoStore.object;
      return (
        <div>
          <Divider />
          <div className="p-formgrid p-grid">
            {contrato.processoExterno ? this._renderValue('Origem', contrato.origem, 3) : this._renderOrigemContrato()}
            {this._renderValue(
              'Forma de Contrato',
              getValueByKey(contrato.formaContrato, DadosEstaticosService.getFormaContrato()),
              3
            )}
            {this._renderValue('Número do Contrato', contrato.numeroContrato, 3)}
            {contrato.formaContrato === 'CONTRATO' && this._renderValue('Ano do Contrato', contrato.anoContrato, 3)}
            {this._renderValue(
              'Permite Aditivo',
              getValueByKey(contrato.permiteAditivo, DadosEstaticosService.getSimNao()),
              3
            )}
            {this._renderValue('Data da Publicação', getValueDate(contrato.dataPublicacao), 3)}
            {this._renderValue('Início da Vigência', getValueDate(contrato.dataVigenciaInicial), 3)}
            {this._renderValue('Fim da Vigência', getValueDate(contrato.dataVigenciaFinal), 3)}
            {this._renderValue('Valor do Contrato', getValueMoney(contrato.valorGlobal), 3)}
            {this._renderValue('Identificador Único do Processo', contrato.id, 3)}
            {this._renderValue('Gestor', contrato.gestor?.nome, 3)}
            {this._renderValue('Fiscal', contrato.fiscal?.nome, 2)}
            {this._renderValue('Objeto', contrato.objeto, 12)}
            <Divider />
            {this._renderValue(
              getValueByKey(contrato.tipo, DadosEstaticosService.getTipoProcesso(), 'value', 'licitante'),
              contrato.contratoLicitante?.licitante?.nome,
              12
            )}
            {this._renderValue('Itens', this.renderItens(contrato))}
            {contrato.id && (
              <div className="p-fluid p-formgrid p-grid p-col-12">
                {this._renderValue(
                  'Cadastrado por',
                  contrato.usuario
                    ? `${contrato.usuario.nome} em ${getValueDate(
                        contrato.dataCadastro,
                        DATE_FORMAT_WITH_HOURS,
                        DATE_PARSE_FORMAT_WITH_HOURS
                      )}`
                    : '-',
                  6
                )}
                {this._renderValue(
                  'Alterado por',
                  this.contratoStore.ultimaAlteracao
                    ? `${this.contratoStore.ultimaAlteracao?.nome} em ${getValueDate(
                        this.contratoStore.ultimaAlteracao?.data,
                        DATE_FORMAT_WITH_HOURS,
                        DATE_PARSE_FORMAT_WITH_HOURS
                      )}`
                    : '-',
                  6
                )}
              </div>
            )}
          </div>
          <Divider />
          {this._renderTabs(contrato)}
          <EmpenhoDetailDialog
            fileStore={this.empenhoStore.fileStore}
            empenho={this.state.selectedRow}
            detalhesVisibility={this.state.detalhesVisibility}
            onClose={() => this.setState({ detalhesVisibility: false })}
          />
        </div>
      );
    } else {
      return <div>Erro ao exibir detalhes do Contrato.</div>;
    }
  }
}

ContratoDetailPage.propTypes = {
  idContrato: PropTypes.number,
  onDetailAditivo: PropTypes.func,
};

export default ContratoDetailPage;
