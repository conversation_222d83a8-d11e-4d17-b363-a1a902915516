import React from 'react';
import { observer } from 'mobx-react';
import PropTypes from 'prop-types';
import GenericFormPage from 'fc/pages/GenericFormPage';
import { Divider } from 'primereact/divider';
import FcButton from 'fc/components/FcButton';
import ResponsavelContratoFormStore from '~/stores/responsavelContrato/formStore';
import ResponsavelContratoForm from '~/pages/responsavelContrato/formResponsavelContrato';

@observer
class ResponsavelContratoFormPage extends GenericFormPage {
  constructor(props) {
    super(props, undefined);
    this.store = new ResponsavelContratoFormStore();
  }

  componentDidMount() {
    const { id } = this.props;
    this.store.initialize(id);
  }

  _goBack(responsavelContrato) {
    this.props.closeMethod(responsavelContrato);
  }

  renderActionButtons() {
    return (
      <div className="p-mt-10 form-actions-dialog">
        <Divider />
        <span className="p-d-flex">
          <FcButton
            label="Voltar"
            type="button"
            className="p-ml-auto p-button-secondary p-mr-2"
            onClick={() => this._goBack()}
            loading={this.store.loading}
          />
          <FcButton label="Salvar" type="button" onClick={this.submitFormData} loading={this.store.loading} />
        </span>
      </div>
    );
  }

  render() {
    const { submitted } = this.state;
    if (this.store.object) {
      return (
        <>
          <ResponsavelContratoForm
            object={this.store.object}
            updateAttribute={this.store.updateAttribute}
            getRule={this.store.getRule}
            onSubmit={this.submitFormData}
            submitted={submitted}
            renderActionButtons={this.renderActionButtons}
          />
        </>
      );
    } else {
      return (
        <div className="card page">
          <i
            className="pi pi-spin pi-spinner"
            style={{
              marginTop: '20px',
              marginLeft: 'calc(50% - 20px)',
              fontSize: '2em',
            }}
          ></i>
        </div>
      );
    }
  }
}

ResponsavelContratoFormPage.propTypes = {
  id: PropTypes.any,
  action: PropTypes.any,
  history: PropTypes.any,
};

export default ResponsavelContratoFormPage;
