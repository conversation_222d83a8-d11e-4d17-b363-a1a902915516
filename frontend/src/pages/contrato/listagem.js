import React from 'react';
import { observer } from 'mobx-react';
import ContratoIndexStore from '~/stores/contrato/indexStore';
import UrlRouter from '~/constants/UrlRouter';
import GenericIndexPage from 'fc/pages/GenericIndexPage';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import {
  getValueByKey,
  getValue,
  getValueMoney,
  getValueDate,
  checkUserGroup,
  hasPermissionProxy,
  generateFullURL,
} from 'fc/utils/utils';
import AdvancedSearch from 'fc/components/AdvancedSearch';
import AccessPermission from '~/constants/AccessPermission';
import RescisaoContratoDialog from './rescisao/rescisaoContratualDialog';
import RequisicaoRemocao from '~/pages/requisicaoRemocao/requisicaoRemocaoModal';
import CardList from 'fc/components/CardList';
import PermissionProxy from 'fc/components/PermissionProxy';
import FcButton from 'fc/components/FcButton';
import { Link } from 'react-router-dom';
import { PrimeIcons } from 'primereact/api';
import { ConfirmDialog } from 'primereact/confirmdialog';
import { isValueValid } from 'fc/utils/utils';

@observer
class ContratoListagemPage extends GenericIndexPage {
  constructor(props) {
    super(props, AccessPermission.contrato);
    this.store = new ContratoIndexStore();
    this.state = {
      showRequisicaoRemocao: false,
      showRescisaoContratual: false,
      showAlteracaoContratualWarning: false,
      rowSelected: undefined,
      showDialogConfirmacaoRescisao: false,
    };
    this._renderDialogRequisicaoRemocao = this._renderDialogRequisicaoRemocao.bind(this);
    this._handleRemocaoRequisicaoModal = this._handleRemocaoRequisicaoModal.bind(this);
    this._closeRequisicaoRemocaoModal = this._closeRequisicaoRemocaoModal.bind(this);
    this._handleRescisaoContratualModal = this._handleRescisaoContratualModal.bind(this);
    this._closeRescisaoContratualModal = this._closeRescisaoContratualModal.bind(this);
    this._handleAlteracaoContratual = this._handleAlteracaoContratual.bind(this);
    this._closeAlteracaoContratual = this._closeAlteracaoContratual.bind(this);
    this._updateDatatable = this._updateDatatable.bind(this);
    this._openDialogConfirmacaoRescisao = this._openDialogConfirmacaoRescisao.bind(this);
    this._closeDialogConfirmacaoRescisao = this._closeDialogConfirmacaoRescisao.bind(this);
    this._renderDialogConfirmacaoRescisao = this._renderDialogConfirmacaoRescisao.bind(this);
  }

  componentDidMount() {
    const { id } = this.props.match?.params || {};
    if (id) this.store.getById(id, (contrato) => this.props.onDetail(contrato));
  }

  _toggleDetails(item) {
    this.setState((oldState) => {
      const newShowValue = !oldState.showDetails;
      return { showDetails: newShowValue, selectedItem: newShowValue ? item : null };
    });
    this.store.getTresCasasDecimais(item, () => this.forceUpdate());
  }

  _renderDialogRequisicaoRemocao() {
    return (
      <RequisicaoRemocao
        history={this.props.history}
        processo={this.state.rowSelected}
        visibleDialog={this.state.showRequisicaoRemocao}
        closeDialog={this._closeRequisicaoRemocaoModal}
        updateTable={this._updateDatatable}
        tipoProcesso="contrato"
      />
    );
  }

  _renderDialogRescisaoContratual() {
    return (
      <RescisaoContratoDialog
        key={this.state.rowSelected}
        visible={this.state.showRescisaoContratual}
        history={this.props.history}
        contrato={this.state.rowSelected}
        closeDialog={this._closeRescisaoContratualModal}
        updateTable={this._updateDatatable}
      />
    );
  }

  _renderDialogAlteracaoContratual() {
    const forma = getValueByKey(
      this.state.rowSelected.formaContrato,
      DadosEstaticosService.getFormaContrato(),
      'value',
      'label'
    );
    const message = `A alteração contratual não está disponível pois o(a) "${forma}" em questão não permite aditivo.`;
    return (
      <ConfirmDialog
        rejectClassName="hidden"
        blockScroll
        visible={this.state.showAlteracaoContratualWarning}
        message={message}
        header="Aviso"
        icon="pi pi-exclamation-triangle"
        acceptLabel="Ok"
        onHide={() => this._closeAlteracaoContratual()}
      />
    );
  }

  _openDialogConfirmacaoRescisao() {
    this.setState({
      showDialogConfirmacaoRescisao: true,
    });
  }

  _closeDialogConfirmacaoRescisao() {
    this.setState({
      showDialogConfirmacaoRescisao: false,
      showRescisaoContratual: true,
    });
  }

  _renderDialogConfirmacaoRescisao() {
    const message = `Você realmente deseja rescindir o contrato nº ${this.state.rowSelected?.numeroContrato}? Atenção! Esse procedimento não poderá ser desfeito!`;
    return (
      <ConfirmDialog
        blockScroll
        visible={this.state.showDialogConfirmacaoRescisao}
        message={message}
        header="Confirmação"
        icon="pi pi-user preview-item"
        acceptClassName="p-button-info"
        draggable={false}
        accept={() => this._closeDialogConfirmacaoRescisao()}
        reject={() => this._openDialogConfirmacaoRescisao()}
      />
    );
  }

  _handleRemocaoRequisicaoModal(contrato) {
    this.setState({ showRequisicaoRemocao: true, rowSelected: contrato });
  }

  _closeRequisicaoRemocaoModal() {
    this.setState({ showRequisicaoRemocao: false, rowSelected: undefined });
  }

  _handleRescisaoContratualModal(contrato) {
    contrato.dataAvisoRescisao
      ? this.setState({ showRescisaoContratual: true, rowSelected: contrato })
      : this.setState({ showDialogConfirmacaoRescisao: true, rowSelected: contrato });
  }

  _closeRescisaoContratualModal() {
    this.setState({ showRescisaoContratual: false, rowSelected: undefined });
  }

  _handleAlteracaoContratual(contrato) {
    if (contrato.permiteAditivo) {
      this.pushUrlToHistory(UrlRouter.cadastrosConsulta.contrato.aditivo.index.replace(':idContrato', contrato.id));
    } else {
      this.setState({ showAlteracaoContratualWarning: true, rowSelected: contrato });
    }
  }

  _closeAlteracaoContratual() {
    this.setState({ showAlteracaoContratualWarning: false, rowSelected: undefined });
  }

  _updateDatatable() {
    this.store.reloadTableData(() => this.forceUpdate());
  }

  getCardEllipsisOptions(cardData) {
    const isAuditor = checkUserGroup('Auditor');
    const isRescindido = cardData?.dataAvisoRescisao;
    const items = [];

    if (
      cardData.idRequisicaoModificacao &&
      hasPermissionProxy(AccessPermission.requisicaoModificacao.writePermission)
    ) {
      const itemToAdd = {
        label: 'Requisição de modificação pendente',
        icon: 'pi pi-exclamation-triangle',
      };

      if (isAuditor) {
        itemToAdd.url = generateFullURL(
          UrlRouter.auditoria.requisicaoModificacao.indexDetail.replace(':id', cardData.idRequisicaoModificacao)
        );
      } else {
        itemToAdd.url = generateFullURL(
          UrlRouter.administracao.requisicaoModificacao.indexDetail.replace(':id', cardData.idRequisicaoModificacao)
        );
      }

      items.push(itemToAdd);
    } else {
      if (hasPermissionProxy(this.getReadPermission())) {
        items.push({
          label: 'Detalhes',
          icon: 'pi pi-eye',
          command: () => this.props.onDetail(cardData),
        });
        items.push({
          label: 'Empenho',
          icon: 'pi pi-money-bill',
          disabled: isRescindido,
          url: generateFullURL(UrlRouter.cadastrosConsulta.contrato.empenho.index.replace(':idContrato', cardData.id)),
        });
        items.push({
          label: 'Alteração Contratual',
          icon: 'pi pi-copy',
          disabled: isRescindido,
          command: () => this._handleAlteracaoContratual(cardData),
        });
      }

      if (hasPermissionProxy(this.getWritePermission())) {
        items.push({
          label: this.store.isRequisicaoModificacao(cardData.dataCadastro)
            ? 'Criar Requisição de Modificação'
            : 'Editar',
          icon: 'pi pi-pencil',
          disabled: isRescindido,
          url: this.store.isRequisicaoModificacao(cardData.dataCadastro)
            ? generateFullURL(
                UrlRouter.administracao.requisicaoModificacao.contrato.requisitar.replace(':id', cardData.id)
              )
            : generateFullURL(UrlRouter.cadastrosConsulta.contrato.editar.replace(':id', cardData.id)),
        });
        items.push({
          label: 'Rescisão Contratual',
          icon: 'pi pi-minus-circle',
          command: () => this._handleRescisaoContratualModal(cardData),
        });
        items.push({
          label: 'Remover',
          icon: 'pi pi-trash',
          command: () => this._handleRemocaoRequisicaoModal(cardData),
        });
      }
    }

    return items;
  }

  render() {
    const isAuditor = checkUserGroup('Auditor');

    const fields = [
      {
        label: 'Número do Contrato',
        field: 'numero',
        value: 'title',
        sortable: true,
        body: (cardData) =>
          isAuditor
            ? cardData.formaContrato === 'CONTRATO'
              ? ` Contrato n° ${cardData?.numeroContrato} - ${getValue(cardData?.entidade?.nome)}`
              : `${getValueByKey(
                  cardData?.formaContrato,
                  DadosEstaticosService.getFormaContrato(),
                  'value',
                  'label'
                )} n° ${cardData?.numeroContrato}`
            : cardData.formaContrato === 'CONTRATO'
            ? `Contrato n° ${cardData?.numeroContrato}`
            : `${getValueByKey(
                cardData?.formaContrato,
                DadosEstaticosService.getFormaContrato(),
                'value',
                'label'
              )} n° ${cardData?.numeroContrato}`,
      },
      {
        field: 'licitante',
        label: 'Contratado(a)',
        value: 'subtitle',
        sortable: true,
        body: ({ licitante }) => getValue(licitante?.nome),
      },
      {
        label: 'Objeto',
        field: 'objeto',
        value: 'mainContent',
        sortable: true,
      },
      {
        field: 'tipo',
        label: 'Origem',
        value: 'iconLabel',
        color: '#8f48d2',
        icon: 'pi pi-file',
        sortable: true,
        body: ({ tipo }) => {
          getValueByKey(tipo, DadosEstaticosService.getTipoProcesso());
        },
      },
      {
        label: 'Valor',
        field: 'valorGlobal',
        value: 'iconLabel',
        color: '#4da73b',
        icon: 'pi pi-money-bill',
        sortable: true,
        body: (cardData) => getValueMoney(cardData?.valorGlobal, 2),
      },
      {
        label: 'Vigência',
        fields: ['dataVigenciaInicial', 'dataVigenciaFinal', 'dataFinalVigente', 'dataAvisoRescisao'],
        combinedColumns: true,
        value: 'iconLabel',
        color: '#4c4c4b',
        icon: 'pi pi-calendar',
        body: (datasVigencia) =>
          isValueValid(datasVigencia[3])
            ? 'Rescindido'
            : isValueValid(datasVigencia[2])
            ? getValueDate(datasVigencia[0]) + ' a ' + getValueDate(datasVigencia[2])
            : getValueDate(datasVigencia[0]) + ' a ' + getValueDate(datasVigencia[1]),
      },
      {
        label: 'Adivitado',
        field: 'aditivado',
        value: 'iconLabel',
        color: '#4da73b',
        showIfExists: true,
        icon: 'pi pi-money-bill',
        sortable: true,
        body: (cardData) => getValueMoney(cardData?.aditivado, 2),
      },
      {
        label: 'Suprimido',
        field: 'suprimido',
        value: 'iconLabel',
        color: '#FF0000',
        showIfExists: true,
        icon: 'pi pi-money-bill',
        sortable: true,
        body: (cardData) => getValueMoney(cardData?.suprimido, 2),
      },
      {
        label: 'Entidade Externa',
        field: 'entidadeExterna',
        value: 'iconLabel',
        color: '#c63737',
        icon: 'pi pi-external-link',
        sortable: true,
        body: (cardData) => cardData?.processoExterno && 'Entidade Externa',
      },
      {
        label: 'Ações',
        value: 'ellipsisItems',
        body: (cardData) => this.getCardEllipsisOptions(cardData),
      },
    ];

    const exportFields = [
      {
        header: 'Número do Contrato',
        field: 'numero',
      },
      {
        header: 'Objeto',
        field: 'objeto',
      },
      {
        header: 'Licitante',
        field: 'licitante',
        body: ({ licitante }) => getValue(licitante?.nome),
      },
      {
        header: 'Entidade Externa',
        field: 'entidadeExterna',
        body: (cardData) => (cardData?.processoExterno ? 'Entidade Externa' : 'Não'),
      },
    ];

    const header = () => (
      <div className="table-header flex justify-content-start">
        <PermissionProxy resourcePermissions={this.getWritePermission()}>
          <Link to={UrlRouter.cadastrosConsulta.contrato.novo}>
            <FcButton
              className="p-button"
              label="Novo"
              style={{ marginBottom: '5px', marginRight: '5px' }}
              icon={PrimeIcons.PLUS}
            />
          </Link>
        </PermissionProxy>
        {this.renderTableDataExport(exportFields, 'contratos', true)}
      </div>
    );

    return (
      <>
        <AdvancedSearch
          searchParams={this.store.getAdvancedSearchParams()}
          store={this.store}
          searchFields={['numeroContrato', 'tipo', 'objeto', 'licitante']}
          filterSuggest={this.store.getFilterSuggest()}
        />
        <CardList
          fields={fields}
          store={this.store}
          labelsLimit={5}
          header={header()}
          onTitleClick={(cardData) => {
            if (hasPermissionProxy(this.getReadPermission())) {
              return {
                command: () => this.props.onDetail(cardData),
              };
            }
          }}
        />
        {this._renderDialogConfirmacaoRescisao()}
        {this._renderDialogRescisaoContratual()}
        {this._renderDialogRequisicaoRemocao()}
        {this.state.showAlteracaoContratualWarning && this._renderDialogAlteracaoContratual()}
      </>
    );
  }
}

export default ContratoListagemPage;
