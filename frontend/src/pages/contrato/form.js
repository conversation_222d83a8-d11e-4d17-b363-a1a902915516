import { action } from 'mobx';
import { observer } from 'mobx-react';
import PropTypes from 'prop-types';
import { Dialog } from 'primereact/dialog';
import { Steps } from 'primereact/steps';
import GenericFormPage from 'fc/pages/GenericFormPage';
import AppBreadCrumb from 'fc/components/AppBreadCrumb';
import PermissionProxy from 'fc/components/PermissionProxy';
import FormField from 'fc/components/FormField';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import FcButton from 'fc/components/FcButton';
import { isValueValid, showNotification } from 'fc/utils/utils';
import AppStore from 'fc/stores/AppStore';
import FcInputTextarea from 'fc/components/FcInputTextarea';
import ContratoFormStore from '~/stores/contrato/formStore';
import RequisicaoModificacaoContratoFormStore from '~/stores/contrato/requisicaoModificacao/formStore';
import UrlRouter from '~/constants/UrlRouter';
import AccessPermission from '~/constants/AccessPermission';
import TabItens from './tabs/tabItens';
import TabProcesso from './tabs/tabProcesso';
import TabDadosBasicos from './tabs/tabDadosBasicos';
import { Message } from 'primereact/message';
import classNames from 'classnames';
import TabResumo from './tabs/tabResumo';
import moment from 'moment';

@observer
class ContratoFormPage extends GenericFormPage {
  reqModificacaoStore;
  constructor(props) {
    super(props, UrlRouter.cadastrosConsulta.contrato.index, AccessPermission.contrato);
    this.store = new ContratoFormStore();
    this.reqModificacaoStore = new RequisicaoModificacaoContratoFormStore();

    this.state = {
      showDialogReqMod: false,
      errorDialogValue: false,
      activeTab: 0,
      showItensWarning: false,
    };
    this._toggleDialogReqMod = this._toggleDialogReqMod.bind(this);
    this.renderDialogRequisicaoModificacao = this.renderDialogRequisicaoModificacao.bind(this);
  }

  componentDidMount() {
    const { id } = this.props;
    if (this.props.action === 'edit') {
      this.store.initialize(id, {}, () => {
        this.store.checkDataCadastro();
        this.store.carregarTiposArquivo(() =>
          this.store.initializeFiles(id, () => {
            this.setState({ activeTab: 2 });
            this.store.currentStep = 2;
          })
        );
        this.store.updateGarantia();
        !this.store.object.processoExterno && this.store.carregarDadosProcesso();
      });
    } else {
      this.store.initialize(id, { formaContrato: 'CONTRATO', lei: 'LEI_N_14133' }, () =>
        this.store.carregarTiposArquivo(() => this.forceUpdate())
      );
    }

    const idLicitacao = this.props.match?.params?.idLicitacao;
    if (idLicitacao) {
      const callback = (processo) => {
        this.handleSelectProcesso(processo);
      };
      this.store.getLicitacao(idLicitacao, callback);
    }
  }

  @action
  handleSelectProcesso(processo) {
    this.store.carregarProcesso(
      processo,
      () => {
        this.setState({ activeTab: 1 });
        this.store.currentStep = 1;
      },
      () => this.setState({ showItensWarning: true })
    );
  }

  selectExternalEntityProcess() {
    this.store.initializeExternalProcess();
    this.setState({ activeTab: 1 });
    this.store.currentStep = 1;
  }

  _toggleDialogReqMod() {
    this.setState((oldState) => ({ showDialogReqMod: !oldState.showDialogReqMod }));
  }

  _goBack() {
    this.props.history.push(UrlRouter.cadastrosConsulta.contrato.index);
  }

  _getFieldErrorMessage(rendered, message) {
    if (rendered) {
      return <small className="p-error">{message}</small>;
    }
    return null;
  }

  renderDialogReqModFooter() {
    return (
      <div>
        <FcButton
          label="Cancelar"
          icon="pi pi-times"
          onClick={() => this._toggleDialogReqMod()}
          className="p-button-text"
        />
        <FcButton
          label="Enviar Requisição"
          icon="pi pi-check"
          onClick={() => {
            if (this.reqModificacaoStore.justificativaJurisdicionado) {
              this.submitFormData();
              this._toggleDialogReqMod();
            } else {
              !this.state.errorDialogValue && this.setState({ errorDialogValue: true });
            }
          }}
        />
      </div>
    );
  }

  renderDialogRequisicaoModificacao() {
    const { getRule } = this.store;
    return (
      <Dialog
        header="Justificativa Requisição"
        visible={this.state.showDialogReqMod}
        style={{ width: '50vw' }}
        footer={this.renderDialogReqModFooter()}
        onHide={() => this._toggleDialogReqMod()}
      >
        <div className="p-fluid p-formgrid p-grid">
          <FormField columns={12} attribute="justificativa" rule={getRule('justificativa')} label="Justificativa">
            <FcInputTextarea
              className={this.state.errorDialogValue ? 'p-invalid p-error' : ''}
              value={this.reqModificacaoStore.justificativaJurisdicionado}
              onChange={(e) => {
                this.reqModificacaoStore.updateAttribute('jurisdicionado', e);
                isValueValid(this.reqModificacaoStore.justificativaJurisdicionado?.length) &&
                  this.setState({ errorDialogValue: false });
              }}
              placeholder="Descreva a justificativa"
            />
          </FormField>
          <div className="p-col-6">
            {this._getFieldErrorMessage(this.state.errorDialogValue, 'Por favor, preencha o campo')}
          </div>
        </div>
      </Dialog>
    );
  }

  handleVoltar() {
    const { activeTab } = this.state;
    if (activeTab == 0) this._goBack();
    else if (activeTab == 1) this.handleChangeTab(0);
    else if (activeTab == 2) this.handleChangeTab(1);
    else if (activeTab == 3) this.handleChangeTab(2);
  }

  isDisabledAvancar() {
    const errorsMessages = {
      message: '',
      disabled: true,
    };

    const processo = this.store.getProcesso();

    if (processo?.lei !== 'LEI_N_8666' && !processo?.processoMigrado && processo?.licitacao?.lei !== 'LEI_N_8666') {
      if (!this.store.enableReqMod && !this.store.object?.contratoLicitante?.itens?.length > 0) {
        errorsMessages.message =
          'É obrigatório adicionar pelo menos um item para um licitante antes de prosseguir para a próxima etapa.';
      } else if (this.store.object?.contratoLicitante?.itens?.length === 0) {
        errorsMessages.message = 'Por favor, adicione ao menos um item antes de avançar para a próxima etapa.';
      } else if (
        processo &&
        isValueValid(processo.valorAdjudicado) &&
        this.store.object?.contratoLicitante?.itens.some((i) => i.vencedorLicitacao.valorUnitario < i.valor)
      ) {
        errorsMessages.message = 'O valor unitário dos itens não deve ser superior ao valor adjudicado';
      } else if (
        this.store.object?.contratoLicitante?.itens?.length > 0 &&
        this.store.object.contratoLicitante.itens.some((i) => !i.preenchido)
      ) {
        errorsMessages.message = 'Todos os itens precisam ser marcados como preenchidos.';
      } else {
        errorsMessages.disabled = false;
      }
    } else if (!this.store.checkValorMaximoContratado() && !this.store.object?.numero) {
      errorsMessages.message =
        'Sem valor disponível para o(a) contratado(a).Verifique os valores atribuídos ou selecione outro licitante.';
    } else if (!this.store.object?.contratoLicitante.licitante) {
      errorsMessages.message = 'É necessário adicionar um licitante.';
    } else {
      errorsMessages.disabled = false;
    }

    return errorsMessages;
  }

  renderActionButtons() {
    const hasWritePermission = !this.getWritePermission() || AppStore.hasPermission(this.getWritePermission());

    const disabledAvancar = this.isDisabledAvancar();
    const { activeTab } = this.state;
    return (
      <div className="p-mt-10 form-actions">
        <div className="p-mt-2">
          <span className="p-mt-10 form-actions p-d-flex align-items-center">
            {disabledAvancar.disabled && (
              <Message className="p-ml-auto p-mr-2" severity="warn" text={disabledAvancar.message} />
            )}
            <FcButton
              label="Voltar"
              type="button"
              className={classNames('p-button-secondary p-mr-2', { 'p-ml-auto': !disabledAvancar.disabled })}
              onClick={() => this.handleVoltar()}
              loading={this.store.loading}
            />
            {hasWritePermission && !this.store.enableReqMod && activeTab === 3 && (
              <FcButton
                label="Salvar"
                type="button"
                loading={this.store.loading}
                onClick={() => {
                  if (this.store.validateSubmittedFiles(this.store.arquivoContratoList)) {
                    this.validateContratoValorZero();
                  }
                }}
              />
            )}
            {activeTab < 3 && (
              <FcButton
                label="Avançar"
                type="button"
                loading={this.store.loading}
                disabled={disabledAvancar.disabled}
                onClick={() => this.handleChangeTab(activeTab + 1)}
              />
            )}
            {hasWritePermission && this.store.enableReqMod && activeTab === 3 && (
              <FcButton
                label="Enviar Requisição"
                onClick={() => this._toggleDialogReqMod()}
                loading={this.store.loading}
              />
            )}
          </span>
        </div>
      </div>
    );
  }

  validateContratoValorZero() {
    this.store.object?.valorGlobal === 0
      ? showNotification('error', null, 'Contrato não pode ser criado com valor 0!')
      : this.submitFormData();
  }

  submitFormData() {
    const execution = () => {
      if (!this.store.rules.hasError) {
        const contrato = this.store.object;
        const contratoDTO = {
          contrato: {
            ...contrato,
            contratoLicitante: {
              ...contrato.contratoLicitante,
            },
          },
          arquivosContrato: this.store.arquivoContratoList,
          filtros: this.store.getOptionsArquivosContrato(),
        };
        !this.store.enableReqMod && this.store.save(this._goBack, this.props.action);
        this.store.enableReqMod &&
          this.reqModificacaoStore.justificativaJurisdicionado &&
          this.reqModificacaoStore.enviarRequisicaoContrato(contratoDTO, this._goBack);
      } else {
        showNotification('error', null, 'Verifique os campos do formulário!');
      }
    };

    if (this.state.submitted) {
      execution();
    } else {
      this.setState({ submitted: true }, execution);
    }
  }

  renderTabDadosBasicos() {
    return (
      <div className="p-col-12">
        <TabDadosBasicos store={this.store} submitted={this.state.submitted} action={this.props.action} />
      </div>
    );
  }

  renderTabResumo() {
    return (
      <div className="p-col-12">
        <TabResumo store={this.store} />
      </div>
    );
  }

  renderTabItens() {
    return (
      <div className="p-col-12">
        <TabItens
          store={this.store}
          readOnly={this.store.enableReqMod}
          submitted={this.state.submitted}
          action={this.props.action}
          getValorUnitarioMaximo={this.getValorUnitarioMaximo}
        />
      </div>
    );
  }

  validarDadosBasicos(callback) {
    const vigenciaInicial = moment(this.store.object.dataVigenciaInicial);
    const vigenciaFinal = moment(this.store.object.dataVigenciaFinal);

    const execution = () => {
      if (this.store.rules.hasError) {
        showNotification('error', null, 'Verifique os campos do formulário!');
      } else if (
        this.store.object.dataVigenciaInicial &&
        this.store.object.dataVigenciaFinal &&
        vigenciaInicial.isAfter(vigenciaFinal)
      ) {
        showNotification('error', null, 'A data do Fim da Vigência não pode ser inferior à data do Início da Vigência');
      } else if (this.store.validateSubmittedFiles(this.store.arquivoContratoList)) {
        this.store.validaArquivos(callback);
      }
    };

    if (this.state.submitted) {
      execution();
    } else {
      this.setState({ submitted: true }, execution);
    }
  }

  validarDadosProcessoExterno(callback) {
    const execution = () => {
      if (this.store.rules.hasError) {
        showNotification('error', null, 'Verifique os campos do formulário!');
      } else {
        callback && callback();
      }
    };

    if (this.state.submitted) {
      execution();
    } else {
      this.setState({ submitted: true }, execution);
    }
  }

  @action
  handleChangeTab(index, isDirectClick = false) {
    const processo = this.store.getProcesso();
    const isDirectProcessSelection = isDirectClick && index === 0 && this.props.action === 'edit';

    if (isDirectProcessSelection) {
      showNotification(
        'error',
        null,
        'Não é possível alterar o processo de origem de um contrato já cadastrado. Caso haja alguma discrepância, o contrato deverá ser excluído, mediante aprovação de auditoria, para ser efetuado um novo cadastro.'
      );
    } else if (index === 0 && this.props.action === 'edit') {
      this._goBack();
    } else if (index === 0) {
      this.store.object = { formaContrato: 'CONTRATO' };
      this.setState({ activeTab: 0 });
      this.store.currentStep = 0;
    } else if (index === 1 && this.store.object.status) {
      this.setState({ activeTab: 1 });
      this.store.currentStep = 1;
    } else if (
      index === 2 &&
      this.store.object?.contratoLicitante.licitante &&
      (processo?.lei === 'LEI_N_8666' || processo?.processoMigrado || processo?.licitacao?.lei == 'LEI_N_8666')
    ) {
      this.setState({ activeTab: 2, submitted: false });
      this.store.currentStep = 2;
    } else if (index === 2 && (this.store.object?.contratoLicitante?.itens?.length > 0 || this.store.enableReqMod)) {
      if (this.store.object.processoExterno) {
        this.validarDadosProcessoExterno(() => {
          this.setState({ activeTab: 2, submitted: false });
          this.store.currentStep = 2;
        });
      } else {
        this.setState({ activeTab: 2, submitted: false });
        this.store.currentStep = 2;
      }
    } else if (index === 3) {
      this.validarDadosBasicos(() => {
        this.setState({ activeTab: 3, submitted: false });
        this.store.currentStep = 3;
      });
    }
  }

  renderTabProcessos() {
    return (
      <div className="p-col-12">
        <TabProcesso
          onSelect={(processo) => this.handleSelectProcesso(processo)}
          onSelectExternalProcess={() => this.selectExternalEntityProcess()}
          store={this.store}
        />
      </div>
    );
  }

  renderDialogWarningItens() {
    return (
      <Dialog
        header="Operação não permitida"
        onHide={() => this.setState({ showItensWarning: false })}
        visible={this.state.showItensWarning}
        style={{ width: '40vw' }}
        footer={<FcButton type="button" label="Ok" onClick={() => this.setState({ showItensWarning: false })} />}
      >
        Não é possível criar contratos a partir deste processo por um dos seguintes motivos:
        <ul>
          <li>Todos os itens/lotes disponíveis foram contratados;</li>
          <li>Não foram cadastrados itens/lotes.</li>
          <li>Os credenciados não estão mais vigentes.</li>
        </ul>
      </Dialog>
    );
  }

  render() {
    const breacrumbItems = [
      { label: 'Contrato', url: UrlRouter.cadastrosConsulta.contrato.index },
      { label: this.props.action === 'new' ? 'Novo' : 'Editar' },
    ];

    const steps = [
      { label: 'Seleção do Processo' },
      { label: 'Escolha dos Itens' },
      {
        label: DadosEstaticosService.getFormaContrato().find((f) => f.value === this.store.object?.formaContrato)?.text,
      },
      { label: 'Resumo' },
    ];

    let content;

    if (this.store.object) {
      content = (
        <>
          <AppBreadCrumb items={breacrumbItems} />
          <div className="card page form-action-buttons">
            <Steps
              model={steps}
              activeIndex={this.state.activeTab}
              onSelect={(e) => this.handleChangeTab(e.index, true)}
              readOnly={false}
            />
            {this.state.activeTab === 0 && this.renderTabProcessos()}
            {this.state.activeTab === 1 && this.renderTabItens()}
            {this.state.activeTab === 2 && this.renderTabDadosBasicos()}
            {this.state.activeTab === 3 && this.renderTabResumo()}
            {this.state.activeTab > 0 && this.renderActionButtons()}
            {this.renderDialogRequisicaoModificacao()}
            {this.renderDialogWarningItens()}
          </div>
        </>
      );
    } else {
      content = (
        <div className="card page">
          <AppBreadCrumb items={breacrumbItems} />
          <i className="pi pi-spin pi-spinner" style={{ fontSize: '2em' }}></i>
        </div>
      );
    }
    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        {content}
      </PermissionProxy>
    );
  }
}

ContratoFormPage.propTypes = {
  id: PropTypes.any,
  action: PropTypes.any,
  history: PropTypes.any,
  match: PropTypes.any,
  isRequisicaoModificacao: PropTypes.bool,
  reqModificacaoStore: PropTypes.any,
};

export default ContratoFormPage;
