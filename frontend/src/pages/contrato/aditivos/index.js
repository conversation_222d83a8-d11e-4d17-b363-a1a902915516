import React from 'react';
import { observer } from 'mobx-react';
import AppBreadCrumb from 'fc/components/AppBreadCrumb';
import UrlRouter from '~/constants/UrlRouter';
import GenericIndexPage from 'fc/pages/GenericIndexPage';
import PermissionProxy from 'fc/components/PermissionProxy';
import AccessPermission from '~/constants/AccessPermission';
import AditivoDetailPage from './indexDetail';
import AditivoContratoListagemPage from './listagem';
import FcCloseableTabView from 'fc/components/FcCloseableTabView';

@observer
class AditivoContratoIndexPage extends GenericIndexPage {
  constructor(props) {
    super(props, AccessPermission.aditivoContrato);
    this.state = {
      data: [
        {
          id: 1,
          header: 'Listagem',
          closeable: false,
          content: <AditivoContratoListagemPage {...props} onDetail={(aditivo) => this.onDetail(aditivo)} />,
        },
      ],
      count: 2,
      activeTabIndex: 0,
    };

    this.setActiveTabIndex = this.setActiveTabIndex.bind(this);
    this.handleCloseTab = this.handleCloseTab.bind(this);
    this.onDetail = this.onDetail.bind(this);
  }

  onDetail(aditivo) {
    const existingTab = this.state.data.find((tab) => tab.idAditivo === aditivo.id);
    if (existingTab) {
      this.setActiveTabIndex(existingTab);
    } else {
      const newAditivo = {
        id: aditivo.id,
        idAditivo: aditivo.id,
        header: 'Aditivo - ' + aditivo.numero,
        closeable: true,
        content: <AditivoDetailPage aditivo={aditivo} />,
      };
      this.setState({ data: [...this.state.data, newAditivo], count: this.state.count + 1 });
    }
  }

  setActiveTabIndex(tab) {
    this.setState({ activeTabIndex: this.state.data.indexOf(tab) });
  }

  handleCloseTab(tab) {
    this.setState((prevState) => {
      return { data: prevState.data.filter((t) => t.id !== tab.id) };
    });
  }

  render() {
    const breacrumbItems = [
      { label: 'Contrato', url: UrlRouter.cadastrosConsulta.contrato.index },
      { label: 'Alteração Contratual' },
    ];

    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        <AppBreadCrumb items={breacrumbItems} />
        <div className="card page index-table">
          <FcCloseableTabView
            tabs={this.state.data}
            activeTabIndex={this.state.activeTabIndex}
            onChangeTab={this.setActiveTabIndex}
            onClose={this.handleCloseTab}
            fixedFirstTab
          />
        </div>
      </PermissionProxy>
    );
  }
}

AditivoContratoIndexPage.displayName = 'AditivoContratoIndexPage';

export default AditivoContratoIndexPage;
