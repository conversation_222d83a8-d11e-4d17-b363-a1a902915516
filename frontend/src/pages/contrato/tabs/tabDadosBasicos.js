import { observer } from 'mobx-react';
import PropTypes from 'prop-types';
import './style.scss';
import Form<PERSON>ield from 'fc/components/FormField';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import { Fieldset } from 'primereact/fieldset';
import { RadioButton } from 'primereact/radiobutton';
import InputMonetary from 'fc/components/InputMonetary';
import { InputText } from 'primereact/inputtext';
import ResponsavelContratoFormPage from '../responsavel/form';
import SelectDialog from 'fc/components/SelectDialog';
import ResponsavelContratoIndexStore from '~/stores/responsavelContrato/indexStore';
import MultipleFileUploader from 'fc/components/MultipleFileUploader';
import moment from 'moment';
import { DATE_PARSE_FORMAT } from 'fc/utils/date';
import FcInputTextarea from 'fc/components/FcInputTextarea';
import React from 'react';
import PermissionProxy from 'fc/components/PermissionProxy';
import { AutoComplete } from 'primereact/autocomplete';
import FcButton from 'fc/components/FcButton';
import { ConfirmDialog } from 'primereact/confirmdialog';
import FcMultiSelect from 'fc/components/FcMultiSelect';
import { SelectButton } from 'primereact/selectbutton';
import { Dropdown } from 'primereact/dropdown';
import { InputMask } from 'primereact/inputmask';
import 'fc/utils/reset-fieldset-styles.scss';
import FcCalendar from 'fc/components/FcCalendar';
import { getValueMoney, somaValoresLotes } from 'fc/utils/utils';

@observer
class TabDadosBasicos extends React.Component {
  store;
  constructor(props) {
    super(props);
    this.store = props.store;
    this.state = {
      idRemove: null,
    };
    this.storeResponsavelContrato = new ResponsavelContratoIndexStore();
    this._handleDeleteRow = this._handleDeleteRow.bind(this);
  }

  componentDidMount() {
    this.store.initializeTabDadosBasicos();
    this.store.carregarTiposArquivo();
    this.store.getPessoasResponsaveis();
    this.props.action === 'edit' && this.store.getQuantidadeAditivosContrato(this.store.object.id);
  }

  getDateAttributeValue(value) {
    return value ? moment(value).toDate() : value;
  }

  _handleDeleteRow() {
    const { gestor, gestorSubstituto, fiscal, fiscalSubstituto } = this.store.object;
    if (gestor?.id === this.state.idRemove) {
      this.store.updateAttribute('gestor', undefined);
    }
    if (gestorSubstituto?.id === this.state.idRemove) {
      this.store.updateAttribute('gestorSubstituto', undefined);
    }
    if (fiscal?.id === this.state.idRemove) {
      this.store.updateAttribute('fiscal', undefined);
    }
    if (fiscalSubstituto?.id === this.state.idRemove) {
      this.store.updateAttribute('fiscalSubstituto', undefined);
    }
  }

  confirmRemove(id) {
    const { deleteRow, reloadTableData } = this.storeResponsavelContrato;
    return (
      <ConfirmDialog
        blockScroll
        visible={this.store.isConfirmDialogVisible}
        message="Você realmente deseja excluir o registro selecionado?"
        header="Excluir registro"
        icon="pi pi-exclamation-triangle"
        acceptClassName="p-button-danger"
        accept={() => {
          deleteRow(id, () => {
            reloadTableData();
            this._handleDeleteRow();
          });
        }}
        onHide={() => this.store.toggleShowConfirmDialog()}
      />
    );
  }

  render() {
    if (!this.store.loadingTabDadosBasicos) {
      const { submitted } = this.props;
      const { getRule } = this.store;
      const formaContrato = this.store.object.formaContrato;

      const searchFieldsResponsavel = ['nome', 'email', 'cpf', 'efetivo'];

      const columnsResponsavel = [
        {
          field: 'nome',
          header: 'Nome',
          sortable: true,
        },
        {
          field: 'email',
          header: 'E-mail',
        },
        {
          field: 'cpf',
          header: 'CPF',
        },
        {
          field: 'telefone',
          header: 'Telefone',
        },
        {
          field: 'efetivo',
          header: 'Efetivo',
          body: ({ efetivo }) => (efetivo ? 'Sim' : 'Não'),
        },
        {
          body: (rowData) => {
            return (
              <FcButton
                icon="pi pi-trash"
                className="p-button-sm p-button-danger"
                onClick={() => {
                  this.setState({ idRemove: rowData.id });
                  this.store.toggleShowConfirmDialog();
                }}
              />
            );
          },
        },
      ];

      const garantias = DadosEstaticosService.getGarantiasContrato();

      return (
        <div className="p-col-12">
          <form>
            {this.store.enableReqMod && (
              <Fieldset legend="AVISO">
                <h6 style={{ color: '#dd0303' }}>
                  A EDIÇÃO DESTE CONTRATO ESTARÁ SUJEITA A RATIFICAÇÃO PELA EQUIPE DE AUDITORIA DO TCE
                </h6>
              </Fieldset>
            )}
            <Fieldset
              legend={
                DadosEstaticosService.getFormaContrato().find((f) => f.value === this.store.object?.formaContrato)?.text
              }
            >
              <div className="p-fluid p-formgrid p-grid">
                {this.store.enableReqMod && (
                  <PermissionProxy resourcePermissions={'admin'} hideOnFail>
                    <FormField
                      columns={4}
                      label="Entidade"
                      rule={getRule('entidade')}
                      submitted={submitted}
                      attribute="entidade"
                    >
                      <AutoComplete
                        value={this.store.object.entidade}
                        suggestions={this.store.entidadesFiltradas}
                        completeMethod={(e) => this.store.getEntidadesFiltradas(e, () => this.forceUpdate())}
                        field="nome"
                        onChange={(e) => this.store.updateAttribute('entidade', e.value)}
                        aria-label="Entidade"
                        dropdownAriaLabel="Selecione uma entidade"
                      />
                    </FormField>
                  </PermissionProxy>
                )}
                {this.store.renderFormField('numero') && (
                  <FormField
                    columns={4}
                    attribute="numero"
                    label={this.store.labelNumero()}
                    rule={getRule('numero')}
                    submitted={submitted}
                  >
                    {formaContrato === 'CONTRATO' ? (
                      this.store.object.processoMigrado ? (
                        <InputText
                          onChange={(e) => this.store.updateAttribute('numero', e)}
                          value={this.store.object.numero}
                          id="numero"
                        />
                      ) : (
                        <InputMask
                          mask={'999'}
                          onChange={(e) => this.store.updateAttribute('numero', e)}
                          value={this.store.object.numero}
                          id="numero"
                        />
                      )
                    ) : (
                      <InputText
                        onChange={(e) => /^$|^\d+$/.test(e.target.value) && this.store.updateAttribute('numero', e)}
                        value={this.store.object.numero ?? ''}
                        id="numero"
                      />
                    )}
                  </FormField>
                )}

                {this.store.renderFormField('dataEmpenho') && (
                  <FormField
                    columns={4}
                    attribute="dataEmpenho"
                    label={this.store.labelData()}
                    rule={getRule('dataEmpenho')}
                    submitted={submitted}
                  >
                    <FcCalendar
                      value={this.getDateAttributeValue(this.store.object.dataEmpenho)}
                      onChange={(e) => this.store.updateAttributeDateWithHours('dataEmpenho', e)}
                      showIcon
                      mask="99/99/9999"
                    />
                  </FormField>
                )}

                {formaContrato === 'CONTRATO' && this.store.renderFormField('anoContrato') && (
                  <FormField
                    columns={2}
                    attribute="anoContrato"
                    label="Ano"
                    rule={getRule('anoContrato')}
                    submitted={submitted}
                  >
                    <Dropdown
                      onChange={(e) => this.store.updateAttribute('anoContrato', e)}
                      placeholder="Informe o ano"
                      value={this.store.object?.anoContrato}
                      id="anoContrato"
                      optionLabel="text"
                      optionValue="value"
                      options={this.store.anos}
                    />
                  </FormField>
                )}
                {this.store.renderFormField('dataPublicacao') && (
                  <FormField
                    columns={formaContrato === 'CONTRATO' ? 2 : 4}
                    attribute="dataPublicacao"
                    label={this.store.labelData()}
                    rule={getRule('dataPublicacao')}
                    submitted={submitted}
                  >
                    <FcCalendar
                      value={this.getDateAttributeValue(this.store.object.dataPublicacao)}
                      maxDate={
                        this.store.object.dataCadastro
                          ? this.getDateAttributeValue(this.store.object.dataCadastro)
                          : this.getDateAttributeValue(moment().format(DATE_PARSE_FORMAT))
                      }
                      onChange={(e) => this.store.updateAttributeDateWithHours('dataPublicacao', e)}
                      showIcon
                      mask="99/99/9999"
                    />
                  </FormField>
                )}

                {this.store.renderFormField('numeroDoe') && (
                  <FormField
                    columns={4}
                    attribute="numeroDoe"
                    label={this.store.labelNumeroDoe()}
                    rule={getRule('numeroDoe')}
                    submitted={submitted}
                  >
                    <InputText
                      onChange={(e) => this.store.updateAttribute('numeroDoe', e)}
                      value={this.store.object.numeroDoe}
                      placeholder={this.store.placeholderNumeroDoe()}
                      keyfilter={this.store.keyFilterNumeroDoe()}
                    />
                  </FormField>
                )}
                {this.store.renderFormField('permiteAditivo') && (
                  <FormField
                    columns={4}
                    attribute="permiteAditivo"
                    label="Permite Aditivo?"
                    rule={getRule('permiteAditivo')}
                    submitted={submitted}
                  >
                    <div className="p-field-radiobutton p-dir-row">
                      <div className="p-field-radiobutton p-col">
                        <RadioButton
                          inputId="permiteAditivo"
                          name="permiteAditivo"
                          value={true}
                          onChange={(e) => this.store.updateAttribute('permiteAditivo', e.value)}
                          checked={this.store.object.permiteAditivo === true}
                          tooltip={this.store.object.motivoRescisao ? 'Contrato Rescindido não permite Aditivo' : ''}
                          disabled={this.store.object.motivoRescisao || this.store.quantidadeAditivos}
                        />
                        <label htmlFor="permiteAditivo">Sim</label>
                      </div>
                      <div className="p-field-radiobutton p-col">
                        <RadioButton
                          inputId="naoPermiteAditivo"
                          name="naoPermiteAditivo"
                          value={false}
                          onChange={(e) => this.store.updateAttribute('permiteAditivo', e.value)}
                          checked={this.store.object.permiteAditivo === false}
                          disabled={this.store.quantidadeAditivos}
                        />
                        <label htmlFor="naoPermiteAditivo">Não</label>
                      </div>
                    </div>
                  </FormField>
                )}

                {this.store.renderFormField('valorGlobal') && (
                  <FormField
                    columns={4}
                    attribute="valorGlobal"
                    label={this.store.labelValor()}
                    rule={getRule('valorGlobal')}
                    submitted={submitted}
                  >
                    <InputMonetary
                      onChange={(e) => this.store.updateAttribute('valorGlobal', e)}
                      placeholder="R$"
                      mode="currency"
                      value={
                        this.store.tipo === 'CR'
                          ? getValueMoney(
                              somaValoresLotes(
                                this.store.object.processo?.termoReferencia.lotes,
                                this.store.object.processo?.termoReferencia?.tresCasasDecimais ? 3 : 2
                              )
                            )
                          : this.store.object.valorGlobal
                      }
                      decimalPlaces={this.store.decimalPlaces}
                      disabled
                    />
                  </FormField>
                )}

                {this.store.renderFormField('cnpjFornecedorContratado') && (
                  <FormField
                    columns={4}
                    attribute="cnpjFornecedorContratado"
                    label={`CNPJ do(a) ${this.store.labelFornecedor()}`}
                    rule={getRule('cnpjFornecedorContratado')}
                    submitted={submitted}
                  >
                    <InputText value={this.store.object.contratoLicitante?.licitante?.cpfCnpj} disabled />
                  </FormField>
                )}

                {this.store.renderFormField('nomeFornecedorContratado') && (
                  <FormField
                    columns={4}
                    attribute="nomeFornecedorContratado"
                    label={`Nome do(a) ${this.store.labelFornecedor()}`}
                    rule={getRule('nomeFornecedorContratado')}
                    submitted={submitted}
                  >
                    <InputText value={this.store.object.contratoLicitante?.licitante?.nome} disabled />
                  </FormField>
                )}
                <FormField
                  columns={4}
                  attribute="autoridadeContratante"
                  label="Autoridade Contratante"
                  rule={getRule('idAutoridadeContratante')}
                  submitted={submitted}
                >
                  <Dropdown
                    id="autoridadeContratante"
                    optionLabel="nome"
                    optionValue="id"
                    value={this.store.object?.idAutoridadeContratante}
                    options={this.store.pessoasResponsaveis}
                    onChange={(e) => {
                      this.store.updateAutoridadeContratanteAttribute('idAutoridadeContratante', e.value);
                    }}
                    placeholder="Selecione a Autoridade Contratante"
                    emptyMessage="Não há responsáveis disponíveis"
                  />
                </FormField>
                {this.store.renderFormField('numeroContratoSei') && (
                  <FormField
                    columns={4}
                    attribute="numeroContratoSei"
                    label="Número do Processo Administrativo"
                    rule={getRule('numeroContratoSei')}
                    submitted={submitted}
                  >
                    <InputText
                      maxLength={50}
                      keyfilter={new RegExp('[0-9|[-]|[/]|[.]')}
                      onChange={(e) => this.store.updateAttribute('numeroContratoSei', e)}
                      value={this.store.object.numeroContratoSei}
                      id="numeroContratoSei"
                      placeholder="Informe o número do processo administrativo"
                    />
                  </FormField>
                )}

                {this.store.renderFormField('dataVigenciaInicial') && (
                  <FormField
                    columns={4}
                    attribute="dataVigenciaInicial"
                    label="Início da Vigência"
                    rule={getRule('dataVigenciaInicial')}
                    submitted={submitted}
                  >
                    <FcCalendar
                      value={this.getDateAttributeValue(this.store.object.dataVigenciaInicial)}
                      onChange={(e) => this.store.updateAttributeDateWithHours('dataVigenciaInicial', e)}
                      showIcon
                      mask="99/99/9999"
                    />
                  </FormField>
                )}

                {this.store.renderFormField('dataVigenciaFinal') && (
                  <FormField
                    columns={4}
                    attribute="dataVigenciaFinal"
                    label="Fim da Vigência"
                    rule={getRule('dataVigenciaFinal')}
                    submitted={submitted}
                  >
                    <FcCalendar
                      value={this.getDateAttributeValue(this.store.object.dataVigenciaFinal)}
                      onChange={(e) => this.store.updateAttributeDateWithHours('dataVigenciaFinal', e)}
                      showIcon
                      mask="99/99/9999"
                    />
                  </FormField>
                )}
                {this.store.renderFormField('fontesDeRecurso') && (
                  <FormField
                    columns={4}
                    attribute="fontesDeRecurso"
                    label="Fontes de Recurso"
                    rule={getRule('fontesDeRecurso')}
                    submitted={submitted}
                  >
                    <FcMultiSelect
                      placeholder="Selecione as fontes de recurso"
                      value={this.store.object.fontesDeRecurso}
                      onChange={(e) => this.store.updateAttribute('fontesDeRecurso', e)}
                      options={this.store.fontesRecursos}
                      showOverlay
                      optionLabel="nome"
                      filterBy="nome"
                      filter
                      selectedItemsLabel="{} itens selecionados"
                      showClear
                    />
                  </FormField>
                )}
                {this.store.renderFormField('objeto') && (
                  <FormField
                    columns={12}
                    attribute="objeto"
                    label={this.store.labelObjeto()}
                    rule={getRule('objeto')}
                    submitted={submitted}
                  >
                    <FcInputTextarea
                      onChange={(e) => this.store.updateAttribute('objeto', e)}
                      placeholder="Informe o objeto"
                      value={this.store.object.objeto}
                    />
                  </FormField>
                )}

                {this.store.renderFormField('tipoEmpenho') && (
                  <>
                    <FormField
                      columns={6}
                      attribute="tipoEmpenho"
                      label="Tipo do Empenho"
                      rule={getRule('tipoEmpenho')}
                      submitted={submitted}
                    >
                      <SelectButton
                        optionLabel="text"
                        optionValue="value"
                        value={this.store.object.tipoEmpenho}
                        options={DadosEstaticosService.getTiposEmpenho()}
                        onChange={(e) => {
                          this.store.updateAttribute('tipoEmpenho', e);
                          this.forceUpdate();
                        }}
                      />
                    </FormField>
                    <div id="blank-space" className="p-col-6" />
                  </>
                )}

                {this.store.renderFormField('garantiaContratual') && (
                  <FormField
                    columns={6}
                    attribute="garantiaContratual"
                    label="Tem Garantia Contratual?"
                    rule={getRule('garantiaContratual')}
                    submitted={submitted}
                  >
                    <div className="p-field-radiobutton p-dir-row">
                      <div className="p-field-radiobutton p-col">
                        <RadioButton
                          inputId="garantiaContratual"
                          name="garantiaContratual"
                          value={true}
                          onChange={(e) => {
                            this.store.updateAttribute('garantiaContratual', e.value);
                            this.store.carregarTiposArquivo();
                          }}
                          checked={this.store.object.garantiaContratual === true}
                        />
                        <label htmlFor="garantiaContratual">Sim</label>
                      </div>

                      <div className="p-field-radiobutton p-col">
                        <RadioButton
                          inputId="naoTemGarantiaContratual"
                          name="naoTemGarantiaContratual"
                          value={false}
                          onChange={(e) => {
                            this.store.updateAttribute('garantiaContratual', e.value);
                            this.store.updateAttribute('garantias', []);
                            this.store.carregarTiposArquivo();
                          }}
                          checked={this.store.object.garantiaContratual === false}
                        />
                        <label htmlFor="naoTemGarantiaContratual">Não</label>
                      </div>
                    </div>
                  </FormField>
                )}

                {this.store.renderFormField('garantiaContratual') && (
                  <FormField
                    columns={6}
                    attribute="garantias"
                    label="Qual?"
                    rule={getRule('garantias')}
                    submitted={submitted}
                  >
                    <FcMultiSelect
                      optionLabel="name"
                      value={garantias.filter((g) => this.store.object.garantias?.includes(g.code))}
                      options={garantias}
                      disabled={!this.store.object.garantiaContratual}
                      onChange={(e) => this.store.updateGarantias(e)}
                      showOverlay
                    />
                  </FormField>
                )}

                {this.store.renderFormField('gestor') && (
                  <FormField
                    columns={6}
                    attribute="gestor"
                    label="Gestor"
                    rule={getRule('gestor')}
                    submitted={submitted}
                  >
                    <SelectDialog
                      value={this.store.object.gestor}
                      label="nome"
                      indexStore={this.storeResponsavelContrato}
                      onChange={(e) => {
                        this.store.updateAttribute('gestor', e);
                        this.forceUpdate();
                      }}
                      headerDialog="Gestor"
                      emptyMessage="Selecione o gestor"
                      nullMessage="Gestor sem nome"
                      dialogColumns={columnsResponsavel}
                      searchFields={searchFieldsResponsavel}
                      canCreate
                      radioMode
                      formPage={(props) => {
                        return <ResponsavelContratoFormPage action="new" closeMethod={props.closeMethod} />;
                      }}
                    />
                  </FormField>
                )}

                {this.store.renderFormField('gestorSubstituto') && (
                  <FormField
                    columns={6}
                    attribute="gestorSubstituto"
                    rule={getRule('gestorSubstituto')}
                    label="Gestor Substituto"
                    submitted={submitted}
                  >
                    <SelectDialog
                      value={this.store.object.gestorSubstituto}
                      label="nome"
                      indexStore={this.storeResponsavelContrato}
                      onChange={(e) => {
                        this.store.updateAttribute('gestorSubstituto', e);
                        this.forceUpdate();
                      }}
                      headerDialog="Gestor Substituto"
                      emptyMessage="Selecione o gestor substituto"
                      nullMessage="Gestor sem nome"
                      dialogColumns={columnsResponsavel}
                      searchFields={searchFieldsResponsavel}
                      canCreate
                      radioMode
                      formPage={(props) => {
                        return <ResponsavelContratoFormPage action="new" closeMethod={props.closeMethod} />;
                      }}
                    />
                  </FormField>
                )}

                {this.store.renderFormField('fiscal') && (
                  <FormField
                    columns={6}
                    attribute="fiscal"
                    rule={getRule('fiscal')}
                    label="Fiscal"
                    submitted={submitted}
                  >
                    <SelectDialog
                      value={this.store.object.fiscal}
                      label="nome"
                      indexStore={this.storeResponsavelContrato}
                      onChange={(e) => {
                        this.store.updateAttribute('fiscal', e);
                        this.forceUpdate();
                      }}
                      headerDialog="Fiscal"
                      emptyMessage="Selecione o fiscal"
                      nullMessage="Fiscal sem nome"
                      dialogColumns={columnsResponsavel}
                      searchFields={searchFieldsResponsavel}
                      canCreate
                      radioMode
                      formPage={(props) => <ResponsavelContratoFormPage action="new" closeMethod={props.closeMethod} />}
                    />
                  </FormField>
                )}

                {this.store.renderFormField('fiscalSubstituto') && (
                  <FormField
                    columns={6}
                    attribute="fiscalSubstituto"
                    rule={getRule('fiscalSubstituto')}
                    label="Fiscal Substituto"
                    submitted={submitted}
                  >
                    <SelectDialog
                      value={this.store.object.fiscalSubstituto}
                      label="nome"
                      indexStore={this.storeResponsavelContrato}
                      onChange={(e) => {
                        this.store.updateAttribute('fiscalSubstituto', e);
                        this.forceUpdate();
                      }}
                      headerDialog="Fiscal Substituto"
                      emptyMessage="Selecione o fiscal substituto"
                      nullMessage="Fiscal sem nome"
                      dialogColumns={columnsResponsavel}
                      searchFields={searchFieldsResponsavel}
                      canCreate
                      radioMode
                      formPage={(props) => <ResponsavelContratoFormPage action="new" closeMethod={props.closeMethod} />}
                    />
                  </FormField>
                )}
              </div>
            </Fieldset>

            <Fieldset legend="Arquivos">
              <div className="p-fluid p-formgrid p-grid">
                <div className="p-col-12">
                  <MultipleFileUploader
                    store={this.store.fileStore}
                    onChangeFiles={(files) => this.store.setArquivoContratoList(files)}
                    filterTypes={{ excluded: ['RESCISAO'] }}
                    fileTypes={DadosEstaticosService.getTipoArquivoContrato()}
                  />
                </div>
              </div>
            </Fieldset>
          </form>
          {this.store.isConfirmDialogVisible && this.confirmRemove(this.state.idRemove)}
        </div>
      );
    } else {
      return <></>;
    }
  }
}

TabDadosBasicos.propTypes = {
  store: PropTypes.object,
  submitted: PropTypes.bool,
  action: PropTypes.string,
};

export default TabDadosBasicos;
