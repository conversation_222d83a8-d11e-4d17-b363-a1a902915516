import React from 'react';
import PropTypes from 'prop-types';
import { Column } from 'primereact/column';
import { RadioButton } from 'primereact/radiobutton';
import { Dropdown } from 'primereact/dropdown';
import { Fieldset } from 'primereact/fieldset';
import { DataTable } from 'primereact/datatable';
import { Dialog } from 'primereact/dialog';
import { InputNumber } from 'primereact/inputnumber';
import FcButton from 'fc/components/FcButton';
import {
  getValueByKey,
  getValue,
  getValueDate,
  getValueMoney,
  getMultipleValuesByKey,
  getLightenColor,
  getNumberUnitThousands,
  isValueValid,
  somaValoresLotes,
} from 'fc/utils/utils';
import { DATE_PARSE_FORMAT_WITH_HOURS, DATE_FORMAT, DATE_FORMAT_WITH_HOURS } from 'fc/utils/date';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import InputNumberFc from 'fc/components/InputNumber';
import { observer } from 'mobx-react';
import { Divider } from 'primereact/divider';
import FormMaterial from '~/pages/gerenciamentoTermo/material';
import InputMonetary from 'fc/components/InputMonetary';
import FormField from 'fc/components/FormField';
import { InputText } from 'primereact/inputtext';
import SelectDialog from 'fc/components/SelectDialog';
import { Message } from 'primereact/message';
import EntidadeExternaIndexStore from '~/stores/entidadeExterna/indexStore';
import FormEntidadeExterna from '../entidadeExterna/formEntidadeExterna';
import { PickList } from 'primereact/picklist';
import ItensEditable from '../itens/itensEditable';
import FcDropdown from 'fc/components/FcDropdown';
import FornecedoresModal from '~/pages/contrato/fornecedoresModal';
import { SelectButton } from 'primereact/selectbutton';
import { ConfirmDialog } from 'primereact/confirmdialog';
import { Tag } from 'primereact/tag';

@observer
class TabItens extends React.Component {
  store;

  constructor(props) {
    super(props);
    this.store = props.store;
    this.refItens = React.createRef();
    this.state = {
      expandedLotes: [],
      showDialogItens: false,
      showDialogNewItens: false,
      dialogVisibility: false,
      currentLesgilacao: undefined,
      currentFormaContrato: undefined,
    };
  }

  toggleDialogNewItens() {
    this.setState({ showDialogNewItens: !this.state.showDialogNewItens });
  }

  componentDidMount() {
    this.store.carregarFundamentacaoLegal(() => this.forceUpdate());
  }

  _renderDialog() {
    return (
      <ConfirmDialog
        visible={this.state.dialogVisibility}
        message={`Ao trocar de ${
          this.store.object.processoExterno ? 'Legislação ou Forma de Contrato' : 'Forma de Contrato'
        }, os arquivos selecionados serão removidos do sistema. Deseja continuar e aplicar a mudança, removendo os arquivos selecionados?`}
        header="Atualização"
        onHide={() => {
          this._toggleDialogVisibility();
        }}
        accept={() => {
          this.store.setArquivoContratoList([]);
          this.store.fileStore.removeAllFiles();
          if (this.state.currentLesgilacao) {
            this.store.updateAttribute('lei', this.state.currentLesgilacao);
            this.store.updateAttribute('legislacaoOutros', '');
            this.setState({ currentLesgilacao: undefined });
          } else if (this.state.currentFormaContrato) {
            this.store.updateAttribute('formaContrato', this.state.currentFormaContrato);
            this.setState({ currentFormaContrato: undefined });
          }
          this.store.carregarTiposArquivo();
        }}
      />
    );
  }

  _toggleDialogVisibility() {
    this.setState({ dialogVisibility: !this.state.dialogVisibility });
  }

  _renderValue(label, value, col = 12) {
    return (
      <div className={`p-col-${col}`}>
        <div className="p-col-12 drawer-content-label">{label}</div>
        <div className="p-col-12" style={{ textAlign: 'justify' }}>
          {value ?? '-'}
        </div>
      </div>
    );
  }

  itemHasUnidadesMedida(item) {
    return item.materialDetalhamento?.pdm?.unidadesMedida?.length > 0;
  }

  renderLicitantes() {
    const contrato = this.store.object;
    const labelLicitante = DadosEstaticosService.getTipoProcesso().find((p) => p.value === contrato.tipo)?.licitante;
    const processo = this.store.getProcesso();
    const valorMaximo = this.store.selectedLicitante && this.store.getValorMaximoByLicitante();

    return (
      <Fieldset legend={`${labelLicitante}`} toggleable>
        <div className="p-grid">
          <div className="p-col-12">
            <Dropdown
              disabled={this.props.readOnly}
              className="p-col-12"
              value={this.store.selectedLicitante}
              options={this.store.vencedores}
              onChange={(e) => {
                this.store.selectLicitante(e.value);
              }}
              optionLabel="licitante.nome"
              optionValue="licitante"
              placeholder={`Selecione o ${labelLicitante.toLowerCase()}`}
            />
          </div>
          {(processo?.lei == 'LEI_N_8666' || processo?.processoMigrado || processo?.licitacao?.lei == 'LEI_N_8666') &&
            this.store.selectedLicitante && (
              <div className="p-col-12 flex flex-column gap-2">
                <label htmlFor="valorFornecedor">{`Digite o valor do ${labelLicitante}`} </label>
                <InputMonetary
                  id="valorFornecedor"
                  onChange={(e) => e >= 0 && this.store.setValorLicitante(e)}
                  placeholder="R$"
                  mode="currency"
                  value={this.store.object.contratoLicitante.valor}
                  decimalPlaces={2}
                  max={valorMaximo ?? this.store.object?.processo?.valor}
                  disabled={this.props.readOnly}
                />
                {!this.props.readOnly && (
                  <span span className="text-sm" style={{ color: '#1d4ed8' }}>
                    Valor máximo: {getValueMoney(valorMaximo ?? this.store.object?.processo?.valor)}
                  </span>
                )}
              </div>
            )}
        </div>
      </Fieldset>
    );
  }

  addKeyList(list) {
    const vencedores = [];
    list.forEach((item, idx) => {
      if (!item.key) item.key = idx;
      item.label = `${item.licitante.nome} - ${item.licitante.cpfCnpj}`;
      if (!vencedores.find((v) => v.label === item.label)) {
        vencedores.push(item);
      }
    });
    return vencedores;
  }

  renderColumns(columns) {
    return columns.map((col) => <Column key={`col-${col.field}`} {...col} />);
  }

  renderInformacoesComplementares() {
    const contrato = this.store.object;
    const isLicitacao = contrato.tipo === 'L';
    return (
      isLicitacao && (
        <Fieldset legend="Informações Complementares" toggleable>
          <div className="p-grid">
            {this._renderValue(
              'Formas de Publicação',
              contrato.processo?.publicacoes?.map((publicacao) => publicacao.tpFormaPublicacao.nome).join(', '),
              6
            )}
            {this._renderValue('Número do Processo Administrativo', contrato.processo?.numeroProcessoAdm, 6)}
            {this._renderValue('Comissão', contrato.processo?.comissao?.tipo, 6)}
            {this._renderValue('Fundamentação Legal', contrato.processo?.regenciaLegal, 6)}
          </div>
        </Fieldset>
      )
    );
  }

  renderFormaContrato() {
    return (
      <Fieldset legend="Forma de Contrato" toggleable>
        <div className="p-grid">
          <div className=" p-col-12">
            {DadosEstaticosService.getFormaContrato().map((f, idx) => {
              return (
                <div className="field-radiobutton" key={`tipo-${idx}`}>
                  <RadioButton
                    disabled={this.props.readOnly}
                    inputId={`tipo-${idx}`}
                    name={f.text}
                    value={f.value}
                    onChange={(formaContrato) => {
                      if (this.store.arquivoContratoList?.length) {
                        this.setState({ currentFormaContrato: formaContrato.value });
                        this._toggleDialogVisibility();
                      } else {
                        this.store.updateAttribute('formaContrato', formaContrato);
                      }
                    }}
                    checked={this.store.object?.formaContrato === f.value}
                  />
                  <label htmlFor={`tipo-${idx}`}>{f.text}</label>
                </div>
              );
            })}
            {this.state.dialogVisibility && this._renderDialog()}
          </div>
        </div>
      </Fieldset>
    );
  }

  renderFooterNewItens() {
    const contrato = this.store.object;
    const disabledOk =
      !contrato.contratoLicitante?.itens.length > 0 ||
      contrato.contratoLicitante?.itens?.filter(
        (i) =>
          !(i.quantidade > 0 && i.valor > 0 && (this.itemHasUnidadesMedida(i) ? isValueValid(i.unidadeMedida) : true))
      ).length > 0;
    return (
      <div className="p-mt-10 form-actions-dialog">
        <Divider />
        <span className="p-d-flex">
          <FcButton
            label="Cancelar"
            type="button"
            className="p-ml-auto p-button-secondary p-mr-2"
            onClick={() => this.toggleDialogNewItens()}
          />
          <FcButton label="Ok" disabled={disabledOk} onClick={() => this.toggleDialogNewItens()} />
        </span>
      </div>
    );
  }

  moveToSelectedItens() {
    this.refItens.current.scrollIntoView({ block: 'start', behavior: 'smooth' });
  }

  renderCamposFundamentacao() {
    const { submitted } = this.props;
    const { updateAttribute, getRule } = this.store;

    const fundDropdown = (
      <FormField
        columns={4}
        attribute="fundamentacaoLegalEntidade"
        label="Fundamentação Legal"
        rule={getRule('fundamentacaoLegalEntidade')}
        submitted={submitted}
      >
        <Dropdown
          onChange={(e) => updateAttribute('fundamentacaoLegalEntidade', e.value)}
          value={this.store.object.fundamentacaoLegalEntidade}
          placeholder="Selecione a fundamentação legal"
          options={this.store.fundamentacoesLegais}
          optionLabel="fundamentacao"
        />
      </FormField>
    );
    let fundInputText;

    if (this.store.object.id && this.store.object.fundamentacao) {
      fundInputText = (
        <FormField columns={4} attribute="fundamentacao" label="Fundamentação Legal" submitted={submitted}>
          <InputText value={this.store.object.fundamentacao} disabled />
        </FormField>
      );
    }

    return (
      <>
        {fundDropdown}
        {fundInputText}
      </>
    );
  }

  renderItens() {
    const contrato = this.store.object;
    const processo = this.store.getProcesso();
    const { sourceFilter, targetFilter } = this.state;
    const selectedItens = this.store.object.contratoLicitante.itens;
    const itens = this.store.getItensDisponiveis()?.filter((itemDisponivel) => {
      return !selectedItens.some((item) => item[this.store.getVencedorField()]?.id === itemDisponivel.id);
    });

    const sourceItemTemplate = (item) => {
      const quantidadeDisponivel = getNumberUnitThousands(this.store.getQuantidadeDisponivel(item));

      return (
        <div className="flex flex-wrap p-2 align-items-center gap-3">
          <div className="flex-1 flex flex-column gap-2">
            <span className="font-bold">
              {item?.itemLote?.numero ? item?.itemLote?.numero + ' - ' : ''}
              {getValue(item?.itemLote?.materialDetalhamento?.pdm?.nome)}
            </span>
            {this.store.object.tipo !== 'CR' && (
              <div className="flex gap-2">
                <Tag
                  value={`Quantidade Disponível: ${quantidadeDisponivel}`}
                  className="p-d-flex"
                  style={{
                    backgroundColor: getLightenColor('#2F83DC', 0.7),
                    color: '#2F83DC',
                    border: `1px solid #2F83DC`,
                  }}
                />
              </div>
            )}
          </div>
        </div>
      );
    };

    const targetItemTemplate = (item) => {
      const quantidadeDisponivel =
        this.store.object.tipo !== 'CR' ? getNumberUnitThousands(this.store.getQuantidadeDisponivel(item)) : 0;
      const quantidadeItemAssociado = this.store.getQuantidadeItemAssociado(item);

      return (
        <div className="flex flex-wrap p-2 align-items-center gap-3">
          <div className="flex-1 flex flex-column gap-2">
            <span className="font-bold">
              {item?.itemLote?.numero ? item?.itemLote?.numero + ' - ' : ''}
              {getValue(item?.itemLote?.materialDetalhamento?.pdm?.nome)}
            </span>
            {this.store.object.tipo !== 'CR' && (
              <div className="flex gap-2">
                <Tag
                  value={`Quantidade: ${quantidadeItemAssociado}/${quantidadeDisponivel}`}
                  className="p-d-flex"
                  style={{
                    backgroundColor: getLightenColor('#2F83DC', 0.7),
                    color: '#2F83DC',
                    border: `1px solid #2F83DC`,
                  }}
                />
              </div>
            )}
          </div>
        </div>
      );
    };

    const renderItensVencedores =
      processo?.termoReferencia || (this.store.object.tipo === 'C' && this.store.object.lei !== 'LEI_N_8666');
    if (this.store.object.processoExterno || renderItensVencedores) {
      return (
        <Fieldset legend="Itens do Contrato" toggleable>
          <div className="p-fluid p-formgrid p-grid">
            {renderItensVencedores && !this.props.readOnly && (
              <>
                <span className="p-field p-col-6" style={{ paddingRight: '30px' }}>
                  <span className="p-input-icon-left">
                    <i className="pi pi-search" />
                    <InputText
                      value={sourceFilter}
                      onChange={(e) => this.setState({ sourceFilter: e.target.value?.toLocaleLowerCase() })}
                    />
                  </span>
                </span>
                <span className="p-field p-col-6" style={{ paddingLeft: '30px' }}>
                  <span className="p-input-icon-left">
                    <i className="pi pi-search" />
                    <InputText
                      value={targetFilter}
                      onChange={(e) => this.setState({ targetFilter: e.target.value?.toLocaleLowerCase() })}
                    />
                  </span>
                </span>
                <div className="p-col-12">
                  <PickList
                    source={itens}
                    target={this.store.object?.contratoLicitante?.itens?.map(
                      (item) => item[this.store.getVencedorField()]
                    )}
                    onChange={(e) => this.store.handleSelectItens(e.source, e.target)}
                    sourceItemTemplate={sourceItemTemplate}
                    targetItemTemplate={targetItemTemplate}
                    sourceHeader="Itens Disponíveis"
                    targetHeader="Itens Adicionados"
                    showSourceControls={false}
                    showTargetControls={false}
                    sourceStyle={{ height: '342px' }}
                    targetStyle={{ height: '342px' }}
                  />
                  {!this.store.getItensDisponiveis()?.length > 0 && contrato.contratoLicitante?.itens?.length === 0 && (
                    <Message
                      severity="info"
                      text="O vencedor selecionado não possui itens que possam ser associados ao contrato."
                      style={{ width: '100%' }}
                    />
                  )}
                </div>
              </>
            )}
            {this.store.object.processoExterno && (
              <div className="p-col-3 p-mt-5 ">
                <FcButton
                  type="button"
                  label="Criar Item"
                  icon="pi pi-plus"
                  disabled={this.props.readOnly || !contrato.contratoLicitante?.licitante}
                  onClick={() => this.toggleDialogNewItens()}
                />
              </div>
            )}
            <div className="p-col-12">
              <ItensEditable
                itens={contrato.contratoLicitante?.itens}
                setQuantidade={(item, e) => this.store.setQuantidade(item, e)}
                setDescricaoComplementar={(item, e) => {
                  this.store.setDescricaoComplementar(item, e);
                  this.forceUpdate();
                }}
                setUnidadeMedida={(item, e) => {
                  this.store.setUnidadeMedida(item, e);
                  this.forceUpdate();
                }}
                setValor={(item, e) => this.store.setValor(item, e)}
                setPreenchido={(item, preenchido) => this.store.setPreenchido(item, preenchido)}
                showDesconto={contrato.licitacao?.tiposLicitacao
                  ?.map((t) => t.nome?.toUpperCase())
                  ?.includes('MAIOR DESCONTO')}
                showEspecificacao={contrato.licitacao?.naturezasDoObjeto?.includes('COMPRAS')}
                decimalPlaces={this.store.decimalPlaces}
                removeItem={(item) => this.store.removeItem(item[this.store.getVencedorField()] ?? item)}
                valor={this.store.object?.valorGlobal}
                vencedorField={this.store.getVencedorField()}
                store={this.store}
                readOnly={this.props.readOnly}
                itemHasUnidadesMedida={this.itemHasUnidadesMedida}
              />
            </div>
          </div>
        </Fieldset>
      );
    }
  }

  renderDialogFornecedor() {
    const fornecedoresForm = (props) => {
      return <FornecedoresModal action="new" closeMethod={props.closeMethod} />;
    };

    const columnsFornecedores = [
      {
        field: 'nome',
        header: 'Nome',
        sortable: true,
      },
      {
        field: 'endereco',
        header: 'Endereço',
        sortable: true,
      },
      {
        field: 'cpfCnpj',
        header: 'CPF/CNPJ',
        sortable: true,
      },
    ];

    return (
      <Fieldset legend={'Selecione o Fornecedor'} toggleable>
        <div className="p-fluid p-formgrid p-grid">
          <FormField columns={12} attribute="fornecedores" label="Fornecedor Contratado">
            <SelectDialog
              value={this.store.object.contratoLicitante?.licitante}
              label="nome"
              indexStore={this.store.fornecedoresIndexStore}
              headerDialog="Fornecedores"
              emptyMessage="Selecione o Fornecedor"
              nullMessage="Fornecedor sem Nome"
              dialogColumns={columnsFornecedores}
              searchFields={['nome', 'cpfCnpj']}
              disabledComponent={this.props.readOnly}
              onChange={(e) => {
                this.store.selectLicitante(e.value);
              }}
              columnsSelectMultiple={columnsFornecedores}
              canCreate
              labelButtonCreate="Adicionar"
              formPage={fornecedoresForm}
            />
          </FormField>
        </div>
      </Fieldset>
    );
  }

  renderFormExternalProcess() {
    const { updateAttribute, getRule } = this.store;
    const submitted = this.props.submitted;

    const columnsEntidadeExterna = [
      {
        field: 'nomeEntidadeExterna',
        header: 'Nome',
        sortable: true,
      },
      {
        field: 'cnpj',
        header: 'CNPJ',
        sortable: true,
      },
      {
        style: { width: '100px' },
        field: 'uf',
        header: 'UF',
        body: ({ uf }) => getValueByKey(uf, DadosEstaticosService.getTipoUF()),
      },
      {
        style: { width: '25%' },
        field: 'municipio.nomeMun',
        header: 'Cidade',
        sortable: true,
      },
    ];

    return (
      <>
        <Fieldset legend={'Dados do Processo'} toggleable>
          <div className="p-fluid p-formgrid p-grid">
            <FormField
              columns={4}
              attribute="tipo"
              rule={getRule('tipo')}
              label={'Tipo do Processo'}
              submitted={submitted}
            >
              <FcDropdown
                inOrder
                placeholder="Selecione o tipo do processo"
                id="tipo"
                value={this.store.object.tipo}
                optionLabel="text"
                optionValue="value"
                options={DadosEstaticosService.getTipoProcesso().filter(this.store.filtraTipoProcesso)}
                onChange={(e) => {
                  updateAttribute('tipo', e);
                  this.store.carregarFundamentacaoLegal(() => this.forceUpdate());
                  this.store.resetFieldsExternalProcess();
                }}
                disabled={this.props.readOnly}
              />
            </FormField>
            {this.store.object.tipo && (
              <>
                {!this.store.object?.processoMigrado && (
                  <FormField
                    columns={4}
                    attribute="lei"
                    label={`A ${getValueByKey(
                      this.store.object.tipo,
                      DadosEstaticosService.getTipoProcesso(),
                      'value'
                    )} será regida por qual legislação?`}
                    rule={getRule('lei')}
                    submitted={submitted}
                  >
                    <SelectButton
                      disabled={this.props.action === 'edit'}
                      optionLabel="text"
                      optionValue="value"
                      value={this.store.object.lei}
                      options={DadosEstaticosService.getTipoLicitacaoLei14133()}
                      onChange={(e) => {
                        if (e.value !== null) {
                          if (this.store.arquivoContratoList?.length) {
                            this.setState({ currentLesgilacao: e.value });
                            this._toggleDialogVisibility();
                          } else {
                            this.store.updateAttribute('lei', e);
                            this.store.carregarTiposArquivo();
                            this.store.carregarFundamentacaoLegal(() => this.forceUpdate());
                            this.store.updateAttribute('legislacaoOutros', '');
                          }
                        }
                      }}
                    />
                  </FormField>
                )}
                {this.store.object?.lei === 'OUTRA' && (
                  <FormField
                    rule={getRule('legislacaoOutros')}
                    columns={4}
                    attribute="legislacaoOutros"
                    label="Outra Lei"
                    infoTooltip="Informe sob qual lei o processo está sendo criado"
                    submitted={submitted}
                  >
                    <InputText
                      disabled={this.props.action === 'edit'}
                      value={this.store.object.legislacaoOutros}
                      placeholder="Informe a legislação"
                      rows={4}
                      onChange={(e) => this.store.updateAttribute('legislacaoOutros', e)}
                    />
                  </FormField>
                )}
                {this.store.object.tipo === 'L' && (
                  <FormField
                    columns={4}
                    attribute="numeroLicitacao"
                    label="Número da Licitação"
                    rule={getRule('numeroLicitacao')}
                    submitted={submitted}
                  >
                    <InputNumberFc
                      keyfilter={new RegExp('^[0-9]+$')}
                      onChange={(e) => updateAttribute('numeroLicitacao', e)}
                      placeholder="Informe o número"
                      value={this.store.object.numeroLicitacao}
                      id="numeroLicitacao"
                      leadingZeros
                      disabled={this.props.readOnly}
                    />
                  </FormField>
                )}
                {this.store.object.tipo === 'D' && (
                  <FormField
                    columns={4}
                    attribute="numeroDispensa"
                    label="Número da Dispensa"
                    rule={getRule('numeroDispensa')}
                    submitted={submitted}
                  >
                    <InputNumberFc
                      keyfilter={new RegExp('^[0-9]+$')}
                      onChange={(e) => updateAttribute('numeroDispensa', e)}
                      placeholder="Informe o número"
                      value={this.store.object.numeroDispensa}
                      id="numeroDispensa"
                      leadingZeros
                      disabled={this.props.readOnly}
                    />
                  </FormField>
                )}
                {this.store.object.tipo === 'I' && (
                  <FormField
                    columns={4}
                    attribute="numeroInexigibilidade"
                    label="Número da Inexigibilidade"
                    rule={getRule('numeroInexigibilidade')}
                    submitted={submitted}
                  >
                    <InputNumberFc
                      keyfilter={new RegExp('^[0-9]+$')}
                      onChange={(e) => updateAttribute('numeroInexigibilidade', e)}
                      placeholder="Informe o número"
                      value={this.store.object.numeroInexigibilidade}
                      id="numeroInexigibilidade"
                      leadingZeros
                      disabled={this.props.readOnly}
                    />
                  </FormField>
                )}

                <FormField
                  columns={4}
                  attribute="numeroProcessoAdmSei"
                  label={'Número do Processo Administrativo'}
                  rule={getRule('numeroProcessoAdmSei')}
                  submitted={submitted}
                >
                  <InputNumberFc
                    onChange={(e) => updateAttribute('numeroProcessoAdmSei', e)}
                    placeholder="Informe o número do processo"
                    value={this.store.object.numeroProcessoAdmSei}
                    id="numeroProcessoAdmSei"
                    leadingZeros
                    disabled={this.props.readOnly}
                  />
                </FormField>
                <FormField
                  columns={4}
                  attribute="anoProcesso"
                  label="Ano"
                  rule={getRule('anoProcesso')}
                  submitted={submitted}
                >
                  <Dropdown
                    onChange={(e) => this.store.updateAttribute('anoProcesso', e)}
                    placeholder="Informe o ano"
                    value={this.store.object?.anoProcesso}
                    id="anoProcesso"
                    optionLabel="text"
                    optionValue="value"
                    options={this.store.anos}
                  />
                </FormField>
                <FormField
                  columns={4}
                  attribute={'entidadeExterna'}
                  label="Entidade"
                  rule={getRule('entidadeExterna')}
                  submitted={submitted}
                >
                  <SelectDialog
                    value={this.store.object.entidadeExterna}
                    label="nomeEntidadeExterna"
                    indexStore={new EntidadeExternaIndexStore()}
                    onChange={(e) => {
                      this.store.updateAttribute('entidadeExterna', e);
                      this.forceUpdate();
                    }}
                    headerDialog="Entidade"
                    emptyMessage="Selecione a Entidade"
                    dialogColumns={columnsEntidadeExterna}
                    searchFields={['nomeEntidadeExterna', 'cnpj', 'municipio']}
                    canCreate
                    formPage={(props) => {
                      return <FormEntidadeExterna action="new" closeMethod={props.closeMethod} />;
                    }}
                  />
                </FormField>
                <FormField
                  columns={4}
                  attribute="valorProcessoEntidadeExterna"
                  label={'Valor'}
                  rule={getRule('valorProcessoEntidadeExterna')}
                  submitted={submitted}
                >
                  <InputMonetary
                    onChange={(e) => this.store.updateAttribute('valorProcessoEntidadeExterna', e)}
                    placeholder="R$"
                    mode="currency"
                    value={this.store.object.valorProcessoEntidadeExterna}
                    decimalPlaces={2}
                    disabled={this.props.readOnly}
                  />
                </FormField>

                {['D', 'I'].includes(this.store.object.tipo) && this.renderCamposFundamentacao()}

                <FormField
                  columns={4}
                  attribute="siteDivugacaoProcesso"
                  label={'Site do Portal de Divulgação do Processo '}
                  rule={getRule('siteDivugacaoProcesso')}
                  submitted={submitted}
                >
                  <InputText
                    onChange={(e) => updateAttribute('siteDivugacaoProcesso', e)}
                    value={this.store.object.siteDivugacaoProcesso}
                    id="siteDivugacaoProcesso"
                    disabled={this.props.readOnly}
                  />
                </FormField>
              </>
            )}
          </div>
        </Fieldset>
      </>
    );
  }
  renderDetalhesProcesso() {
    const contrato = this.store.object;
    const isLicitacao = contrato.tipo === 'L';
    const isDispensa = contrato.tipo === 'D';
    const isCarona = contrato.tipo === 'C';
    const isInexigibilidade = contrato.tipo === 'I';
    const isCredenciamento = contrato.tipo === 'CR';
    const legenda = `Detalhes ${isCredenciamento ? 'do' : 'da'} ${getValueByKey(
      contrato.tipo,
      DadosEstaticosService.getTipoProcesso()
    )}`;
    const processo = contrato?.processo;
    return (
      <>
        {processo && (
          <Fieldset legend={legenda} toggleable>
            <div>
              <div className="p-grid">
                {isLicitacao && this.renderLicitacao(processo)}
                {isDispensa && this.renderDispensa(processo)}
                {isCarona && this.renderCarona(processo)}
                {isInexigibilidade && this.renderInexigibilidade(processo)}
                {isCredenciamento && this.renderCredenciamento(processo)}
              </div>
            </div>
          </Fieldset>
        )}
      </>
    );
  }

  renderCredenciamento(credenciamento) {
    const contrato = this.store.object;

    return (
      <>
        {this._renderValue(
          'Data de Cadastro',
          getValueDate(credenciamento.dataCadastro, DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS),
          4
        )}
        {this._renderValue(
          'Tipo do Processo',
          getValueByKey(contrato.tipo, DadosEstaticosService.getTipoProcesso()),
          4
        )}
        {this._renderValue('Número do Processo', credenciamento.numeroProcesso, 4)}

        {this._renderValue('Responsável pelo Credenciamento', credenciamento.responsavel?.nome, 4)}
        {this._renderValue(
          'Tipo de Contratação',
          getValueByKey(credenciamento.tipoContratacao, DadosEstaticosService.getTipoContratacao()),
          4
        )}
        {this._renderValue('Termo de Referência', credenciamento.termoReferencia?.identificadorProcesso, 4)}
        {this._renderValue(
          'Naturezas do Objeto',
          getValue(
            getMultipleValuesByKey(credenciamento.naturezasDoObjeto, DadosEstaticosService.getNaturezaObjetoLicitacao())
          ),
          4
        )}
        {this._renderValue('Sítio Divulgação', getValue(credenciamento.sitioDivulgacao), 4)}

        {this._renderValue(
          'Valor Total Estimado',
          getValueMoney(
            somaValoresLotes(
              credenciamento?.termoReferencia.lotes,
              credenciamento?.termoReferencia?.tresCasasDecimais ? 3 : 2
            )
          ),
          4
        )}
        {this._renderValue('Objeto', credenciamento.objeto, 8)}
      </>
    );
  }

  renderLicitacao(licitacao) {
    return (
      <>
        {this._renderValue(
          'Data de Cadastro',
          getValueDate(licitacao.dataCadastro, DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS),
          4
        )}
        {this._renderValue('Número da Licitação', licitacao.numero, 4)}
        {licitacao.lei === 'LEI_N_14133'
          ? this._renderValue(
              'Critérios de Julgamento',
              licitacao.tiposLicitacao?.map((tipo) => tipo?.nome).join(', '),
              4
            )
          : this._renderValue('Critérios de Julgamento', licitacao.tipo?.nome, 4)}
        {this._renderValue(
          'Sistema de Registro de Preços (SRP)',
          licitacao.srp
            ? getValueByKey(licitacao.srp, DadosEstaticosService.getSimNao())
            : getValueByKey(licitacao.termoReferencia?.srp, DadosEstaticosService.getSimNao()),
          4
        )}

        {this._renderValue('Pregoeiro', licitacao.pregoeiro?.nome, 4)}
        {this._renderValue(
          'Naturezas do Objeto',
          getValue(
            getMultipleValuesByKey(licitacao.naturezasDoObjeto, DadosEstaticosService.getNaturezaObjetoLicitacao())
          ),
          4
        )}
        {this._renderValue(
          'Fontes de Recurso',
          licitacao.fontesDeRecurso?.map((fonte) => fonte.descricao).join(', '),
          4
        )}
        {this._renderValue('Termo de Referência', licitacao.termoReferencia?.identificadorProcesso, 4)}
        {this._renderValue(
          'Valor Total Estimado',
          getValueMoney(licitacao.valorEstimado, licitacao.termoReferencia?.tresCasasDecimais ? 3 : 2),
          4
        )}
        {this._renderValue('Objeto', licitacao.objeto, 12)}
      </>
    );
  }

  renderDispensa(dispensa) {
    const contrato = this.store.object;
    return (
      <>
        {this._renderValue('Número da Dispensa', dispensa.numeroProcesso, 4)}
        {this._renderValue(
          dispensa.lei == 'LEI_N_14133' ? 'Data da Autorização' : 'Data de Ratificação',
          getValueDate(dispensa.dataPedido, DATE_FORMAT, DATE_PARSE_FORMAT_WITH_HOURS),
          4
        )}
        {this._renderValue(
          'Data de Cadastro',
          getValueDate(dispensa.dataCadastro, DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS),
          4
        )}
        {this._renderValue(
          'Tipo do Processo',
          getValueByKey(contrato.tipo, DadosEstaticosService.getTipoProcesso()),
          4
        )}
        {this._renderValue(
          'Responsável pela Dispensa',
          dispensa.processoMigrado ? dispensa.gestor : dispensa.responsavelDispensa?.nome,
          4
        )}
        {this._renderValue('Termo de Referência', dispensa.termoReferencia?.identificadorProcesso, 4)}
        {this._renderValue(
          'Naturezas do Objeto',
          getValue(
            getMultipleValuesByKey(dispensa.naturezasDoObjeto, DadosEstaticosService.getNaturezaObjetoLicitacao())
          ),
          4
        )}
        {this._renderValue(
          'Valor da dispensa',
          getValueMoney(dispensa.valor, dispensa.termoReferencia?.tresCasasDecimais ? 3 : 2),
          4
        )}
        {this._renderValue('Fundamentação Legal', dispensa.fundamentacao, 4)}
        {this._renderValue('Justificativa', dispensa.justificativa, 6)}
        {this._renderValue('Objeto', dispensa.objeto, 8)}
        {this._renderValue('Observações', getValue(dispensa.observacoes), 6)}
      </>
    );
  }

  renderCarona(carona) {
    const contrato = this.store.object;
    return (
      <>
        {this._renderValue('Número', carona.numeroProcessoGerenciadorAta, 4)}
        {this._renderValue(
          'Data de Adesão',
          getValueDate(carona.dataAdesao, DATE_FORMAT, DATE_PARSE_FORMAT_WITH_HOURS),
          4
        )}
        {this._renderValue(
          'Data de Cadastro',
          getValueDate(carona.dataCadastro, DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS),
          4
        )}
        {this._renderValue(
          'Tipo do Processo',
          getValueByKey(contrato.tipo, DadosEstaticosService.getTipoProcesso()),
          4
        )}
        {this._renderValue(
          'Responsável pela Adesão',
          carona.processoMigrado ? carona.responsavelAdesao : carona.responsavelAdesaoCarona?.nome,
          4
        )}
        {this._renderValue('Gerenciador da Ata', carona.gerenciadorAta, 4)}
        {this._renderValue('Termo de Referência', carona.termoReferencia?.identificadorProcesso, 4)}
        {this._renderValue(
          'Natureza do Objeto',
          getValueByKey(carona.naturezasDoObjeto, DadosEstaticosService.getNaturezaObjetoLicitacao()),
          4
        )}
        {this._renderValue(
          'Data de Validade da Ata',
          getValueDate(carona.dataValidadeAta, DATE_FORMAT, DATE_PARSE_FORMAT_WITH_HOURS),
          4
        )}
        {this._renderValue('Observações', carona.observacoes, 4)}
        {this._renderValue('Justificativa', getValue(carona.justificativa), 4)}
        {this._renderValue(
          'Valor da adesão/carona',
          getValueMoney(carona.valor, carona.termoReferencia?.tresCasasDecimais ? 3 : 2),
          4
        )}
        {this._renderValue('Objeto', carona.objeto, 12)}
      </>
    );
  }

  renderInexigibilidade(inexigibilidade) {
    const contrato = this.store.object;
    return (
      <>
        {this._renderValue('Número da Inexigibilidade', inexigibilidade.numeroProcesso, 4)}
        {this._renderValue(
          inexigibilidade.lei == 'LEI_N_14133' ? 'Data da Autorização' : 'Data de Ratificação',
          getValueDate(inexigibilidade.dataPedido, DATE_FORMAT, DATE_PARSE_FORMAT_WITH_HOURS),
          4
        )}
        {this._renderValue(
          'Data de Cadastro',
          getValueDate(inexigibilidade.dataCadastro, DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS),
          4
        )}
        {this._renderValue(
          'Tipo do Processo',
          getValueByKey(contrato.tipo, DadosEstaticosService.getTipoProcesso()),
          4
        )}
        {this._renderValue(
          'Responsável pela Inexigibilidade',
          inexigibilidade.processoMigrado
            ? inexigibilidade.responsavelRatificacao
            : inexigibilidade.responsavelInexigibilidade?.nome,
          4
        )}
        {this._renderValue('Termo de Referência', inexigibilidade.termoReferencia?.identificadorProcesso, 4)}
        {this._renderValue(
          'Naturezas do Objeto',
          getValue(
            getMultipleValuesByKey(
              inexigibilidade.naturezasDoObjeto,
              DadosEstaticosService.getNaturezaObjetoLicitacao()
            )
          ),
          4
        )}
        {this._renderValue('Fundamentação Legal', inexigibilidade.fundamentacao, 4)}
        {this._renderValue(
          'Valor da Inexigibilidade',
          getValueMoney(inexigibilidade.valor, inexigibilidade.termoReferencia?.tresCasasDecimais ? 3 : 2),
          4
        )}
        {this._renderValue('Justificativa', inexigibilidade.justificativa, 6)}
        {this._renderValue('Objeto', inexigibilidade.objeto, 8)}
        {this._renderValue('Observações', getValue(inexigibilidade.observacoes), 6)}
      </>
    );
  }

  renderDialogNewItens() {
    const contrato = this.store.object;
    const columnsAdicionados = [
      {
        header: 'Código',
        body: (item) => getValue(item.materialDetalhamento?.codigo),
      },
      {
        header: 'Descrição',
        style: { width: '30%' },
        body: ({ materialDetalhamento }) => {
          return materialDetalhamento?.pdm?.nome;
        },
      },
      {
        header: 'Descrição Complementar',
        field: 'descricaoComplementar',
        style: { width: '20%' },
        body: (item) => (
          <InputText
            value={item.descricaoComplementar}
            onChange={(e) => this.store.setDescricaoComplementar(item, e.target.value, () => this.forceUpdate())}
          />
        ),
      },
      {
        header: (
          <span>
            Unidade de Medida <span style={{ color: 'red' }}>*</span>
          </span>
        ),
        field: 'unidadeMedida',
        style: { width: '20%' },
        body: (item) =>
          item.materialDetalhamento?.pdm?.tipoMaterial == 'M' ? (
            this.itemHasUnidadesMedida(item) ? (
              <FcDropdown
                options={item.materialDetalhamento?.pdm?.unidadesMedida}
                optionLabel="textUnidadeMedida"
                optionValue="id"
                value={item.unidadeMedida?.id}
                onChange={(e) =>
                  this.store.setUnidadeMedida(
                    item,
                    item.materialDetalhamento?.pdm?.unidadesMedida?.find((unidade) => unidade.id === e.target.value),
                    () => this.forceUpdate()
                  )
                }
                placeholder="Selecione a unidade de medida utilizada"
              />
            ) : (
              <b>-</b>
            )
          ) : this.itemHasUnidadesMedida(item) ? (
            <span>
              {this.store.setUnidadeMedida(
                item,
                item.materialDetalhamento?.pdm?.unidadesMedida?.find((unidade) => unidade.descricao === 'UNIDADE')
              )}
              Unidade
            </span>
          ) : (
            '-'
          ),
      },
      {
        header: 'Quantidade',
        field: 'quantidade',
        style: { width: '10%' },
        body: (item) => (
          <InputNumber value={item.quantidade} onChange={(e) => this.store.setQuantidade(item, e.value)} />
        ),
      },
      {
        header: 'Valor Unitário',
        field: 'valorUnitario',
        style: { width: '10%' },
        body: (item) => (
          <InputMonetary
            placeholder="R$"
            value={item.valor}
            onChange={(value) => this.store.setValor(item, value, true)}
            decimalPlaces={2}
          />
        ),
      },
      {
        header: 'Valor',
        field: 'valor',
        style: { width: '10%' },
        body: (item) => getValueMoney(item.valor * item.quantidade),
      },
      {
        style: { width: '10%', textAlign: 'center' },
        body: (item) => (
          <FcButton
            type="button"
            icon="pi pi-trash"
            tooltip="Remover"
            className="p-button-sm p-button-danger"
            onClick={() => this.store.removeItem(item)}
          />
        ),
      },
    ];
    return (
      <Dialog
        header="Criar Itens"
        visible={this.state.showDialogNewItens}
        onHide={() => this.toggleDialogNewItens()}
        footer={this.renderFooterNewItens()}
        style={{ width: '80vw' }}
        draggable={false}
      >
        <div className="p-fluid p-formgrid p-grid">
          {contrato.contratoLicitante?.licitante && (
            <div className="p-col-12">
              <div className="p-col-12">
                <FormMaterial
                  onSetMaterial={(material) => {
                    this.store.criarItem(material);
                  }}
                  moveToSelectedItens={() => this.moveToSelectedItens()}
                />
              </div>
              <div ref={this.refItens}>
                <Fieldset legend="Itens Adicionados">
                  <DataTable
                    rowHover
                    value={contrato.contratoLicitante?.itens}
                    responsiveLayout="scroll"
                    emptyMessage="Nenhum item adicionado ao contrato."
                    footer={`Valor Total: ${getValueMoney(contrato.valorGlobal)}`}
                    editMode="cell"
                  >
                    {this.renderColumns(columnsAdicionados)}
                  </DataTable>
                </Fieldset>
              </div>
            </div>
          )}
        </div>
      </Dialog>
    );
  }

  render() {
    const isExternalProcess = !!this.store.object.processoExterno;
    if (!this.store.loadingTabItens && !this.store.loadingFundamentacoes) {
      return (
        <>
          {!isExternalProcess && this.renderDetalhesProcesso()}
          {!isExternalProcess && this.renderInformacoesComplementares()}
          {isExternalProcess && this.renderFormExternalProcess()}
          {this.renderFormaContrato()}
          {!isExternalProcess && this.renderLicitantes()}
          {isExternalProcess && this.renderDialogFornecedor()}
          {this.renderItens()}
          {this.renderDialogNewItens()}
        </>
      );
    } else {
      return <></>;
    }
  }
}

TabItens.propTypes = {
  store: PropTypes.object,
  readOnly: PropTypes.bool,
  submitted: PropTypes.bool,
  action: PropTypes.any,
};

export default TabItens;
