import React from 'react';
import { observer } from 'mobx-react';
import IndexDataTable from 'fc/components/IndexDataTable';
import FcButton from 'fc/components/FcButton';
import GenericIndexPage from 'fc/pages/GenericIndexPage';
import AdvancedSearch from 'fc/components/AdvancedSearch';
import { DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS } from 'fc/utils/date';
import {
  getValueByKey,
  getValueDate,
  getValue,
  getValueMoney,
  getValueElipsis,
  somaValoresLotes,
  generateFullURL,
} from 'fc/utils/utils';
import PermissionProxy from 'fc/components/PermissionProxy';
import AppStore from 'fc/stores/AppStore';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import AccessPermission from '~/constants/AccessPermission';
import ProcessoViewIndexStore from '~/stores/contrato/selecionarProcesso/indexStore';
import ContratoFormStore from '~/stores/contrato/formStore';
import UrlRouter from '~/constants/UrlRouter';

@observer
class TabProcesso extends GenericIndexPage {
  constructor(props) {
    super(props, AccessPermission.contrato);
    this.store = new ProcessoViewIndexStore();
    this.formStore = new ContratoFormStore();
  }

  componentDidMount() {
    if (!AppStore.getContextEntity()) this.store.load();
  }

  _checkProcessoIsBlocked(rowData) {
    if (rowData.tipoProcesso !== 'L') return ['', false];
    if (rowData.licitacao.fase !== 'FINALIZACAO') return ['Não está na fase de finalização', true];
    if (rowData.licitacao.naturezaOcorrencia == 'SUSPENDER') return ['Processo suspenso', true];
    if (
      rowData.licitacao.naturezaOcorrencia == 'FINALIZAR' &&
      (rowData.licitacao.statusOcorrenciaAtual === 'FRACASSADA' ||
        rowData.licitacao.statusOcorrenciaAtual === 'REVOGADA' ||
        rowData.licitacao.statusOcorrenciaAtual === 'DESERTA' ||
        rowData.licitacao.statusOcorrenciaAtual === 'ANULADA')
    )
      return ['Processo finalizado', true];
    return ['', false];
  }

  _checkProcessoIsReqMod(rowData) {
    switch (rowData.tipoProcesso) {
      case 'L':
        return rowData.licitacao?.idRequisicaoModificacao;
      case 'I':
        return rowData.inexigibilidade?.idRequisicaoModificacao;
      case 'D':
        return rowData.dispensa?.idRequisicaoModificacao;
      case 'C':
        return rowData.carona?.idRequisicaoModificacao;
      case 'CR':
        return rowData.credenciamento?.idRequisicaoModificacao;
    }
  }

  renderDetailButton = (rowData) => {
    const processo = rowData.tipoProcesso;

    const links = {
      L: {
        link: UrlRouter.cadastrosConsulta.licitacao.detalhe.replace(':id', rowData?.licitacao?.id),
        permission: AccessPermission.licitacao.readPermission,
      },
      D: {
        link: UrlRouter.cadastrosConsulta.dispensa.detalhe.replace(':id', rowData?.dispensa?.id),
        permission: AccessPermission.dispensa.readPermission,
      },
      I: {
        link: UrlRouter.cadastrosConsulta.inexigibilidade.detalhe.replace(':id', rowData?.inexigibilidade?.id),
        permission: AccessPermission.inexigibilidade.readPermission,
      },
      C: {
        link: UrlRouter.cadastrosConsulta.carona.detalhe.replace(':id', rowData?.carona?.id),
        permission: AccessPermission.carona.readPermission,
      },
      CR: {
        link: UrlRouter.cadastrosConsulta.credenciamento.detalhe.replace(':id', rowData?.credenciamento?.id),
        permission: AccessPermission.credenciamento.readPermission,
      },
    };

    const linkProcesso = links[processo].link;

    return (
      <div className="actions p-d-flex p-jc-end">
        <PermissionProxy resourcePermissions={links[processo].permission}>
          <FcButton
            icon="pi pi-eye"
            tooltip="Detalhes"
            className="p-button-bg p-button-info p-mr-2"
            onClick={() => window.open(generateFullURL(linkProcesso), '_blank')}
          />
        </PermissionProxy>
      </div>
    );
  };

  render() {
    const columns = [
      {
        field: 'tipoProcesso',
        header: 'Tipo do Processo',
        sortable: true,
        body: (rowData) => {
          getValueByKey(rowData?.tipoProcesso, DadosEstaticosService.getTipoProcesso());
        },
        style: { width: '10%' },
      },
      {
        field: 'numero',
        header: 'Número',
        body: ({ numero }) => getValue(numero),
        sortable: true,
        style: { width: '5%' },
      },
      {
        field: 'objeto',
        header: 'Objeto',
        sortable: true,
        body: (rowData) => getValueElipsis(rowData.objeto, 50),
      },
      {
        field: 'valor',
        header: 'Valor',
        sortable: true,
        body: (rowData) => {
          const processo = getValueByKey(
            rowData?.tipoProcesso,
            DadosEstaticosService.getTipoProcesso(),
            'value',
            'urlLabel'
          );
          return rowData?.tipoProcesso === 'CR'
            ? getValueMoney(
                somaValoresLotes(
                  rowData[processo]?.termoReferencia.lotes,
                  rowData[processo]?.termoReferencia?.tresCasasDecimais ? 3 : 2
                )
              )
            : getValueMoney(rowData.valor, rowData[processo]?.termoReferencia?.tresCasasDecimais ? 3 : 2);
        },
        style: { width: '10%' },
      },
      {
        field: 'dataProcesso',
        header: 'Data do Processo',
        sortable: true,
        body: ({ dataProcesso }) => getValueDate(dataProcesso, DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS),
        style: { width: '15%' },
      },
      {
        style: { width: '15%' },
        body: (rowData) => {
          const [message, blockedProcess] = this._checkProcessoIsBlocked(rowData);

          return (
            <div className="actions p-d-flex p-jc-end">
              {this.renderDetailButton(rowData)}
              {blockedProcess ? (
                <>
                  <FcButton
                    icon="pi pi pi-exclamation-triangle"
                    tooltip={'Seleção de licitação bloqueada: ' + message}
                    className="p-button-bg p-button-warning p-mr-2"
                  />
                </>
              ) : (
                <>
                  {this._checkProcessoIsReqMod(rowData) ? (
                    <FcButton
                      icon="pi pi pi-exclamation-triangle"
                      tooltip="Seleção bloqueada por existir requisição de modificação em aberto"
                      className="p-button-bg p-button-warning p-mr-2"
                    />
                  ) : (
                    <PermissionProxy resourcePermissions={this.getReadPermission()}>
                      <FcButton
                        icon="pi pi-arrow-circle-right"
                        tooltip="Selecionar"
                        className="p-button-bg p-button-success p-mr-2"
                        onClick={() => {
                          return this.props.onSelect(rowData);
                        }}
                      />
                    </PermissionProxy>
                  )}
                </>
              )}
            </div>
          );
        },
      },
    ];
    const { listKey, loading } = this.store;
    const { getDefaultTableProps } = this;

    const header = (
      <div className="table-header">
        <PermissionProxy resourcePermissions={this.getWritePermission()}>
          <FcButton
            className="p-button"
            label="Processo de Entidade Externa"
            style={{ marginRight: '10px' }}
            icon="pi pi-plus"
            onClick={() => {
              this.props.onSelectExternalProcess();
              this.props.store.carregarTiposArquivo();
            }}
          />
        </PermissionProxy>
      </div>
    );

    return (
      <div className="card page index-table">
        <AdvancedSearch
          searchParams={this.store.getAdvancedSearchParams()}
          store={this.store}
          searchFields={['numero', 'tipoProcesso', 'objeto']}
          filterSuggest={this.store.getFilterSuggest()}
        />
        <IndexDataTable
          columns={columns}
          value={listKey}
          loading={loading}
          header={header}
          {...getDefaultTableProps()}
        />
        {this.state.detalhesVisibility && this.renderDetalhesDialog()}
      </div>
    );
  }
}

TabProcesso.displayName = 'ListagemProcesso';

export default TabProcesso;
