import React from 'react';
import PropTypes from 'prop-types';
import { Link } from 'react-router-dom';
import { observer } from 'mobx-react';
import { Divider } from 'primereact/divider';
import { Column } from 'primereact/column';
import {
  getValueMoney,
  getValue<PERSON>y<PERSON><PERSON>,
  getValueDate,
  getValue,
  getNumberFractionDigits,
  getNumberUnitThousands,
  isValueValid,
} from 'fc/utils/utils';
import { DATE_FORMAT, DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT, DATE_PARSE_FORMAT_WITH_HOURS } from 'fc/utils/date';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import UrlRouter from '~/constants/UrlRouter';
import { DataTable } from 'primereact/datatable';
import AdvancedSearch from 'fc/components/AdvancedSearch';
import FcButton from 'fc/components/FcButton';
import AditivoContratoIndexStore from '~/stores/contrato/aditivo/indexStore';
import SearchTypes from 'fc/components/AdvancedSearch/SearchTypes';
import AppStore from 'fc/stores/AppStore';
import AccessPermission from '~/constants/AccessPermission';
import './style.scss';
import FcCloseableTabView from 'fc/components/FcCloseableTabView';
import ScrollMenu from 'fc/components/ScrollMenu';
import classNames from 'classnames';
import EmpenhoDetailDialog from 'pages/contrato/empenhos/EmpenhoDetailDialog';
import EmpenhoContratoIndexStore from '~/stores/contrato/empenhos/indexStore';
import MultipleFileUploader from 'fc/components/MultipleFileUploader';
import { Tag } from 'primereact/tag';

@observer
class ContratoDetailPage extends React.Component {
  constructor(props) {
    super(props);
    this.aditivoStore = new AditivoContratoIndexStore();
    this.empenhoStore = new EmpenhoContratoIndexStore();
    this.state = {
      decimalPlaces: 2,
      activeTabIndex: 0,
      selectedRow: null,
      detalhesVisibility: false,
    };
  }

  componentDidMount() {
    const { contratoStore } = this.props;
    contratoStore.carregaDecimalPlaces((decimalPlaces) => this.setState({ decimalPlaces: decimalPlaces }));
  }
  getValueStyle() {
    return { fontSize: AppStore.layout === 'extenso' ? '1.26rem' : '1.1rem' };
  }

  getValueLayoutClassNames() {
    const tipoLayout = AppStore.layout;
    return { 'py-0': tipoLayout === 'compacto', 'py-2': tipoLayout === 'default' || tipoLayout === 'extenso' };
  }

  _renderValue(label, value, col = 12, type = 'value', url = '#') {
    return (
      <div
        style={this.getValueStyle()}
        className={classNames(
          `details-set details-display flex align-items-center p-col-12 p-md-${col} p-sm-${col >= 6 ? col : 6}`,
          this.getValueLayoutClassNames()
        )}
      >
        <div
          style={{ color: 'rgb(0 0 0 / 50%)', paddingLeft: 0 }}
          className="xl:col-2 lg:col-3 md:col-4 sm:col-4 details-attributte drawer-content-label"
        >
          {label}
        </div>

        {type == 'value' && (
          <div
            style={{ paddingLeft: 0 }}
            className={`xl:col-10 lg:col-9 md:col-8 sm:col-8 details-value p-text-justify`}
          >
            {value ?? '-'}
          </div>
        )}

        {type == 'list' && (
          <div style={{ display: 'block', paddingLeft: 0 }}>
            {value?.map((value) => (
              <div className={`details-value p-text-justify`}>{value ?? '-'}</div>
            ))}
          </div>
        )}

        {type == 'link' && (
          <div
            style={{ paddingLeft: 0 }}
            className={`xl:col-10 lg:col-9 md:col-8 sm:col-8 details-value p-text-justify`}
          >
            {
              <Link target="_blank" rel="noreferrer" style={{ color: '#3F51B5' }} to={url}>
                <u>{value}</u>
              </Link>
            }
          </div>
        )}
      </div>
    );
  }

  _renderValueWithExtraLabel(mainLabel, mainValue, extraLabel, extraValue, col = 12, type = 'value', url = '#') {
    return (
      <div
        style={this.getValueStyle()}
        className={classNames(
          `details-set details-display flex align-items-center p-col-12 p-md-${col} p-sm-${col >= 6 ? col : 6}`,
          this.getValueLayoutClassNames()
        )}
      >
        <div
          style={{ color: 'rgb(0 0 0 / 50%)', paddingLeft: 0 }}
          className="xl:col-2 lg:col-3 md:col-4 sm:col-4 details-attributte drawer-content-label"
        >
          {mainLabel}
        </div>

        <div style={{ display: 'flex' }}>
          {type == 'value' && (
            <div
              style={{ paddingLeft: 0 }}
              className={`xl:col-10 lg:col-9 md:col-8 sm:col-8 details-value p-text-justify`}
            >
              {mainValue ?? '-'}
            </div>
          )}

          {type == 'list' && (
            <div style={{ display: 'block', paddingLeft: 0 }}>
              {value?.map((value) => (
                <div className={`details-value p-text-justify`}>{value ?? '-'}</div>
              ))}
            </div>
          )}

          {type == 'link' && (
            <div
              style={{ paddingLeft: 0 }}
              className={`xl:col-10 lg:col-9 md:col-8 sm:col-8 details-value p-text-justify`}
            >
              {
                <Link target="_blank" rel="noreferrer" style={{ color: '#3F51B5' }} to={url}>
                  <u>{value}</u>
                </Link>
              }
            </div>
          )}

          <div
            style={{ paddingLeft: 0 }}
            className={`xl:col-10 lg:col-9 md:col-8 sm:col-8 details-value p-text-justify`}
          >
            <Tag value={`${extraLabel} : ${extraValue}`}></Tag>
          </div>
        </div>
      </div>
    );
  }

  _renderColumns(columns) {
    return columns.map((col, idx) => <Column className={`p-p-3`} key={`col-${idx}`} {...col} />);
  }

  _renderDivider(label) {
    return (
      <div style={{ flexBasis: 'auto', display: 'flex' }}>
        <h5
          style={{
            padding: '7px',
            whiteSpace: 'nowrap',
            width: 'auto',
            color: '#333333',
            alignContent: 'left',
            marginRight: '5px',
            marginTop: '11px',
          }}
        >
          {label}
        </h5>
        <Divider />
      </div>
    );
  }

  _renderOrigemContrato() {
    const { contratoStore } = this.props;
    const contrato = contratoStore.object;
    const processo = contrato.processo;
    let content = <></>;
    if (processo) {
      const links = {
        L: {
          link: UrlRouter.cadastrosConsulta.licitacao.detalhe.replace(':id', processo.id),
          text: `${`Licitação - ${processo.numeroLicitacao}` ?? ''}`,
          permission: AccessPermission.licitacao.readPermission,
        },
        D: {
          link: UrlRouter.cadastrosConsulta.dispensa.detalhe.replace(':id', processo.id),
          text: `Dispensa ${
            processo.numeroProcesso && processo.numeroProcesso.includes('/')
              ? processo.numeroProcesso
              : `${processo.numeroProcesso}/${processo.anoDispensa}`
          }`,
          permission: AccessPermission.dispensa.readPermission,
        },
        I: {
          link: UrlRouter.cadastrosConsulta.inexigibilidade.detalhe.replace(':id', processo.id),
          text: `Inexigibilidade ${
            processo.numeroProcesso && processo.numeroProcesso.includes('/')
              ? processo.numeroProcesso
              : `${processo.numeroProcesso}/${processo.anoInexigibilidade}`
          }`,
          permission: AccessPermission.inexigibilidade.readPermission,
        },
        C: {
          link: UrlRouter.cadastrosConsulta.carona.detalhe.replace(':id', processo.id),
          text: `Adesão/Carona ${
            processo.numeroProcessoGerenciadorAta && processo.numeroProcessoGerenciadorAta.includes('/')
              ? processo.numeroProcessoGerenciadorAta
              : `${processo.numeroProcessoGerenciadorAta}/${processo.anoCarona}`
          }`,
          permission: AccessPermission.carona.readPermission,
        },
        CR: {
          link: UrlRouter.cadastrosConsulta.credenciamento.detalhe.replace(':id', processo.id),
          text: `Credenciamento ${
            processo.numero && processo.numero.includes('/') ? processo.numero : `${processo.numero}/${processo.ano}`
          }`,
          permission: AccessPermission.credenciamento.readPermission,
        },
      };
      const linkProcesso = links[contrato.tipo];
      content = AppStore.hasPermission(linkProcesso.permission)
        ? this._renderValue('Origem', linkProcesso.text, 12, 'link', linkProcesso.link)
        : this._renderValue('Origem', linkProcesso.text);
    }
    return content;
  }

  _renderTabVencedor() {
    const columns = [
      {
        field: 'licitante.nome',
        header: 'Nome',
        style: { width: '40%' },
      },
      {
        field: 'licitante.cpfCnpj',
        header: 'CNPJ/CPF',
        style: { width: '30%' },
      },
      {
        field: 'licitante.pessoaFisica',
        header: 'Tipo Pessoa',
        body: (vencedor) => {
          if (vencedor) {
            return getValueByKey(vencedor.licitante?.pessoaFisica, DadosEstaticosService.getTipoPessoa());
          }
        },
        style: { width: '20%' },
      },
    ];
    const contrato = this.props.contratoStore.object;
    const headerVencedor = this._getHeaderVencedor()?.toLocaleLowerCase();
    const vencedores = [contrato.contratoLicitante];
    const processo = contrato?.processo;

    if (processo?.lei === 'LEI_N_8666') {
      columns.push({
        field: 'valor',
        header: 'Valor',
        body: (vencedor) => getValueMoney(vencedor?.valor),
        style: { width: '20%' },
      });
    }

    return (
      <DataTable rowHover emptyMessage={`Nenhum ${headerVencedor} encontrado`} value={vencedores} rows={1}>
        {this._renderColumns(columns)}
      </DataTable>
    );
  }

  _renderValueEmpenho(label, value, col = 12) {
    return (
      <div className={`p-mt-3 p-col-12 p-md-${col} p-sm-${col >= 6 ? col : 6}`}>
        <div className="p-col-12 drawer-content-label font-bold">{label}</div>
        <div className={`p-col-12`}>{value ? value : '-'}</div>
      </div>
    );
  }

  _renderTabEmpenho() {
    const columns = [
      {
        field: 'numeroEmpenho',
        header: 'Número',
      },
      {
        field: 'valorEmpenho',
        header: 'Valor',
        body: ({ valorEmpenho }) => getValueMoney(valorEmpenho),
      },
      {
        field: 'dataEmpenho',
        header: 'Data de Empenho',
        body: ({ dataEmpenho }) => getValueDate(dataEmpenho, DATE_FORMAT, DATE_PARSE_FORMAT),
      },
      {
        field: 'tipoEmpenho',
        header: 'Tipo',
        body: ({ tipoEmpenho }) => getValueByKey(tipoEmpenho, DadosEstaticosService.getTiposEmpenho()),
      },
      {
        field: 'dataCadastro',
        header: 'Data de Cadastro',
        body: ({ dataCadastro }) => getValueDate(dataCadastro, DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS),
      },
      {
        style: { width: '40px' },
        body: (rowData) => {
          return (
            <div className="actions p-d-flex p-jc-end">
              <FcButton
                icon="pi pi-eye"
                className="p-button-sm p-mr-2"
                tooltip="Detalhes"
                onClick={() => {
                  this.empenhoStore.initializeArquivos(rowData.id);
                  this.setState({ detalhesVisibility: true, selectedRow: rowData });
                }}
              />
            </div>
          );
        },
      },
    ];
    const { contratoStore } = this.props;
    const empenhos = contratoStore.object?.empenhos?.filter((emp) => emp.status !== 'REMOVIDA');
    return (
      <DataTable
        rowHover
        emptyMessage={'Nenhum Empenho Encontrado'}
        value={empenhos}
        className="p-datatable-sm"
        paginator
        rows={5}
      >
        {this._renderColumns(columns)}
      </DataTable>
    );
  }

  _renderTabItens() {
    const { contratoLicitante } = this.props.contratoStore.object;
    const licitacao = this.props.contratoStore.object?.licitacao;
    const { decimalPlaces } = this.state;
    const showDesconto = licitacao?.tiposLicitacao?.map((t) => t.nome?.toUpperCase())?.includes('MAIOR DESCONTO');
    const showEspecificacao = licitacao?.naturezasDoObjeto?.includes('COMPRAS');
    let numero = 1;
    const columnsAdicionados = [
      {
        header: 'Código',
        body: (item) =>
          !item.itemGerado ? (
            <span>
              {getValue(item[this.props.contratoStore.getVencedorField()].itemLote?.materialDetalhamento?.codigo)}
            </span>
          ) : (
            <span>{getValue(item.materialDetalhamento?.codigo)}</span>
          ),
      },
      {
        header: 'Descrição',
        style: { width: '30%' },
        body: (item) => {
          if (!item.itemGerado) {
            return `${numero++} - ${getValue(
              item[this.props.contratoStore.getVencedorField()].itemLote?.materialDetalhamento?.pdm?.nome
            )}`;
          } else {
            return item.materialDetalhamento.pdm.nome;
          }
        },
      },
      {
        header: 'Descrição Complementar',
        body: (item) =>
          !item.itemGerado ? (
            <span>{getValue(item[this.props.contratoStore.getVencedorField()].itemLote?.descricaoComplementar)}</span>
          ) : (
            <span>{getValue(item.descricaoComplementar)}</span>
          ),
      },
      {
        header: 'Unidade de Medida',
        body: (item) =>
          !item.itemGerado ? (
            <span>
              {getValue(item[this.props.contratoStore.getVencedorField()].itemLote?.unidadeMedida?.textUnidadeMedida)}
            </span>
          ) : (
            <span>{getValue(item.unidadeMedida?.textUnidadeMedida)}</span>
          ),
      },
      {
        header: 'Marca/Modelo',
        className: 'text-justify',
        body: (item) =>
          item.itemGerado ? (
            '-'
          ) : (
            <span>{getValue(item[this.props.contratoStore.getVencedorField()].marcaModelo)}</span>
          ),
      },
      showEspecificacao && {
        header: 'Especificação',
        body: (item) => <span>{getValue(item[this.props.contratoStore.getVencedorField()].especificacao)}</span>,
      },
      {
        header: 'Quantidade',
        body: (item) => <span>{getNumberUnitThousands(item.quantidade)}</span>,
      },
      {
        header: 'Valor Unitário',
        body: (item) => <span>{getValueMoney(item.valor, decimalPlaces)}</span>,
      },
      showDesconto && {
        header: 'Desconto(%)',
        body: (item) => <span>{getNumberFractionDigits(item.desconto, 2)}</span>,
      },
      {
        header: 'Valor',
        body: (item) => <span>{getValueMoney(item.valor * item.quantidade, decimalPlaces)}</span>,
      },
    ];
    return (
      <DataTable
        rowHover
        value={contratoLicitante?.itens}
        responsiveLayout="scroll"
        emptyMessage="Nenhum item adicionado ao contrato."
      >
        {this._renderColumns(columnsAdicionados)}
      </DataTable>
    );
  }

  _renderTabAditivo() {
    const { onDetailAditivo, contratoStore } = this.props;
    const contrato = contratoStore.object;
    if (contrato) {
      const columns = [
        {
          field: 'numero',
          header: 'Número',
          sortable: true,
        },
        {
          field: 'dataPublicacao',
          header: 'Data da Publicação',
          body: ({ dataPublicacao }) => getValueDate(dataPublicacao, DATE_FORMAT, DATE_PARSE_FORMAT),
          sortable: true,
        },
        {
          field: 'tipoAlteracao',
          header: 'Tipo de Alteração Contratual',
          body: ({ tipoAlteracao }) => getValueByKey(tipoAlteracao, DadosEstaticosService.getTipoAlteracaoContratual()),
          sortable: true,
        },
        {
          style: { width: '40px' },
          body: (rowData) => {
            return (
              <div className="actions p-d-flex p-jc-end">
                <FcButton
                  icon="pi pi-eye"
                  className="p-button-sm p-mr-2"
                  tooltip="Detalhes"
                  onClick={() => onDetailAditivo(rowData)}
                />
              </div>
            );
          },
        },
      ];
      const { listKey, loading } = this.aditivoStore;
      const filterSuggest = [
        {
          id: '',
          field: 'contrato',
          operator: 'EQUAL_TO',
          value: contrato?.id,
          formatted: '',
          fixed: true,
          invisible: true,
          completeParam: {
            field: 'contrato',
            label: 'Id Contrato',
            type: SearchTypes.NUMBER,
          },
        },
      ];
      return (
        <>
          <AdvancedSearch
            searchParams={contratoStore.getAdvancedSearchParamsAditivo()}
            store={this.aditivoStore}
            searchFields={['numero']}
            filterSuggest={filterSuggest}
          />
          <DataTable
            rowHover
            value={listKey}
            className="p-datatable-sm"
            paginator
            emptyMessage="Nenhum aditivo."
            rows={10}
            loading={loading}
          >
            {this._renderColumns(columns)}
          </DataTable>
        </>
      );
    }
  }

  _renderTabArquivos(tipo) {
    const { contratoStore } = this.props;

    return tipo ? (
      <MultipleFileUploader
        fileTypes={DadosEstaticosService.getTipoArquivoRescisaoContrato()}
        downloadOnly
        store={contratoStore.fileStore}
        filterTypes={{ included: ['RESCISAO'] }}
      />
    ) : (
      <MultipleFileUploader
        fileTypes={DadosEstaticosService.getTipoArquivoContrato()}
        downloadOnly
        store={contratoStore.fileStore}
        filterTypes={{ excluded: ['RESCISAO'] }}
      />
    );
  }

  _getHeaderVencedor() {
    const contrato = this.props.contratoStore.object;
    return getValueByKey(contrato.tipo, DadosEstaticosService.getTipoProcesso(), 'value', 'licitante');
  }

  _renderTabs() {
    const processo = this.props.contratoStore.object.processo;
    const tabs = [];
    tabs.push({ id: 0, header: this.props.contratoStore.labelFornecedor(), content: this._renderTabVencedor() });
    if (processo?.lei !== 'LEI_N_8666') {
      tabs.push({ id: 1, header: 'Itens', content: this._renderTabItens() });
    }
    tabs.push({ id: 2, header: 'Empenhos', content: this._renderTabEmpenho() });
    if (this.props.contratoStore?.object?.id) {
      tabs.push({ id: 3, header: 'Aditivos', content: this._renderTabAditivo() });
    }
    tabs.push({ id: 4, header: 'Arquivos', content: this._renderTabArquivos() });
    if (this.props.contratoStore?.object?.dataAvisoRescisao) {
      tabs.push({ id: 5, header: 'Arquivos de Rescisão', content: this._renderTabArquivos('RESCISAO') });
    }

    return (
      <FcCloseableTabView
        tabs={tabs}
        activeTabIndex={this.state.activeTabIndex}
        onChangeTab={(tab) => this.setState({ activeTabIndex: tabs.indexOf(tab) })}
      />
    );
  }

  _getOrigemContratoExterno({ tipo, numeroLicitacao, numeroCarona, numeroDispensa, numeroInexigibilidade }) {
    return {
      L: 'Licitação: ' + numeroLicitacao,
      D: 'Dispensa: ' + numeroDispensa,
      I: 'Inexigibilidade: ' + numeroInexigibilidade,
      C: 'Carona: ' + numeroCarona,
    }[tipo];
  }

  _renderDadosBasicos() {
    const { contratoStore, showGerenciamento } = this.props;
    const { decimalPlaces } = this.state;
    const contrato = contratoStore.object;
    const { idContrato } = contratoStore;
    const isFormaContrato = contrato.formaContrato === 'CONTRATO';
    const isFormaEmpenho = contrato.formaContrato === 'EQUIVALENTE_EMPENHO';
    const isFormaCartaContrato = contrato.formaContrato === 'CARTA_CONTRATO';

    return (
      <div className="p-fluid p-formgrid">
        <div id={`detalhes-${idContrato}`}>{this._renderDivider('Detalhes')}</div>
        {contrato.processoExterno
          ? this._renderValue('Origem', contrato.origem ?? this._getOrigemContratoExterno(contrato))
          : this._renderOrigemContrato()}
        {this._renderValue(
          contratoStore.labelNumero(),
          contrato.anoContrato ? contrato.numero + '/' + contrato.anoContrato : contrato.numero
        )}
        {this._renderValue(contratoStore.labelObjeto(), contrato.objeto)}
        {this._renderValue(contratoStore.labelValor(), getValueMoney(contrato.valorGlobal, decimalPlaces))}
        {contrato.formaContrato !== 'CARTA_CONTRATO' &&
          this._renderValue(contratoStore.labelData(), getValueDate(contratoStore.valueData()))}
        {isFormaContrato &&
          this._renderValue(
            'Permite Aditivo',
            getValueByKey(contrato.permiteAditivo, DadosEstaticosService.getSimNao())
          )}
        {(isFormaContrato || isFormaCartaContrato) && (
          <>
            {this._renderValue('Início da Vigência', getValueDate(contrato.dataVigenciaInicial))}
            {contrato.dataVigenciaFinal != contrato.dataFinalVigente && isValueValid(contrato.dataFinalVigente)
              ? this._renderValueWithExtraLabel(
                  'Fim da Vigência',
                  getValueDate(contrato.dataVigenciaFinal),
                  'Vigência Atual',
                  getValueDate(contrato.dataFinalVigente)
                )
              : this._renderValue('Fim da Vigência', getValueDate(contrato.dataVigenciaFinal))}
          </>
        )}
        {isValueValid(contrato.dataAvisoRescisao) &&
          this._renderValue('Rescindido em', getValueDate(contrato.dataAvisoRescisao))}
        {isFormaEmpenho && (
          <>
            {this._renderValue(
              'Tipo do Empenho',
              getValueByKey(contrato.tipoEmpenho, DadosEstaticosService.getTiposEmpenho())
            )}
          </>
        )}
        {this._renderValue(
          'Fontes de Recurso',
          contrato.fontesDeRecurso?.map((f) => f.nome),
          12,
          'list'
        )}
        {contrato.processoExterno &&
          this._renderValue('Órgão/Entidade Gerenciadora', contrato.entidadeExterna.nomeEntidadeExterna)}
        <div id={`gestoresFiscais-${idContrato}`}>
          {this._renderDivider(
            !isFormaContrato ? 'Gestores e Fiscais do Equivalente de Contrato' : 'Gestores e Fiscais do Contrato'
          )}
        </div>
        {this._renderValue('Gestor', contrato.gestor?.nome)}
        {this._renderValue('Gestor Substituto', contrato.gestorSubstituto?.nome)}
        {this._renderValue('Fiscal', contrato.fiscal?.nome)}
        {this._renderValue('Fiscal Substituto', contrato.fiscalSubstituto?.nome)}
        {showGerenciamento && (
          <>
            <div id={`gerenciamento-${idContrato}`}>{this._renderDivider('Gerenciamento')}</div>
            {contrato.id && this._renderValue('Cadastrado por', contrato.usuario ? contrato.usuario.nome : null)}
            {contrato.id &&
              this._renderValue(
                'Data/Hora do Cadastro',
                getValueDate(contrato.dataCadastro, DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS)
              )}
            {contrato.id &&
              this._renderValue(
                'Alterado por',
                contratoStore.ultimaAlteracao ? contratoStore.ultimaAlteracao?.nome : null
              )}
            {contrato.id &&
              this._renderValue(
                'Data/Hora da Alteração',
                getValueDate(contratoStore.ultimaAlteracao?.data, DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS)
              )}
          </>
        )}
      </div>
    );
  }

  render() {
    const { idContrato } = this.props.contratoStore;
    return (
      <>
        <div className="relative">
          <div className="scroll-menu">
            <ScrollMenu
              title="sumário"
              layoutPosition="left"
              offsetTopOnScroll={50}
              links={[
                { id: `detalhes-${idContrato}`, label: 'Detalhes' },
                { id: `gestoresFiscais-${idContrato}`, label: 'Gestores e Fiscais do Contrato' },
                { id: `gerenciamento-${idContrato}`, label: 'Gerenciamento' },
              ]}
            />
          </div>
        </div>
        {this._renderDadosBasicos()}
        <Divider style={{ marginBottom: `1px` }} />
        {this._renderTabs()}
        <EmpenhoDetailDialog
          fileStore={this.empenhoStore.fileStore}
          empenho={this.state.selectedRow}
          detalhesVisibility={this.state.detalhesVisibility}
          onClose={() => this.setState({ detalhesVisibility: false })}
        />
      </>
    );
  }
}

ContratoDetailPage.defaultProps = {
  countDownloadRequest: false,
  showGerenciamento: true,
};

ContratoDetailPage.propTypes = {
  contratoStore: PropTypes.any,
  countDownloadRequest: PropTypes.bool,
  showGerenciamento: PropTypes.bool,
  onDetailAditivo: PropTypes.func,
  onDetailEmpenho: PropTypes.func,
};

export default ContratoDetailPage;
