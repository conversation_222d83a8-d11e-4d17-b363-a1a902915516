import React from 'react';
import PropTypes from 'prop-types';
import { observer } from 'mobx-react';
import { ProgressSpinner } from 'primereact/progressspinner';
import ContratoDetailPage from './detail';
import ContratoFormStore from '~/stores/contrato/formStore';

@observer
class ContratoIndexDetailPage extends React.Component {
  constructor(props) {
    super(props);
    this.contratoStore = new ContratoFormStore();
    this.arquivoStore = this.contratoStore.fileStore;
    this.state = {
      selectedRow: null,
      detalhesVisibility: false,
    };
  }

  componentDidMount() {
    const { id } = this.props;
    if (id) {
      this.contratoStore.initializeWithAssociatedProcess(id, {}, () => {
        this.contratoStore.initializeFiles(id);
        this.contratoStore.carregaUltimaAlteracao(id);
        this.contratoStore.carregarDadosProcesso();
      });
    }
  }

  render() {
    const contrato = this.contratoStore?.object;
    const { onDetailAditivo } = this.props;
    let content = <></>;
    if (this.contratoStore.loading) {
      content = (
        <div className="p-d-inline p-d-flex align-items-center">
          <ProgressSpinner />
        </div>
      );
    } else if (contrato) {
      content = (
        <ContratoDetailPage contratoStore={this.contratoStore} onDetailAditivo={onDetailAditivo} countDownloadRequest />
      );
    } else {
      content = <div>Erro ao exibir detalhes do Contrato.</div>;
    }

    return content;
  }
}

ContratoIndexDetailPage.propTypes = {
  id: PropTypes.number,
  onDetailAditivo: PropTypes.func,
};

export default ContratoIndexDetailPage;
