import React from 'react';
import { observer } from 'mobx-react';
import PropTypes from 'prop-types';
import GenericFormPage from 'fc/pages/GenericFormPage';
import FormField from 'fc/components/FormField';
import FcButton from 'fc/components/FcButton';
import AccessPermission from '~/constants/AccessPermission';
import { Dialog } from 'primereact/dialog';
import { InputTextarea } from 'primereact/inputtextarea';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import { isValueValid, showNotification } from 'fc/utils/utils';
import { Fieldset } from 'primereact/fieldset';
import RequisicaoModificacaoContratoFormStore from '~/stores/contrato/requisicaoModificacao/formStore';
import RescisaoFormStore from '~/stores/contrato/rescisao/formStore';
import ContratoFormStore from '~/stores/contrato/formStore';
import MultipleFileUploader from 'fc/components/MultipleFileUploader';
import FcDropdown from 'fc/components/FcDropdown';
import FcCalendar from 'fc/components/FcCalendar';

@observer
class RescisaoContratoDialog extends GenericFormPage {
  reqModificacaoStore;
  constructor(props) {
    super(props, undefined, AccessPermission.rescisaoContratual);
    this.store = new RescisaoFormStore();
    this.contratoStore = new ContratoFormStore();
    this.state = {
      errorDialogValue: false,
      showDialogReqMod: false,
      submitted: false,
    };
    this.validadeDialogAndSubmit = this.validadeDialogAndSubmit.bind(this);
    this.contratoStore.fileStore.tipoArquivoEnum = DadosEstaticosService.getTipoArquivoRescisaoContrato();

    this.reqModificacaoStore = new RequisicaoModificacaoContratoFormStore();
    this.isReqMod = false;
  }

  componentDidMount() {
    if (this.props.contrato) {
      this.contratoStore.initialize(this.props.contrato?.id, {}, () => {
        this.contratoStore.initializeFiles(this.props.contrato?.id, () => this.forceUpdate());
        this.store.initialize(this.contratoStore.object);
        if (this.contratoStore.object?.dataCadastroRescisao) {
          this.isReqMod = true;
        }
      });
    }
  }

  validadeDialogAndSubmit() {
    if (this.contratoStore.object.motivoRescisao) {
      this.setState({ errorDialogValue: false });
      this.contratoStore.salvarRescisaoContrato(this.props.contrato, () => {
        this.props.closeDialog();
        this.props.updateTable();
      });
    } else {
      this.setState({ errorDialogValue: true });
    }
    this._getFieldErrorMessage(this.state.errorDialogValue);
  }

  submitFormData(e) {
    e && e.preventDefault();
    const execution = () => {
      if (!this.store.rules.hasError) {
        if (!this.isReqMod) {
          this.contratoStore.salvarRescisaoContrato(
            Object.assign(this.contratoStore.object, this.store.rescisao),
            () => {
              this.props.closeDialog();
              this.props.updateTable();
            }
          );
        }
      } else {
        showNotification('error', null, 'Verifique os campos do formulário!');
      }
    };

    if (this.state.submitted) {
      execution();
    } else {
      this.setState({ submitted: true }, execution);
    }
  }

  submitReqMod() {
    const contratoDTO = {
      contrato: Object.assign(this.contratoStore.object, this.store.rescisao),
      arquivosContrato: this.contratoStore.arquivoContratoList,
    };
    this.isReqMod &&
      this.reqModificacaoStore.justificativaJurisdicionado &&
      this.reqModificacaoStore.enviarRequisicaoRescisao(contratoDTO, () => {
        this._toggleDialogReqMod();
        this.props.closeDialog();
        this.props.updateTable();
      });
  }

  _getFieldErrorMessage(rendered, message) {
    if (rendered) {
      return <small className="p-error">{message}</small>;
    }
    return null;
  }

  _toggleDialogReqMod() {
    if (!this.store.rules.hasError) {
      this.setState((oldState) => ({ showDialogReqMod: !oldState.showDialogReqMod }));
    } else {
      showNotification('error', null, 'Verifique os campos do formulário!');
    }
  }

  renderDialogReqModFooter() {
    return (
      <div>
        <FcButton
          label="Cancelar"
          icon="pi pi-times"
          onClick={() => this._toggleDialogReqMod()}
          className="p-button-text"
        />
        <FcButton
          label="Enviar Requisição"
          icon="pi pi-check"
          loading={this.contratoStore.loading}
          onClick={() => {
            if (this.reqModificacaoStore.justificativaJurisdicionado) {
              this.submitReqMod();
            } else {
              !this.state.errorDialogValue && this.setState({ errorDialogValue: true });
            }
          }}
        />
      </div>
    );
  }

  renderDialogRequisicaoModificacao() {
    return (
      <Dialog
        header="Justificativa Requisição"
        visible={this.state.showDialogReqMod}
        style={{ width: '50vw' }}
        footer={this.renderDialogReqModFooter()}
        onHide={() => this._toggleDialogReqMod()}
      >
        <div className="p-fluid p-formgrid p-grid">
          <FormField columns={12} attribute="justificativa" label="Justificativa">
            <InputTextarea
              className={this.state.errorDialogValue ? 'p-invalid p-error' : ''}
              value={this.reqModificacaoStore.justificativaJurisdicionado}
              onChange={(e) => {
                this.reqModificacaoStore.updateAttribute('jurisdicionado', e);
                isValueValid(this.reqModificacaoStore.justificativaJurisdicionado?.length) &&
                  this.setState({ errorDialogValue: false });
              }}
              placeholder="Descreva a justificativa"
            />
          </FormField>
          <div className="p-col-6">
            {this._getFieldErrorMessage(this.state.errorDialogValue, 'Por favor, preencha o campo')}
          </div>
        </div>
      </Dialog>
    );
  }

  renderFooter() {
    return (
      <div>
        <FcButton
          label="Cancelar"
          type="button"
          className="p-button-secondary"
          loading={this.contratoStore.loading}
          onClick={() => {
            this.isReqMod = false;
            this.props.closeDialog();
          }}
        />
        {!this.isReqMod && (
          <FcButton
            label="Enviar"
            className="p-ml-auto p-mr-2"
            type="button"
            onClick={this.submitFormData}
            loading={this.contratoStore.loading}
          />
        )}
        {this.isReqMod && (
          <FcButton label="Enviar Requisição" className="p-ml-auto p-mr-2" onClick={() => this._toggleDialogReqMod()} />
        )}
      </div>
    );
  }

  render() {
    const { validateField } = this;
    const { submitted } = this.state;
    const { visible, closeDialog } = this.props;

    return (
      <Dialog
        header="Rescisão/Extinção Contratual"
        visible={visible}
        modal
        footer={this.renderFooter()}
        style={{ width: '80vw' }}
        onHide={closeDialog}
        draggable={false}
        resizable={false}
        dismissableMask
      >
        {this.contratoStore.object && !this.contratoStore.loadingFiles ? (
          <div>
            <form>
              {this.isReqMod && (
                <Fieldset legend="AVISO">
                  <h6 style={{ color: '#dd0303' }}>
                    A EDIÇÃO DESTE CONTRATO ESTARÁ SUJEITA A RATIFICAÇÃO PELA EQUIPE DE AUDITORIA DO TCE
                  </h6>
                </Fieldset>
              )}
              <div className="p-fluid p-formgrid p-grid">
                <FormField
                  columns={4}
                  attribute="dataAvisoRescisao"
                  label="Data da publicação da rescisão/extinção contratual"
                  submitted={submitted}
                  rule={this.store.getRule('dataAvisoRescisao')}
                >
                  <FcCalendar
                    value={this.getDateAttributeValue(this.store.rescisao?.dataAvisoRescisao)}
                    onChange={(e) => this.store.updateAttribute('dataAvisoRescisao', e)}
                    showIcon
                    mask="99/99/9999"
                  />
                </FormField>

                <FormField
                  columns={8}
                  attribute="motivoRescisao"
                  label="Motivo"
                  rule={this.store.getRule('motivoRescisao')}
                  submitted={submitted}
                  style={{ padding: '0.5rem' }}
                >
                  <FcDropdown
                    inOrder
                    {...validateField('motivoRescisao')}
                    onChange={(e) => this.store.updateAttribute('motivoRescisao', e)}
                    placeholder="Selecione o motivo"
                    value={this.store.rescisao?.motivoRescisao}
                    id="motivoRescisao"
                    optionLabel="text"
                    optionValue="value"
                    options={DadosEstaticosService.getMotivosRescisaoContrato()}
                  />
                </FormField>
              </div>
              <div className="p-fluid p-formgrid p-grid">
                {this.store.rescisao?.motivoRescisao === 'OUTROS' && (
                  <FormField
                    columns={12}
                    attribute="outroMotivoRescisao"
                    label="Outros Motivos"
                    submitted={submitted}
                    rule={this.store.getRule('outroMotivoRescisao')}
                  >
                    <InputTextarea
                      value={this.store.rescisao?.outroMotivoRescisao}
                      onChange={(e) => this.store.updateAttribute('outroMotivoRescisao', e)}
                      placeholder="Informe o motivo"
                    />
                  </FormField>
                )}
                <FormField
                  columns={12}
                  attribute="formaExtincao"
                  label="Forma de Extinção"
                  rule={this.store.getRule('formaExtincao')}
                  submitted={submitted}
                >
                  <FcDropdown
                    {...validateField('formaExtincao')}
                    onChange={(e) => {
                      this.store.updateAttribute('formaExtincao', e);
                      this.forceUpdate();
                    }}
                    placeholder="Selecione a forma de extinção"
                    value={this.store.rescisao?.formaExtincao}
                    id="formaExtincao"
                    optionLabel="text"
                    optionValue="value"
                    options={DadosEstaticosService.getFormasExtincaoContrato()}
                    inOrder
                  />
                </FormField>
                <FormField
                  columns={12}
                  attribute="descricaoRescisao"
                  label="Descrição"
                  submitted={submitted}
                  rule={this.store.getRule('descricaoRescisao')}
                >
                  <InputTextarea
                    value={this.store.rescisao?.descricaoRescisao}
                    onChange={(e) => {
                      this.store.updateAttribute('descricaoRescisao', e);
                    }}
                    placeholder="Informe a descrição"
                  />
                </FormField>
              </div>
              <Fieldset legend="Arquivos">
                <div className="p-fluid p-formgrid p-grid">
                  <div className="p-col-12">
                    <MultipleFileUploader
                      store={this.contratoStore.fileStore}
                      onChangeFiles={(files) => this.contratoStore.setArquivoContratoList(files)}
                      filterTypes={{ included: ['RESCISAO'] }}
                      fileTypes={DadosEstaticosService.getTipoArquivoRescisaoContrato()}
                    />
                  </div>
                </div>
              </Fieldset>
            </form>
            {this.renderDialogRequisicaoModificacao()}
          </div>
        ) : (
          <div className="card page">
            <i className="pi pi-spin pi-spinner" style={{ fontSize: '2em' }}></i>
          </div>
        )}
      </Dialog>
    );
  }
}

RescisaoContratoDialog.propTypes = {
  history: PropTypes.any,
  contrato: PropTypes.any.isRequired,
  closeDialog: PropTypes.func.isRequired,
  updateTable: PropTypes.func.isRequired,
  visible: PropTypes.bool.isRequired,
};

export default RescisaoContratoDialog;
