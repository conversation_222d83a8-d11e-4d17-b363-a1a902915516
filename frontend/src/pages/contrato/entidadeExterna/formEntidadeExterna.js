import React from 'react';
import { observer } from 'mobx-react';
import PropTypes from 'prop-types';
import EntidadeExternaFormStore from '~/stores/entidadeExterna/formStore';
import { InputText } from 'primereact/inputtext';
import UrlRouter from '~/constants/UrlRouter';
import GenericFormPage from 'fc/pages/GenericFormPage';
import FormField from 'fc/components/FormField';
import AccessPermission from '~/constants/AccessPermission';
import PermissionProxy from 'fc/components/PermissionProxy';
import { InputMask } from 'primereact/inputmask';
import AsyncDropdown from 'fc/components/AsyncDropdown';
import { Divider } from 'primereact/divider';
import FcButton from 'fc/components/FcButton';

@observer
class FormEntidadeExterna extends GenericFormPage {
  constructor(props) {
    super(props, UrlRouter.administracao.entidadeExterna.index, AccessPermission.entidadeExterna);

    this.state = { submitted: false };
    this.store = new EntidadeExternaFormStore();
  }

  _goBack(entidadeExterna) {
    this.props.closeMethod(entidadeExterna);
  }

  renderActionButtons() {
    return (
      <div className="p-mt-10 form-actions-dialog">
        <Divider />
        <span className="p-d-flex">
          <PermissionProxy resourcePermissions={this.getReadPermission()}>
            <FcButton
              label="Voltar"
              type="button"
              className="p-ml-auto p-button-secondary p-mr-2"
              onClick={() => this._goBack()}
              loading={this.store.loading}
            />
          </PermissionProxy>

          <PermissionProxy resourcePermissions={this.getWritePermission()}>
            <FcButton label="Salvar" type="button" onClick={this.submitFormData} loading={this.store.loading} />
          </PermissionProxy>
        </span>
      </div>
    );
  }

  render() {
    const { submitted } = this.state;
    const { updateAttribute, getRule, updateMunicipio } = this.store;
    const { submitFormData } = this;

    let content;

    if (this.store.object) {
      content = (
        <>
          <div className="card page">
            <form onSubmit={submitFormData}>
              <div className="p-fluid p-formgrid p-grid">
                <FormField
                  columns={6}
                  attribute="nomeEntidadeExterna"
                  label="Nome"
                  rule={getRule('nomeEntidadeExterna')}
                  submitted={submitted}
                >
                  <InputText
                    onChange={(e) => updateAttribute('nomeEntidadeExterna', e)}
                    placeholder="Informe o nome"
                    value={this.store.object.nomeEntidadeExterna}
                  />
                </FormField>

                <FormField columns={6} attribute="cnpj" label="CNPJ" rule={getRule('cnpj')} submitted={submitted}>
                  <InputMask
                    mask={'99.999.999/9999-99'}
                    onChange={(e) => updateAttribute('cnpj', e)}
                    placeholder="Informe o CNPJ"
                    value={this.store.object.cnpj}
                    id="cnpj"
                  />
                </FormField>
                <FormField
                  columns={6}
                  attribute="municipio"
                  label="Cidade/UF"
                  rule={getRule('municipio')}
                  submitted={submitted}
                >
                  <AsyncDropdown
                    onChange={updateMunicipio}
                    value={this.store.object.municipio?.id}
                    placeholder="Selecione a cidade"
                    store={this.store.municipioSelectStore}
                  />
                </FormField>
              </div>
              {this.renderActionButtons()}
            </form>
          </div>
        </>
      );
    } else {
      content = (
        <div className="card page">
          <i className="pi pi-spin pi-spinner" style={{ fontSize: '2em' }}></i>
        </div>
      );
    }
    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        {content}
      </PermissionProxy>
    );
  }
}

FormEntidadeExterna.propTypes = {
  id: PropTypes.any,
  action: PropTypes.any,
  history: PropTypes.any,
};

export default FormEntidadeExterna;
