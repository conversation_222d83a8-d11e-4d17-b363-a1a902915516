import React from 'react';
import { observer } from 'mobx-react';
import AppBreadCrumb from 'fc/components/AppBreadCrumb';
import GenericIndexPage from 'fc/pages/GenericIndexPage';
import PermissionProxy from 'fc/components/PermissionProxy';
import AccessPermission from '~/constants/AccessPermission';
import FcCloseableTabView from 'fc/components/FcCloseableTabView';
import EmpenhoContratoListagemPage from './listagem';
import UrlRouter from '~/constants/UrlRouter';

@observer
class EmpenhoContratoIndexPage extends GenericIndexPage {
  constructor(props) {
    super(props, AccessPermission.empenho);
    this.state = {
      data: [
        {
          id: 1,
          header: 'Listagem',
          closeable: false,
          content: <EmpenhoContratoListagemPage {...props} />,
        },
      ],
      count: 2,
      activeTabIndex: 0,
    };

    this.setActiveTabIndex = this.setActiveTabIndex.bind(this);
    this.handleCloseTab = this.handleCloseTab.bind(this);
  }

  setActiveTabIndex(tab) {
    this.setState({ activeTabIndex: this.state.data.indexOf(tab) });
  }

  handleCloseTab(tab) {
    this.setState((prevState) => {
      return { data: prevState.data.filter((t) => t.id !== tab.id) };
    });
  }

  render() {
    const breacrumbItems = [
      { label: 'Contrato', url: UrlRouter.cadastrosConsulta.contrato.index },
      { label: 'Empenho' },
    ];

    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        <AppBreadCrumb items={breacrumbItems} />
        <div className="card page index-table">
          <FcCloseableTabView
            tabs={this.state.data}
            activeTabIndex={this.state.activeTabIndex}
            onChangeTab={this.setActiveTabIndex}
            onClose={this.handleCloseTab}
            fixedFirstTab
          />
        </div>
      </PermissionProxy>
    );
  }
}

EmpenhoContratoIndexPage.displayName = 'EmpenhoContratoIndexPage';

export default EmpenhoContratoIndexPage;
