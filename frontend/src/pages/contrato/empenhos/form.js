import UrlRouter from '~/constants/UrlRouter';
import AccessPermission from '~/constants/AccessPermission';
import { observer } from 'mobx-react';
import PropTypes from 'prop-types';
import GenericFormPage from 'fc/pages/GenericFormPage';
import AppBreadCrumb from 'fc/components/AppBreadCrumb';
import PermissionProxy from 'fc/components/PermissionProxy';
import FormField from 'fc/components/FormField';
import { Fieldset } from 'primereact/fieldset';
import InputMonetary from 'fc/components/InputMonetary';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import { ProgressSpinner } from 'primereact/progressspinner';
import EmpenhoContratoFormStore from '~/stores/contrato/empenhos/formStore';
import InputNumber from 'fc/components/InputNumber';
import { SelectButton } from 'primereact/selectbutton';
import MultipleFileUploader from 'fc/components/MultipleFileUploader';
import { showNotification } from 'fc/utils/utils';
import 'fc/utils/reset-fieldset-styles.scss';
import FcCalendar from 'fc/components/FcCalendar';

@observer
class EmpenhoContratoFormPage extends GenericFormPage {
  constructor(props) {
    super(
      props,
      UrlRouter.cadastrosConsulta.contrato.empenho.index.replace(':idContrato', props.idContrato),
      AccessPermission.empenho
    );

    this.store = new EmpenhoContratoFormStore();
  }

  componentDidMount() {
    const { id, idContrato } = this.props;
    this.store.initialize(id, { tipoEmpenho: 'ORDINARIO' }, () => {
      this.store.carregaContrato(idContrato);
    });
  }

  submitFormData(e) {
    e && e.preventDefault();
    const execution = () => {
      if (!this.store.rules.hasError) {
        if (!this.store.arquivos?.some((file) => file.tipo == '')) {
          this.store.save(this._goBack, this.props.action);
        } else {
          showNotification('error', null, 'O tipo dos arquivos é obrigatório!');
        }
      } else {
        showNotification('error', null, 'Verifique os campos do formulário!');
      }
    };

    if (this.state.submitted) {
      execution();
    } else {
      this.setState({ submitted: true }, execution);
    }
  }

  render() {
    const { getRule } = this.store;
    const { props, submitFormData } = this;
    const { submitted } = this.state;

    const breacrumbItems = [
      { label: 'Contrato', url: UrlRouter.cadastrosConsulta.contrato.index },
      {
        label: 'Empenho',
        url: UrlRouter.cadastrosConsulta.contrato.empenho.index.replace(':idContrato', props.idContrato),
      },
      { label: props.action === 'new' ? 'Novo' : 'Editar' },
    ];

    let content;

    if (this.store.object) {
      content = (
        <>
          <AppBreadCrumb items={breacrumbItems} />
          <div className="card page form-action-buttons">
            <form onSubmit={submitFormData}>
              <Fieldset legend="Empenho">
                <div className="p-fluid p-formgrid p-grid">
                  <FormField
                    columns={4}
                    attribute="numeroEmpenho"
                    label="Número do Empenho"
                    rule={getRule('numeroEmpenho')}
                    submitted={submitted}
                  >
                    <InputNumber
                      onChange={(e) => this.store.updateAttribute('numeroEmpenho', e)}
                      value={this.store.object.numeroEmpenho}
                      id="numeroEmpenho"
                      leadingZeros
                      keyfilter={new RegExp('^[0-9]+$')}
                    />
                  </FormField>

                  <FormField
                    columns={4}
                    attribute="dataEmpenho"
                    label="Data"
                    rule={getRule('dataEmpenho')}
                    submitted={submitted}
                  >
                    <FcCalendar
                      value={this.getDateAttributeValue(this.store.object.dataEmpenho)}
                      onChange={(e) => this.store.updateAttributeDate('dataEmpenho', e)}
                      showIcon
                      selectOtherMonths
                      mask="99/99/9999"
                    />
                  </FormField>
                  <FormField
                    columns={4}
                    attribute="valorEmpenho"
                    label="Valor"
                    rule={getRule('valorEmpenho')}
                    submitted={submitted}
                  >
                    <InputMonetary
                      onChange={(e) => this.store.updateAttribute('valorEmpenho', e)}
                      placeholder="R$"
                      mode="currency"
                      value={this.store.object.valorEmpenho}
                    />
                  </FormField>
                </div>
                <div className="p-fluid p-formgrid p-grid">
                  <FormField
                    columns={4}
                    attribute="tipoEmpenho"
                    label="Tipo do Empenho"
                    rule={getRule('tipoEmpenho')}
                    submitted={submitted}
                  >
                    <SelectButton
                      optionLabel="text"
                      optionValue="value"
                      value={this.store.object.tipoEmpenho}
                      options={DadosEstaticosService.getTiposEmpenho()}
                      onChange={(e) => {
                        this.store.updateAttribute('tipoEmpenho', e);
                        this.store.updateAttribute('tipoEmpenhoOutro', '');
                        this.forceUpdate();
                      }}
                    />
                  </FormField>
                </div>
                <FormField columns={12} attribute="arquivos" label="Arquivos" submitted={submitted}>
                  <MultipleFileUploader
                    store={this.store.fileStore}
                    onChangeFiles={(files) => this.store.setFileList(files)}
                    fileTypes={DadosEstaticosService.getTipoArquivoEmpenho()}
                  />
                </FormField>
              </Fieldset>

              {this.renderActionButtons()}
            </form>
          </div>
        </>
      );
    } else {
      content = (
        <div className="card page">
          <AppBreadCrumb items={breacrumbItems} />
          <div className="p-d-inline p-d-flex align-items-center">
            <ProgressSpinner />
          </div>
        </div>
      );
    }
    return (
      <PermissionProxy resourcePermissions={this.getWritePermission()} blockOnFail>
        {content}
      </PermissionProxy>
    );
  }
}

EmpenhoContratoFormPage.propTypes = {
  id: PropTypes.any,
  idContrato: PropTypes.any,
  action: PropTypes.any,
  history: PropTypes.any,
};

export default EmpenhoContratoFormPage;
