import React, { useState } from 'react';
import { observer } from 'mobx-react';
import FcButton from 'fc/components/FcButton';
import { getNumberFractionDigits, getValueMoney, getValue, getNumberUnitThousands, isValueValid } from 'fc/utils/utils';
import Tooltip from 'fc/components/Tooltip';
import classNames from 'classnames';
import { Tag } from 'primereact/tag';
import { DataTable } from 'primereact/datatable';
import { Column } from 'react-virtualized';
import Panel from './panel';
import { InputNumber } from 'primereact/inputnumber';
import InputMonetary from 'fc/components/InputMonetary';
import { InputText } from 'primereact/inputtext';
import FcDropdown from 'fc/components/FcDropdown';
import './style.scss';

const ItensEditable = observer(
  ({
    itens,
    valor,
    setPreenchido,
    setQuantidade,
    setDescricaoComplementar,
    setUnidadeMedida,
    setValor,
    showEspecificacao,
    decimalPlaces,
    showDesconto,
    removeItem,
    vencedorField,
    store,
    readOnly,
    itemHasUnidadesMedida,
  }) => {
    const [collapsed, setCollapsed] = useState(false);
    const [editing, setEditing] = useState({});

    const toggleCollapsed = () => {
      setCollapsed(!collapsed);
    };

    const enableEdit = (item, field) => {
      if (!item.preenchido) {
        const itemId = item.itemGerado ? item.id : item[vencedorField]?.id;
        const newEditing = editing[itemId] ?? {};
        if (!newEditing[itemId]) {
          newEditing[itemId] = {};
        }
        newEditing[itemId][field] = true;
        setEditing(newEditing);
      }
    };

    const handleKey = (key) => {
      if (key === 'Enter') {
        setEditing({});
      }
    };

    const isItemValid = (item) => {
      return item.itemGerado
        ? item.quantidade > 0 &&
            item.valor > 0 &&
            (itemHasUnidadesMedida(item) ? isValueValid(item.unidadeMedida) : true)
        : item.quantidade > 0 && item.valor > 0;
    };

    const _renderDataTable = (itens) => {
      const columns = [
        {
          header: 'Código',
          body: (item) => {
            if (!item.itemGerado) {
              return getValue(item[vencedorField].itemLote?.materialDetalhamento?.codigo);
            } else {
              return getValue(item.materialDetalhamento?.codigo);
            }
          },
        },
        {
          header: 'Item',
          style: { width: '15%' },
          body: (item) => {
            if (item[vencedorField]) {
              return `${
                item[vencedorField].itemLote?.numero ? item[vencedorField].itemLote?.numero + ' - ' : ''
              } ${getValue(item[vencedorField].itemLote?.materialDetalhamento?.pdm?.nome)}`;
            } else {
              return `${getValue(item?.materialDetalhamento?.pdm?.nome)}`;
            }
          },
        },
        {
          header: 'Marca/Modelo',
          style: { width: '10%' },
          body: (item) => {
            if (!item.itemGerado) {
              return (
                <div className="item-container others-c">
                  <span className="text-disabled">{getValue(item[vencedorField].marcaModelo)}</span>
                </div>
              );
            } else {
              return '-';
            }
          },
        },
        showEspecificacao && {
          header: 'Especificação',
          body: (item) => (
            <div className="item-container others-c">
              <span className="text-disabled">{getValue(item[vencedorField].especificacao)}</span>
            </div>
          ),
        },
        {
          header: 'Descrição Complementar',
          field: 'descricaoComplementar',
          style: { width: '20%' },
          body: (item) => {
            if (!item.itemGerado) {
              return getValue(item[vencedorField]?.itemLote?.descricaoComplementar);
            } else {
              return !editing[item.id] && !item.preenchido ? (
                <InputText
                  value={item.descricaoComplementar}
                  onChange={(e) => setDescricaoComplementar(item, e.target.value)}
                />
              ) : (
                <span
                  className={classNames({ pointer: !item.preenchido })}
                  onClick={() => enableEdit(item, 'descricaoComplementar')}
                >
                  {getValue(item.descricaoComplementar)}
                </span>
              );
            }
          },
        },
        {
          header: (
            <span>
              Unidade de Medida <span style={{ color: 'red' }}>*</span>
            </span>
          ),
          field: 'unidadeMedida',
          style: { width: '10%' },
          body: (item) => {
            if (!item.itemGerado) {
              return getValue(item[vencedorField].itemLote?.unidadeMedida?.textUnidadeMedida);
            } else {
              return !editing[item.id] && !item.preenchido ? (
                item.materialDetalhamento?.pdm?.tipoMaterial === 'M' ? (
                  itemHasUnidadesMedida(item) ? (
                    <FcDropdown
                      className={!item.unidadeMedida ? 'p-invalid p-error' : ''}
                      options={item.materialDetalhamento?.pdm?.unidadesMedida}
                      optionLabel="textUnidadeMedida"
                      optionValue="id"
                      value={item.unidadeMedida?.id}
                      onChange={(e) =>
                        setUnidadeMedida(
                          item,
                          item.materialDetalhamento?.pdm?.unidadesMedida?.find(
                            (unidade) => unidade.id === e.target.value
                          )
                        )
                      }
                      placeholder="Selecione a unidade de medida utilizada"
                    />
                  ) : (
                    '-'
                  )
                ) : itemHasUnidadesMedida(item) ? (
                  <span>Unidade</span>
                ) : (
                  '-'
                )
              ) : (
                <span
                  className={classNames({ pointer: !item.preenchido })}
                  onClick={() => enableEdit(item, 'unidadeMedida')}
                >
                  {getValue(item.unidadeMedida?.textUnidadeMedida)}
                </span>
              );
            }
          },
        },
        {
          header: 'Quantidade',
          body: (item) => {
            if (!item.itemGerado) {
              return !editing[item.id] && !item.preenchido ? (
                store.object.tipo == 'CR' ? (
                  <InputMonetary
                    value={item.quantidade}
                    onChange={(e) => setQuantidade(item, e)}
                    decimalPlaces={item[store.getVencedorField()]?.itemLote.fracionario ? 2 : 0}
                    min={0}
                  />
                ) : (
                  <>
                    <div className="flex flex-column gap-1 item-container qtd-c">
                      <InputMonetary
                        value={item.quantidade}
                        onChange={(e) => setQuantidade(item, e)}
                        decimalPlaces={item[store.getVencedorField()]?.itemLote.fracionario ? 2 : 0}
                        min={0}
                        max={store.getQuantidadeDisponivel(item)}
                      />
                      <span className="text-sm" style={{ color: '#1d4ed8' }}>
                        Quantidade máxima: {getNumberUnitThousands(store.getQuantidadeDisponivel(item))}
                      </span>
                    </div>
                  </>
                )
              ) : (
                <span
                  className={classNames({ pointer: !item.preenchido })}
                  onClick={() => enableEdit(item, 'quantidade')}
                >
                  {getNumberUnitThousands(item.quantidade)}
                </span>
              );
            } else {
              return !editing[item.id] && !item.preenchido ? (
                <InputNumber value={item.quantidade} onChange={(e) => setQuantidade(item, e.value)} />
              ) : (
                <span
                  className={classNames({ pointer: !item.preenchido })}
                  onClick={() => enableEdit(item, 'quantidade')}
                >
                  {getValue(item.quantidade)}
                </span>
              );
            }
          },
        },
        {
          header: 'Valor Unitário',
          body: (item) => {
            if (!item.itemGerado) {
              return !editing[item.id] && !item.preenchido && store.object.tipo !== 'CR' ? (
                <div className="flex flex-column item-container unit-c">
                  <InputNumber
                    value={item.valor}
                    onChange={(e) => {
                      const valorMaximo = store.getValorUnitarioMaximo(item);
                      const novoValor = e.value > valorMaximo ? valorMaximo : e.value;
                      setValor(item, novoValor);
                    }}
                    mode="currency"
                    currency="BRL"
                    locale="pt-BR"
                    minFractionDigits={2}
                    min={0}
                    max={store.getValorUnitarioMaximo(item)}
                    onKeyDown={({ key }) => handleKey(key)}
                  />

                  <div>
                    <span className="text-sm" style={{ color: '#1d4ed8' }}>
                      Valor Unitário Máximo: {getValueMoney(store.getValorUnitarioMaximo(item), decimalPlaces)}
                    </span>
                  </div>
                </div>
              ) : (
                <span
                  className={store.object.tipo === 'CR' ? 'text-disabled' : classNames({ pointer: !item.preenchido })}
                  onClick={() => enableEdit(item, 'valorUnitario')}
                >
                  {getValueMoney(item.valor, decimalPlaces)}
                </span>
              );
            } else {
              return !editing[item.id] && !item.preenchido ? (
                <div className="item-container others-c">
                  <InputNumber
                    onKeyDown={({ key }) => handleKey(key)}
                    value={item.valor}
                    onChange={(e) => setValor(item, e.value)}
                    mode="currency"
                    currency="BRL"
                    locale="pt-BR"
                    minFractionDigits={2}
                    min={0}
                  />
                </div>
              ) : (
                <span
                  className={classNames({ pointer: !item.preenchido })}
                  onClick={() => enableEdit(item, 'valorUnitario')}
                >
                  {getValueMoney(item.valor, decimalPlaces)}
                </span>
              );
            }
          },
        },
        showDesconto && {
          header: 'Desconto(%)',
          body: (item) => (
            <div className="item-container others-c">
              <span className="text-disabled">{getNumberFractionDigits(item[vencedorField].desconto, 2)}</span>
            </div>
          ),
        },
        {
          header: 'Valor',
          body: (item) => (
            <div className="item-container others-c">
              <span className="text-disabled">{getValueMoney(item.valor * item.quantidade, decimalPlaces)}</span>
            </div>
          ),
        },
        {
          header: 'Status',
          body: (item) =>
            item.preenchido ? (
              <Tag severity="success" value="Preenchido" rounded />
            ) : (
              <Tag severity="danger" value="Pendente" rounded />
            ),
        },
        {
          header: 'Ações',
          body: (item) => (
            <>
              {!item.preenchido && (
                <FcButton
                  type="button"
                  icon="pi pi-check"
                  className="p-button-text toggle-button"
                  onClick={() => {
                    setEditing({});
                    isItemValid(item) && setPreenchido(item, true);
                  }}
                />
              )}
              {item.preenchido && (
                <FcButton
                  type="button"
                  icon="pi pi-pencil"
                  className="p-button-text toggle-button"
                  disabled={readOnly}
                  onClick={() => {
                    enableEdit(item);
                    setPreenchido(item, false);
                  }}
                />
              )}
              <FcButton
                type="button"
                icon="pi pi-trash"
                className="p-button-text toggle-button"
                disabled={readOnly}
                onClick={() => {
                  setEditing({});
                  removeItem(item);
                }}
              />
            </>
          ),
        },
      ];
      return (
        <DataTable
          rowHover
          value={itens}
          emptyMessage="Nenhum item disponível"
          style={{ maxWidth: '100%' }}
          className="p-datatable-sm "
        >
          {columns.map((c, idx) => {
            return <Column key={`field-${idx}`} {...c} />;
          })}
        </DataTable>
      );
    };

    const _renderHeader = (className) => {
      return (
        <>
          <div className="flex-left pointer" onClick={() => toggleCollapsed()}>
            <div className="info-lote">
              <span className="feedback">
                <i
                  className={classNames('p-m-2', {
                    'pi pi-angle-right': collapsed,
                    'pi pi-angle-down': !collapsed,
                  })}
                />
              </span>
              <strong className="p-ml-2" onClick={() => toggleCollapsed()}>
                Itens
              </strong>
            </div>
          </div>
          <div className="flex-right p-mr-2">
            <div className="flex-left p-mr-5 sm:w-26rem lg:w-17rem xl:w-30rem">
              <div>
                <strong className="p-mr-5">Valor</strong>
                <span className="p-ml-2 text-disabled">{getValueMoney(valor, decimalPlaces)}</span>
              </div>
            </div>
            <div className="feedback">
              {className?.includes('panel-check') && (
                <span className={`circle check`}>
                  <Tooltip value="Preenchido" sideOffset={10} delayDuration={100}>
                    <i className="pi pi-check" />
                  </Tooltip>
                </span>
              )}
              {className?.includes('panel-warning') && (
                <span className="circle warning">
                  <Tooltip value="Parcialmente Preenchido" sideOffset={10} delayDuration={100}>
                    <span className="default">!</span>
                  </Tooltip>
                </span>
              )}
              {className?.includes('panel-error') && (
                <span className="circle error">
                  <Tooltip value="Não Preenchido" sideOffset={10} delayDuration={100}>
                    <i className="pi pi-times" />
                  </Tooltip>
                </span>
              )}
            </div>
          </div>
        </>
      );
    };

    const className = itens.filter((item) => !item.preenchido)?.length === 0 ? 'panel-check' : 'panel-warning';
    let content = <></>;
    if (itens?.length > 0) {
      return (
        <div className="p-mt-2">
          <Panel
            className={className}
            header={_renderHeader(className)}
            content={_renderDataTable(itens)}
            collapsed={collapsed}
          />
        </div>
      );
    }
    return content;
  }
);

export default ItensEditable;
