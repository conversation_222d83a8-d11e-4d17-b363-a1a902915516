import React from 'react';
import { observer } from 'mobx-react';
import AppBreadCrumb from 'fc/components/AppBreadCrumb';
import GenericIndexPage from 'fc/pages/GenericIndexPage';
import PermissionProxy from 'fc/components/PermissionProxy';
import AccessPermission from '~/constants/AccessPermission';
import ContratoDetailPage from './detalhes/indexDetail';
import ContratoListagemPage from './listagem';
import AditivoDetailPage from './aditivos/indexDetail';
import FcCloseableTabView from 'fc/components/FcCloseableTabView';

@observer
class ContratoIndexPage extends GenericIndexPage {
  constructor(props) {
    super(props, AccessPermission.contrato);
    this.state = {
      data: [
        {
          id: 1,
          header: 'Listagem',
          closeable: false,
          content: <ContratoListagemPage {...props} onDetail={(contrato) => this.onDetail(contrato)} />,
        },
      ],
      count: 2,
      activeIndex: 0,
    };

    this.setActiveTabIndex = this.setActiveTabIndex.bind(this);
    this.onDetail = this.onDetail.bind(this);
    this.handleCloseTab = this.handleCloseTab.bind(this);
    this.onDetailAditivo = this.onDetailAditivo.bind(this);
  }

  onDetail(contrato) {
    const existingTab = this.state.data.find((tab) => tab.idContrato === contrato.id);
    if (existingTab) {
      this.setActiveTabIndex(existingTab);
    } else {
      const newcontrato = {
        id: contrato.id,
        idContrato: contrato.id,
        header: ` Contrato - ${contrato.numeroContrato}`,
        closeable: true,
        content: <ContratoDetailPage id={contrato.id} onDetailAditivo={(aditivo) => this.onDetailAditivo(aditivo)} />,
      };
      this.setState({ data: [...this.state.data, newcontrato], count: this.state.count + 1 });
    }
  }

  setActiveIndex(activeIndex) {
    this.setState({ activeIndex });
  }

  onDetailAditivo(aditivo) {
    const existingTab = this.state.data.find((tab) => tab.idAditivo === aditivo.id);
    if (existingTab) {
      this.setActiveTabIndex(existingTab);
    } else {
      const newAditivo = {
        id: aditivo.id,
        idAditivo: aditivo.id,
        header: 'Aditivo - ' + aditivo.numero + ' - Contr. ' + aditivo.contrato.numero,
        closeable: true,
        content: <AditivoDetailPage aditivo={aditivo} />,
      };
      this.setState({ data: [...this.state.data, newAditivo], count: this.state.count + 1 });
    }
  }

  setActiveTabIndex(tab) {
    this.setState({ activeTabIndex: this.state.data.indexOf(tab) });
  }

  handleCloseTab(tab) {
    this.setState((prevState) => {
      return { data: prevState.data.filter((t) => t.id !== tab.id) };
    });
  }

  render() {
    const breacrumbItems = [{ label: 'Contrato' }];
    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        <AppBreadCrumb items={breacrumbItems} />
        <div className="card page index-table">
          <FcCloseableTabView
            tabs={this.state.data}
            activeTabIndex={this.state.activeTabIndex}
            onChangeTab={this.setActiveTabIndex}
            onClose={this.handleCloseTab}
            fixedFirstTab
          />
        </div>
      </PermissionProxy>
    );
  }
}

ContratoIndexPage.displayName = 'ContratoIndexPage';

export default ContratoIndexPage;
