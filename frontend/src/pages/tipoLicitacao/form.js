import React from 'react';
import { observer } from 'mobx-react';
import PropTypes from 'prop-types';
import TipoLicitacaoFormStore from '~/stores/tipoLicitacao/formStore';
import { InputText } from 'primereact/inputtext';
import UrlRouter from '~/constants/UrlRouter';
import GenericFormPage from 'fc/pages/GenericFormPage';
import AppBreadCrumb from 'fc/components/AppBreadCrumb';
import Form<PERSON>ield from 'fc/components/FormField';
import PermissionProxy from 'fc/components/PermissionProxy';
import AccessPermission from '~/constants/AccessPermission';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import FcInputTextarea from 'fc/components/FcInputTextarea';
import FcMultiSelect from 'fc/components/FcMultiSelect';

@observer
class TipoLicitacaoFormPage extends GenericFormPage {
  constructor(props) {
    super(props, UrlRouter.administracao.tipoLicitacao.index, AccessPermission.tipoLicitacao);
    this.store = new TipoLicitacaoFormStore();
  }

  componentDidMount() {
    const { id } = this.props;
    this.store.initialize(id, { tipoNativo: false });
  }

  render() {
    const { submitted } = this.state;
    const { getRule } = this.store;
    const { submitFormData } = this;

    const breacrumbItems = [
      { label: 'Critérios de Julgamento', url: UrlRouter.administracao.tipoLicitacao.index },
      { label: this.props.action === 'new' ? 'Novo' : 'Editar' },
    ];

    let content;
    if (this.store.object) {
      content = (
        <>
          <AppBreadCrumb items={breacrumbItems} />
          <div className="card page form-action-buttons">
            <form onSubmit={submitFormData}>
              <div className="p-fluid p-formgrid p-grid">
                <FormField columns={6} attribute="nome" label="Nome" rule={getRule('nome')} submitted={submitted}>
                  <InputText
                    onChange={(e) => this.store.updateAttribute('nome', e)}
                    placeholder="Informe o nome"
                    value={this.store.object.nome}
                    id="nome"
                  />
                </FormField>

                <FormField
                  columns={12}
                  attribute="descricao"
                  label="Descrição"
                  rule={getRule('descricao')}
                  submitted={submitted}
                >
                  <FcInputTextarea
                    rows={5}
                    onChange={(e) => this.store.updateAttribute('descricao', e)}
                    placeholder="Informe a descrição"
                    value={this.store.object.descricao}
                    id="descricao"
                  />
                </FormField>
                <div className="p-field p-col-6">
                  <FormField
                    columns={12}
                    attribute="legislacao"
                    label="Legislação"
                    rule={getRule('legislacao')}
                    submitted={submitted}
                  >
                    <FcMultiSelect
                      onChange={(e) => this.store.updateAttribute('legislacao', e)}
                      placeholder="Selecione as legislações"
                      value={this.store.object.legislacao}
                      options={DadosEstaticosService.getTipoLicitacaoLegislacao()}
                      optionValue="value"
                      optionLabel="text"
                      filterBy="text"
                      filter
                      selectedItemsLabel="{} itens selecionados"
                      showClear
                      showOverlay
                    />
                  </FormField>
                </div>
              </div>
              {this.renderActionButtons()}
            </form>
          </div>
        </>
      );
    } else {
      content = (
        <div className="card page">
          <AppBreadCrumb items={breacrumbItems} />
          <i
            className="pi pi-spin pi-spinner"
            style={{
              marginTop: '20px',
              marginLeft: 'calc(50% - 20px)',
              fontSize: '2em',
            }}
          ></i>
        </div>
      );
    }
    return (
      <PermissionProxy
        resourcePermissions={this.props.action === 'edit' ? ['admin'] : this.getWritePermission()}
        blockOnFail
      >
        {content}
      </PermissionProxy>
    );
  }
}

TipoLicitacaoFormPage.propTypes = {
  id: PropTypes.any,
  action: PropTypes.any,
  history: PropTypes.any,
};

export default TipoLicitacaoFormPage;
