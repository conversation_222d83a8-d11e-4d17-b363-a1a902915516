import React from 'react';
import { observer } from 'mobx-react';
import PropTypes from 'prop-types';
import UrlRouter from '~/constants/UrlRouter';
import GenericFormPage from 'fc/pages/GenericFormPage';
import FormField from 'fc/components/FormField';
import PermissionProxy from 'fc/components/PermissionProxy';
import AccessPermission from '~/constants/AccessPermission';
import { ConfirmDialog } from 'primereact/confirmdialog';
import AppBreadCrumb from 'fc/components/AppBreadCrumb';
import FcButton from 'fc/components/FcButton';
import { isValueValid, showNotification } from 'fc/utils/utils';
import { InputText } from 'primereact/inputtext';
import ConfiguracoesEmailFormStore from '~/stores/configuracoesEmail/formStore';
import { PrimeIcons } from 'primereact/api';
import InputNumber from 'fc/components/InputNumber';
import { Dialog } from 'primereact/dialog';
import { InputTextarea } from 'primereact/inputtextarea';
import { Checkbox } from 'primereact/checkbox';
import { Password } from 'primereact/password';

@observer
class ConfiguracoesEmailFormPage extends GenericFormPage {
  constructor(props) {
    super(props, UrlRouter.home, AccessPermission.configuracoesEmail);
    this.store = new ConfiguracoesEmailFormStore();

    this.state = {
      submitted: false,
      visibleDialogEnvioEmailTeste: false,
      tempSenha: '',
    };

    this.toggleDialogVisibility = this.toggleDialogVisibility.bind(this);
    this.toggleDialogEnvioEmailTesteVisibility = this.toggleDialogEnvioEmailTesteVisibility.bind(this);
  }

  componentDidMount() {
    this.store.initialize({ ssl: false, tls: true });
  }

  submitFormData(e) {
    e && e.preventDefault();
    const execution = () => {
      if (!this.store.rules.hasError) {
        this.store.save();
        this.setState({ tempSenha: '' });
      } else {
        showNotification('error', null, 'Verifique os campos do formulário!');
      }
    };

    if (this.state.submitted) {
      execution();
    } else {
      this.setState({ submitted: true }, execution);
    }
  }

  toggleDialogVisibility() {
    this.setState({ dialogVisibility: !this.state.dialogVisibility });
  }

  toggleDialogEnvioEmailTesteVisibility() {
    this.setState({ visibleDialogEnvioEmailTeste: !this.state.visibleDialogEnvioEmailTeste });
  }

  _getFieldErrorMessage(rendered, message) {
    if (rendered) {
      return <small className="p-error">{message}</small>;
    }
    return null;
  }

  renderDialogTeste() {
    return (
      <Dialog
        blockScroll
        header="Testar envio de e-mail"
        visible={this.state.visibleDialogEnvioEmailTeste}
        style={{ width: '50vw' }}
        onHide={() => {
          this.toggleDialogEnvioEmailTesteVisibility();
          this.store.redefinirEmailTeste();
        }}
        draggable={false}
        resizable={false}
        footer={
          <div>
            <FcButton
              label="Voltar"
              type="button"
              className="p-ml-auto p-button-secondary p-mr-1"
              onClick={() => {
                this.toggleDialogEnvioEmailTesteVisibility();
                this.store.redefinirEmailTeste();
              }}
              loading={this.store.loading}
            />
            <FcButton
              label="Enviar"
              type="button"
              onClick={() => {
                if (this.store.emailTeste.destinatario) {
                  this.store.enviarEmailTeste();
                  this.toggleDialogEnvioEmailTesteVisibility();
                } else {
                  !this.state.errorDialogValue && this.setState({ errorDialogValue: true });
                }
              }}
              loading={this.store.loading}
            />
          </div>
        }
      >
        <div className="p-fluid p-formgrid p-grid">
          <FormField
            columns={12}
            attribute="enderecoSmtp"
            label={
              <span>
                Destinatário<span className="p-error"> *</span>
              </span>
            }
          >
            <div>
              <InputText
                placeholder="Informe o e-mail do destinatário"
                className={
                  this.state.errorDialogValue && !isValueValid(this.store.emailTeste?.destinatario)
                    ? 'p-invalid p-error'
                    : ''
                }
                value={this.store.emailTeste?.destinatario}
                onChange={(e) => this.store.updateAttributeTesteEmail('destinatario', e)}
              />
              <div className="p-col-6">
                {this._getFieldErrorMessage(this.state.errorDialogValue, 'Por favor, preencha o campo')}
              </div>
            </div>
          </FormField>

          <FormField columns={12} attribute="enderecoSmtp" label="Assunto">
            <InputText
              placeholder="Informe o assunto do e-mail"
              value={this.store.emailTeste?.assunto}
              onChange={(e) => this.store.updateAttributeTesteEmail('assunto', e)}
            />
          </FormField>
          <FormField columns={12} attribute="enderecoSmtp" label="Corpo">
            <InputTextarea
              rows={5}
              cols={30}
              placeholder="Informe corpo do e-mail"
              value={this.store.emailTeste?.corpo}
              onChange={(e) => this.store.updateAttributeTesteEmail('corpo', e)}
              autoResize
            />
          </FormField>
        </div>
      </Dialog>
    );
  }

  renderDialog() {
    return (
      <ConfirmDialog
        blockScroll
        visible={this.state.dialogVisibility}
        message="O remetente do serviço de e-mail será atualizado, tem certeza que deseja continuar?"
        header="Atualização do serviço de e-mail"
        icon="pi pi-user"
        acceptClassName="p-button-info"
        onHide={() => this.toggleDialogVisibility()}
        accept={(e) => {
          this.submitFormData(e);
        }}
      />
    );
  }

  renderActionButtons() {
    return (
      <div className="p-mt-10 form-actions">
        <div className="p-mt-2">
          <span className="p-mr-1 p-d-inline p-d-flex align-items-center">
            <PermissionProxy resourcePermissions={this.getWritePermission()}>
              <FcButton
                label="Salvar"
                type="button"
                className="p-ml-auto p-button-primary p-mr-2"
                loading={this.store.loading}
                onClick={() => this.toggleDialogVisibility()}
              />
            </PermissionProxy>
          </span>
        </div>
      </div>
    );
  }

  render() {
    const { submitted } = this.state;
    const { getRule, updateAttribute } = this.store;
    const { submitFormData } = this;

    const breacrumbItems = [
      { label: 'Configuracões de E-mail', url: UrlRouter.administracao.configuracoesEmail.editar },
      { label: 'Editar' },
    ];

    let content;
    if (this.store.object) {
      content = (
        <>
          <AppBreadCrumb items={breacrumbItems} />
          <div className="card page form-action-buttons">
            <form onSubmit={submitFormData}>
              <div className="p-fluid p-formgrid p-grid">
                <FormField
                  columns={4}
                  attribute="enderecoSmtp"
                  label="Endereço SMTP"
                  rule={getRule('enderecoSmtp')}
                  submitted={submitted}
                >
                  <InputText
                    placeholder="Informe o Endereço SMTP"
                    value={this.store.object.enderecoSmtp}
                    onChange={(e) => updateAttribute('enderecoSmtp', e)}
                  />
                </FormField>
                <FormField
                  columns={4}
                  attribute="portaSmtp"
                  label="Porta SMTP"
                  rule={getRule('portaSmtp')}
                  submitted={submitted}
                >
                  <InputNumber
                    onChange={(e) => updateAttribute('portaSmtp', e)}
                    placeholder="Informe a porta SMTP"
                    value={this.store.object.portaSmtp}
                  />
                </FormField>
                <FormField attribute="senha" columns={4} label="Senha" rule={getRule('senha')} submitted={submitted}>
                  <Password
                    onChange={(e) => {
                      this.setState({ tempSenha: e.target.value });
                      updateAttribute('senha', e);
                    }}
                    placeholder="Informe a senha"
                    value={this.state.tempSenha}
                    feedback={false}
                    toggleMask
                  />
                </FormField>
                <FormField
                  attribute="emailRemetente"
                  columns={4}
                  label="E-mail do remetente"
                  rule={getRule('emailRemetente')}
                  submitted={submitted}
                >
                  <InputText
                    onChange={(e) => updateAttribute('emailRemetente', e)}
                    placeholder="Informe o e-mail do remetente"
                    value={this.store.object.emailRemetente}
                  />
                </FormField>
                <FormField
                  attribute="nomeRemetente"
                  columns={4}
                  label="Nome do remetente"
                  rule={getRule('nomeRemetente')}
                  submitted={submitted}
                >
                  <InputText
                    onChange={(e) => updateAttribute('nomeRemetente', e)}
                    placeholder="Informe o nome do remetente"
                    value={this.store.object.nomeRemetente}
                  />
                </FormField>
                <FormField columns={6} attribute="tls" label="TLS" checkbox submitted={submitted}>
                  <Checkbox
                    inputId="tls"
                    checked={this.store.object.tls}
                    onChange={(e) => this.store.updateAttributeCheckbox('tls', e)}
                    id="tls"
                  />
                </FormField>
                <FormField columns={6} attribute="ssl" label="SSL" checkbox submitted={submitted}>
                  <Checkbox
                    inputId="ssl"
                    checked={this.store.object.ssl}
                    onChange={(e) => this.store.updateAttributeCheckbox('ssl', e)}
                    id="ssl"
                  />
                </FormField>
                <FcButton
                  type="button"
                  className="p-button"
                  label="Testar envio de e-mail"
                  style={{ width: '15rem', marginLeft: '7px', marginBottom: '5px', marginRight: '5px' }}
                  icon={PrimeIcons.ENVELOPE}
                  onClick={() => {
                    this.toggleDialogEnvioEmailTesteVisibility();
                  }}
                ></FcButton>
              </div>
              {this.renderActionButtons()}
              {this.state.visibleDialogEnvioEmailTeste && this.renderDialogTeste()}
              {this.state.dialogVisibility && this.renderDialog()}
            </form>
          </div>
        </>
      );
    } else {
      content = (
        <div className="card page">
          <AppBreadCrumb items={breacrumbItems} />
          <i
            className="pi pi-spin pi-spinner"
            style={{
              marginTop: '20px',
              marginLeft: 'calc(50% - 20px)',
              fontSize: '2em',
            }}
          ></i>
        </div>
      );
    }
    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        {content}
      </PermissionProxy>
    );
  }
}

ConfiguracoesEmailFormPage.propTypes = {
  history: PropTypes.any,
};

export default ConfiguracoesEmailFormPage;
