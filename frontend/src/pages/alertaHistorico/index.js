import React from 'react';
import { observer } from 'mobx-react';
import IndexDataTable from 'fc/components/IndexDataTable';
import FcButton from 'fc/components/FcButton';
import AppBreadCrumb from 'fc/components/AppBreadCrumb';
import UrlRouter from '~/constants/UrlRouter';
import GenericIndexPage from 'fc/pages/GenericIndexPage';
import AdvancedSearch from 'fc/components/AdvancedSearch';
import AccessPermission from '~/constants/AccessPermission';
import PermissionProxy from 'fc/components/PermissionProxy';
import AlertaHistoricoIndexStore from '~/stores/alertaHistorico/indexStore';
import {
  checkUserContextIsInspetor,
  generateFullURL,
  checkUserGroup,
  getValue,
  getValueByKey,
  getValueDate,
} from 'fc/utils/utils';
import { DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS } from 'fc/utils/date';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import FcCloseableTabView from 'fc/components/FcCloseableTabView';
import { Dialog } from 'primereact/dialog';
import FcDropdown from 'fc/components/FcDropdown';
import ExportPdfAlertaButton from './exportPdfAlertaButton';

@observer
class AlertaHistoricoIndexPage extends GenericIndexPage {
  constructor(props) {
    super(props, AccessPermission.alerta);
    this.state = { visibleDialogArquivamento: false, idRemove: null, activeTabIndex: 0 };
    this.store = new AlertaHistoricoIndexStore();

    this._toggleShowConfirmDialogArquivamento = this._toggleShowConfirmDialogArquivamento.bind(this);
  }

  _toggleShowConfirmDialogArquivamento() {
    this.setState({ visibleDialogArquivamento: !this.state.visibleDialogArquivamento });
  }

  confirmArquivamento(id) {
    const { reloadTableData } = this.store;

    return (
      <Dialog
        blockScroll
        header="Arquivar"
        visible={this.state.visibleDialogArquivamento}
        style={{ width: '25vw' }}
        onHide={() => this._toggleShowConfirmDialogArquivamento()}
        draggable={false}
        resizable={false}
        footer={
          <div className="p-mt-2">
            <span className="p-mr-1 p-d-inline p-d-flex align-items-center">
              <FcButton
                label="Cancelar"
                type="button"
                className="p-ml-auto p-button-secondary p-mr-2"
                onClick={() => this._toggleShowConfirmDialogArquivamento()}
                loading={this.store.loading}
              />
              <FcButton
                label="Arquivar"
                onClick={() =>
                  this.store.arquivarAlertaAnalise(id, () => {
                    reloadTableData();
                    this._toggleShowConfirmDialogArquivamento();
                  })
                }
                loading={this.store.loading}
              />
            </span>
          </div>
        }
      >
        <div className="p-fluid p-formgrid p-grid">
          <div className="p-col-12">
            <FcDropdown
              inOrder
              style={{ marginTop: '5px', minWidth: '15rem' }}
              optionLabel="label"
              optionValue="value"
              options={DadosEstaticosService.getStatusArquivamento().filter(
                (s) => !['REJEITADO', 'ARQUIVADO_AUTOMATICAMENTE'].includes(s.value)
              )}
              placeholder="Selecione um status"
              value={this.store.statusArquivamento}
              onChange={(e) => {
                this.store.updateStatusArquivamento(e.value);
                this.forceUpdate();
              }}
            />
          </div>
        </div>
      </Dialog>
    );
  }

  _redirectDetail(rowData) {
    if (rowData.tipo === 'L') {
      window.open(
        generateFullURL(UrlRouter.cadastrosConsulta.licitacao.detalhe.replace(':id', rowData.idProcesso)),
        '_blank'
      );
    } else if (rowData.tipo === 'D') {
      window.open(
        generateFullURL(UrlRouter.cadastrosConsulta.dispensa.detalhe.replace(':id', rowData.idProcesso)),
        '_blank'
      );
    } else if (rowData.tipo === 'I') {
      window.open(
        generateFullURL(UrlRouter.cadastrosConsulta.inexigibilidade.detalhe.replace(':id', rowData.idProcesso)),
        '_blank'
      );
    } else if (rowData.tipo === 'C') {
      window.open(
        generateFullURL(UrlRouter.cadastrosConsulta.carona.detalhe.replace(':id', rowData.idProcesso)),
        '_blank'
      );
    } else if (rowData.tipo === 'CR') {
      window.open(
        generateFullURL(UrlRouter.cadastrosConsulta.credenciamento.detalhe.replace(':id', rowData.idProcesso)),
        '_blank'
      );
    }
  }

  getIndexDataTableComponent() {
    const columns = [
      {
        field: 'data',
        header: 'Data de Emissão',
        body: ({ data }) => getValueDate(data, DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS),
        sortable: true,
      },
      checkUserGroup(['Auditor', 'Administrador']) && {
        field: 'usuarioResponsavel',
        header: 'Auditor Responsável',
        body: ({ usuarioResponsavel }) => getValue(usuarioResponsavel?.nome),
        sortable: true,
      },
      {
        field: 'entidade',
        header: 'Entidade',
        body: ({ entidade }) => getValue(entidade?.nome),
        sortable: true,
      },
      {
        field: 'tipoProcesso',
        header: 'Processo',
        body: ({ tipoProcesso }) => getValue(tipoProcesso),
        sortable: true,
      },
      {
        field: 'dataAberturaProcesso',
        header: 'Data de Abertura',
        body: ({ dataAberturaProcesso }) =>
          getValueDate(dataAberturaProcesso, DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS),
        sortable: true,
      },
      {
        field: 'status',
        header: 'Status',
        body: ({ status }) => getValueByKey(status, DadosEstaticosService.getStatusAlertaAnalise()),
        sortable: true,
      },
      {
        style: { width: '165px' },
        body: (rowData) => {
          return (
            <div className="actions p-d-flex p-jc-end">
              <PermissionProxy resourcePermissions={this.getReadPermission()}>
                <FcButton
                  tooltip="Detalhes do Processo"
                  icon="pi pi-external-link"
                  className="p-button-sm p-button-success p-mr-2"
                  onClick={() => this._redirectDetail(rowData)}
                />
              </PermissionProxy>
              <PermissionProxy resourcePermissions={this.getReadPermission()}>
                <FcButton
                  tooltip="Visualizar Alerta"
                  icon="pi pi-eye"
                  className="p-button-sm p-mr-2"
                  onClick={() => this.pushUrlToHistory(UrlRouter.alerta.editar.replace(':id', rowData.id))}
                />
              </PermissionProxy>
              {checkUserGroup(['Auditor', 'Administrador']) && (
                <PermissionProxy resourcePermissions={this.getWritePermission()}>
                  <FcButton
                    tooltip="Arquivar Alerta"
                    icon="pi pi-file"
                    className="p-button-sm p-button-danger p-mr-2"
                    onClick={() => {
                      this.setState({ idRemove: rowData.id });
                      this._toggleShowConfirmDialogArquivamento();
                    }}
                    disabled={
                      !(
                        rowData.status !== 'ARQUIVADO' &&
                        (checkUserContextIsInspetor() || checkUserGroup(['Administrador']))
                      )
                    }
                  />
                </PermissionProxy>
              )}
              <PermissionProxy resourcePermissions={this.getReadPermission()}>
                <ExportPdfAlertaButton data={rowData}></ExportPdfAlertaButton>
              </PermissionProxy>
            </div>
          );
        },
      },
    ];

    const { listKey, loading } = this.store;
    const { getDefaultTableProps } = this;
    return <IndexDataTable columns={columns} value={listKey} loading={loading} {...getDefaultTableProps()} />;
  }

  render() {
    const breacrumbItems = [{ label: 'Alertas' }];
    const tabs = [];
    tabs.push({ id: 0, header: 'Em Aberto', content: this.getIndexDataTableComponent() });
    tabs.push({ id: 1, header: 'Arquivados', content: this.getIndexDataTableComponent() });

    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        <AppBreadCrumb items={breacrumbItems} />
        <div className="card page index-table">
          <AdvancedSearch
            key={this.state.activeTabIndex}
            searchParams={this.store.getAdvancedSearchParams()}
            store={this.store}
            searchFields={
              checkUserGroup('Auditor', 'Administrador')
                ? ['entidade', 'tipoProcesso', 'usuarioResponsavel']
                : ['entidade', 'tipoProcesso']
            }
            filterSuggest={this.store.getFilterSuggest()}
            useOr
          />
          <FcCloseableTabView
            tabs={tabs}
            activeTabIndex={this.state.activeTabIndex}
            onChangeTab={(tab) => {
              this.store.changeAbaVisualizacao(tab.id);
              this.setState({ activeTabIndex: tab.id });
            }}
          />
          {this.state.visibleDialogArquivamento && this.confirmArquivamento(this.state.idRemove)}
        </div>
      </PermissionProxy>
    );
  }
}

AlertaHistoricoIndexPage.displayName = 'AlertaHistoricoIndexPage';

export default AlertaHistoricoIndexPage;
