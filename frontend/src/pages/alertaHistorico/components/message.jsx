import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { Card } from 'primereact/card';
import { Message } from 'primereact/message';
import { Dialog } from 'primereact/dialog';
import FcButton from 'fc/components/FcButton';
import { DATE_FORMAT, DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS } from 'fc/utils/date';
import { checkUserGroup, getFirstAndLastName, getValueDate } from 'fc/utils/utils';
import MultipleFileUploader from 'fc/components/MultipleFileUploader';

const AlertMessage = ({ message, store }) => {
  const { usuario, resposta, data, mensagem, arquivos, prazoResposta } = message;
  const [readFiles, setReadFiles] = useState(false);

  const renderReadFiles = () => {
    return (
      <Dialog
        blockScroll
        header={'Arquivos da Mensagem'}
        visible={readFiles}
        style={{ width: '80vw' }}
        onHide={() => setReadFiles(false)}
        draggable={false}
        resizable={false}
      >
        <MultipleFileUploader
          downloadOnly
          store={message.tdaFiles ? store.readFileStoreTda : store.readFileStore}
          showFileType={false}
          onChangeFiles={(files) => store.setFileList(files)}
        />
      </Dialog>
    );
  };

  return usuario ? (
    <>
      <Card className={resposta ? 'card-default response' : 'card-default alert'}>
        {!checkUserGroup('Jurisdicionado') && (
          <>
            <strong>
              {resposta ? getFirstAndLastName(usuario.nome) : `Auditor: ${getFirstAndLastName(usuario.nome)}`}
            </strong>
            <br />
            <br />
          </>
        )}
        <div dangerouslySetInnerHTML={{ __html: mensagem }} />
        <br />
        {data && <p className="date">{getValueDate(data, DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS)}</p>}

        {!resposta && (
          <Message
            severity="warn"
            text={`Prazo para resposta: ${getValueDate(prazoResposta, DATE_FORMAT)}`}
            className="message"
          />
        )}
        {arquivos && (
          <div className="file-container">
            <FcButton
              tooltip={`${arquivos.length} arquivo(s)`}
              icon="pi pi-file-pdf"
              type="button"
              className="file-button"
              onClick={() => {
                store.setReadFileStore(arquivos, message.tdaFiles, () => setReadFiles(true));
              }}
            />
          </div>
        )}
        {readFiles && renderReadFiles()}
      </Card>
    </>
  ) : (
    <></>
  );
};

AlertMessage.propTypes = {
  message: PropTypes.shape({
    usuario: PropTypes.shape({
      nome: PropTypes.string,
    }),
    resposta: PropTypes.bool,
    data: PropTypes.string,
    mensagem: PropTypes.string,
    arquivos: PropTypes.array,
    prazoResposta: PropTypes.string,
    tdaFiles: PropTypes.bool,
  }),
  store: PropTypes.object,
};

export default AlertMessage;
