import React from 'react';
import { observer } from 'mobx-react';
import PropTypes from 'prop-types';
import GenericFormPage from 'fc/pages/GenericFormPage';
import AppBreadCrumb from 'fc/components/AppBreadCrumb';
import PermissionProxy from 'fc/components/PermissionProxy';
import UrlRouter from '~/constants/UrlRouter';
import AccessPermission from '~/constants/AccessPermission';
import AlertaHistoricoFormStore from '~/stores/alertaHistorico/formStore';
import FcButton from 'fc/components/FcButton';
import { checkUserContextIsInspetor, checkUserGroup } from 'fc/utils/utils';
import AppStore from 'fc/stores/AppStore';
import { ConfirmDialog } from 'primereact/confirmdialog';
import { Dialog } from 'primereact/dialog';
import './style.scss';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import { Link } from 'react-router-dom/cjs/react-router-dom.min';
import FcDropdown from 'fc/components/FcDropdown';
import { Fieldset } from 'primereact/fieldset';
import FormField from 'fc/components/FormField';
import { InputTextarea } from 'primereact/inputtextarea';
import { CKEditor } from '@ckeditor/ckeditor5-react';
import MultipleFileUploader from 'fc/components/MultipleFileUploader';
import { Message } from 'primereact/message';
import TermoEditor from 'fc/editors/termoReferencia';
import AlertScroll from './components/chat';

@observer
class AlertaHistoricoFormPage extends GenericFormPage {
  constructor(props) {
    super(props, UrlRouter.alerta.index, AccessPermission.alerta);
    this.store = new AlertaHistoricoFormStore();
    this.toggleShowConfirmDialogArquivamento = this.toggleShowConfirmDialogArquivamento.bind(this);

    this.state = {
      renderConfirmDialog: false,
      readFiles: false,
      visibleDialogArquivamento: false,
      visibleDialogProcesso: false,
      visibleDialogUpload: false,
    };
  }

  componentDidMount() {
    const { id } = this.props;
    this.store.initialize(id, () => this.forceUpdate());
  }

  toggleShowConfirmDialog() {
    this.setState((oldState) => ({ renderConfirmDialog: !oldState.renderConfirmDialog }));
  }

  _confirmSubmit() {
    return (
      <ConfirmDialog
        blockScroll
        visible={this.state.renderConfirmDialog}
        message="Uma nova mensagem não poderá ser enviada até que esta seja respondida. Deseja prosseguir?"
        header="Confirmar envio"
        icon="pi pi-exclamation-triangle"
        acceptClassName="p-button-danger"
        accept={(e) => this.submitFormData(e)}
        onHide={() => this.toggleShowConfirmDialog()}
      />
    );
  }

  toggleShowConfirmDialogArquivamento() {
    this.setState({ visibleDialogArquivamento: !this.state.visibleDialogArquivamento });
  }

  confirmArquivamento() {
    const { alertaAnalise } = this.store;
    const { toggleShowConfirmDialogArquivamento, _goBack } = this;

    return (
      <Dialog
        blockScroll
        header="Arquivar"
        visible={this.state.visibleDialogArquivamento}
        style={{ width: '25vw' }}
        onHide={() => toggleShowConfirmDialogArquivamento()}
        draggable={false}
        resizable={false}
        footer={
          <div className="p-mt-2">
            <span className="p-mr-1 p-d-inline p-d-flex align-items-center">
              <FcButton
                label="Cancelar"
                type="button"
                className="p-ml-auto p-button-secondary p-mr-2"
                onClick={() => toggleShowConfirmDialogArquivamento()}
                loading={this.store.loading}
              />
              <FcButton
                label="Arquivar"
                onClick={() => {
                  this.store.arquivarAlertaAnalise(alertaAnalise.id, () => {
                    toggleShowConfirmDialogArquivamento();
                    _goBack();
                  });
                }}
                loading={this.store.loading}
              />
            </span>
          </div>
        }
      >
        <div className="p-fluid p-formgrid p-grid">
          <div className="p-col-12">
            <FcDropdown
              inOrder
              className="dropdown-archive"
              optionLabel="label"
              optionValue="value"
              options={DadosEstaticosService.getStatusArquivamento().filter(
                (s) => !['REJEITADO', 'ARQUIVADO_AUTOMATICAMENTE'].includes(s.value)
              )}
              placeholder="Selecione um status"
              value={this.store.statusArquivamento}
              onChange={(e) => {
                this.store.updateStatusArquivamento(e.value);
                this.forceUpdate();
              }}
            />
          </div>
        </div>
      </Dialog>
    );
  }

  _renderValue(label, value, col = 12, type = 'value', url = '#') {
    return (
      <div
        style={{ fontSize: '1.1rem' }}
        className={` details-set details-display p-col-12 p-md-${col} p-sm-${col >= 6 ? col : 6}`}
      >
        <div
          style={{ color: 'rgb(0 0 0 / 50%)', paddingLeft: 0 }}
          className="xl:col-2 lg:col-3 md:col-4 sm:col-4 details-attributte drawer-content-label"
        >
          {label}
        </div>

        {type == 'value' && (
          <div
            style={{ paddingLeft: 0 }}
            className={`xl:col-10 lg:col-9 md:col-8 sm:col-8 details-value p-text-justify`}
          >
            {value ?? '-'}
          </div>
        )}

        {type == 'list' && (
          <div style={{ display: 'block', paddingLeft: 0 }}>
            {value?.map((value) => (
              <div className={`details-value p-text-justify`}>{value ?? '-'}</div>
            ))}
          </div>
        )}

        {type == 'link' && (
          <div
            style={{ paddingLeft: 0 }}
            className={`xl:col-10 lg:col-9 md:col-8 sm:col-8 details-value p-text-justify`}
          >
            {
              <Link target="_blank" rel="noreferrer" style={{ color: '#3F51B5' }} to={url}>
                <u>{value}</u>
              </Link>
            }
          </div>
        )}
      </div>
    );
  }

  _renderOrigemAlerta() {
    const idLicitacao = this.store.alertaAnalise.tdaLicitacao?.idLicitacao;
    const idDispensa = this.store.alertaAnalise.tdaDispensa?.idDispensa;
    const idInexigibilidade = this.store.alertaAnalise.tdaInexigibilidade?.idInexigibilidade;
    const idCarona = this.store.alertaAnalise.tdaCarona?.idCarona;
    const idCredenciamento = this.store.alertaAnalise.tdaCredenciamento?.idCredenciamento;

    let content = <></>;

    const links = {
      L: {
        link: UrlRouter.cadastrosConsulta.licitacao.detalhe.replace(':id', idLicitacao),
        text: this.store.modalidade,
        permission: AccessPermission.licitacao.readPermission,
      },
      D: {
        link: UrlRouter.cadastrosConsulta.dispensa.detalhe.replace(':id', idDispensa),
        text: this.store.modalidade,
      },
      I: {
        link: UrlRouter.cadastrosConsulta.inexigibilidade.detalhe.replace(':id', idInexigibilidade),
        text: this.store.modalidade,
      },
      C: {
        link: UrlRouter.cadastrosConsulta.carona.detalhe.replace(':id', idCarona),
        text: this.store.modalidade,
      },
      CR: {
        link: UrlRouter.cadastrosConsulta.credenciamento.detalhe.replace(':id', idCredenciamento),
        text: this.store.modalidade,
      },
    };
    const typeProcess = this.store.alertaAnalise?.tipo;
    const linkProcesso = links[typeProcess];
    content = this._renderValue('Origem', linkProcesso.text, 12, 'link', linkProcesso.link);

    return content;
  }

  render() {
    const { submitFormData } = this;
    const { submitted } = this.state;
    const { getRule } = this.store;

    const hasWritePermission = !this.getWritePermission() || AppStore.hasPermission(this.getWritePermission());

    const breacrumbItems = [{ label: 'Alertas', url: UrlRouter.alerta.index }, { label: 'Editar' }];

    let content = '';
    if (this.store.object && !this.store.loading && !this.store.loadingProcess) {
      content = (
        <div>
          <AppBreadCrumb items={breacrumbItems} />
          <div className="p-col-12" style={{ float: 'inline-start' }}>
            <Dialog
              header="Dados do Processo"
              visible={this.state.visibleDialogProcesso}
              position="top-right"
              onHide={() => this.setState({ visibleDialogProcesso: false })}
              style={{ minWidth: '500px' }}
            >
              <div>
                <p>{this._renderOrigemAlerta()}</p>
                <p>{this._renderValue('Entidade', this.store.entidade)}</p>
                <p>{this._renderValue('Data de Abertura', this.store.dataAbertura)}</p>
              </div>
            </Dialog>
            <FcButton
              label="Dados do Processo"
              icon="pi pi-list"
              onClick={() => this.setState({ visibleDialogProcesso: true })}
            />
          </div>
          <form onSubmit={submitFormData}>
            <div
              className="p-fluid p-formgrid p-grid"
              style={{
                display: 'flex',
                flexDirection: 'column',
                marginTop: '15px',
                width: '100%',
              }}
            >
              <div style={{ width: 'inherit' }}>
                <AlertScroll store={this.store} />
              </div>
              {this.store._hasAnswer() && (
                <div className="card page form-action-buttons" style={{ backgroundColor: 'rgba(225, 222, 249, 1)' }}>
                  {this.store.mensagemRejeitada && (
                    <div style={{ paddingBottom: '5px' }}>
                      <Fieldset className="p-col-12" legend={<Message severity="error" text="Mensagem Rejeitada" />}>
                        <FormField columns={12} submitted={submitted} label="Mensagem do Inspetor">
                          <InputTextarea value={this.store.mensagemRejeicao} rows={5} cols={30} disabled />
                        </FormField>
                      </Fieldset>
                    </div>
                  )}

                  <FormField
                    columns={12}
                    label="Resposta"
                    attribute="mensagem"
                    rule={getRule('mensagem')}
                    submitted={submitted}
                  >
                    <CKEditor
                      editor={TermoEditor}
                      data={this.store.object.mensagem}
                      onChange={(_, editor) => this.store.updateMensagem(editor)}
                    />
                  </FormField>

                  <div className="footer-answer">
                    {this.store.showFieldsJurisdicionado() && (
                      <div>
                        <Dialog
                          header="Arquivos da Mensagem"
                          visible={this.state.visibleDialogUpload}
                          onHide={() => {
                            this.setState({ visibleDialogUpload: false });
                          }}
                          style={{ width: '90rem' }}
                        >
                          <FormField columns={12} attribute="arquivos">
                            <MultipleFileUploader
                              store={this.store.writeFileStore}
                              showFileType={false}
                              onChangeFiles={(files) => this.store.setFileList(files)}
                              ShowSaveRemoveNotifications
                            />
                          </FormField>
                          {}
                        </Dialog>
                        <FcButton
                          label="Arquivos"
                          icon="pi pi-plus"
                          type="button"
                          style={{ width: 'fit-content', backgroundColor: '#617c4d' }}
                          onClick={() => this.setState({ visibleDialogUpload: true })}
                        />
                      </div>
                    )}
                    <FcButton
                      label="Voltar"
                      type="button"
                      className="p-ml-auto p-button-secondary p-mr-2"
                      onClick={() => this._goBack()}
                      loading={this.store.loading}
                      style={{ width: 'fit-content' }}
                    />
                    {hasWritePermission &&
                      checkUserGroup(['Auditor', 'Administrador']) &&
                      (checkUserContextIsInspetor() || checkUserGroup(['Administrador'])) && (
                        <FcButton
                          label="Arquivar"
                          type="button"
                          className="p-button-danger p-mr-2"
                          loading={this.store.loading}
                          onClick={() => this.toggleShowConfirmDialogArquivamento()}
                          style={{ width: 'fit-content' }}
                        />
                      )}
                    {hasWritePermission && (
                      <FcButton
                        label="Responder"
                        type="button"
                        loading={this.store.loading}
                        disabled={!this.store._hasAnswer()}
                        onClick={() => this.setState({ renderConfirmDialog: true })}
                        style={{ width: 'fit-content' }}
                      />
                    )}
                  </div>
                  {this.state.renderConfirmDialog && this._confirmSubmit()}
                </div>
              )}
            </div>
          </form>
        </div>
      );
    } else {
      content = (
        <div className="card page">
          <AppBreadCrumb items={breacrumbItems} />
          <i className="pi pi-spin pi-spinner" style={{ fontSize: '2em' }}></i>
        </div>
      );
    }

    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        {content}
        {this.state.visibleDialogArquivamento && this.confirmArquivamento()}
      </PermissionProxy>
    );
  }
}

AlertaHistoricoFormPage.propTypes = {
  id: PropTypes.any,
  action: PropTypes.any,
  history: PropTypes.any,
};

export default AlertaHistoricoFormPage;
