import React, { Component } from 'react';
import PropTypes from 'prop-types';
import FcButton from 'fc/components/FcButton';
import { getFirstAndLastName, getOriginUrl, getValueByKey, getValueDate } from 'fc/utils/utils';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import { DATE_FORMAT, DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS } from 'fc/utils/date';
class ExportPdfAlertaButton extends Component {
  constructor(props) {
    super(props);
  }

  printPdf() {
    const w = 1280,
      h = 720;
    const dualScreenLeft = window.screenLeft ?? window.screenX;
    const dualScreenTop = window.screenTop ?? window.screenY;
    const width = window.innerWidth ?? document.documentElement.clientWidth ?? screen.width;
    const height = window.innerHeight ?? document.documentElement.clientHeight ?? screen.height;
    const systemZoom = width / window.screen.availWidth;
    const left = (width - w) / 2 / systemZoom + dualScreenLeft;
    const top = (height - h) / 2 / systemZoom + dualScreenTop;
    const id = this.props.data.id;
    const entidade = this.props.data.entidade.nome;
    const windowPrint = window.open(
      ' ',
      entidade,
      `
    scrollbars=yes,
    width=${w / systemZoom}, 
    height=${h / systemZoom}, 
    top=${top}, 
    left=${left}
    `
    );

    const cssUrl = getOriginUrl() + '/assets/termo-referencia.css';
    const cssCklUrl = getOriginUrl() + '/assets/ck-style.css';

    windowPrint.document.write(`<html><head><title>TRIBUNAL DE CONTAS DO ESTADO DO ACRE</title>`);
    windowPrint.document.write(`<link rel="stylesheet" href="${cssUrl}">`);
    windowPrint.document.write(`<link rel="stylesheet" href="${cssCklUrl}">`);
    windowPrint.document.write('</head><body>');
    windowPrint.document.write(document.getElementById(`div-to-print-${id}`).innerHTML);
    windowPrint.document.write('</body></html>');
    windowPrint.document.close();
    windowPrint.focus();
    setTimeout(() => {
      windowPrint.print();
      windowPrint.close();
    }, 500);
  }

  _renderDadosProcesso() {
    const { tipo, tipoProcesso, entidade, dataAberturaProcesso } = this.props.data;
    const tipoExtraido = getValueByKey(tipo, DadosEstaticosService.getTipoProcesso());

    return (
      <div>
        <h3>1. Dados do Processo: </h3>
        <div>
          <strong>Origem: </strong> {tipoExtraido} - {tipoProcesso}{' '}
        </div>
        <div>
          <strong>Entidade: </strong> {entidade.nome}
        </div>
        <div>
          <strong>Data de Abertura: </strong> {getValueDate(dataAberturaProcesso)}
        </div>
      </div>
    );
  }

  _renderMensagens() {
    const { mensagens } = this.props.data;
    return (
      <div>
        <h3 style={{ marginBottom: '0' }}> 2. Histórico do Alerta: </h3>
        <hr />
        {mensagens.map((mensagem, index) => {
          return (
            <div>
              <div>
                <div
                  style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    padding: '0 5px 0 5px',
                    margin: '0',
                  }}
                >
                  <h4>Mensagem {index + 1}:</h4>

                  <p style={{ fontSize: '0.8rem' }}>
                    Data de envio:{' '}
                    {getValueDate(mensagem.dataMensagem, DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS)}
                  </p>
                </div>
                <div dangerouslySetInnerHTML={{ __html: mensagem.mensagemAuditor }} />
                <span style={{ fontSize: '0.7rem' }}>
                  <i>Prazo para resposta: {getValueDate(mensagem.prazoResposta, DATE_FORMAT)}</i>
                </span>
              </div>
              {mensagem.respostaJurisdicionado && (
                <div>
                  <hr />
                  <p>
                    <strong>Resposta à mensagem {index + 1}:</strong>
                  </p>
                  <div
                    style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      padding: '0 5px 0 5px',
                      margin: '0',
                    }}
                  >
                    <p>
                      <strong>{getFirstAndLastName(mensagem.usuarioResposta.nome)}</strong>
                    </p>
                    {mensagem.dataResposta && (
                      <p style={{ fontSize: '0.8rem' }}>
                        Data de envio:{' '}
                        {getValueDate(mensagem.dataResposta, DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS)}
                      </p>
                    )}
                  </div>

                  <p dangerouslySetInnerHTML={{ __html: mensagem.respostaJurisdicionado }} />

                  {index < mensagens.length - 1 && <hr />}
                </div>
              )}
            </div>
          );
        })}
      </div>
    );
  }

  render() {
    const id = this.props.data.id;
    return (
      <>
        <FcButton
          tooltip="Imprimir Alerta"
          icon="pi pi-print"
          className="p-button-sm p-button-info p-mr-2"
          onClick={() => this.printPdf()}
        />
        <div style={{ display: 'none' }}>
          <div id={`div-to-print-${id}`}>
            <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
              <img
                src={getOriginUrl() + '/assets/images/tceac-small-black.png'}
                alt="Logo"
                style={{ height: '2.5em', marginBottom: '0.5em' }}
              />
            </div>

            {this._renderDadosProcesso()}
            {this._renderMensagens()}
          </div>
        </div>
      </>
    );
  }
}

ExportPdfAlertaButton.defaultProps = {
  data: [],
};

ExportPdfAlertaButton.propTypes = {
  data: PropTypes.array.isRequired,
};

export default ExportPdfAlertaButton;
