import React from 'react';
import { observer } from 'mobx-react';
import IndexDataTable from 'fc/components/IndexDataTable';
import FcButton from 'fc/components/FcButton';
import { PrimeIcons } from 'primereact/api';
import AppBreadCrumb from 'fc/components/AppBreadCrumb';
import UrlRouter from '~/constants/UrlRouter';
import GenericIndexPage from 'fc/pages/GenericIndexPage';
import { getValueByKey, getValueDate } from 'fc/utils/utils';
import AdvancedSearch from 'fc/components/AdvancedSearch';
import { DATE_FORMAT, DATE_PARSE_FORMAT } from 'fc/utils/date';
import AccessPermission from '~/constants/AccessPermission';
import PermissionProxy from 'fc/components/PermissionProxy';
import ComissaoIndexStore from '~/stores/comissao/indexStore';
import DadosEstaticosService from '~/services/DadosEstaticosService';

@observer
class ComissaoIndexPage extends GenericIndexPage {
  constructor(props) {
    super(props, AccessPermission.comissao);
    this.store = new ComissaoIndexStore();
    this.state = {
      idRemove: null,
    };
  }

  render() {
    const columns = [
      {
        field: 'numero',
        header: 'Número',
        sortable: true,
      },
      {
        field: 'tipo',
        header: 'Tipo',
        body: ({ tipo }) => getValueByKey(tipo, DadosEstaticosService.getTipoComissao()),
      },
      {
        field: 'tipoConjunto',
        header: 'Conjunto',
        sortable: true,
        body: ({ tipoConjunto }) => getValueByKey(tipoConjunto, DadosEstaticosService.getTipoConjuntoComissao()),
      },
      {
        field: 'dataVigenciaInicial',
        header: 'Início da Vigência',
        sortable: true,
        body: ({ dataVigenciaInicial }) => getValueDate(dataVigenciaInicial, DATE_FORMAT, DATE_PARSE_FORMAT),
      },
      {
        field: 'dataVigenciaFinal',
        header: 'Fim da Vigência',
        sortable: true,
        body: ({ dataVigenciaFinal }) => getValueDate(dataVigenciaFinal, DATE_FORMAT, DATE_PARSE_FORMAT),
      },
      {
        style: { width: '110px' },
        body: (rowData) => {
          return (
            <div className="actions p-d-flex p-jc-end">
              <PermissionProxy resourcePermissions={this.getWritePermission()}>
                <FcButton
                  icon="pi pi-pencil"
                  className="p-button-sm p-button-success p-mr-2"
                  onClick={() =>
                    this.pushUrlToHistory(UrlRouter.administracao.comissao.editar.replace(':id', rowData.id))
                  }
                />
              </PermissionProxy>
              <PermissionProxy resourcePermissions={this.getWritePermission()}>
                <FcButton
                  icon="pi pi-trash"
                  className="p-button-sm p-button-danger"
                  onClick={() => {
                    this.setState({ idRemove: rowData.id });
                    this.store.toggleShowConfirmDialog();
                  }}
                />
              </PermissionProxy>
            </div>
          );
        },
      },
    ];

    const header = (
      <div className="table-header">
        <PermissionProxy resourcePermissions={this.getWritePermission()}>
          <FcButton
            className="p-button"
            label="Novo"
            style={{ marginBottom: '5px', marginRight: '5px' }}
            icon={PrimeIcons.PLUS}
            onClick={() => this.pushUrlToHistory(UrlRouter.administracao.comissao.novo)}
          />
        </PermissionProxy>
      </div>
    );

    const { listKey, loading } = this.store;
    const { getDefaultTableProps } = this;

    const breacrumbItems = [{ label: 'Comissao' }];

    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        <AppBreadCrumb items={breacrumbItems} />
        <div className="card page index-table">
          <AdvancedSearch
            searchParams={this.store.getAdvancedSearchParams()}
            store={this.store}
            searchFields={['numero']}
            filterSuggest={this.store.getFilterSuggest()}
          />
          <IndexDataTable
            columns={columns}
            value={listKey}
            header={header}
            loading={loading}
            {...getDefaultTableProps()}
          />
          {this.store.isConfirmDialogVisible && this.confirmRemove(this.state.idRemove)}
        </div>
      </PermissionProxy>
    );
  }
}

ComissaoIndexPage.displayName = 'ComissaoIndexPage';

export default ComissaoIndexPage;
