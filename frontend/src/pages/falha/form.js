import React from 'react';
import { observer } from 'mobx-react';
import PropTypes from 'prop-types';
import FalhaFormStore from '~/stores/falha/formStore';
import { InputText } from 'primereact/inputtext';
import UrlRouter from '~/constants/UrlRouter';
import GenericFormPage from 'fc/pages/GenericFormPage';
import AppBreadCrumb from 'fc/components/AppBreadCrumb';
import FormField from 'fc/components/FormField';
import AccessPermission from '~/constants/AccessPermission';
import PermissionProxy from 'fc/components/PermissionProxy';
import FcInputTextarea from 'fc/components/FcInputTextarea';
import FcCalendar from 'fc/components/FcCalendar';

@observer
class FalhaFormPage extends GenericFormPage {
  constructor(props) {
    super(props, UrlRouter.administracao.falha.index, AccessPermission.falha);

    this.store = new FalhaFormStore();
  }

  componentDidMount() {
    const { id } = this.props;
    this.store.initialize(id);
  }

  render() {
    const { submitted } = this.state;
    const { updateAttribute, updateAttributeDateWithHours, getRule } = this.store;
    const { submitFormData } = this;

    const breacrumbItems = [
      { label: 'Falhas', url: UrlRouter.administracao.falha.index },
      { label: this.props.action === 'new' ? 'Novo' : 'Editar' },
    ];

    let content;

    if (this.store.object) {
      content = (
        <>
          <AppBreadCrumb items={breacrumbItems} />
          <div className="card page form-action-buttons">
            <form onSubmit={submitFormData}>
              <div className="p-fluid p-formgrid p-grid">
                <FormField
                  columns={12}
                  attribute="motivo"
                  label="Motivo"
                  rule={getRule('motivo')}
                  submitted={submitted}
                >
                  <InputText
                    onChange={(e) => updateAttribute('motivo', e)}
                    placeholder="Informe o motivo"
                    value={this.store.object.motivo}
                  />
                </FormField>

                <FormField attribute="detalhes" label="Detalhes" rule={getRule('detalhes')} submitted={submitted}>
                  <FcInputTextarea
                    rows={5}
                    cols={30}
                    value={this.store.object.detalhes}
                    onChange={(e) => updateAttribute('detalhes', e)}
                  />
                </FormField>

                <FormField
                  columns={6}
                  attribute="dataInicio"
                  label="Data Início"
                  rule={getRule('dataInicio')}
                  submitted={submitted}
                >
                  <FcCalendar
                    value={this.getDateAttributeValue(this.store.object.dataInicio)}
                    placeholder="Selecione a data início"
                    onChange={(e) => updateAttributeDateWithHours('dataInicio', e)}
                    showTime
                    mask="99/99/9999"
                  />
                </FormField>

                <FormField
                  columns={6}
                  attribute="dataFim"
                  label="Data Fim"
                  rule={getRule('dataFim')}
                  submitted={submitted}
                >
                  <FcCalendar
                    value={this.getDateAttributeValue(this.store.object.dataFim)}
                    placeholder="Selecione a data fim"
                    onChange={(e) => updateAttributeDateWithHours('dataFim', e)}
                    showTime
                    mask="99/99/9999"
                  />
                </FormField>
              </div>
              {this.renderActionButtons()}
            </form>
          </div>
        </>
      );
    } else {
      content = (
        <div className="card page">
          <AppBreadCrumb items={breacrumbItems} />
          <i
            className="pi pi-spin pi-spinner"
            style={{
              marginTop: '20px',
              marginLeft: 'calc(50% - 20px)',
              fontSize: '2em',
            }}
          ></i>
        </div>
      );
    }
    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        {content}
      </PermissionProxy>
    );
  }
}

FalhaFormPage.propTypes = {
  id: PropTypes.any,
  action: PropTypes.any,
  history: PropTypes.any,
};

export default FalhaFormPage;
