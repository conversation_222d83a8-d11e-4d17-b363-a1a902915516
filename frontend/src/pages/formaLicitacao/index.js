import React from 'react';
import { observer } from 'mobx-react';
import FormaLicitacaoIndexStore from '../../stores/formaLicitacao/indexStore';
import IndexDataTable from 'fc/components/IndexDataTable';
import FcButton from 'fc/components/FcButton';
import { PrimeIcons } from 'primereact/api';
import AppBreadCrumb from 'fc/components/AppBreadCrumb';
import UrlRouter from '../../constants/UrlRouter';
import GenericIndexPage from 'fc/pages/GenericIndexPage';
import AdvancedSearch from 'fc/components/AdvancedSearch';
import AccessPermission from '~/constants/AccessPermission';
import PermissionProxy from 'fc/components/PermissionProxy';

@observer
class FormaLicitacaoIndexPage extends GenericIndexPage {
  constructor(props) {
    super(props, AccessPermission.formaLicitacao);
    this.store = new FormaLicitacaoIndexStore();
    this.state = {
      idRemove: null,
    };
  }

  pushUrlToHistory(url) {
    url && this.props.history.push(url);
  }

  render() {
    const columns = [
      {
        field: 'nome',
        header: 'Nome',
        sortable: true,
      },
      {
        field: 'descricao',
        header: 'Descrição',
        sortable: true,
      },
      {
        style: { width: '110px' },
        body: (rowData) => {
          return (
            <div className="actions p-d-flex p-jc-end">
              <PermissionProxy resourcePermissions={this.getReadPermission()}>
                <FcButton
                  icon="pi pi-pencil"
                  className="p-button-sm p-button-success p-mr-2"
                  onClick={() =>
                    this.pushUrlToHistory(UrlRouter.administracao.formaLicitacao.editar.replace(':id', rowData.id))
                  }
                  disabled={rowData.tipoNativo}
                />
              </PermissionProxy>
              <PermissionProxy resourcePermissions={this.getWritePermission()}>
                <FcButton
                  icon="pi pi-trash"
                  className="p-button-sm p-button-danger"
                  onClick={() => {
                    this.setState({ idRemove: rowData.id });
                    this.store.toggleShowConfirmDialog();
                  }}
                  disabled={rowData.tipoNativo}
                />
              </PermissionProxy>
            </div>
          );
        },
      },
    ];

    const header = (
      <div className="table-header">
        <PermissionProxy resourcePermissions={this.getWritePermission()}>
          <FcButton
            className="p-button"
            label="Novo"
            style={{ marginBottom: '5px', marginRight: '5px' }}
            icon={PrimeIcons.PLUS}
            onClick={() => this.pushUrlToHistory(UrlRouter.administracao.formaLicitacao.novo)}
          />
        </PermissionProxy>
        {this.renderTableDataExport(columns, 'formasDeLicitacao')}
      </div>
    );

    const { listKey, loading } = this.store;
    const { getDefaultTableProps } = this;

    const breacrumbItems = [{ label: 'Formas de Licitação' }];

    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        <AppBreadCrumb items={breacrumbItems} />
        <div className="card page index-table">
          <AdvancedSearch
            searchParams={this.store.getAdvancedSearchParams()}
            store={this.store}
            searchFields={['nome']}
          />
          <IndexDataTable
            setColumnsToExport={this.setColumnsToExport}
            columns={columns}
            value={listKey}
            header={header}
            loading={loading}
            {...getDefaultTableProps()}
          />
          {this.store.isConfirmDialogVisible && this.confirmRemove(this.state.idRemove)}
        </div>
      </PermissionProxy>
    );
  }
}

FormaLicitacaoIndexPage.displayName = 'FormaLicitacaoIndexPage';

export default FormaLicitacaoIndexPage;
