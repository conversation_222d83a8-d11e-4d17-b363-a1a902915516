import React from 'react';
import { observer } from 'mobx-react';
import PropTypes from 'prop-types';
import FormaLicitacaoFormStore from '../../stores/formaLicitacao/formStore';
import { InputText } from 'primereact/inputtext';
import UrlRouter from '../../constants/UrlRouter';
import GenericFormPage from 'fc/pages/GenericFormPage';
import AppBreadCrumb from 'fc/components/AppBreadCrumb';
import FormField from 'fc/components/FormField';
import AccessPermission from '~/constants/AccessPermission';
import PermissionProxy from 'fc/components/PermissionProxy';
import FcInputTextarea from 'fc/components/FcInputTextarea';

@observer
class FormaLicitacaoFormPage extends GenericFormPage {
  constructor(props) {
    super(props, UrlRouter.administracao.formaLicitacao.index, AccessPermission.formaLicitacao);

    this.store = new FormaLicitacaoFormStore();
  }

  componentDidMount() {
    const { id } = this.props;
    this.store.initialize(id, { tipoNativo: false });
  }

  render() {
    const { submitted } = this.state;
    const { getRule } = this.store;
    const { submitFormData } = this;

    const breacrumbItems = [
      { label: 'Formas de Licitação', url: UrlRouter.administracao.formaLicitacao.index },
      { label: this.props.action === 'new' ? 'Novo' : 'Editar' },
    ];

    let content;

    if (this.store.object) {
      content = (
        <>
          <AppBreadCrumb items={breacrumbItems} />
          <div className="card page form-action-buttons">
            <form onSubmit={submitFormData}>
              <div className="p-fluid p-formgrid p-grid">
                <FormField columns={6} attribute="nome" label="Nome" rule={getRule('nome')} submitted={submitted}>
                  <InputText
                    onChange={(e) => this.store.updateAttribute('nome', e)}
                    placeholder="Informe o nome"
                    value={this.store.object.nome}
                    id="nome"
                  />
                </FormField>

                <FormField attribute="descricao" label="Descrição" rule={getRule('descricao')} submitted={submitted}>
                  <FcInputTextarea
                    rows={5}
                    cols={30}
                    value={this.store.object.descricao}
                    onChange={(e) => this.store.updateAttribute('descricao', e)}
                  />
                </FormField>
              </div>
              {this.renderActionButtons()}
            </form>
          </div>
        </>
      );
    } else {
      content = (
        <div className="card page">
          <AppBreadCrumb items={breacrumbItems} />
          <i
            className="pi pi-spin pi-spinner"
            style={{
              marginTop: '20px',
              marginLeft: 'calc(50% - 20px)',
              fontSize: '2em',
            }}
          ></i>
        </div>
      );
    }
    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        {content}
      </PermissionProxy>
    );
  }
}

FormaLicitacaoFormPage.propTypes = {
  id: PropTypes.any,
  action: PropTypes.any,
  history: PropTypes.any,
};

export default FormaLicitacaoFormPage;
