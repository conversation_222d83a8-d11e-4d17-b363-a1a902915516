/* eslint-disable unused-imports/no-unused-vars */
import React, { useEffect, useRef, useState } from 'react';
import classNames from 'classnames';
import { useHistory } from 'react-router-dom';
import StatusSistemaService from '~/services/StatusSistemaService';

import AppTopbar from '~/components/AppTopbar';
import AppMenu from '~/components/AppMenu';
import AppRightMenu from '~/components/AppRightMenu';

import PrimeReact from 'primereact/api';

import 'primereact/resources/primereact.min.css';
import 'primeicons/primeicons.css';
import 'primeflex/primeflex.css';
import '../App.scss';

import { Toast } from 'primereact/toast';
import AppStore from 'fc/stores/AppStore';
import PropTypes from 'prop-types';
import MenusItems from '~/constants/MenuItems';
import {
  getOriginUrl,
  textToObject,
  getMenuAuthorized,
  showNotification,
  showErrorNotification,
  isValueValid,
  getValueByKey,
  getValue,
  getValueDate,
  checkuserContextIsAuditor,
  checkUserContextIsInspetor,
} from 'fc/utils/utils';
import { runInAction } from 'mobx';
import GrupoUsuarioService from '~/services/GrupoUsuarioService';
import NotificacaoService from '~/services/NotificacaoService';
import EntradaService from '~/services/EntradaService';
import UrlRouter from '~/constants/UrlRouter';
import { Card } from 'primereact/card';
import { ProgressBar } from 'primereact/progressbar';
import { Dialog } from 'primereact/dialog';
import { Divider } from 'primereact/divider';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import FcButton from 'fc/components/FcButton';
import { InputText } from 'primereact/inputtext';
import { checkUserGroup } from 'fc/utils/utils';
import AlertaAnaliseEntidadeViewService from '~/services/AlertaAnaliseEntidadeViewService';
import SearchOperators from 'fc/components/AdvancedSearch/SearchOperators';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import { DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS } from 'fc/utils/date';
import { useRightbarContext } from 'fc/hooks/useRightbarContext';

export const RTLContext = React.createContext();

const Template = ({ children }) => {
  const [menuMode, setMenuMode] = useState('static');
  const [desktopMenuActive, setDesktopMenuActive] = useState(true);
  const [mobileMenuActive, setMobileMenuActive] = useState(false);
  const [activeTopbarItem, setActiveTopbarItem] = useState(null);
  const [colorMode, setColorMode] = useState('light');
  const [rightMenuActive, setRightMenuActive] = useState(false);
  const [menuActive, setMenuActive] = useState(false);
  const [inputStyle, setInputStyle] = useState('filled');
  const [isRTL, setRTL] = useState(false);
  const [ripple, setRipple] = useState(true);
  const [mobileTopbarActive, setMobileTopbarActive] = useState(false);
  const [menuTheme, setMenuTheme] = useState('light');
  const [topbarTheme, setTopbarTheme] = useState('blue');
  const [isInputBackgroundChanged, setIsInputBackgroundChanged] = useState(false);
  const [inlineMenuActive, setInlineMenuActive] = useState({});
  const [newThemeLoaded, setNewThemeLoaded] = useState(false);
  const [searchActive, setSearchActive] = useState(false);
  const [loading, setLoading] = useState(true);
  const [isLoginPage, setIsLoginPage] = useState(true);
  const [permissions, setPermissions] = useState(null);
  const [visibleEntityDialog, setVisibleEntityDialog] = useState(false);
  const [selectedEntity, setSelectedEntity] = useState('');
  const [dialogSelectedEntity, setDialogSelectedEntity] = useState('');
  const [entitiesFilters, setEntitiesFilters] = useState([]);
  const [entities, setEntities] = useState([]);
  const [entityFilter, setEntityFilter] = useState('');
  const [hasNoContextEntity, setHasNoContextEntity] = useState(false);
  const [menus, setMenus] = useState(MenusItems);
  const [userGroups, setUserGroups] = useState([]);
  const [notifications, setNotifications] = useState([]);
  const [visibleWarningsDialog, setVisibleWarningsDialog] = useState(false);
  const [openWarnings, setOpenWarnings] = useState([]);
  const [notificationsIntervalId, setNotificationIntervalId] = useState(null);
  const { rightbarContent, rightbarActive } = useRightbarContext();
  const [isNewSession, setIsNewSession] = useState(() => {
    const tokenAtual = localStorage.getItem('tokenAtual');
    const token = localStorage.getItem('token');
    if (tokenAtual === token) {
      return false;
    } else {
      localStorage.setItem('tokenAtual', token);
      return true;
    }
  });

  const history = useHistory();
  let currentInlineMenuKey = useRef(null);
  const notification = useRef();

  PrimeReact.ripple = true;

  let searchClick;
  let topbarItemClick;
  let menuClick;
  let inlineMenuClick;

  const origin = getOriginUrl();

  useEffect(() => {
    if (entityFilter)
      setEntitiesFilters(entities?.filter((e) => e.nome?.toLowerCase().includes(entityFilter.toLowerCase())));
    else setEntitiesFilters(entities);
  }, [entityFilter, entities]);

  useEffect(() => {
    setMenus(getMenuAuthorized(MenusItems));
  }, [permissions]);

  useEffect(() => {
    if (menuMode === 'overlay') {
      hideOverlayMenu();
    }
    if (menuMode === 'static') {
      setDesktopMenuActive(true);
    }
  }, [menuMode]);

  useEffect(() => {
    const tokenAtual = localStorage.getItem('tokenAtual');
    const token = localStorage.getItem('token');
    if (tokenAtual === token) {
      setIsNewSession(false);
    } else {
      setIsNewSession(true);
      localStorage.setItem('tokenAtual', token);
    }
  }, [localStorage.getItem('token')]);

  useEffect(() => {
    if (!AppStore.notificationRefFunction) {
      AppStore.setNotificationRefFunction(getNotificationComponent);
    }
    if (!localStorage.getItem('token') || !localStorage.getItem('userDetails')) {
      localStorage.removeItem('token');
      localStorage.removeItem('userDetails');
      localStorage.removeItem('entity');
      const url = history.location.pathname;
      if (url !== '/login' && url !== '/') {
        history.push('/login?callback=' + encodeURIComponent(url));
      } else {
        const queryUrl = history.location.search;
        history.push(queryUrl ? '/login/' + queryUrl : '/login');
      }
    } else {
      AppStore.setData('userDetails', textToObject(localStorage.getItem('userDetails')));
      const userDetails = textToObject(localStorage.getItem('userDetails'));
      const entity = localStorage.getItem(`entity-${userDetails.id}`);
      if (entity !== undefined && entity !== null) {
        setSelectedEntity(+entity);
        AppStore.setData('selectedContextEntity', +entity);
      } else {
        AppStore.setData('contextEntities', []);
        setSelectedEntity('');
        AppStore.setData('selectedContextEntity', undefined);
      }
    }
    initializeApplication();
    onColorModeChange(colorMode);
  }, [isLoginPage]);

  useEffect(() => {
    if (
      (history.location.pathname.includes(UrlRouter.login) && !isLoginPage) ||
      (!history.location.pathname.includes(UrlRouter.login) && isLoginPage)
    ) {
      setIsLoginPage(!isLoginPage);
    }
  }, [history.location.pathname]);

  function initializeNotificationJob() {
    const intervalId = setInterval(() => {
      NotificacaoService.getAll()
        .then((response) =>
          runInAction(() => {
            setNotifications(response.data);
          })
        )
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        );
    }, 30000);
    setNotificationIntervalId(intervalId);
  }

  function initializeApplication() {
    setLoading(true);

    const promises = [];
    if (!isLoginPage) promises.push(StatusSistemaService.getStatus());
    const userDetails = AppStore.getData('userDetails');
    if (!isLoginPage && userDetails) {
      promises.push(EntradaService.getContextEntities());
      promises.push(GrupoUsuarioService.carregarGruposUsuarioLogado());
      promises.push(NotificacaoService.getAll());
    }

    Promise.all(promises)
      .then((responses) =>
        runInAction(() => {
          if (responses[1]) {
            const contextEntities = responses[1].data;
            AppStore.setData('contextEntities', contextEntities);
            setEntities(contextEntities);
            if (contextEntities?.length > 0) {
              const entity = localStorage.getItem(`entity-${userDetails.id}`);
              if (entity !== undefined && !contextEntities?.some((entidade) => entidade.id === +entity)) {
                localStorage.removeItem(`entity-${userDetails.id}`);
                setSelectedEntity('');
                AppStore.setData('selectedContextEntity', undefined);
              }
              setHasNoContextEntity(false);
              !AppStore.getContextEntity() && showContextEntityDialog();
            } else {
              setHasNoContextEntity(true);
              AppStore.setData('hasNoContextEntity', true);
            }
          }

          if (responses[2]) {
            const data = responses[2].data;
            setUserGroups(data);
            AppStore.setData('userGroups', data);
            const permissions = getAllPermissionsFromUserGroups(data);
            AppStore.setData('permissions', permissions);
            setPermissions(permissions);
            (AppStore.getContextEntity() || checkUserGroup('Auditor')) && getPendingWarnings();
          }

          if (responses[3]) {
            const data = responses[3].data;
            setNotifications(data);
            initializeNotificationJob();
          }
        })
      )
      .catch((error) =>
        runInAction(() => {
          if (error && error.response && error.response.status === 401) {
            const queryUrl = history.location.search;
            history.push(queryUrl ? '/login/' + queryUrl : '/login');
          } else if (error && error.response && error.response.status === 500) {
            history.push('/');
          }
        })
      )
      .finally(() =>
        runInAction(() => {
          setLoading(false);
        })
      );
  }

  const getPendingWarnings = () => {
    const isAuditor = checkUserGroup('Auditor');
    const isJurisdicionado = checkUserGroup('Jurisdicionado');
    const isInspetor = checkuserContextIsAuditor() && checkUserContextIsInspetor();
    const filtro = {
      page: { index: 1, size: 100 },
      sort: {
        by: 'data',
        order: 'desc',
      },
      andParameters: [],
    };

    if (isAuditor && !isInspetor) {
      filtro.andParameters.push({
        field: 'usuarioResponsavel',
        operator: SearchOperators.EQUAL_TO.value,
        value: AppStore.getData('userDetails')?.id,
      });
      filtro.andParameters.push({
        field: 'status',
        operator: SearchOperators.EQUAL_TO.value,
        value: 'RESPONDIDO',
      });
    } else if (isInspetor) {
      filtro.andParameters.push({
        field: 'status',
        operator: SearchOperators.EQUAL_TO.value,
        value: 'INSPETOR',
      });
    } else {
      filtro.andParameters.push({
        field: 'status',
        operator: SearchOperators.EQUAL_TO.value,
        value: 'JURISDICIONADO',
      });
    }

    isJurisdicionado &&
      AppStore.getContextEntity() &&
      filtro.andParameters.push({
        field: 'entidade',
        operator: SearchOperators.EQUAL_TO.value,
        value: AppStore.getContextEntity()?.id,
      });

    (isJurisdicionado || isAuditor || isInspetor) &&
      AppStore.noNeedWaitContextEntity() &&
      AlertaAnaliseEntidadeViewService.advancedSearch(filtro)
        .then((responses) =>
          runInAction(() => {
            setOpenWarnings(responses.data.items);
            responses.data?.items?.length && showWarningsDialog();
          })
        )
        .catch((error) =>
          runInAction(() => {
            showErrorNotification(error);
          })
        );
  };

  const getAllPermissionsFromUserGroups = (userGroups = []) => {
    const permissions = new Set();
    userGroups.forEach((userGroup) => {
      const { permissoes } = userGroup;
      if (permissoes) {
        permissoes.forEach(({ nome }) => permissions.add(nome));
      }
    });
    return [...permissions];
  };

  useEffect(() => {
    const appLogoLink = document.getElementById('app-logo');
    if (appLogoLink) {
      appLogoLink.src = origin + '/assets/images/logo-licon.png';
    }
  }, [topbarTheme]);

  const onColorModeChange = (mode) => {
    setColorMode(mode);
    setIsInputBackgroundChanged(true);

    if (isInputBackgroundChanged) {
      if (mode === 'dark') {
        setInputStyle('filled');
      } else {
        setInputStyle('outlined');
      }
    }

    if (mode === 'dark') {
      setMenuTheme('dark');
      setTopbarTheme('dark');
    } else {
      setMenuTheme('light');
      setTopbarTheme('blue');
    }

    const layoutLink = document.getElementById('layout-css');

    const layoutHref = getOriginUrl() + `/assets/layout/css/layout-${mode}.css`;
    replaceLink(layoutLink, layoutHref);

    const themeLink = document.getElementById('theme-css');
    const themeHref = getOriginUrl() + `/assets/theme/indigo/theme-${mode}.css`;

    replaceLink(themeLink, themeHref, () => {
      setNewThemeLoaded(true);
    });
  };

  const replaceLink = (linkElement, href, callback) => {
    if (isIE()) {
      linkElement.setAttribute('href', href);

      if (callback) {
        callback();
      }
    } else {
      const id = linkElement.getAttribute('id');
      const cloneLinkElement = linkElement.cloneNode(true);

      cloneLinkElement.setAttribute('href', href);
      cloneLinkElement.setAttribute('id', id + '-clone');

      linkElement.parentNode.insertBefore(cloneLinkElement, linkElement.nextSibling);

      cloneLinkElement.addEventListener('load', () => {
        linkElement.remove();
        cloneLinkElement.setAttribute('id', id);

        if (callback) {
          callback();
        }
      });
    }
  };

  const onMenuClick = () => {
    menuClick = true;
  };

  const onMenuButtonClick = (event) => {
    menuClick = true;

    if (isDesktop()) setDesktopMenuActive((prevState) => !prevState);
    else setMobileMenuActive((prevState) => !prevState);

    event.preventDefault();
  };

  const onTopbarItemClick = (event) => {
    topbarItemClick = true;
    if (activeTopbarItem === event.item) setActiveTopbarItem(null);
    else {
      setActiveTopbarItem(event.item);
    }

    event.originalEvent.preventDefault();
  };

  const onSearch = (event) => {
    searchClick = true;
    setSearchActive(event);
  };

  const onMenuItemClick = (event) => {
    if (!event.item.items && (menuMode === 'overlay' || !isDesktop())) {
      hideOverlayMenu();
    }

    if (!event.item.items && (isHorizontal() || isSlim())) {
      setMenuActive(false);
    }
  };

  const onRootMenuItemClick = () => {
    setMenuActive((prevState) => !prevState);
  };

  const onRightMenuButtonClick = () => {
    setRightMenuActive((prevState) => !prevState);
  };

  const onMobileTopbarButtonClick = (event) => {
    setMobileTopbarActive((prevState) => !prevState);
    event.preventDefault();
  };

  const onDocumentClick = (event) => {
    if (!searchClick && event.target.localName !== 'input') {
      setSearchActive(false);
    }

    if (!topbarItemClick) {
      setActiveTopbarItem(null);
    }

    if (!menuClick && (menuMode === 'overlay' || !isDesktop())) {
      if (isHorizontal() || isSlim()) {
        setMenuActive(false);
      }
      hideOverlayMenu();
    }

    if (inlineMenuActive[currentInlineMenuKey.current] && !inlineMenuClick) {
      const menuKeys = { ...inlineMenuActive };
      menuKeys[currentInlineMenuKey.current] = false;
      setInlineMenuActive(menuKeys);
    }

    if (!menuClick && (isSlim() || isHorizontal())) {
      setMenuActive(false);
    }

    searchClick = false;
    topbarItemClick = false;
    inlineMenuClick = false;
    menuClick = false;
  };

  const hideOverlayMenu = () => {
    setMobileMenuActive(false);
    setDesktopMenuActive(false);
  };

  const isDesktop = () => {
    return window.innerWidth > 1024;
  };

  const isHorizontal = () => {
    return menuMode === 'horizontal';
  };

  const isSlim = () => {
    return menuMode === 'slim';
  };

  const isIE = () => {
    return /(MSIE|Trident\/|Edge\/)/i.test(window.navigator.userAgent);
  };

  const layoutContainerClassName = classNames(
    'layout-wrapper ',
    'layout-menu-' + menuTheme + ' layout-topbar-' + topbarTheme,
    {
      'layout-menu-static': menuMode === 'static',
      'layout-menu-overlay': menuMode === 'overlay',
      'layout-menu-slim': menuMode === 'slim',
      'layout-menu-horizontal': menuMode === 'horizontal',
      'layout-menu-active': desktopMenuActive,
      'layout-menu-mobile-active': mobileMenuActive,
      'layout-rightbar-collapsed': !rightbarActive,
      'layout-rightbar-active': true,
      'layout-topbar-mobile-active': mobileTopbarActive,
      'layout-rightmenu-active': rightMenuActive,
      'p-input-filled': inputStyle === 'filled',
      'p-ripple-disabled': !ripple,
      'layout-rtl': isRTL,
    }
  );

  const getNotificationComponent = () => {
    return notification.current;
  };

  const renderNotificationComponent = () => {
    return <Toast ref={(e) => (notification.current = e)} />;
  };

  const handleSelecionaEntidade = (entidade) => {
    setDialogSelectedEntity(entidade);
    const userDetails = textToObject(localStorage.getItem('userDetails'));
    AppStore.setEntityChanged(selectedEntity !== entidade.id);
    setVisibleEntityDialog(false);
    setSelectedEntity(entidade.id);
    localStorage.setItem('entity', entidade.id);
    AppStore.setData('selectedContextEntity', getSelectedEntity());
    localStorage.setItem(`entity-${userDetails.id}`, entidade.id);
    history.push('/');
    window.location.reload();
    getPendingWarnings();
  };

  const _renderEntitySelectionDialog = () => {
    return (
      <Dialog
        header="Seleção de Entidade"
        style={{ width: '50vw' }}
        draggable={false}
        visible={visibleEntityDialog}
        closable={false}
      >
        <Divider />
        <div className="p-fluid p-formgrid p-grid">
          <div className="p-field p-col-12">
            <span className="p-float-label">
              <InputText
                id="entity-filter"
                onChange={(e) => {
                  setEntityFilter(e.target.value);
                }}
                value={entityFilter}
              />
              <label htmlFor="entity-filter">Nome</label>
            </span>
          </div>
          <div className="p-field p-col-12">
            <DataTable
              rowHover
              selectionMode="single"
              selection={dialogSelectedEntity}
              value={entitiesFilters}
              className="p-datatable-sm"
              paginator
              rows={5}
              emptyMessage={'Nenhuma entidade encontrada'}
              onSelectionChange={(e) => handleSelecionaEntidade(e.value)}
            >
              <Column key="col-nome" field="nome" />
            </DataTable>
          </div>
          <div className="p-field p-col-12">
            <span style={{ float: 'right' }} className="p-d-flex">
              {selectedEntity && (
                <FcButton
                  label="Cancelar"
                  type="button"
                  className="p-ml-auto p-button-secondary p-mr-2"
                  onClick={() => {
                    setVisibleEntityDialog(false);
                    setDialogSelectedEntity(selectedEntity);
                  }}
                />
              )}
            </span>
          </div>
        </div>
      </Dialog>
    );
  };

  const _renderWarningsDialog = () => {
    return (
      <Dialog
        header="Alertas em Aberto"
        style={{ width: '80vw' }}
        draggable={false}
        visible={visibleWarningsDialog && openWarnings?.length && isNewSession}
        closable={false}
      >
        <div className="p-fluid p-formgrid p-grid">
          <div className="p-field p-col-12">
            <DataTable
              rowHover
              value={openWarnings}
              className="p-datatable-sm"
              paginator
              rows={5}
              emptyMessage={'Nenhum alerta em aberto'}
            >
              <Column
                header="Data de Emissão"
                key="col-data"
                field="data"
                body={({ data }) => getValueDate(data, DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS)}
              />
              {checkUserGroup('Auditor') && (
                <Column
                  header="Auditor Responsável"
                  key="col-usuarioResponsavel"
                  field="usuarioResponsavel"
                  body={({ usuarioResponsavel }) => getValue(usuarioResponsavel?.nome)}
                />
              )}
              <Column
                header="Entidade"
                key="col-entidade"
                field="entidade"
                body={({ entidade }) => getValue(entidade?.nome)}
              />
              <Column
                header="Processo"
                key="col-tipoProcesso"
                field="tipoProcesso"
                body={({ tipoProcesso }) => getValue(tipoProcesso)}
              />
              <Column
                header="Data de Abertura"
                key="col-dataAberturaProcesso"
                field="dataAberturaProcesso"
                body={({ dataAberturaProcesso }) =>
                  getValueDate(dataAberturaProcesso, DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS)
                }
              />
              <Column
                header="Status"
                key="col-status"
                field="status"
                body={({ status }) => getValueByKey(status, DadosEstaticosService.getStatusAlertaAnalise())}
              />
              <Column
                style={{ width: '150px' }}
                header=""
                key="col-acoes"
                field=""
                body={(rowData) => {
                  return (
                    <FcButton
                      icon="pi pi-eye"
                      label="Visualizar Alerta"
                      className="p-button-sm p-button-success p-mr-2"
                      onClick={() => {
                        checkuserContextIsAuditor() && checkUserContextIsInspetor()
                          ? rowData?.id && history.push(UrlRouter.auditoria.alertasInspetor.index)
                          : rowData?.id && history.push(UrlRouter.alerta.editar.replace(':id', rowData?.id));
                        setVisibleWarningsDialog(false);
                      }}
                    />
                  );
                }}
              />
            </DataTable>
          </div>
          <div className="p-field p-col-12">
            <span style={{ float: 'right' }} className="p-d-flex">
              <FcButton
                label="Fechar"
                type="button"
                className="p-ml-auto p-button-secondary"
                onClick={() => {
                  setVisibleWarningsDialog(false);
                }}
              />
            </span>
          </div>
        </div>
      </Dialog>
    );
  };
  const _renderLoadingScreen = () => {
    const origin = getOriginUrl();
    return (
      <Card
        style={{
          width: '370px',
          height: '140px',
          margin: 'auto',
          top: 'calc(50vh - 60px)',
          left: 'calc(50vw - 185px)',
          position: 'fixed',
        }}
      >
        <div style={{ marginBottom: '20px' }}>
          <img
            id="app-logo"
            src={origin + '/assets/images/logo-licon.png'}
            alt="ultima-layout"
            style={{ height: '80px' }}
          />
        </div>
        <ProgressBar mode="indeterminate" style={{ height: '8px' }} />
      </Card>
    );
  };

  const getSelectedEntity = () => {
    const filtered = AppStore.getData('contextEntities').filter((entity) => entity.id === selectedEntity);
    return filtered.length > 0 ? filtered[0] : null;
  };

  const getSelectedEntityLabel = () => {
    const entity = getSelectedEntity();
    return entity ? entity.nome : '-';
  };

  const showContextEntityDialog = () => {
    setDialogSelectedEntity(AppStore.getContextEntity());
    setVisibleEntityDialog(true);
  };

  const showWarningsDialog = () => {
    setVisibleWarningsDialog(true);
  };

  const changeEntityAndRedirect = (entityId, link) => {
    const userDetails = textToObject(localStorage.getItem('userDetails'));
    if (entityId) {
      setSelectedEntity(entityId);
      localStorage.setItem('entity', entityId);
      AppStore.setData('selectedContextEntity', getSelectedEntity());
      localStorage.setItem(`entity-${userDetails.id}`, entityId);
    }
    history.push(link);
  };

  const _updateStatusNotifications = (notificacoes, status) => {
    NotificacaoService.updateStatus({ notificacoes, status }).then((response) =>
      runInAction(() => {
        setNotifications(response.data);
      })
    );
  };

  const onClickNotification = (notificacao) => {
    const { link, entidade } = notificacao;
    if (isValueValid(link)) {
      _updateStatusNotifications([notificacao], 'CLICADA');
      changeEntityAndRedirect(entidade?.id, link);
    } else {
      showNotification('info', 'A notificação em questão não possui ação ao ser clicada!');
    }
  };

  const onViewNotifications = (notificacoes) => {
    const notificacoesToUpdate = notificacoes.filter((n) => n.status === 'EMITIDA');
    notificacoesToUpdate.length > 0 && _updateStatusNotifications(notificacoesToUpdate, 'VIZUALIZADA');
  };

  const onClearNotifications = (notifications) => {
    setNotifications([]);
    _updateStatusNotifications(notifications, 'REMOVIDA');
  };

  const renderChildrenWithTemplate = () => {
    if (!permissions) return _renderLoadingScreen();
    const userDetails = textToObject(localStorage.getItem('userDetails') ?? '');
    return (
      <>
        <AppTopbar
          horizontal={isHorizontal()}
          activeTopbarItem={activeTopbarItem}
          onMenuButtonClick={onMenuButtonClick}
          onTopbarItemClick={onTopbarItemClick}
          onRightMenuButtonClick={onRightMenuButtonClick}
          onMobileTopbarButtonClick={onMobileTopbarButtonClick}
          mobileTopbarActive={mobileTopbarActive}
          searchActive={searchActive}
          onSearch={onSearch}
          onClickEntityButton={() => showContextEntityDialog()}
          selectedEntityLabel={getSelectedEntityLabel()}
          showEntityButton={!hasNoContextEntity}
          userDetails={userDetails}
          userGroups={userGroups}
          notifications={notifications}
          onClickNotification={onClickNotification}
          onViewNotifications={onViewNotifications}
          onClearNotifications={onClearNotifications}
          onLogout={() => notificationsIntervalId && clearInterval(notificationsIntervalId)}
        />

        <div className="menu-wrapper" onClick={onMenuClick}>
          <div className="layout-menu-container">
            <AppMenu
              model={menus}
              onMenuItemClick={onMenuItemClick}
              onRootMenuItemClick={onRootMenuItemClick}
              menuMode={menuMode}
              active={menuActive}
            />
          </div>
        </div>
        <div className="layout-main">
          <div className="layout-content">
            {renderNotificationComponent()}
            {children}
          </div>
        </div>
        <div className="rightbar-wrapper">
          <div className="layout-rightbar-container">{rightbarContent}</div>
        </div>
        {_renderEntitySelectionDialog()}
        {_renderWarningsDialog()}
      </>
    );
  };

  return loading ? (
    _renderLoadingScreen()
  ) : (
    <RTLContext.Provider value={isRTL}>
      <div className={layoutContainerClassName} onClick={onDocumentClick}>
        {isLoginPage ? children : renderChildrenWithTemplate()}
        <AppRightMenu rightMenuActive={rightMenuActive} onRightMenuButtonClick={onRightMenuButtonClick} />
        {mobileMenuActive && <div className="layout-mask modal-in"></div>}
      </div>
    </RTLContext.Provider>
  );
};

Template.propTypes = {
  children: PropTypes.any,
};

export default Template;
