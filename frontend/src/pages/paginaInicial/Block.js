import { Chart } from 'primereact/chart';
import PropTypes from 'prop-types';
import React from 'react';
import { Link } from 'react-router-dom';
import FcButton from 'fc/components/FcButton';
import PermissionProxy from 'fc/components/PermissionProxy';
import AppStore from 'fc/stores/AppStore';
import { Skeleton } from 'primereact/skeleton';

class Block extends React.Component {
  constructor(props) {
    super(props);
    this.chartRef = React.createRef();
  }
  calculaOrdemPreco(price) {
    const umMilhao = 10 ** 6;
    const umBilhao = 10 ** 9;
    const ordemMil = price / umMilhao < 1;
    const ordemMilhao = price / umBilhao < 1;
    const ordemBilhao = price / umBilhao >= 1;
    if (price === 'NaN') {
      return '...';
    }
    if (price == 0) {
      return 'R$ ' + price + ',00';
    } else if (ordemMil) {
      return ('R$ ' + (price / 10 ** 3).toFixed(2) + ' mil').replace('.', ',');
    } else if (ordemMilhao) {
      return ('R$ ' + (price / umMilhao).toFixed(1) + 'M').replace('.', ',');
    } else if (ordemBilhao) {
      return ('R$ ' + (price / umBilhao).toFixed(2) + 'B').replace('.', ',');
    }
  }
  render() {
    const permissions = [
      this.props.permissionsAttributes.readPermission,
      this.props.permissionsAttributes.writePermission,
    ];
    const button = (
      <PermissionProxy resourcePermissions={permissions}>
        <FcButton
          label={`${this.props.quantity} ${this.props.masculineGenderText ? 'novo' : 'nova'}(s) no último ano`}
          style={{
            backgroundColor: this.props.color,
            borderRadius: 10,
            padding: '10px',
            height: '20%',
            width: '100%',
          }}
        ></FcButton>
      </PermissionProxy>
    );
    if (this.props.graphData) {
      return (
        <div className="col-12 md:col-6 lg:col-3">
          <div className="card shadow-2 border-radius h-full">
            <div className="flex justify-content-between mb-3">
              <div>
                <div className="block text-500 font-medium mb-3">
                  <i className={this.props.classIcon}></i> {'  '}
                  {this.props.title}
                </div>
                <div className="text-900 font-medium text-xl">{this.calculaOrdemPreco(this.props.price)}</div>
              </div>
              <div
                className="flex align-items-center justify-content-center bg-blue-100 border-round"
                style={{ width: '2.5rem', height: '2.5rem' }}
              ></div>
            </div>
            <div>
              {AppStore.hasPermission(permissions) && this.props.route ? (
                <Link to={this.props.route}>{button}</Link>
              ) : (
                <>{button}</>
              )}
            </div>
            <br />
            <div className="p-d-flex p-ai-end">
              <Chart
                ref={this.chartRef}
                type="line"
                data={this.props.graphData}
                options={this.props.graphOptions}
                height="45%"
                width="100%"
              />
            </div>
          </div>
        </div>
      );
    } else {
      return (
        <div className="col-12 md:col-6 lg:col-3">
          <div className="card shadow-2 border-radius">
            <div className="justify-content-between mb-3">
              <div className="gap-5 justify-content-center">
                <div className="block text-500 font-medium mb-3">
                  <i className={this.props.classIcon}></i> {'  '}
                  {this.props.title}
                </div>
                <Skeleton width="100%" height="2.5rem" className="my-3" />
              </div>
              <div className="text-900 font-medium text-xl">
                <Skeleton width="100%" height="8rem" className="my-3" />
              </div>
            </div>
          </div>
        </div>
      );
    }
  }
}
Block.propTypes = {
  title: PropTypes.string,
  price: PropTypes.number,
  quantity: PropTypes.number,
  classIcon: PropTypes.string,
  color: PropTypes.string,
  graphData: PropTypes.object,
  graphOptions: PropTypes.object,
  route: PropTypes.string,
  permissionsAttributes: PropTypes.object.isRequired,
  masculineGenderText: PropTypes.bool,
};

export default Block;
