import React from 'react';
import { observer } from 'mobx-react';
import PropTypes from 'prop-types';
import GenericFormPage from 'fc/pages/GenericFormPage';
import AppBreadCrumb from 'fc/components/AppBreadCrumb';
import PermissionProxy from 'fc/components/PermissionProxy';
import Form<PERSON>ield from 'fc/components/FormField';
import UrlRouter from '~/constants/UrlRouter';
import AccessPermission from '~/constants/AccessPermission';
import ObjetoAnaliseAutomaticaFormStore from '~/stores/objetoAnaliseAutomatica/formStore';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import InputMonetary from 'fc/components/InputMonetary';
import FcInputTextarea from 'fc/components/FcInputTextarea';
import { Checkbox } from 'primereact/checkbox';
import { Divider } from 'primereact/divider';
import { Chips } from 'primereact/chips';
import FcMultiSelect from 'fc/components/FcMultiSelect';
import FcDropdown from 'fc/components/FcDropdown';
import AsyncMultiselect from 'fc/components/AsyncMultiselect';

@observer
class ObjetoAnaliseAutomaticaFormPage extends GenericFormPage {
  constructor(props) {
    super(props, UrlRouter.auditoria.analiseAutomatica.index, AccessPermission.paramAnaliseAutomatica);
    this.store = new ObjetoAnaliseAutomaticaFormStore();
  }

  componentDidMount() {
    const { id } = this.props;
    this.store.initialize(id, { retroativo: false });
  }

  render() {
    const { submitted } = this.state;
    const { updateAttribute, getRule } = this.store;
    const { submitFormData } = this;

    const breacrumbItems = [
      { label: 'Análise Automática', url: UrlRouter.auditoria.analiseAutomatica.index },
      { label: this.props.action === 'new' ? 'Novo' : 'Editar' },
    ];

    let content = '';
    if (this.store.object) {
      content = (
        <>
          <AppBreadCrumb items={breacrumbItems} />
          <div className="card page form-action-buttons">
            <form onSubmit={submitFormData}>
              <div className="p-fluid p-formgrid p-grid">
                <FormField
                  columns={4}
                  attribute="tipo"
                  label="Tipo de Análise Automática"
                  rule={getRule('tipo')}
                  submitted={submitted}
                >
                  <FcDropdown
                    inOrder
                    {...this.validateField('tipo')}
                    onChange={(e) => this.store.updateAttribute('tipo', e)}
                    placeholder="Selecione o tipo de análise automática"
                    value={this.store.object.tipo}
                    id="tipo"
                    optionLabel="text"
                    optionValue="value"
                    options={DadosEstaticosService.getTiposAnaliseAutomatica()}
                  />
                </FormField>
                <Divider />
                {this.store.object.tipo === 'VALOR' && (
                  <>
                    <FormField
                      columns={4}
                      attribute="categorias"
                      label="Categorias"
                      rule={getRule('categorias')}
                      submitted={submitted}
                    >
                      <FcMultiSelect
                        {...this.validateField('categorias')}
                        onChange={(e) => this.store.updateAttribute('categorias', e)}
                        placeholder="Selecione uma ou mais categorias"
                        value={this.store.object.categorias}
                        id="categorias"
                        optionLabel="text"
                        optionValue="value"
                        options={DadosEstaticosService.getCategoriasNaturezaObjeto()}
                        showOverlay
                      />
                    </FormField>
                    <FormField
                      columns={4}
                      attribute="valor"
                      label="Valor"
                      rule={getRule('valor')}
                      submitted={submitted}
                    >
                      <InputMonetary
                        onChange={(e) => updateAttribute('valor', e)}
                        placeholder="R$"
                        value={this.store.object.valor}
                      />
                    </FormField>
                  </>
                )}
                {this.store.object.tipo === 'OBJETO' && (
                  <>
                    <FormField
                      columns={12}
                      attribute="objeto"
                      label="Objeto"
                      rule={getRule('objeto')}
                      submitted={submitted}
                    >
                      <FcInputTextarea
                        onChange={(e) => updateAttribute('objeto', e)}
                        placeholder="Informe o objeto"
                        rows={4}
                        value={this.store.object.objeto}
                      />
                    </FormField>
                    <FormField
                      columns={12}
                      attribute="similares"
                      label="Possível Similar"
                      rule={getRule('similares')}
                      submitted={submitted}
                    >
                      <Chips
                        onChange={(e) => updateAttribute('similares', e)}
                        placeholder="Informe os valores"
                        rows={2}
                        value={this.store.object.similares}
                      />
                    </FormField>
                  </>
                )}
                {this.store.object.tipo && <Divider />}
                <FormField columns={7} attribute="entidades" label="Entidades" submitted={submitted}>
                  <AsyncMultiselect
                    placeholder="Selecione as entidades"
                    value={this.store.object?.entidades}
                    onChange={(e) => this.store.updateAttribute('entidades', e)}
                    store={this.store.entidadesStore}
                    label="nome"
                    showOverlay
                  />
                </FormField>
                <FormField
                  columns={5}
                  attribute="tiposAquisicao"
                  label="Tipos de Aquisição"
                  rule={getRule('tiposAquisicao')}
                  submitted={submitted}
                >
                  <FcMultiSelect
                    {...this.validateField('tiposAquisicao')}
                    onChange={(e) => this.store.updateAttribute('tiposAquisicao', e)}
                    placeholder="Selecione um ou mais tipos de aquisição"
                    value={this.store.object.tiposAquisicao}
                    id="tiposAquisicao"
                    optionLabel="text"
                    optionValue="value"
                    options={DadosEstaticosService.getTiposProcesso()}
                    showOverlay
                  />
                </FormField>
                <FormField
                  columns={12}
                  attribute="retroativo"
                  label="Análise Retroativa"
                  checkbox
                  submitted={submitted}
                >
                  <Checkbox
                    inputId="retroativo"
                    checked={this.store.object.retroativo}
                    onChange={(e) => this.store.updateAttributeCheckbox('retroativo', e)}
                    id="retroativo"
                  />
                </FormField>
              </div>
              {this.renderActionButtons()}
            </form>
          </div>
        </>
      );
    } else {
      content = (
        <div className="card page">
          <AppBreadCrumb items={breacrumbItems} />
          <i className="pi pi-spin pi-spinner" style={{ fontSize: '2em' }}></i>
        </div>
      );
    }

    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        {content}
      </PermissionProxy>
    );
  }
}

ObjetoAnaliseAutomaticaFormPage.propTypes = {
  id: PropTypes.any,
  action: PropTypes.any,
  history: PropTypes.any,
};

export default ObjetoAnaliseAutomaticaFormPage;
