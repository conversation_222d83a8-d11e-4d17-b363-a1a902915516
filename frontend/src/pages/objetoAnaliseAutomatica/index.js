import React from 'react';
import { Link } from 'react-router-dom';
import { observer } from 'mobx-react';
import ObjetoAnaliseAutomaticaIndexStore from '~/stores/objetoAnaliseAutomatica/indexStore';
import IndexDataTable from 'fc/components/IndexDataTable';
import FcButton from 'fc/components/FcButton';
import { PrimeIcons } from 'primereact/api';
import { getValue, getValueByKey } from 'fc/utils/utils';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import AppBreadCrumb from 'fc/components/AppBreadCrumb';
import UrlRouter from '~/constants/UrlRouter';
import GenericIndexPage from 'fc/pages/GenericIndexPage';
import AdvancedSearch from 'fc/components/AdvancedSearch';
import AccessPermission from '~/constants/AccessPermission';
import PermissionProxy from 'fc/components/PermissionProxy';

@observer
class ObjetoAnaliseAutomaticaIndexPage extends GenericIndexPage {
  constructor(props) {
    super(props, AccessPermission.paramAnaliseAutomatica);
    this.store = new ObjetoAnaliseAutomaticaIndexStore();
    this.state = {
      idRemove: null,
    };
  }

  render() {
    const columns = [
      {
        field: 'tipo',
        header: 'Tipo',
        body: ({ tipo }) => getValueByKey(tipo, DadosEstaticosService.getTiposAnaliseAutomatica()),
        sortable: true,
      },
      {
        field: 'entidades',
        header: 'Entidades',
        body: ({ entidades }) => getValue(entidades?.map((e) => e.nome).join(', ')),
        sortable: true,
      },
      {
        field: 'objeto',
        header: 'Objeto',
        body: ({ objeto }) => getValue(objeto),
        sortable: true,
      },
      {
        field: 'retroativo',
        header: 'Análise Retroativa',
        sortable: true,
        body: ({ retroativo }) => getValueByKey(retroativo, DadosEstaticosService.getSimNao()),
      },
      {
        style: { width: '165px' },
        body: (rowData) => {
          return (
            <div className="actions p-d-flex p-jc-end">
              <PermissionProxy resourcePermissions={this.getWritePermission()}>
                <FcButton
                  icon="pi pi-pencil"
                  className="p-button-sm p-button-success p-mr-2"
                  onClick={() =>
                    this.pushUrlToHistory(UrlRouter.auditoria.analiseAutomatica.editar.replace(':id', rowData.id))
                  }
                />
              </PermissionProxy>
              <PermissionProxy resourcePermissions={this.getWritePermission()}>
                <FcButton
                  icon="pi pi-trash"
                  className="p-button-sm p-button-danger"
                  onClick={() => {
                    this.setState({ idRemove: rowData.id });
                    this.store.toggleShowConfirmDialog();
                  }}
                />
              </PermissionProxy>
            </div>
          );
        },
      },
    ];

    const header = (
      <div className="table-header p-grid p-dir-col">
        <Link to={UrlRouter.auditoria.analiseAutomatica.novo}>
          <FcButton
            className="p-button"
            label="Novo"
            style={{ marginBottom: '5px', marginRight: '5px' }}
            icon={PrimeIcons.PLUS}
          />
        </Link>
      </div>
    );

    const { listKey, loading } = this.store;
    const { getDefaultTableProps } = this;

    const breacrumbItems = [{ label: 'Análise Automática' }];

    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        <AppBreadCrumb items={breacrumbItems} />
        <div className="card page index-table">
          <AdvancedSearch
            searchParams={this.store.getAdvancedSearchParams()}
            store={this.store}
            searchFields={[]}
            useOr
          />
          <IndexDataTable
            columns={columns}
            value={listKey}
            header={header}
            loading={loading}
            {...getDefaultTableProps()}
          />
          {this.store.isConfirmDialogVisible && this.confirmRemove(this.state.idRemove)}
        </div>
      </PermissionProxy>
    );
  }
}

ObjetoAnaliseAutomaticaIndexPage.displayName = 'ObjetoAnaliseAutomaticaIndexPage';

export default ObjetoAnaliseAutomaticaIndexPage;
