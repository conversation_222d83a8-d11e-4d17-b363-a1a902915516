import React from 'react';
import { observer } from 'mobx-react';
import PropTypes from 'prop-types';
import GenericFormPage from 'fc/pages/GenericFormPage';
import AppBreadCrumb from 'fc/components/AppBreadCrumb';
import PermissionProxy from 'fc/components/PermissionProxy';
import <PERSON><PERSON>ield from 'fc/components/FormField';
import { InputText } from 'primereact/inputtext';
import UrlRouter from '~/constants/UrlRouter';
import AccessPermission from '~/constants/AccessPermission';
import SecaoChecklistFormStore from '~/stores/secaoChecklist/formStore';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import FcButton from 'fc/components/FcButton';
import FcMultiSelect from 'fc/components/FcMultiSelect';

@observer
class SecaoChecklistFormPage extends GenericFormPage {
  constructor(props) {
    super(props, UrlRouter.auditoria.secaoChecklist.index, AccessPermission.secaoChecklist);
    this.store = new SecaoChecklistFormStore();

    if (props.closeMethod) {
      this._goBack = (secaoChecklist) => this.props.closeMethod(secaoChecklist);
    }
  }

  renderActionButtons() {
    return (
      <div className="p-mt-10 form-actions">
        <div className="p-mt-2">
          <span className="p-mr-1 p-d-inline p-d-flex align-items-center">
            <FcButton
              label="Voltar"
              type="button"
              className="p-ml-auto p-button-secondary p-mr-2"
              onClick={() => this._goBack()}
              loading={this.store.loading}
            />
            <FcButton label="Salvar" type="button" onClick={this.submitFormData} loading={this.store.loading} />
          </span>
        </div>
      </div>
    );
  }

  render() {
    const { submitted } = this.state;
    const { updateAttribute, getRule } = this.store;
    const { submitFormData } = this;

    const breacrumbItems = [
      { label: 'Seções dos Checklists', url: UrlRouter.auditoria.secaoChecklist.index },
      { label: this.props.action === 'new' ? 'Novo' : 'Editar' },
    ];

    let content = '';
    if (this.store.object) {
      content = (
        <>
          <AppBreadCrumb items={breacrumbItems} />
          <div className="card page form-action-buttons">
            <form onSubmit={submitFormData}>
              <div className="p-fluid p-formgrid p-grid">
                <FormField columns={6} attribute="titulo" label="Título" rule={getRule('titulo')} submitted={submitted}>
                  <InputText
                    onChange={(e) => updateAttribute('titulo', e)}
                    placeholder="Informe o título"
                    value={this.store.object.titulo}
                  />
                </FormField>
                <FormField
                  columns={6}
                  attribute="tiposProcesso"
                  label="Tipos de Processo"
                  rule={getRule('tiposProcesso')}
                >
                  <FcMultiSelect
                    onChange={(e) => this.store.updateAttribute('tiposProcesso', e)}
                    placeholder="Selecione os tipos de processo"
                    value={this.store.object.tiposProcesso}
                    options={DadosEstaticosService.getTiposProcesso()}
                    optionValue="value"
                    optionLabel="text"
                    filterBy="text"
                    filter
                    selectedItemsLabel="{} itens selecionados"
                    showClear
                    showOverlay
                  />
                </FormField>
              </div>
              {this.renderActionButtons()}
            </form>
          </div>
        </>
      );
    } else {
      content = (
        <div className="card page">
          <AppBreadCrumb items={breacrumbItems} />
          <i className="pi pi-spin pi-spinner" style={{ fontSize: '2em' }}></i>
        </div>
      );
    }

    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        {content}
      </PermissionProxy>
    );
  }
}

SecaoChecklistFormPage.propTypes = {
  id: PropTypes.any,
  action: PropTypes.any,
  history: PropTypes.any,
};

export default SecaoChecklistFormPage;
