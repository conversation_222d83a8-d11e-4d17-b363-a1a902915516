import React from 'react';
import { observer } from 'mobx-react';
import PropTypes from 'prop-types';
import SecaoFormStore from '../../stores/secao/formStore';
import { InputText } from 'primereact/inputtext';
import UrlRouter from '../../constants/UrlRouter';
import GenericFormPage from 'fc/pages/GenericFormPage';
import { Checkbox } from 'primereact/checkbox';
import FormField from 'fc/components/FormField';
import AppBreadCrumb from 'fc/components/AppBreadCrumb';
import PermissionProxy from 'fc/components/PermissionProxy';
import AccessPermission from '~/constants/AccessPermission';
import InputMonetary from 'fc/components/InputMonetary';

@observer
class SecaoFormPage extends GenericFormPage {
  constructor(props) {
    super(props, UrlRouter.termoReferencia.secao.index, AccessPermission.secoes);
    this.store = new SecaoFormStore();
  }

  componentDidMount() {
    const { id } = this.props;
    this.store.initialize(id, { obrigatoria: true, geradaTermo: false });
    this.props.action === 'new' && this.store.getMaiorOrdem();
  }

  render() {
    const { submitted } = this.state;
    const { updateAttribute, getRule } = this.store;
    const { submitFormData } = this;

    const breacrumbItems = [
      { label: 'Seções', url: UrlRouter.termoReferencia.secao.index },
      { label: this.props.action === 'new' ? 'Novo' : 'Editar' },
    ];

    let content;
    if (this.store.object && !this.store.object.geradaTermo) {
      content = (
        <>
          <AppBreadCrumb items={breacrumbItems} />
          <div className="card page form-action-buttons">
            <form onSubmit={submitFormData}>
              <div className="p-fluid p-formgrid p-grid">
                <FormField columns={2} attribute="ordem" label="Ordem" rule={getRule('ordem')} submitted={submitted}>
                  <InputMonetary
                    onChange={(e) => this.store.updateAttribute('ordem', e)}
                    value={this.store.object.ordem}
                    max={999999999}
                    thousandsPlaceMode
                  />
                </FormField>

                <FormField
                  columns={10}
                  attribute="titulo"
                  label="Título"
                  rule={getRule('titulo')}
                  submitted={submitted}
                >
                  <InputText
                    onChange={(e) => updateAttribute('titulo', e)}
                    placeholder="Informe o título"
                    value={this.store.object.titulo}
                  />
                </FormField>

                <FormField
                  columns={12}
                  attribute="notasExplicativas"
                  label="Notas Explicativas"
                  rule={getRule('notasExplicativas')}
                  submitted={submitted}
                >
                  <InputText
                    onChange={(e) => updateAttribute('notasExplicativas', e)}
                    placeholder="Descreva as notas explicativas"
                    value={this.store.object.notasExplicativas}
                  />
                </FormField>

                <FormField
                  columns={12}
                  attribute="obrigatoria"
                  label="Obrigatória"
                  rule={getRule('obrigatoria')}
                  submitted={submitted}
                  checkbox
                >
                  <Checkbox
                    onChange={(e) => updateAttribute('obrigatoria', e.checked)}
                    checked={this.store.object.obrigatoria}
                  />
                </FormField>
              </div>
              {this.renderActionButtons()}
            </form>
          </div>
        </>
      );
    } else {
      content = (
        <div className="card page">
          <AppBreadCrumb items={breacrumbItems} />
          <i
            className="pi pi-spin pi-spinner"
            style={{
              marginTop: '20px',
              marginLeft: 'calc(50% - 20px)',
              fontSize: '2em',
            }}
          ></i>
        </div>
      );
    }
    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        {content}
      </PermissionProxy>
    );
  }
}

SecaoFormPage.propTypes = {
  id: PropTypes.any,
  action: PropTypes.any,
  history: PropTypes.any,
};

export default SecaoFormPage;
