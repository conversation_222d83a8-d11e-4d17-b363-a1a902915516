import React from 'react';
import { observer } from 'mobx-react';
import AppBreadCrumb from 'fc/components/AppBreadCrumb';
import GenericIndexPage from 'fc/pages/GenericIndexPage';
import PermissionProxy from 'fc/components/PermissionProxy';
import AccessPermission from '~/constants/AccessPermission';
import ObraIndexStore from '~/stores/obras/indexStore';
import '~/assets/leaflet.mask';
import ObraMapContainer from './ObraMapContainer';
import { Sidebar } from 'primereact/sidebar';
import FcButton from 'fc/components/FcButton';
import './style.scss';
import CardList from 'fc/components/CardList';
import {
  getLightenColor,
  getValue,
  getValueByKey,
  getValueDate,
  getValueMoney,
  hasPermissionProxy,
} from 'fc/utils/utils';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import { DATE_FORMAT, DATE_PARSE_FORMAT } from 'fc/utils/date';
import AdvancedSearch from 'fc/components/AdvancedSearch';
import ObraMedicaoDialog from './obraMedicaoDialog';
import Tooltip from 'fc/components/Tooltip';
import { Tag } from 'primereact/tag';
import ObraMedicaoHistorico from './obraMedicaoHistorico';
import { ConfirmDialog } from 'primereact/confirmdialog';

@observer
class ObraIndexPage extends GenericIndexPage {
  constructor(props) {
    super(props, AccessPermission.obraMedicao);
    this.store = new ObraIndexStore();

    this.state = {
      windowWidth: window.innerWidth,
      showSideBarPanel: true,
      showDialogObraMedicao: false,
      selectedObra: {},
      showMedicaoHistory: false,
      visibleIniciaObraConfirmDialog: false,
      selectedObraMedicaoDTO: null,
    };

    this._showSideBarPanel = this._showSideBarPanel.bind(this);
    this._closeObraMedicaoDialog = this._closeObraMedicaoDialog.bind(this);
    this._renderDialogObraMedicao = this._renderDialogObraMedicao.bind(this);
    this._showMedicaoHistory = this._showMedicaoHistory.bind(this);
    this.showObraMedicaoDialog = this.showObraMedicaoDialog.bind(this);
  }

  componentDidMount() {
    const { store } = this;
    store.loadCurrentEnte();
    window.addEventListener('resize', this.handleResize);
  }

  componentWillUnmount() {
    window.removeEventListener('resize', this.handleResize);
  }

  handleResize = () => {
    this.setState({
      windowWidth: window.innerWidth,
    });
  };

  _showSideBarPanel() {
    this.setState({
      showSideBarPanel: !this.state.showSideBarPanel,
    });
  }

  _showMedicaoHistory(obra) {
    this.setState({
      selectedObra: obra,
      showMedicaoHistory: obra ? true : false,
    });
  }

  showObraMedicaoDialog(obra, idObraMedicaoReqMod, reloadMedicoes) {
    !obra.status || obra.status === 'NAO_INICIADA'
      ? this.setState({
          selectedObra: obra,
          visibleIniciaObraConfirmDialog: true,
          idObraMedicaoReqMod,
          reloadFunction: reloadMedicoes,
        })
      : this.setState({
          selectedObra: obra,
          showDialogObraMedicao: true,
          idObraMedicaoReqMod,
          reloadFunction: reloadMedicoes,
        });
  }

  _closeObraMedicaoDialog() {
    this.setState({ showDialogObraMedicao: false });
  }

  _renderDialogObraMedicao() {
    return (
      <ObraMedicaoDialog
        visible={this.state.showDialogObraMedicao}
        closeDialog={this._closeObraMedicaoDialog}
        obra={this.state.selectedObra}
        idObraMedicaoReqMod={this.state.idObraMedicaoReqMod}
        onSave={this.state.reloadFunction}
      />
    );
  }

  _renderMedicaoHistory() {
    return (
      <ObraMedicaoHistorico
        obra={this.state.selectedObra}
        showObraMedicaoDialogReqMod={this.showObraMedicaoDialog}
        goBack={() => this._showMedicaoHistory()}
      />
    );
  }

  getMarkerStyle(tipo) {
    const styleDefinition = {
      'Unidade Institucional': {
        icon: 'briefcase',
        prefix: 'ion',
      },
      'Unidade de Saúde': {
        icon: 'medkit',
        prefix: 'ion',
      },
      'Unidade de Lazer': {
        icon: 'leaf',
        prefix: 'ion',
      },
      'Manutenção Viária': {
        icon: 'model-s',
        prefix: 'ion',
      },
      'Outras obras': {
        icon: 'wrench',
        prefix: 'ion',
      },
      'Pavimentação de Vias': {
        icon: 'model-s',
        prefix: 'ion',
      },
      'Unidade de Educação': {
        icon: 'android-book',
        prefix: 'ion',
      },
      'Rede de Abastecimento de Água': {
        icon: 'waterdrop',
        prefix: 'ion',
      },
      'Construção/Manuenção de Praças': {
        icon: 'leaf',
        prefix: 'ion',
      },
      'Rede de Coleta de Esgoto': {
        icon: 'waterdrop',
        prefix: 'ion',
      },
      'Construção/Manutenção de Pontes': {
        icon: 'hammer',
        prefix: 'ion',
      },
      'Unidade de Segurança Pública': {
        icon: 'eye',
        prefix: 'ion',
      },
      'Melhoria Sanitária Domiciliar': {
        icon: 'trash-a',
        prefix: 'ion',
      },
      'Construção/Manutenção de Calçadas': {
        icon: 'hammer',
        prefix: 'ion',
      },
      'Construção/Manutenção de Ramais': {
        icon: 'hammer',
        prefix: 'ion',
      },
      'Rede de Drenagem': {
        icon: 'ios7-rainy',
        prefix: 'ion',
      },
      'Estação de Tratamento de Esgoto': {
        icon: 'waterdrop',
        prefix: 'ion',
      },
      'Estação de Tratamento de Água': {
        icon: 'waterdrop',
        prefix: 'ion',
      },
      Habitação: {
        icon: 'home',
        prefix: 'ion',
      },
      'Perfuração de Poço': {
        icon: 'waterdrop',
        prefix: 'ion',
      },
    };

    if (Object.keys(styleDefinition).includes(tipo)) {
      return { ...styleDefinition[tipo], markerColor: 'cadetblue' };
    } else {
      return {
        icon: 'help',
        markerColor: 'blue',
        prefix: 'ion',
      };
    }
  }

  _formatCardTitle = (licitacao, showEntity = true) => {
    if (licitacao) {
      const { entidade, numero, ano } = licitacao;
      const titleEntityContext = ` ${numero} / ${ano}`;

      return showEntity ? `${titleEntityContext} - ${entidade.nome}` : titleEntityContext;
    }
    return '-';
  };

  _getCardEllipsisOptions(obra) {
    const items = [];

    if (hasPermissionProxy(this.getReadPermission())) {
      items.push({
        label: 'Ver no mapa',
        icon: 'pi pi-eye',
        command: () => {
          this.store.updateSelectedObra(obra);
          this._showSideBarPanel();
          this.store.panToSelectedObra();
        },
      });
    }
    if (hasPermissionProxy(AccessPermission.obraMedicao.writePermission)) {
      items.push({
        label: 'Nova Medição',
        icon: 'pi pi-pencil',
        disabled: !obra.contrato || obra.status === 'FINALIZADA' || obra.requisicaoModificacaoMedicao,
        command: () => this.showObraMedicaoDialog(obra, null, () => this.store.load()),
      });
    }
    if (
      hasPermissionProxy([AccessPermission.obraMedicao.readPermission, AccessPermission.obraMedicao.writePermission])
    ) {
      items.push({
        label: 'Histórico de Medições',
        icon: 'pi pi-list',
        command: () => this.setState({ showMedicaoHistory: false }, () => this._showMedicaoHistory(obra)),
      });
    }

    return items;
  }

  renderIniciaObraConfirmDialog() {
    const message = 'Obra ainda não iniciada. Deseja inicializar a execução da obra?';

    return (
      <ConfirmDialog
        visible={this.state.visibleIniciaObraConfirmDialog}
        message={message}
        header="Iniciar Obra"
        icon="pi pi-info-circle"
        acceptClassName="p-button-warning"
        accept={() =>
          this.store.setStatusObra(this.state.selectedObra.id, 'EM_ANDAMENTO', () =>
            this.setState({
              showDialogObraMedicao: true,
            })
          )
        }
        onHide={() => this.setState({ visibleIniciaObraConfirmDialog: false })}
      />
    );
  }

  _renderInformationCard() {
    const { store } = this;
    const { selectedObra } = store;
    const { windowWidth } = this.state;

    const iconStyle = this.getMarkerStyle(selectedObra?.tipo?.nome);

    const tagContent = [
      {
        field: 'valorEstimado',
        tagColor: '#2F83DC',
        icon: 'pi pi-money-bill',
        valor: getValueMoney(getValue(selectedObra?.licitacao?.valorEstimado)),
        toolTip: 'Valor Estimado',
      },
      {
        field: 'dataCadastro',
        tagColor: '#4da73b',
        icon: 'pi pi-calendar',
        valor: getValueDate(selectedObra.licitacao.dataCadastro, DATE_FORMAT, DATE_PARSE_FORMAT),
        toolTip: 'Data de Cadastro',
      },
      {
        field: 'status',
        tagColor: '#38AAAD',
        icon: 'pi pi-tag',
        valor: getValueByKey(selectedObra?.status ?? 'DESCONHECIDO', DadosEstaticosService.getStatusObra()),
        toolTip: 'Status',
      },
      {
        field: 'categoria',
        tagColor: '#d080fa',
        icon: 'pi pi-tag',
        valor: getValue(selectedObra?.categoria?.nome),
        toolTip: 'Categoria',
      },
      {
        field: 'tipo',
        tagColor: '#fac055',
        icon: 'pi pi-tag',
        valor: getValue(selectedObra?.tipo?.nome),
        toolTip: 'Tipo',
      },
    ];

    const obraNaoIniciada = !store?.selectedObra?.status || store?.selectedObra?.status === 'NAO_INICIADA';

    return (
      <div className={`information-div ${!store.selectedObra ? 'fade-out' : 'fade-in'}`}>
        <div className="detail-drawer-content">
          <div className={'p-col-12 p-md header'}>
            <div className="p-col-12 information-card-title pr-0">
              <div className="p-col-1 information-card-icon-wrapper">
                <i
                  className={`${iconStyle.prefix} ${iconStyle.prefix}-${iconStyle.icon}`}
                  style={{ color: '#fff' }}
                ></i>
              </div>
              <div className="p-col-9">
                <span>{this._formatCardTitle(selectedObra.licitacao, false)}</span>
                <div className={`p-col-9 p-text-justify information-card-entity sub-headers`}>
                  <span>{getValue(store.selectedObra?.entidade?.nome)}</span>
                </div>
              </div>
              {windowWidth >= 800 && (
                <div className="p-col-2 information-card-actions flex justify-content-end">
                  <PermissionProxy resourcePermissions={[AccessPermission.obraMedicao.writePermission]}>
                    <Tooltip value={obraNaoIniciada ? 'Inicializar Obra' : 'Nova Medição'}>
                      <span>
                        <FcButton
                          className="p-button-rounded p-button-outlined"
                          icon={obraNaoIniciada ? 'pi pi-play' : 'pi pi-plus'}
                          style={{ marginRight: '5px', color: '#689F37' }}
                          type="button"
                          disabled={
                            !selectedObra.contrato ||
                            selectedObra.status === 'FINALIZADA' ||
                            selectedObra.requisicaoModificacaoMedicao
                          }
                          onClick={() => this.showObraMedicaoDialog(selectedObra, null, () => this.store.load())}
                        />
                      </span>
                    </Tooltip>
                  </PermissionProxy>
                  <PermissionProxy
                    resourcePermissions={[
                      AccessPermission.obraMedicao.readPermission,
                      AccessPermission.obraMedicao.writePermission,
                    ]}
                  >
                    <Tooltip value="Histórico de Medições">
                      <span>
                        <FcButton
                          className={'p-button-rounded p-button-info p-button-outlined'}
                          icon={'pi pi-list'}
                          style={{ marginRight: '5px' }}
                          type="button"
                          onClick={() => {
                            !this.state.showSideBarPanel && this._showSideBarPanel();
                            this.setState({ showMedicaoHistory: false }, () => this._showMedicaoHistory(selectedObra));
                          }}
                        />
                      </span>
                    </Tooltip>
                  </PermissionProxy>
                  <Tooltip value="Fechar">
                    <span>
                      <FcButton
                        className={'p-button-rounded p-button-danger p-button-outlined'}
                        icon={'pi pi-times'}
                        type="button"
                        onClick={() => store.updateSelectedObra(undefined)}
                      />
                    </span>
                  </Tooltip>
                </div>
              )}
            </div>
          </div>
          <div className={`p-col-12 p-text-justify descricao-objeto`}>
            <span>{getValue(store.selectedObra?.licitacao?.objeto)}</span>
          </div>

          <div className="flex gap-1 flex-wrap information-card-tag">
            {tagContent.map((tag) => {
              return (
                <Tooltip value={tag.toolTip} sideOffset={0} key={tag.field}>
                  <div style={{ cursor: 'default' }}>
                    <Tag
                      icon={tag.icon}
                      className="info-item"
                      style={{
                        backgroundColor: getLightenColor(tag.tagColor, 0.7),
                        color: tag.tagColor,
                        border: `1px solid ${tag.tagColor}`,
                      }}
                    >
                      <span style={{ color: tag.tagColor }}>{tag.valor}</span>
                    </Tag>
                  </div>
                </Tooltip>
              );
            })}
          </div>
        </div>
      </div>
    );
  }

  renderObraListInstance() {
    const fields = [
      {
        label: 'Licitação',
        field: 'licitacao',
        value: 'title',
        body: (obra) => this._formatCardTitle(obra?.licitacao),
      },

      {
        field: 'valorEstimado',
        label: 'Valor',
        value: 'iconLabel',
        color: '#2F83DC',
        icon: 'pi pi-money-bill',
        body: (obra) => getValueMoney(getValue(obra?.licitacao?.valorEstimado)),
      },
      {
        field: 'dataCadastro',
        label: 'Data de Cadastro',
        value: 'iconLabel',
        color: '#4da73b',
        icon: 'pi pi-calendar',
        body: (obra) => {
          return getValueDate(obra.licitacao.dataCadastro, DATE_FORMAT, DATE_PARSE_FORMAT);
        },
      },
      {
        field: 'status',
        label: 'Status',
        value: 'iconLabel',
        color: '#38AAAD',
        icon: 'pi pi-tag',
        body: (obra) => getValueByKey(obra?.status ?? 'DESCONHECIDO', DadosEstaticosService.getStatusObra()),
      },
      {
        field: 'categoria',
        label: 'Categoria',
        value: 'iconLabel',
        color: '#d080fa',
        icon: 'pi pi-tag',
        body: (obra) => getValue(obra?.categoria?.nome),
      },
      {
        field: 'tipo',
        label: 'Tipo',
        value: 'iconLabel',
        color: '#fac055',
        icon: 'pi pi-tag',
        body: (obra) => getValue(obra?.tipo?.nome),
      },
      {
        label: 'Ações',
        value: 'ellipsisItems',
        body: (obra) => this._getCardEllipsisOptions(obra),
      },
    ];

    return (
      <div>
        <CardList fields={fields} store={this.store} />
      </div>
    );
  }

  render() {
    const { store, state } = this;
    const { showSideBarPanel, showMedicaoHistory, showDialogObraMedicao, selectedObra } = state;
    const breacrumbItems = [{ label: 'Medição' }];
    let content;
    if (this.store.ente) {
      content = (
        <div>
          <AppBreadCrumb items={breacrumbItems} />
          <FcButton
            icon={showSideBarPanel ? 'pi pi-chevron-right' : 'pi pi-chevron-left'}
            className={' p-button-lg'}
            style={{
              width: '50px',
              height: '50px',
              zIndex: 1,
              position: 'absolute',
              right: 0,
              marginTop: '1px',
              marginRight: showSideBarPanel ? '531px' : '0px',
              transitionDuration: '.5s',
              transitionProperty: 'margin-right',
            }}
            onClick={() => this._showSideBarPanel()}
          />
          <Sidebar
            position="right"
            dismissable={false}
            modal={false}
            style={{
              width: '530px',
              display: 'flex',
              flexDirection: 'column',
              paddingTop: '1rem',
            }}
            visible={showSideBarPanel}
            onHide={() => {
              showMedicaoHistory ? this._showMedicaoHistory() : this._showSideBarPanel();
            }}
          >
            {showMedicaoHistory && selectedObra && this._renderMedicaoHistory()}
            {!showMedicaoHistory && (
              <div>
                <div className="obras-list-advanced-search">
                  <AdvancedSearch
                    searchParams={store.getAdvancedSearchParams()}
                    store={store}
                    searchFields={['nome']}
                    filterSuggest={store.getFilterSuggest()}
                    alwaysDrawer
                  />
                </div>
                <div> {this.renderObraListInstance()}</div>
              </div>
            )}
          </Sidebar>

          <ObraMapContainer ente={store.ente} obras={store.listKey} store={store} />
        </div>
      );
    } else {
      content = (
        <div className="card page index-table">
          <AppBreadCrumb items={breacrumbItems} />
          <i
            className="pi pi-spin pi-spinner"
            style={{
              marginTop: '20px',
              marginLeft: 'calc(50% - 20px)',
              fontSize: '2em',
            }}
          ></i>
        </div>
      );
    }

    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        {content}
        {showDialogObraMedicao && this._renderDialogObraMedicao()}
        {store.selectedObra && this._renderInformationCard()}
        {this.renderIniciaObraConfirmDialog()}
      </PermissionProxy>
    );
  }
}

ObraIndexPage.displayName = 'ObraIndexPage';

export default ObraIndexPage;
