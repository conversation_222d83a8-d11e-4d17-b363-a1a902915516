import { observer } from 'mobx-react';
import { useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, useMap } from 'react-leaflet';
import '../licitacao/tabs/map.less';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import 'leaflet-draw/dist/leaflet.draw.css';
import 'leaflet.awesome-markers/dist/leaflet.awesome-markers.js';
import 'leaflet.awesome-markers/dist/leaflet.awesome-markers.css';
import 'ionicons';

const MunicipiosGeometry = require('~/constants/municipios_ac.json');
import '~/assets/leaflet.mask';

const ObraMapContainer = observer(({ ente, obras, store }) => {
  return (
    <div>
      <MapContainer zoom={3} preferCanvas={true} style={{ height: '90vh', width: '110wh', zIndex: 0 }}>
        <MapContent ente={ente} obras={obras} store={store} />
      </MapContainer>
    </div>
  );
});

const MapContent = observer(({ ente, obras, store }) => {
  const map = useMap();

  store.setPanMapToPointFunction((point) => {
    map.flyTo(point, 18);
  });

  useEffect(() => {
    try {
      const enteNome = ente && ente.nome ? ente.nome : 'Estado do Acre';
      const enteGeoJson = fetchEnteGeometryFromNome(enteNome);

      setMapRestrictions(enteGeoJson, enteNome);
      addEnteBoundsToMap(enteGeoJson);
    } catch (e) {
      console.error(e);
    }
  }, []);

  const getMarkerStyle = (tipo) => {
    const styleDefinition = {
      'Unidade Institucional': {
        icon: 'briefcase',
        prefix: 'ion',
      },
      'Unidade de Saúde': {
        icon: 'medkit',
        prefix: 'ion',
      },
      'Unidade de Lazer': {
        icon: 'leaf',
        prefix: 'ion',
      },
      'Manutenção Viária': {
        icon: 'model-s',
        prefix: 'ion',
      },
      'Outras obras': {
        icon: 'wrench',
        prefix: 'ion',
      },
      'Pavimentação de Vias': {
        icon: 'model-s',
        prefix: 'ion',
      },
      'Unidade de Educação': {
        icon: 'android-book',
        prefix: 'ion',
      },
      'Rede de Abastecimento de Água': {
        icon: 'waterdrop',
        prefix: 'ion',
      },
      'Construção/Manuenção de Praças': {
        icon: 'leaf',
        prefix: 'ion',
      },
      'Rede de Coleta de Esgoto': {
        icon: 'waterdrop',
        prefix: 'ion',
      },
      'Construção/Manutenção de Pontes': {
        icon: 'hammer',
        prefix: 'ion',
      },
      'Unidade de Segurança Pública': {
        icon: 'eye',
        prefix: 'ion',
      },
      'Melhoria Sanitária Domiciliar': {
        icon: 'trash-a',
        prefix: 'ion',
      },
      'Construção/Manutenção de Calçadas': {
        icon: 'hammer',
        prefix: 'ion',
      },
      'Construção/Manutenção de Ramais': {
        icon: 'hammer',
        prefix: 'ion',
      },
      'Rede de Drenagem': {
        icon: 'ios7-rainy',
        prefix: 'ion',
      },
      'Estação de Tratamento de Esgoto': {
        icon: 'waterdrop',
        prefix: 'ion',
      },
      'Estação de Tratamento de Água': {
        icon: 'waterdrop',
        prefix: 'ion',
      },
      Habitação: {
        icon: 'home',
        prefix: 'ion',
      },
      'Perfuração de Poço': {
        icon: 'waterdrop',
        prefix: 'ion',
      },
    };

    if (Object.keys(styleDefinition).includes(tipo)) {
      return { ...styleDefinition[tipo], markerColor: 'cadetblue' };
    } else {
      return {
        icon: 'help',
        markerColor: 'blue',
        prefix: 'ion',
      };
    }
  };

  const renderEdificacao = (obra) => {
    const localizacao = obra?.localizacao;
    let marker = [];
    const customMarker = L.AwesomeMarkers.icon(getMarkerStyle(obra?.tipo?.nome));

    if (localizacao) {
      if (localizacao.type === 'Polygon') {
        marker = localizacao.coordinates[0][0];
      } else if (localizacao.type === 'LineString') {
        marker = localizacao.coordinates[0];
      } else {
        marker = localizacao.coordinates;
      }
    }
    return (
      <>
        {marker && marker.length > 0 && (
          <Marker
            position={marker}
            radius={20}
            icon={customMarker}
            eventHandlers={{ click: () => store.updateSelectedObra(obra) }}
          />
        )}
      </>
    );
  };

  const setMapRestrictions = (enteGeoJson, nome) => {
    const fullGeoJsonMask = {
      type: 'FeatureCollection',
      features: [enteGeoJson],
    };

    L.mask(fullGeoJsonMask, { fitBounds: false, fillOpacity: 0.6, restrictBounds: false }).addTo(map);

    const acreGeoJson = fetchEnteGeometryFromNome(nome);
    const acreBounds = L.geoJSON(acreGeoJson).getBounds();
    map.fitBounds(acreBounds);
    map.setMinZoom(map.getZoom());
  };

  const addEnteBoundsToMap = (enteGeoJson) => {
    const mapGeom = L.geoJSON(enteGeoJson).setStyle({
      fillOpacity: 0,
    });

    const geometryBounds = mapGeom.getBounds();
    mapGeom.addTo(map);
    map.fitBounds(geometryBounds);
  };

  const fetchEnteGeometryFromNome = (nome) => {
    const enteGeoJson = MunicipiosGeometry.features;
    const upperNome = nome?.toUpperCase();
    const filtered = enteGeoJson?.filter(({ properties }) => {
      return properties['NOME']?.toUpperCase() === upperNome;
    });
    return filtered?.length > 0 ? filtered[0] : null;
  };

  return (
    <>
      <TileLayer
        attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        url="https://tile.openstreetmap.org/{z}/{x}/{y}.png"
      />
      {obras?.map((obra) => renderEdificacao(obra))}
    </>
  );
});

export default ObraMapContainer;
