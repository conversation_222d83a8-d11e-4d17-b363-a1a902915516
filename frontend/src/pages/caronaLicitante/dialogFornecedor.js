import React from 'react';
import PropTypes from 'prop-types';
import { observer } from 'mobx-react';
import { getValue, getValue<PERSON>oney, showNotification } from 'fc/utils/utils';
import GenericIndexPage from 'fc/pages/GenericIndexPage';
import { DataTable } from 'primereact/datatable';
import { Dialog } from 'primereact/dialog';
import FcButton from 'fc/components/FcButton';
import { Column } from 'jspdf-autotable';
import AppBreadCrumb from 'fc/components/AppBreadCrumb';
import UrlRouter from '~/constants/UrlRouter';
import FormField from 'fc/components/FormField';
import { Fieldset } from 'primereact/fieldset';
import InputMonetary from 'fc/components/InputMonetary';
import InputNumber from 'fc/components/InputNumber';
import SelectDialog from 'fc/components/SelectDialog';
import LicitanteIndexStore from '~/stores/licitante/indexStore';
import LicitanteFormPage from '../licitante/form';

@observer
class DialogFornecedor extends GenericIndexPage {
  constructor(props) {
    super(props);
    this.store = this.props.store;
    this.termo = this.props.termo;
    this.state = {
      showVisualizationDialog: false,
      showVisualizationDialogItens: false,
      showVisualizationDialogLotes: false,
      editFornecedor: false,
      itemValorTotal: 0,
      valorTotalLote: 0,
      selectedKeyItem: undefined,
      selectedLote: undefined,
      expandedRows: null,
    };
    this.toggleShowDialogFornecedor = this.toggleShowDialogFornecedor.bind(this);
    this.toggleShowDialogItens = this.toggleShowDialogItens.bind(this);
    this.renderDialogFornecedorFooter = this.renderDialogFornecedorFooter.bind(this);
    this.toggleRow = this.toggleRow.bind(this);
  }

  componentDidMount() {
    const { id } = this.props;
    this.store.initialize(id);
    this.store.initializeItensLicitante();
  }

  _renderColumns(columns) {
    return columns.map((col) => <Column key={`col-${col.field}`} {...col} />);
  }

  toggleShowDialogItens() {
    this.setState({ showVisualizationDialogItens: !this.state.showVisualizationDialogItens });
  }

  toggleShowDialogLotes() {
    this.setState({ showVisualizationDialogLotes: !this.state.showVisualizationDialogLotes });
  }

  renderDialogItens() {
    const { selectedKeyItem, selectedLote } = this.state;
    const { keyedListItens, addItemLicitante } = this.store;

    const item = keyedListItens(selectedLote?.itens ?? [])[selectedKeyItem];

    if (this.state.showVisualizationDialogItens) {
      return (
        <Dialog
          header={item?.materialDetalhamento?.pdm?.nome}
          visible={this.state.showVisualizationDialogItens}
          style={{ width: '30vw' }}
          onHide={() => {
            this.store.clearItemLicitante();
            this.toggleShowDialogItens();
          }}
          footer={
            <FcButton
              type="button"
              label="Adicionar"
              onClick={() => {
                const save = addItemLicitante(selectedLote, item);
                if (save) {
                  this.store.loadedArvoreLoteItens();
                  this.setState({ itemValorTotal: 0 });
                  this.toggleShowDialogItens();
                }
              }}
            />
          }
        >
          {this.renderDescricaoDialog(item)}
        </Dialog>
      );
    } else {
      return '';
    }
  }

  renderDialogLotes() {
    const { selectedLote } = this.state;
    const { addLoteLicitante } = this.store;

    if (this.state.showVisualizationDialogLotes) {
      return (
        <Dialog
          header={'Adicionar lote completo - ' + selectedLote.nome}
          visible={this.state.showVisualizationDialogLotes}
          style={{ width: '50vw' }}
          onHide={() => {
            this.store.clearFormAddLote(selectedLote);
            this.setState({ valorTotalLote: 0 });
            this.toggleShowDialogLotes();
          }}
          footer={
            <FcButton
              type="button"
              label="Adicionar"
              onClick={() => {
                const save = addLoteLicitante(selectedLote);
                if (save) {
                  this.store.loadedArvoreLoteItens();
                  this.setState({ valorTotalLote: 0 });
                  this.toggleShowDialogLotes();
                }
              }}
            />
          }
        >
          <div>Ao adicionar um lote completo todos os itens são adicionados.</div>
          <div>É necessário atribuir um valor unitário a cada um dos itens do lote.</div>
          <br />
          <FormField label="Itens">{this.rendeItensLoteTable(selectedLote)}</FormField>
          <FormField columns={8} attribute="valorTotalItem" label={''}>
            <>
              Valor total do lote:
              <span>{getValueMoney(this.state.valorTotalLote ?? 0, this.props?.termo?.tresCasasDecimais ? 3 : 2)}</span>
            </>
          </FormField>
        </Dialog>
      );
    } else {
      return '';
    }
  }

  renderDescricaoDialog(item) {
    const { itemLicitante, setItemLicitanteAttribute } = this.store;

    if (item) {
      return (
        <div>
          <strong> Descrição: </strong>
          <span>{item.materialDetalhamento?.pdm?.nome}</span>
          <br /> <br />
          <strong>Descrição Complementar: </strong> <span>{item.descricaoComplementar ?? ''}</span> <br /> <br />
          <strong>Quantidade Disponivel: </strong> <span> {item.quantidade ?? ''}</span> <br /> <br />
          <strong>Valor unitário estimado : </strong>
          <span>{getValueMoney(item.valorUnitarioEstimado, this.props?.termo?.tresCasasDecimais ? 3 : 2) ?? ''}</span>
          <br /> <br />
          <>
            <Fieldset legend="Preencher os valores">
              <FormField columns={8} attribute="valorUnitario" label={'Valor Unitário Item'}>
                <InputMonetary
                  placeholder="R$"
                  value={itemLicitante.valorUnitario}
                  onChange={(value) => {
                    setItemLicitanteAttribute('valorUnitario', value);
                    setItemLicitanteAttribute('valorTotal', itemLicitante.valorUnitario * itemLicitante.quantidade);
                    this.setState({ itemValorTotal: itemLicitante.valorTotal });
                  }}
                  decimalPlaces={this.props?.termo?.tresCasasDecimais ? 3 : 2}
                />
              </FormField>
              <FormField columns={8} attribute="quantidade" label={'Quantidade'}>
                <InputNumber
                  min={0}
                  max={item.quantidade}
                  value={itemLicitante.quantidade}
                  onChange={(e) => {
                    if (e.target.value === 'NaN' || e.target.value === '') e.target.value = 0;
                    const valor = e.target.value < item.quantidade + 1 ? e.target.value : item.quantidade;
                    setItemLicitanteAttribute('quantidade', parseInt(valor));
                    setItemLicitanteAttribute('valorTotal', itemLicitante.valorUnitario * itemLicitante.quantidade);
                    this.setState({ itemValorTotal: itemLicitante.valorTotal });
                  }}
                />
              </FormField>
              <FormField columns={8} attribute="valorTotalItem" label={''}>
                <>
                  Valor Total:
                  <span>
                    {getValueMoney(this.state.itemValorTotal ?? 0, this.props?.termo?.tresCasasDecimais ? 3 : 2)}
                  </span>
                </>
              </FormField>
            </Fieldset>
          </>
        </div>
      );
    }
  }

  rendeItensTable(lote) {
    const { keyedListItens } = this.store;

    const availableItensColumns = [
      {
        field: 'quantidade',
        header: 'Quantidade disponível',
        style: { width: '30%' },
      },
      {
        header: 'Descrição do item',
        style: { width: '60%' },
        body: (rowData) => {
          return getValue(rowData.materialDetalhamento?.pdm?.nome);
        },
      },
      {
        field: 'adicionar',
        style: { width: '5%' },
        body: (rowData) => {
          return (
            <>
              <FcButton
                type="button"
                tooltip={'Adicionar Item'}
                icon={'pi pi-plus'}
                className="p-button-raised"
                onClick={() => {
                  this.toggleShowDialogItens();
                  this.setState({ selectedKeyItem: rowData.key, selectedLote: lote });
                }}
              />
            </>
          );
        },
      },
    ];

    return (
      <>
        <DataTable rowHover emptyMessage="Nenhum item disponível" value={keyedListItens(lote.itens)}>
          {this.renderColumns(availableItensColumns)}
        </DataTable>
      </>
    );
  }

  rendeItensLoteTable(lote) {
    const { selectedLote } = this.state;
    const { keyedListItens, setItemLoteValorTotal, valorTotalLote } = this.store;

    const availableItensColumns = [
      {
        field: 'quantidade',
        header: 'Quantidade',
        style: { width: '10%' },
      },
      {
        header: 'Descrição',
        style: { width: '30%' },
        body: (rowData) => {
          return getValue(rowData.materialDetalhamento?.pdm?.nome);
        },
      },
      {
        header: 'Valor Estimado',
        style: { width: '10%' },
        body: (rowData) => {
          return getValueMoney(rowData.valorUnitarioEstimado ?? 0, this.props?.termo?.tresCasasDecimais ? 3 : 2);
        },
      },
      {
        header: 'Valor Unitário',
        style: { width: '10%' },
        body: (rowData) => {
          return (
            <InputMonetary
              placeholder="R$"
              value={rowData.valorUnitario}
              onChange={(value) => {
                setItemLoteValorTotal(rowData, value);
                this.setState({ valorTotalLote: valorTotalLote(selectedLote) });
              }}
              decimalPlaces={this.props?.termo?.tresCasasDecimais ? 3 : 2}
            />
          );
        },
      },
      {
        header: 'Valor Total por Item',
        style: { width: '30%' },
        body: (rowData) => {
          return getValueMoney((rowData?.valorUnitario ?? 0) * rowData.quantidade);
        },
      },
    ];

    return (
      <>
        <DataTable rowHover emptyMessage="Nenhum item disponível" value={keyedListItens(lote.itens)}>
          {this.renderColumns(availableItensColumns)}
        </DataTable>
      </>
    );
  }

  toggleRow(row) {
    const expandedRows = this.state.expandedRows ? this.state.expandedRows : [];
    expandedRows[row.value.id] = expandedRows[row.value.id] ? undefined : true;
    this.setState({ expandedRows: expandedRows });
  }

  renderTableLotes() {
    const columns = [
      {
        field: 'nome',
        header: 'Nome Lote',
        style: { width: '100%' },
        body: (row) => row.nome,
      },
      {
        style: { width: '110px', textAlign: 'center' },
        body: (row) => (
          <FcButton
            type="button"
            icon="pi pi-plus"
            tooltip="Adicionar lote"
            label="Adicionar"
            onClick={() => {
              this.toggleShowDialogLotes();
              this.setState({ selectedLote: row });
              this.store.clearFormAddLote(row);
            }}
          />
        ),
      },
    ];

    return (
      <>
        <DataTable
          rowHover
          emptyMessage="Nenhum lote disponível"
          expandedRows={this.state.expandedRows}
          onRowToggle={(e) => this.setState({ expandedRows: e.data })}
          rowExpansionTemplate={(rowData) => this.rendeItensTable(rowData)}
          value={this.store.availableItensListKeyed()}
          selectionMode="single"
          onSelectionChange={this.toggleRow}
          tabIndex={'key'}
          className="datatable-responsive p-datatable-sm"
        >
          <Column expander style={{ width: '3em' }} />
          {this.renderColumns(columns)}
        </DataTable>

        {this.renderDialogItens()}
        {this.renderDialogLotes()}
      </>
    );
  }

  rendeItensTableFornecedor(itens) {
    const { keyedListItens } = this.store;

    const columns = [
      {
        field: 'quantidade',
        header: 'Quantidade',
      },

      {
        header: 'Item',
        body: (rowData) => {
          return getValue(rowData?.itemCatalogo?.materialDetalhamento?.pdm?.nome);
        },
      },

      {
        header: 'Valor Unitário',
        body: (rowData) => {
          return getValueMoney(rowData?.valorUnitario ?? 0);
        },
      },
      {
        header: 'Valor Total Item',
        body: (rowData) => {
          return getValueMoney(rowData?.valorTotal ?? 0);
        },
      },
    ];

    return (
      <>
        <DataTable rowHover emptyMessage="Nenhum item disponível" value={keyedListItens(itens)}>
          {this.renderColumns(columns)}
        </DataTable>
      </>
    );
  }

  renderTableLotesFornecedor() {
    const { arvoreLotesListKeyed } = this.store;

    const columns = [
      {
        field: 'nome',
        header: 'Nome Lote',
        style: { width: '100%' },
        body: (row) => row.lote.nome,
      },
      {
        style: { width: '110px', textAlign: 'center' },
        body: (row) => {
          return (
            <FcButton
              type="button"
              icon="pi pi-trash"
              className="p-button-danger p-mr-1"
              tooltip="Apagar lote"
              onClick={() => {
                this.store.removeLoteLicitante(row);
                this.store.loadedArvoreLoteItens();
              }}
            />
          );
        },
      },
    ];

    return (
      <>
        <DataTable
          rowHover
          emptyMessage="Nenhum lote adicionado"
          expandedRows={this.state.expandedRows}
          onRowToggle={(e) => this.setState({ expandedRows: e.data })}
          rowExpansionTemplate={(rowData) => this.rendeItensTableFornecedor(rowData.itens)}
          value={arvoreLotesListKeyed()}
          selectionMode="single"
          onSelectionChange={this.toggleRow}
          tabIndex={'key'}
          className="datatable-responsive p-datatable-sm"
        >
          <Column expander style={{ width: '3em' }} />
          {this.renderColumns(columns)}
        </DataTable>

        {this.renderDialogItens()}
      </>
    );
  }

  renderTableFornecedor() {
    const { editaFornecedorCarona } = this.store;
    const columns = [
      {
        style: { width: '60%' },
        field: 'licitante',
        header: 'Licitante',
        body: ({ licitante }) => licitante?.nome,
      },
      {
        style: { width: '10%' },
        field: 'valor',
        header: 'Valor Total',
        body: ({ valor }) => getValueMoney(valor ?? 0),
      },
      {
        style: { width: '18%', align: 'center' },

        body: (rowData) => {
          return (
            rowData.valor && (
              <>
                <FcButton
                  type="button"
                  icon="pi pi-pencil"
                  className="p-button-success p-mr-1"
                  onClick={() => {
                    this.store.updateLotesView();
                    this.toggleShowDialogFornecedor();
                    this.setState({ editFornecedor: true });
                    editaFornecedorCarona(rowData.key);
                  }}
                />
                <FcButton
                  type="button"
                  icon="pi pi-trash"
                  className="p-button-danger p-mr-1"
                  onClick={() => {
                    this.store.removeFornecedor(rowData);
                  }}
                />
              </>
            )
          );
        },
      },
    ];
    return (
      <DataTable
        rowHover
        value={this.store.fornecedoresListKeyed}
        footer={
          <div style={{ textAlign: 'right' }} className="p-mr-5">
            Valor Total:{' '}
            <span>
              {this.store.totalValueFornecedores ? getValueMoney(this.store.totalValueFornecedores) : getValueMoney(0)}
            </span>
          </div>
        }
        emptyMessage="Nenhum fornecedor adicionado"
      >
        {this._renderColumns(columns)}
      </DataTable>
    );
  }

  toggleShowDialogFornecedor() {
    this.setState({ showVisualizationDialog: !this.state.showVisualizationDialog });
  }

  renderDialogFornecedorFooter() {
    const { addCaronaLicitante } = this.store;
    return (
      <div>
        <FcButton
          label="Cancelar"
          icon="pi pi-times"
          type="button"
          onClick={() => {
            this.toggleShowDialogFornecedor();
            this.store.clearFormFornecedor(this.state.editFornecedor);
          }}
          className="p-button-text"
        />
        <FcButton
          label="Salvar"
          onClick={() => {
            if (addCaronaLicitante(this.state.editFornecedor)) {
              const message = this.state.editFornecedor
                ? 'Edições salvas com sucesso!'
                : 'Fornecedor salvo com sucesso!';
              showNotification('success', null, message);
              this.setState({ editFornecedor: false });
              this.store.clearFormFornecedor(this.state.editFornecedor);
              this.toggleShowDialogFornecedor();
            }
          }}
        />
      </div>
    );
  }

  renderDialogFornecedores() {
    const { editFornecedor } = this.state;

    const licitanteForm = (props) => {
      return <LicitanteFormPage history={history} action="new" closeMethod={props.closeMethod} index="carona" />;
    };

    return (
      <>
        <Dialog
          header={editFornecedor ? 'Editar Fornecedor' : 'Adicionar Fornecedor'}
          footer={this.renderDialogFornecedorFooter}
          visible={this.state.showVisualizationDialog}
          onHide={() => {
            this.store.clearFormFornecedor(this.state.editFornecedor);
            this.setState({ showVisualizationDialog: false, editFornecedor: false });
          }}
          style={{ width: '80vw', maxWidth: '700px' }}
          draggable={false}
        >
          <form>
            <div className="p-fluid p-formgrid p-grid">
              <FormField columns={12} attribute="licitante" label="Licitante">
                <SelectDialog
                  value={this.store.object.licitante}
                  label="nome"
                  indexStore={new LicitanteIndexStore()}
                  onChange={(e) => this.store.updateAttribute('licitante', e)}
                  headerDialog="Licitante"
                  emptyMessage="Selecione um Licitante"
                  nullMessage="Licitante sem nome"
                  disabled={editFornecedor}
                  dialogColumns={[
                    {
                      field: 'nome',
                      header: 'Nome',
                      sortable: true,
                    },
                    {
                      field: 'cpfCnpj',
                      header: 'CPF/CNPJ',
                      sortable: true,
                    },
                  ]}
                  canCreate
                  formPage={licitanteForm}
                  searchFields={['nome', 'cpfCnpj']}
                />
              </FormField>

              <FormField columns={12} attribute="escolhaItens" label={''}>
                <Fieldset legend="Lotes">{this.renderTableLotes()} </Fieldset>
              </FormField>

              <FormField columns={12} attribute="itensEscolhidos" label={''}>
                <Fieldset legend="Itens Licitante">{this.renderTableLotesFornecedor()}</Fieldset>
              </FormField>

              <FormField columns={6} attribute="valor" label={'Valor Total '}>
                {<b> {getValueMoney(this.store.totalValueItens ?? 0)}</b>}
              </FormField>
            </div>
          </form>
        </Dialog>
      </>
    );
  }

  render() {
    const breacrumbItems = [
      { label: 'Adesão/Carona', url: UrlRouter.cadastrosConsulta.carona.index },
      { label: this.props.action === 'new' ? 'Novo' : 'Editar' },
    ];

    if (this.store.object) {
      return (
        <>
          <div className="p-col-3">
            <FcButton
              type="button"
              label="Adicionar Fornecedor"
              icon="pi pi-plus"
              onClick={() => {
                this.store.initializeObject();
                this.store.updateLotesView();
                this.props.termo
                  ? this.setState({ showVisualizationDialog: true })
                  : showNotification('error', null, 'Termo de referência não foi selecionado');
              }}
            />
          </div>
          <br />
          {this.renderDialogFornecedores()}
          {this.renderTableFornecedor()}
          {this.renderDialogItens()}
        </>
      );
    } else {
      return (
        <div className="card page">
          <AppBreadCrumb items={breacrumbItems} />
          <i className="pi pi-spin pi-spinner" style={{ fontSize: '2em' }}></i>
        </div>
      );
    }
  }
}

DialogFornecedor.propTypes = {
  termo: PropTypes.any,
  store: PropTypes.any,
};

export default DialogFornecedor;
