import React from 'react';
import { observer } from 'mobx-react';
import PropTypes from 'prop-types';
import EntidadeExternaFormStore from '../../stores/entidadeExterna/formStore';
import { InputText } from 'primereact/inputtext';
import UrlRouter from '../../constants/UrlRouter';
import GenericFormPage from 'fc/pages/GenericFormPage';
import FormField from 'fc/components/FormField';
import AppBreadCrumb from 'fc/components/AppBreadCrumb';
import AccessPermission from '~/constants/AccessPermission';
import PermissionProxy from 'fc/components/PermissionProxy';
import { InputMask } from 'primereact/inputmask';
import AsyncDropdown from 'fc/components/AsyncDropdown';

@observer
class EntidadeExternaFormPage extends GenericFormPage {
  constructor(props) {
    super(props, UrlRouter.administracao.entidadeExterna.index, AccessPermission.entidadeExterna);
    this.store = new EntidadeExternaFormStore();
  }

  componentDidMount() {
    const { id } = this.props;
    this.store.initialize(id);
  }

  render() {
    const { submitted } = this.state;
    const { updateAttribute, getRule, updateMunicipio } = this.store;
    const { submitFormData } = this;

    const breacrumbItems = [
      { label: 'Entidades Externas', url: UrlRouter.administracao.entidadeExterna.index },
      { label: this.props.action === 'new' ? 'Novo' : 'Editar' },
    ];

    let content;

    if (this.store.object) {
      content = (
        <>
          <AppBreadCrumb items={breacrumbItems} />
          <div className="card page form-action-buttons">
            <form onSubmit={submitFormData}>
              <div className="p-fluid p-formgrid p-grid">
                <FormField
                  columns={6}
                  attribute="nomeEntidadeExterna"
                  label="Nome"
                  rule={getRule('nomeEntidadeExterna')}
                  submitted={submitted}
                >
                  <InputText
                    onChange={(e) => updateAttribute('nomeEntidadeExterna', e)}
                    placeholder="Informe o nome"
                    value={this.store.object.nomeEntidadeExterna}
                  />
                </FormField>

                <FormField columns={6} attribute="cnpj" label="CNPJ" rule={getRule('cnpj')} submitted={submitted}>
                  <InputMask
                    mask={'99.999.999/9999-99'}
                    onChange={(e) => this.store.updateAttribute('cnpj', e)}
                    placeholder="Informe o CPF"
                    value={this.store.object.cnpj}
                    id="cnpj"
                  />
                </FormField>
                <FormField
                  columns={6}
                  attribute="municipio"
                  label="Cidade/UF"
                  rule={getRule('municipio')}
                  submitted={submitted}
                >
                  <AsyncDropdown
                    onChange={updateMunicipio}
                    value={this.store.object.municipio?.id}
                    placeholder="Selecione a cidade"
                    store={this.store.municipioSelectStore}
                  />
                </FormField>
              </div>
              {this.renderActionButtons()}
            </form>
          </div>
        </>
      );
    } else {
      content = (
        <div className="card page">
          <AppBreadCrumb items={breacrumbItems} />
          <i className="pi pi-spin pi-spinner" style={{ fontSize: '2em' }}></i>
        </div>
      );
    }
    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        {content}
      </PermissionProxy>
    );
  }
}

EntidadeExternaFormPage.propTypes = {
  id: PropTypes.any,
  action: PropTypes.any,
  history: PropTypes.any,
};

export default EntidadeExternaFormPage;
