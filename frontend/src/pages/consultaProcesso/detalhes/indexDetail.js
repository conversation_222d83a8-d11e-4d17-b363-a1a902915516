import React from 'react';
import PropTypes from 'prop-types';
import { observer } from 'mobx-react';
import './style.scss';
import { Divider } from 'primereact/divider';
import { DataTable } from 'primereact/datatable';
import { generateFullURL, getValue<PERSON><PERSON><PERSON><PERSON>, getValueDate, getValue<PERSON><PERSON> } from 'fc/utils/utils';
import { DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS } from 'fc/utils/date';
import { Column } from 'primereact/column';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import ConsultaProcessoIndexStore from '~/stores/consultaProcesso/indexStore';
import OcorrenciaLicitacaoDetailPage from '~/pages/ocorrenciaLicitacao/ocorrenciaLicitacaoDetailPage';
import FcButton from 'fc/components/FcButton';
import ConsultaContratoDetailPage from '../consultaContrato';
import { Feature<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ay<PERSON> } from 'react-leaflet';
import { Dialog } from 'primereact/dialog';
import FcCloseableTabView from 'fc/components/FcCloseableTabView';
import { Link } from 'react-router-dom/cjs/react-router-dom.min';
import ScrollMenu from 'fc/components/ScrollMenu';
import classNames from 'classnames';
import AppStore from 'fc/stores/AppStore';
import MultipleFileUploader from 'fc/components/MultipleFileUploader';
import UrlRouter from '~/constants/UrlRouter';

@observer
class ConsultaProcessoDetailPage extends React.Component {
  licitacao;

  constructor(props) {
    super(props);
    this.store = new ConsultaProcessoIndexStore();
    this.state = {
      visibleDialogObras: false,
      showDetails: false,
      activeTabIndex: 0,
    };
  }

  componentDidMount() {
    this.store.carregaUltimaAlteracao(this.props.licitacao.id);
    this.store.initializeTdaLicitacao(this.props.licitacao.id);
    if (this.props.licitacao.obra) {
      this.store.carregarEdificacaoObra(this.props.licitacao, (licitacao) => this._toggleDetails(licitacao));
    } else {
      this._toggleDetails(this.props.licitacao);
    }
    if (this.store?.idLicitacao) {
      this.store.hasAlert(this.store.idLicitacao, () => this.forceUpdate());
    }
  }

  _renderColumns(columns) {
    return columns.map((col) => <Column key={`col-${col.field}`} {...col} />);
  }

  getValueStyle() {
    return { fontSize: AppStore.layout === 'extenso' ? '1.26rem' : '1.1rem' };
  }

  getValueLayoutClassNames() {
    const tipoLayout = AppStore.layout;
    return { 'py-0': tipoLayout === 'compacto', 'py-2': tipoLayout === 'default' || tipoLayout === 'extenso' };
  }

  _renderValue(label, value, col = 12, type = 'value', url = '#') {
    return (
      <div
        style={this.getValueStyle()}
        className={classNames(
          `details-set details-display flex align-items-center p-col-12 p-md-${col} p-sm-${col >= 6 ? col : 6}`,
          this.getValueLayoutClassNames()
        )}
      >
        <div
          style={{ color: 'rgb(0 0 0 / 50%)', paddingLeft: 0 }}
          className="xl:col-2 lg:col-3 md:col-4 sm:col-4 details-attributte drawer-content-label"
        >
          {label}
        </div>

        {type == 'value' && (
          <div style={{ paddingLeft: 0 }} className="xl:col-10 lg:col-9 md:col-8 sm:col-8 details-value p-text-justify">
            {value ?? '-'}
          </div>
        )}

        {type == 'list' && (
          <div style={{ display: 'block', paddingLeft: 0 }}>
            {value?.length ? value.map((value) => <div className={`details-value p-text-justify`}>{value}</div>) : '-'}
          </div>
        )}

        {type == 'link' && (
          <div style={{ paddingLeft: 0 }} className="xl:col-10 lg:col-9 md:col-8 sm:col-8 details-value p-text-justify">
            {
              <Link target="_blank" rel="noreferrer" style={{ color: '#3F51B5' }} to={url}>
                <u>{value}</u>
              </Link>
            }
          </div>
        )}
      </div>
    );
  }

  _renderDivider(label) {
    return (
      <div style={{ flexBasis: 'auto', display: 'flex' }}>
        <h5
          style={{
            padding: '7px',
            whiteSpace: 'nowrap',
            width: 'auto',
            color: '#333333',
            alignContent: 'left',
            marginRight: '5px',
            marginTop: '11px',
          }}
        >
          {label}
        </h5>
        <Divider />
      </div>
    );
  }

  _toggleDetails(item) {
    this.setState((oldState) => {
      const newShowValue = !oldState.showDetails;
      return { showDetailsItemAditivo: newShowValue, selectedItemAditivo: newShowValue ? item : null };
    });
  }

  _renderAlert() {
    if (this.store.hasPermissionAlerta() && this.store.idAlerta) {
      return (
        <FcButton
          icon="pi pi-bell"
          label="Histórico de Alertas"
          className="p-button-danger my-3"
          onClick={() => {
            window.open(generateFullURL(UrlRouter.alerta.editar.replace(':id', this.store.idAlerta)), '_blank');
          }}
        />
      );
    }
  }

  _renderTabs() {
    const licitacao = this.props.licitacao;
    const tabs = [];
    tabs.push({
      id: 0,
      header: 'Natureza do Objeto',
      content: this._renderTabNaturezaDoObjeto(licitacao),
    });
    tabs.push({
      id: 1,
      header: 'Fontes de Recurso',
      content: this._renderTabFontesDeRecurso(licitacao),
    });
    tabs.push({
      id: 2,
      header: 'Formas de Publicação',
      content: this._renderTabFormasDePublicao(licitacao),
    });
    tabs.push({
      id: 3,
      header: 'Licitantes',
      content: this._renderTabLicitantes(licitacao),
    });
    tabs.push({
      id: 4,
      header: 'Vencedores',
      content: this._renderTabVencedores(licitacao),
    });
    tabs.push({
      id: 5,
      header: 'Arquivos',
      content: this._renderTabArquivosLicitacao(licitacao),
    });
    tabs.push({
      id: 6,
      header: 'Histórico de Atualizações',
      content: <OcorrenciaLicitacaoDetailPage ocorrenciaList={licitacao.ocorrencias} />,
    });
    tabs.push({
      id: 7,
      header: 'Contratos',
      content: <ConsultaContratoDetailPage store={this.store} onDetailContrato={this.props.onDetailContrato} />,
    });

    return (
      <FcCloseableTabView
        tabs={tabs}
        activeTabIndex={this.state.activeTabIndex}
        onChangeTab={(tab) => this.setState({ activeTabIndex: tab.id })}
      />
    );
  }

  _toggleDialogObras() {
    this.setState((oldState) => ({ visibleDialogObras: !oldState.visibleDialogObras }));
  }

  renderEdificacao(licitacao) {
    const obra = licitacao?.obra?.edificacao?.localizacao;
    const polyline = [];
    const polygon = [];
    let marker = [];
    if (obra) {
      if (obra.type === 'Polygon') {
        polygon.push(obra.coordinates[0]);
      } else if (obra.type === 'LineString') {
        polyline.push(obra.coordinates);
      } else {
        marker = obra.coordinates;
      }
    }
    return (
      <>
        {marker && marker.length > 0 && <Marker position={marker} radius={20} />}
        {polyline && polyline.length > 0 && <Polyline positions={polyline} />}
        {polygon && polygon.length > 0 && <Polygon positions={polygon} />}
      </>
    );
  }

  _renderDialogObras(licitacao) {
    let defaultCoordinates = [-8.921198844909668, -70.98129272460939];

    const obra = licitacao?.obra?.edificacao?.localizacao;

    if (obra) {
      if (obra.type === 'Polygon') {
        defaultCoordinates = obra.coordinates[0][0];
      } else if (obra.type === 'LineString') {
        defaultCoordinates = obra.coordinates[0];
      } else {
        defaultCoordinates = obra.coordinates;
      }
    }

    return (
      <Dialog
        header="Localização de obra"
        visible={this.state.visibleDialogObras}
        style={{ width: '80%' }}
        footer={[]}
        onHide={() => this._toggleDialogObras()}
      >
        <div>
          <MapContainer center={defaultCoordinates} zoom={16} style={{ height: '70vh', width: '100wh' }}>
            <TileLayer
              attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
              url="https://tile.openstreetmap.org/{z}/{x}/{y}.png"
            />
            <FeatureGroup />

            {obra && this.renderEdificacao(licitacao)}
          </MapContainer>
        </div>
      </Dialog>
    );
  }

  _renderButton(col = 12) {
    return (
      <div className={`p-col-${col}`}>
        <FcButton
          label="Visualizar no mapa"
          type="button"
          className="p-ml-auto p-button-secondary p-mr-2"
          onClick={() => this._toggleDialogObras()}
        />
      </div>
    );
  }

  _renderTabNaturezaDoObjeto(licitacao) {
    const naturezasDoObjetoKeys = licitacao.naturezasDoObjeto ?? [];
    const filteredList = DadosEstaticosService.getNaturezaObjetoLicitacao().filter((item) =>
      naturezasDoObjetoKeys.includes(item.value)
    );
    const columns = [
      {
        field: 'text',
        header: 'Nome',
      },
    ];
    return (
      <DataTable rowHover value={filteredList} className="p-datatable-sm" emptyMessage="Nenhuma natureza adicionada">
        {this._renderColumns(columns)}
      </DataTable>
    );
  }

  _renderTabFontesDeRecurso(licitacao) {
    const filteredList = licitacao.fontesDeRecurso;
    const columns = [
      {
        field: 'nome',
        header: 'Nome',
      },
    ];
    return (
      <DataTable
        rowHover
        value={filteredList}
        className="p-datatable-sm"
        emptyMessage="Nenhuma fonte de recurso adicionada"
      >
        {this._renderColumns(columns)}
      </DataTable>
    );
  }

  _renderTabFormasDePublicao(licitacao) {
    const columns = [
      {
        field: 'tpFormaPublicacao',
        header: 'Nome',
        body: ({ tpFormaPublicacao }) => tpFormaPublicacao.nome,
      },
      {
        header: 'Descrição',
        body: (rowData) =>
          this.store.verificaFormaPublicacao(rowData.tpFormaPublicacao.nome, rowData.descricao, rowData.pagina),
      },
    ];

    return (
      <DataTable
        rowHover
        value={licitacao.publicacoes}
        className="p-datatable-sm"
        emptyMessage="Nenhuma publicação adicionada."
      >
        {this._renderColumns(columns)}
      </DataTable>
    );
  }

  _renderTabLicitantes(licitacao) {
    const columns = [
      {
        field: 'nome',
        header: 'Nome',
      },
      {
        field: 'pessoaFisica',
        header: 'Tipo de Pessoa',
        body: ({ pessoaFisica }) => getValueByKey(pessoaFisica, DadosEstaticosService.getTipoPessoa()),
      },
      {
        field: 'internacional',
        header: 'Pessoa/Empresa Internacional',
        body: ({ internacional }) => getValueByKey(internacional, DadosEstaticosService.getSimNao()),
      },
      {
        field: 'cpfCnpj',
        header: 'CPF/CNPJ',
      },
    ];
    return (
      <DataTable rowHover value={licitacao.licitantes} emptyMessage="Nenhum licitante adicionado.">
        {this._renderColumns(columns)}
      </DataTable>
    );
  }

  _renderTabVencedores(licitacao) {
    const columns = [
      {
        field: 'licitante',
        header: 'Licitante',
        body: ({ licitante }) => licitante.nome,
      },
      {
        field: 'loteItem',
        header: 'Lote/Item',
      },
      {
        field: 'valor',
        header: 'Valor (R$)',
        body: ({ valor }) => getValueMoney(valor),
      },
    ];
    return (
      <DataTable rowHover value={licitacao.vencedores} emptyMessage="Nenhum vencedor adicionado.">
        {this._renderColumns(columns)}
      </DataTable>
    );
  }

  _renderTitle(licitacao) {
    const title = `${licitacao.numero}/${licitacao.ano}`;
    return licitacao.entidade ? title + ` - ${licitacao.entidade.nome}` : title;
  }

  _renderOrgaosParticipantes({ orgaosParticipantes }) {
    let content = '-';
    if (orgaosParticipantes && orgaosParticipantes.length > 0) {
      content = (
        <ul style={{ padding: '2px', listStyleType: 'none' }}>
          {orgaosParticipantes.map((element, idx) => {
            return (
              <li key={idx} style={{ margin: '7px', paddingTop: '2px', textAlign: 'left' }}>
                <i className="pi pi-chevron-circle-right p-mr-2" style={{ color: '#2a5dac' }}></i>
                <span
                  style={{
                    padding: '2px',
                  }}
                >
                  {element ? element.nome : '-'}
                </span>
              </li>
            );
          })}
        </ul>
      );
    }
    return content;
  }

  _renderTabArquivosLicitacao() {
    this.store.initialize(this.props.licitacao.id);

    return (
      <MultipleFileUploader
        fileTypes={DadosEstaticosService.getTipoArquivoLicitacao()}
        store={this.store.fileStore}
        multiple
        downloadOnly
      />
    );
  }

  render() {
    if (this.props.licitacao && !this.store.loading) {
      const { licitacao } = this.props;
      const { id } = licitacao;
      return (
        <div className="p-fluid  p-formgrid">
          <div className="relative">
            <div className="scroll-menu">
              <ScrollMenu
                title="sumário"
                layoutPosition="left"
                offsetTopOnScroll={50}
                links={[
                  { id: `detalhesLicitacao-${id}`, label: 'Detalhes da Licitação' },
                  { id: `informacoesComplementares-${id}`, label: 'Informações Complementares' },
                  { id: `gerenciamento-${id}`, label: 'Gerenciamento' },
                ]}
              />
            </div>
          </div>
          <div id={`detalhesLicitacao-${id}`}>{this._renderDivider('Detalhes da Licitação')}</div>
          {this._renderValue('Título', this._renderTitle(licitacao))}
          {this._renderValue('Sistema de Registro de Preços (SRP)', licitacao.termoReferencia?.srp ? 'Sim' : 'Não')}
          {this._renderValue(
            'Data de Abertura',
            getValueDate(licitacao.dataAbertura, DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS)
          )}
          {this._renderValue(
            licitacao.lei === 'LEI_N_14133' ? 'Tipos' : 'Tipo',
            licitacao.lei === 'LEI_N_14133'
              ? licitacao.tiposLicitacao?.map((tipo) => tipo.descricao).join(', ')
              : licitacao.tipo?.descricao
          )}
          {this._renderValue('Pregoeiro', licitacao.pregoeiro?.nome)}
          {this._renderValue('Valor Total Estimado', getValueMoney(licitacao.valorEstimado))}
          {this._renderValue('Objeto', licitacao.objeto)}
          {licitacao.obra && this._renderValue('Tipo da Obra', licitacao.obra?.tipo?.nome)}
          {licitacao.obra && this._renderValue('Categoria da Obra', licitacao.obra?.categoria?.nome)}
          {licitacao.obra &&
            this._renderValue('Obra', licitacao.obra?.edificacao ? this._renderButton(licitacao) : '-')}
          <div id={`informacoesComplementares-${id}`}>{this._renderDivider('Informações Complementares')}</div>
          {this._renderValue('Número do Processo Administrativo', licitacao.numeroProcessoAdm)}
          {this._renderValue('Comissão', licitacao.comissao?.tipo)}
          {this._renderValue('Órgãos Participantes', this._renderOrgaosParticipantes(licitacao))}
          {this._renderValue('Regência Legal', licitacao.regenciaLegal)}
          <div id={`gerenciamento-${id}`}>{this._renderDivider('Gerenciamento')}</div>
          {this._renderValue('Cadastrado por', licitacao.usuario.nome)}
          {this._renderValue(
            'Data/Hora de Cadastro',
            getValueDate(licitacao.dataCadastro, DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS)
          )}
          {this._renderValue('Alterado por', this.store.ultimaAlteracao?.nome)}
          {this._renderValue(
            'Data/Hora de Alteração',
            getValueDate(this.store.ultimaAlteracao?.data, DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS)
          )}
          <div style={{ width: '11%' }}>{this._renderAlert()}</div>
          {this._renderTabs()}
          {this._renderDialogObras(licitacao)}
        </div>
      );
    } else {
      return (
        <div>
          <i
            className="pi pi-spin pi-spinner"
            style={{
              marginTop: '20px',
              marginLeft: 'calc(50% - 20px)',
              fontSize: '2em',
            }}
          ></i>
        </div>
      );
    }
  }
}

ConsultaProcessoDetailPage.propTypes = {
  licitacao: PropTypes.any,
  onDetailContrato: PropTypes.func,
};

export default ConsultaProcessoDetailPage;
