import React from 'react';
import { observer } from 'mobx-react';
import GenericIndexPage from 'fc/pages/GenericIndexPage';
import AdvancedSearch from 'fc/components/AdvancedSearch';
import { DATE_FORMAT, DATE_PARSE_FORMAT } from 'fc/utils/date';
import {
  hasPermissionProxy,
  getValueDate,
  getValue,
  getValueMoney,
  checkUserGroup,
  generateFullURL,
} from 'fc/utils/utils';
import AccessPermission from '~/constants/AccessPermission';
import UrlRouter from '~/constants/UrlRouter';
import CardList from 'fc/components/CardList';
import ProcessoLicitacaoIndexStore from '~/stores/consultaProcesso/ProcessoLicitacaoIndexStore';

@observer
class ConsultaProcessoListagemPage extends GenericIndexPage {
  constructor(props) {
    super(props, AccessPermission.consultaProcessos);

    this.store = new ProcessoLicitacaoIndexStore();
  }

  onPageChange(e) {
    if (!this.store.loading && e) {
      const pagination = {
        page: {
          index: e.page + 1,
          size: e.rows,
        },
      };

      this.store.load(pagination);
    }
  }

  getCardEllipsisOptions(cardData) {
    const isAuditor = checkUserGroup('Auditor');
    const items = [];

    if (
      cardData.idRequisicaoModificacao &&
      hasPermissionProxy(AccessPermission.requisicaoModificacao.writePermission)
    ) {
      const itemToAdd = {
        label: 'Requisição de modificação pendente',
        icon: 'pi pi-exclamation-triangle',
      };

      if (isAuditor) {
        itemToAdd.url = generateFullURL(
          UrlRouter.auditoria.requisicaoModificacao.indexDetail.replace(':id', cardData.idRequisicaoModificacao)
        );
      } else {
        itemToAdd.url = generateFullURL(
          UrlRouter.administracao.requisicaoModificacao.indexDetail.replace(':id', cardData.idRequisicaoModificacao)
        );
      }

      items.push(itemToAdd);
    } else {
      if (hasPermissionProxy(this.getReadPermission())) {
        items.push({
          label: 'Detalhes',
          icon: 'pi pi-eye',
          command: () => this.props.onDetail(cardData),
        });
      }
      if (hasPermissionProxy(AccessPermission.licitacao.readPermission)) {
        items.push({
          label: 'Detalhes da Licitação',
          icon: 'pi pi-external-link',
          command: () => this.props.onDetailLicitacao(cardData),
        });
      }
    }

    return items;
  }

  render() {
    const fields = [
      {
        label: 'Número',
        field: 'numero',
        value: 'title',
        sortable: true,
        body: (cardData) => `Licitação: ${getValue(cardData.numeroAnoLicitacao)}`,
      },
      {
        label: 'Entidade/Órgão',
        field: 'entidade',
        value: 'subtitle',
        sortable: true,
        body: (cardData) => getValue(cardData.entidade.nome),
      },
      {
        label: 'Objeto',
        field: 'objeto',
        value: 'mainContent',
        sortable: true,
      },
      {
        label: 'Data de Abertura',
        field: 'dataAbertura',
        value: 'iconLabel',
        color: '#c73f16',
        icon: 'pi pi-calendar',
        sortable: true,
        body: ({ dataAbertura }) => getValueDate(dataAbertura, DATE_FORMAT, DATE_PARSE_FORMAT),
      },
      {
        label: 'Valor Estimado',
        field: 'valorEstimado',
        value: 'iconLabel',
        color: '#4da73b',
        icon: 'pi pi-money-bill',
        sortable: true,
        body: (cardData) =>
          getValueMoney(cardData?.valorEstimado, cardData?.termoReferencia?.tresCasasDecimais ? 3 : 2),
      },
      { label: 'Ações', value: 'ellipsisItems', body: (cardData) => this.getCardEllipsisOptions(cardData) },
    ];

    const exportFields = [
      {
        header: 'Número',
        field: 'numero',
      },
      {
        header: 'Entidade/Órgão',
        field: 'entidade',
        body: (cardData) => cardData.entidade?.nome ?? getValue(cardData.orgaoNome),
      },
      {
        header: 'Data de Abertura',
        field: 'dataAbertura',
        body: ({ dataAbertura }) => getValueDate(dataAbertura, DATE_FORMAT, DATE_PARSE_FORMAT),
      },
      {
        header: 'Objeto',
        field: 'objeto',
      },
    ];

    const header = () => this.renderTableDataExport(exportFields, 'licitacao', true);

    return (
      <>
        <AdvancedSearch
          searchParams={this.store.getAdvancedSearchParams()}
          store={this.store}
          searchFields={['numeroAnoLicitacao', 'entidade', 'objeto']}
          filterSuggest={this.store.getFilterSuggest()}
          useOr
        />
        <CardList
          fields={fields}
          store={this.store}
          header={header()}
          onTitleClick={(cardData) => {
            if (hasPermissionProxy(this.getReadPermission())) {
              return {
                command: () => this.props.onDetail(cardData),
              };
            }
          }}
        />
      </>
    );
  }
}

export default ConsultaProcessoListagemPage;
