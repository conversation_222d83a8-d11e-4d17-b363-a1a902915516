import React from 'react';
import PropTypes from 'prop-types';
import { observer } from 'mobx-react';
import FcButton from 'fc/components/FcButton';
import { Dialog } from 'primereact/dialog';
import AdvancedSearch from 'fc/components/AdvancedSearch';
import AditivoContratoIndexStore from '~/stores/contrato/aditivo/indexStore';
import SearchTypes from 'fc/components/AdvancedSearch/SearchTypes';
import { getValueByKey, getValueDate, getValueMoney } from 'fc/utils/utils';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import { DATE_FORMAT, DATE_PARSE_FORMAT } from 'fc/utils/date';
import { DataTable } from 'primereact/datatable';
import { Column } from 'react-virtualized';
import { Sidebar } from 'primereact/sidebar';
import AditivoDetailPage from '../contrato/aditivos/indexDetail.js';
import ContratoIndexDetailStore from '~/stores/contrato/indexDetailStore.js';

@observer
class ConsultaContratoDetailPage extends React.Component {
  constructor(props) {
    super(props);
    this.store = this.props.store;
    this.state = {
      selectedContrato: null,
      selectedItemAditivo: null,
      showDetails: false,
      showDetailsAditivo: false,
      showDetailsItemAditivo: false,
    };
    this.contratoStore = new ContratoIndexDetailStore();
    this.aditivoStore = new AditivoContratoIndexStore();
    this._toggleDetailsAditivos = this._toggleDetailsAditivos.bind(this);
    this._toggleDetailsItemAditivo = this._toggleDetailsItemAditivo.bind(this);
  }

  _toggleDetailsAditivos(item) {
    this.setState((oldState) => {
      const newShowValue = !oldState.showDetailsAditivo;
      return { showDetailsAditivo: newShowValue, selectedContrato: newShowValue ? item : null };
    });
  }

  _toggleDetailsItemAditivo(item) {
    this.setState((oldState) => {
      const newShowValue = !oldState.showDetailsItemAditivo;
      return { showDetailsItemAditivo: newShowValue, selectedItemAditivo: newShowValue ? item : null };
    });
  }

  _renderDialogAditivo() {
    const item = this.state.selectedContrato;
    if (item) {
      const columns = [
        {
          field: 'numero',
          header: 'Número',
          sortable: true,
        },
        {
          field: 'dataPublicacao',
          header: 'Data da Publicação',
          body: ({ dataPublicacao }) => getValueDate(dataPublicacao, DATE_FORMAT, DATE_PARSE_FORMAT),
          sortable: true,
        },
        {
          field: 'tipoAlteracao',
          header: 'Tipo de Alteração Contratual',
          body: ({ tipoAlteracao }) => getValueByKey(tipoAlteracao, DadosEstaticosService.getTipoAlteracaoContratual()),
          sortable: true,
        },
        {
          style: { width: '40px' },
          body: (rowData) => {
            return (
              <div className="actions p-d-flex p-jc-end">
                <FcButton
                  icon="pi pi-list"
                  tooltip="Detalhes Aditivo"
                  className="p-button-sm p-button-info p-mr-1"
                  onClick={() => this._toggleDetailsItemAditivo(rowData)}
                />
              </div>
            );
          },
        },
      ];

      const header = (
        <div style={{ width: '100%', textAlign: 'left' }}>
          <h4 style={{ margin: 0 }}>Aditivo - {this.state.selectedContrato?.numeroContrato}</h4>
        </div>
      );

      const { listKey, loading } = this.aditivoStore;

      const filterSuggest = [
        {
          id: '',
          field: 'contrato',
          operator: 'EQUAL_TO',
          value: this.state.selectedContrato?.id,
          formatted: '',
          fixed: true,
          completeParam: {
            field: 'contrato',
            label: 'Id Contrato',
            type: SearchTypes.NUMBER,
          },
        },
      ];

      return (
        <Dialog
          header={header}
          visible={this.state.showDetailsAditivo}
          style={{ width: '80vw' }}
          onHide={() => this.setState({ showDetailsAditivo: false })}
          draggable={false}
          resizable={false}
          dismissableMask
        >
          <AdvancedSearch
            searchParams={this.store.getAdvancedSearchParamsAditivo()}
            store={this.aditivoStore}
            searchFields={['numero']}
            filterSuggest={filterSuggest}
          />
          <DataTable
            rowHover
            value={listKey}
            className="p-datatable-sm"
            paginator
            emptyMessage="Nenhum Aditivo"
            rows={10}
            loading={loading}
          >
            {this._renderColumns(columns)}
          </DataTable>
          {this._renderDetalhesAditivo()}
        </Dialog>
      );
    }
  }

  _renderDetalhesAditivo() {
    const header = (
      <div style={{ width: '100%', textAlign: 'left' }}>
        <h4 style={{ margin: 0 }}>Aditivo - {this.state.selectedItemAditivo?.numero}</h4>
      </div>
    );
    return (
      <Sidebar
        position="right"
        style={{ width: 'auto' }}
        className="info-drawer"
        visible={this.state.showDetailsItemAditivo}
        onHide={() => this.setState({ showDetailsItemAditivo: false })}
      >
        {header}
        <AditivoDetailPage aditivo={this.state.selectedItemAditivo} />
      </Sidebar>
    );
  }

  _renderDialogDetalhesAditivo() {
    const item = this.state.selectedItemAditivo;
    if (item) {
      const header = (
        <div style={{ width: '100%', textAlign: 'left' }}>
          <h4 style={{ margin: 0 }}>Contrato - {this.state.selectedItemAditivo?.numero}</h4>
        </div>
      );

      return (
        <Dialog
          header={header}
          visible={this.state.showDetailsItemAditivo}
          style={{ width: '80vw' }}
          onHide={() => this.setState({ showDetailsItemAditivo: false })}
          draggable={false}
          resizable={false}
          dismissableMask
        >
          {this._renderDetalhesAditivo()}
        </Dialog>
      );
    }
  }

  _renderColumns(columns) {
    return columns.map((col) => <Column key={`col-${col.field}`} {...col} />);
  }

  render() {
    const columns = [
      {
        field: 'numero',
        header: 'Número',
        sortable: true,
      },
      {
        field: 'dataPublicacao',
        header: 'Data da Publicação',
        body: ({ dataPublicacao }) => getValueDate(dataPublicacao, DATE_FORMAT, DATE_PARSE_FORMAT),
        sortable: true,
      },
      {
        field: 'valorGlobal',
        header: 'Valor do Contrato',
        body: (rowData) => getValueMoney(rowData.valorGlobal),
      },
      {
        style: { width: '110px' },
        body: (rowData) => {
          return (
            <div className="actions p-d-flex p-jc-end">
              <FcButton
                icon="pi pi-eye"
                tooltip="Ver Contrato"
                className="p-button-sm p-mr-2"
                onClick={() => this.props.onDetailContrato(rowData)}
              />
              <FcButton
                icon="pi pi-copy"
                tooltip="Listagem Aditivos"
                className="p-button p-button-sm p-button-warning p-mr-1"
                onClick={() => this._toggleDetailsAditivos(rowData)}
              />
            </div>
          );
        },
      },
    ];

    const { listKey, loading } = this.contratoStore;

    return (
      <>
        <AdvancedSearch
          searchParams={this.store.getAdvancedSearchParamsContrato()}
          store={this.contratoStore}
          searchFields={['numero']}
          filterSuggest={this.store.getFilterSuggestContrato()}
        />
        <DataTable
          rowHover
          value={listKey}
          className="p-datatable-sm"
          paginator
          emptyMessage="Nenhum Contrato"
          rows={10}
          loading={loading}
        >
          {this._renderColumns(columns)}
        </DataTable>
        {this._renderDialogAditivo()}
      </>
    );
  }
}

ConsultaContratoDetailPage.propTypes = {
  store: PropTypes.any.isRequired,
  onDetailContrato: PropTypes.func,
};

export default ConsultaContratoDetailPage;
