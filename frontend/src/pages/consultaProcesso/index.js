import React from 'react';
import { observer } from 'mobx-react';
import GenericIndexPage from 'fc/pages/GenericIndexPage';
import ConsultaProcessoIndexStore from '~/stores/consultaProcesso/indexStore';
import ConsultaProcessoListagemPage from './indexListagem';
import ConsultaProcessoDetailPage from './detalhes/indexDetail';
import { getValue } from 'fc/utils/utils';
import AppBreadCrumb from 'fc/components/AppBreadCrumb';
import AccessPermission from '~/constants/AccessPermission';
import PermissionProxy from 'fc/components/PermissionProxy';
import ContratoDetailPage from '../contrato/indexDetail';
import FcCloseableTabView from 'fc/components/FcCloseableTabView';
import EditMod from '../licitacao/boardLicitacoes/editMod';
import AditivoDetailPage from '../contrato/aditivos/indexDetail';

@observer
class ConsultaProcessoIndexPage extends GenericIndexPage {
  constructor(props) {
    super(props, AccessPermission.consultaProcessos);
    this.store = new ConsultaProcessoIndexStore();

    this.state = {
      data: [
        {
          id: 1,
          header: 'Listagem',
          closeable: false,
          content: (
            <ConsultaProcessoListagemPage
              {...props}
              boardStore={this.store}
              onDetail={(licitacao) => this.onDetail(licitacao)}
              onDetailLicitacao={(licitacao) => this.onDetailLicitacao(licitacao)}
            />
          ),
        },
      ],
      count: 2,
      activeTabIndex: 0,
    };

    this.onDetailContrato = this.onDetailContrato.bind(this);
    this.setActiveTabIndex = this.setActiveTabIndex.bind(this);
    this.handleCloseTab = this.handleCloseTab.bind(this);
    this.onDetailLicitacao = this.onDetailLicitacao.bind(this);
    this.updateDatatable = this.updateDatatable.bind(this);
    this.onDetailAditivo = this.onDetailAditivo.bind(this);
    this.closeDeletedTab = this.closeDeletedTab.bind(this);
  }

  onDetail(licitacao) {
    const existingTab = this.state.data.find((tab) => tab.idLicitacaoConsultarProcesso === licitacao.id);
    if (existingTab) {
      this.setActiveTabIndex(existingTab);
    } else {
      const newLicitacaoConsultaProcessosDetailPage = {
        id: this.state.count,
        idLicitacaoConsultarProcesso: licitacao.id,
        header:
          getValue(licitacao.numero) +
          '/' +
          getValue(licitacao.ano) +
          `${licitacao.orgao ? ' - ' + licitacao.orgao : ''}`,
        closeable: true,
        content: <ConsultaProcessoDetailPage licitacao={licitacao} onDetailContrato={this.onDetailContrato} />,
      };
      this.setState({
        data: [...this.state.data, newLicitacaoConsultaProcessosDetailPage],
        count: this.state.count + 1,
      });
    }
  }

  getTitleLicitacao(licitacao) {
    return (
      getValue(licitacao.numero) + '/' + getValue(licitacao.ano) + `${licitacao.orgao ? ' - ' + licitacao.orgao : ''}`
    );
  }

  updateDatatable(idLicitacao) {
    this.store.reloadTableData(() => this.updateHeaderTab(idLicitacao));
  }

  updateHeaderTab(idLicitacao) {
    this.store.getLicitacaoById(idLicitacao, (licitacao) => {
      const dataUpdate = this.state.data.map((tab) => {
        if (tab.idLicitacao === idLicitacao) {
          tab.header = this.getTitleLicitacao(licitacao);
          tab.content = (
            <EditMod
              licitacao={licitacao}
              index={tab.id}
              history={this.props.history}
              updateDataTable={this.updateDatatable}
              store={this.store}
            />
          );
        }
        return tab;
      });

      this.setState({ data: dataUpdate });
    });
  }

  onDetailLicitacao(licitacao) {
    const existingTab = this.state.data.find((tab) => tab.idLicitacao === licitacao.id);
    if (existingTab) {
      this.setActiveTabIndex(existingTab);
    } else {
      const newLicitacaoDetailPage = {
        id: this.state.count,
        idLicitacao: licitacao.id,
        header: this.getTitleLicitacao(licitacao),
        closeable: true,
        content: (
          <div>
            <EditMod
              licitacao={licitacao}
              index={this.state.count}
              history={this.props.history}
              updateDataTable={this.updateDatatable}
              store={this.store}
              onRemove={this.closeDeletedTab}
            />
          </div>
        ),
      };
      this.setState({ data: [...this.state.data, newLicitacaoDetailPage], count: this.state.count + 1 });
    }
  }

  onDetailContrato(contrato) {
    const existingTab = this.state.data.find((tab) => tab.idContrato === contrato.id);
    if (existingTab) {
      this.setActiveTabIndex(existingTab);
    } else {
      const header = (
        <div style={{ width: '100%', textAlign: 'left' }}>
          <h5 style={{ margin: 0 }}>
            Contrato - {contrato.numeroContrato} - {contrato.entidade?.nome}
          </h5>
        </div>
      );
      const newContratoDetailPage = {
        id: this.state.count,
        idContrato: contrato.id,
        header: 'Contrato ' + contrato.numeroContrato,
        closeable: true,
        content: (
          <div>
            {header}
            <ContratoDetailPage idContrato={contrato?.id} onDetailAditivo={this.onDetailAditivo} />
          </div>
        ),
      };
      this.setState({ data: [...this.state.data, newContratoDetailPage], count: this.state.count + 1 });
    }
  }

  onDetailAditivo(aditivo) {
    const existingTab = this.state.data.find((tab) => tab.idAditivo === aditivo.id);
    if (existingTab) {
      this.setActiveTabIndex(existingTab);
    } else {
      const newAditivo = {
        id: aditivo.id,
        idAditivo: aditivo.id,
        header: 'Aditivo - ' + aditivo.numero + ' - Contr. ' + aditivo.contrato.numero,
        closeable: true,
        content: <AditivoDetailPage aditivo={aditivo} />,
      };
      this.setState({
        data: [...this.state.data, newAditivo],
        count: this.state.count + 1,
      });
    }
  }

  setActiveTabIndex(tab) {
    this.setState({ activeTabIndex: this.state.data.indexOf(tab) });
  }

  closeDeletedTab(idLicitacao) {
    const deletedTab = this.state.data.find((tab) => tab.idLicitacao === idLicitacao);
    this.handleCloseTab(deletedTab);
    this.setState({ activeTabIndex: 0 });
  }

  handleCloseTab(tab) {
    this.setState((prevState) => {
      return { data: prevState.data.filter((t) => t.id !== tab.id) };
    });
  }

  render() {
    const breacrumbItems = [{ label: 'Consultar Processos' }];
    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        <AppBreadCrumb items={breacrumbItems} />
        <div className="card page index-table">
          <FcCloseableTabView
            tabs={this.state.data}
            activeTabIndex={this.state.activeTabIndex}
            onChangeTab={this.setActiveTabIndex}
            onClose={this.handleCloseTab}
            fixedFirstTab
          />
        </div>
      </PermissionProxy>
    );
  }
}

ConsultaProcessoIndexPage.displayName = 'ConsultaProcessoIndexPage';

export default ConsultaProcessoIndexPage;
