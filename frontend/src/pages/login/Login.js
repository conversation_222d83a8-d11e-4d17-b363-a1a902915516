import React, { useState } from 'react';
import { InputText } from 'primereact/inputtext';
import { useHistory } from 'react-router-dom';
import { observer } from 'mobx-react';
import { useStores } from '~/hooks/useStores';
import { Message } from 'primereact/message';
// eslint-disable-next-line no-restricted-imports
import { Button } from 'primereact/button';
import './style.scss';
import { getOriginUrl } from 'fc/utils/utils';

const Login = observer(() => {
  const history = useHistory();
  const { loginStore } = useStores();
  const [login, setLogin] = useState('');
  const [password, setPassword] = useState('');
  const origin = getOriginUrl();

  const pushUrlToHistory = (url) => {
    url && history.push(url);
  };

  const loginCallback = () => {
    const urlParams = new URLSearchParams(history.location.search);
    const callback = urlParams.get('callback');
    setLogin('');
    setPassword('');
    pushUrlToHistory(callback ?? '/');
  };

  const onLogin = () => {
    loginStore.sendLogin(login, password, loginCallback);
  };

  const handleKeypress = (e) => {
    if (e.code == 'NumpadEnter' || e.code == 'Enter') {
      onLogin();
    }
  };

  return (
    <div style={{ height: '100vh' }} className="pages-body login-page p-d-flex p-flex-column">
      <div className="p-as-center gap-8 p-mt-auto p-mb-auto p-d-flex">
        <div className="p-as-center gap-3 p-mt-auto p-mb-auto p-d-flex p-flex-column">
          <img className="tce-logo-img" src={origin + '/assets/layout/images/pages/LOGO-VERSAO-PREFERENCIAL.png'} />
          <img className="lsi-ufcg-logo-img" src={origin + '/assets/layout/images/pages/logo-lsi-ufcg-2.png'} />
        </div>
        <div className="login-card-container pages-panel card p-d-flex p-flex-column">
          <div className="flex align-items-center justify-content-center gap-2 p-px-3 p-py-1">
            <img className="tce-icon-img" src={origin + '/assets/layout/images/pages/licon-icon-logo.png'} />
            <h1 className="login-title">LICON</h1>
          </div>

          <div style={{ marginTop: '1.5rem' }} className="input-panel p-d-flex p-flex-column p-px-3">
            <div className="p-inputgroup">
              <span className="p-inputgroup-addon">
                <i className="pi pi-user"></i>
              </span>
              <span className="p-float-label">
                <InputText type="text" id="username" value={login} onChange={(e) => setLogin(e.target.value)} />
                <label htmlFor="username">Login</label>
              </span>
            </div>

            <div className="p-inputgroup p-mt-3 p-mb-3">
              <span className="p-inputgroup-addon">
                <i className="pi pi-lock"></i>
              </span>
              <span className="p-float-label">
                <InputText
                  type="password"
                  id="password"
                  value={password}
                  onKeyPress={handleKeypress}
                  onChange={(e) => setPassword(e.target.value)}
                />
                <label htmlFor="password">Senha</label>
              </span>
            </div>
          </div>

          <div className="p-mb-3" style={{ maxWidth: '282px' }}>
            {loginStore.error && (
              <Message style={{ width: '100%' }} severity={loginStore.errorType} text={loginStore.error} />
            )}
          </div>
          <Button
            id="btn-login"
            className="p-mb-6 p-px-3"
            type="submit"
            onClick={onLogin}
            label="ENTRAR"
            loadingOptions={{ position: 'right' }}
            loading={loginStore.loading}
          />
        </div>
      </div>
    </div>
  );
});

export default Login;
