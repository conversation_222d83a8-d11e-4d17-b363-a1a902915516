import { observer } from 'mobx-react';
import { Column } from 'primereact/column';
import { DataTable } from 'primereact/datatable';
import React, { useState } from 'react';
import FcButton from 'fc/components/FcButton';
import { getValue, getValue<PERSON><PERSON><PERSON><PERSON>, getValueMoney, isValueValid } from 'fc/utils/utils';
import { Dialog } from 'primereact/dialog';
import { Fieldset } from 'primereact/fieldset';
import { InputText } from 'primereact/inputtext';
import InputMonetary from 'fc/components/InputMonetary';
import { Divider } from 'primereact/divider';
import FormField from 'fc/components/FormField';
import { Dropdown } from 'primereact/dropdown';
import FcInputTextarea from 'fc/components/FcInputTextarea';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import './style.scss';
import Lote from './lote';
import Resumo from './resumo';
import { Steps } from 'primereact/steps';
import classNames from 'classnames';
import LoteUnico from './loteUnico';
import ResumoLoteUnico from './resumoLoteUnico';
import FcDropdown from 'fc/components/FcDropdown';

const Vencedor = observer((props) => {
  const store = props.store;
  const [submitted, setSubmitted] = useState(false);
  const [activeIndexLotesItens, setActiveIndexLotesItens] = useState(0);

  const renderFooter = () => {
    const { entidadeAntiga, readOnly } = props;
    return (
      <div className="p-mt-10 form-actions-dialog">
        <Divider />
        <span className="p-d-flex">
          <FcButton
            label={readOnly ? 'Voltar' : 'Cancelar'}
            type="button"
            className="p-ml-auto p-button-secondary p-mr-2"
            onClick={() => store.toggleDialogVencedores()}
          />
          {!readOnly && (
            <FcButton
              label={store.edit ? 'Salvar' : 'Adicionar'}
              disabled={!entidadeAntiga && !store?.vencedor[store.propItens]?.length > 0}
              onClick={() => {
                setSubmitted(true);
                store.adicionarVencedor(() => setSubmitted(false));
              }}
            />
          )}
        </span>
      </div>
    );
  };

  const renderDialogVencedores = () => {
    const { labelLicitante, readOnly, reqModificacao, processoMigrado } = props;
    const alreadyModified =
      !store.modifyVencedor && store.vencedor.motivoAlteracao && store.vencedor.obsMotivoAlteracao;

    return (
      <>
        <Dialog
          header={`Adicionar ${labelLicitante.charAt(0).toUpperCase() + labelLicitante.slice(1)}es`}
          visible={store.showDialogVencedores}
          style={{ width: '80vw' }}
          draggable={false}
          closable={false}
          footer={renderFooter()}
        >
          <form className="p-fluid p-formgrid p-grid">
            <div className="p-col-12">
              <Fieldset className="p-col-12" legend={labelLicitante.charAt(0).toUpperCase() + labelLicitante.slice(1)}>
                <div className="p-fluid p-formgrid p-grid">
                  {store.vencedor && (
                    <FormField
                      columns={9}
                      attribute="licitante"
                      label="Licitante"
                      submitted={submitted}
                      rule={store.getRule('licitante')}
                    >
                      <div className="p-fluid p-grid p-formgrid">
                        <div className="p-col-10">
                          {store.edit && !store.modifyVencedor ? (
                            <InputText
                              value={
                                reqModificacao && store.vencedor.newLicitante
                                  ? store.vencedor.newLicitante.nome?.trim()
                                  : store.vencedor?.licitante?.nome
                              }
                              disabled
                            />
                          ) : (
                            <Dropdown
                              value={store.vencedor.licitante}
                              onChange={(e) => store.updateLicitante(e.value)}
                              optionLabel="nome"
                              options={store.licitantes}
                              emptyMessage="Não há licitantes disponíveis"
                              placeholder={`Selecione um ${labelLicitante}`}
                            />
                          )}
                        </div>
                        {store.edit && !readOnly && (
                          <div className="p-col-2 align-items-center" style={{ display: 'flex' }}>
                            <FcButton
                              icon="pi pi-pencil"
                              className="p-button-sm p-button-success"
                              type="button"
                              onClick={() => store.changeModifyVencedor()}
                              disabled={alreadyModified}
                            />
                          </div>
                        )}
                      </div>
                    </FormField>
                  )}
                  {store.vencedor && !processoMigrado && (
                    <FormField
                      columns={3}
                      attribute="valor"
                      label="Valor"
                      submitted={submitted}
                      rule={store.getRule('valor')}
                    >
                      <InputMonetary
                        placeholder="R$"
                        value={store.vencedor.valor}
                        onChange={(e) => store.updateAttributeLicitante('valor', e)}
                        decimalPlaces={2}
                        disabled={readOnly}
                        min={0}
                      />
                    </FormField>
                  )}
                  {(store.modifyVencedor || alreadyModified) && (
                    <>
                      <FormField
                        columns={12}
                        attribute="motivoAlteracao"
                        label="Motivo"
                        submitted={submitted}
                        rule={store.getRule('motivoAlteracao')}
                      >
                        <FcDropdown
                          inOrder
                          value={store.vencedor.motivoAlteracao}
                          onChange={(e) => store.updateAttributeLicitante('motivoAlteracao', e)}
                          optionLabel="text"
                          options={DadosEstaticosService.getMotivoAlteracao()}
                          placeholder="Selecione um motivo para a mudança"
                          disabled={alreadyModified}
                        />
                      </FormField>
                      <FormField
                        columns={12}
                        attribute="obsMotivoAlteracao"
                        label="Descrição"
                        submitted={submitted}
                        rule={store.getRule('obsMotivoAlteracao')}
                      >
                        <FcInputTextarea
                          rows={4}
                          value={store.vencedor.obsMotivoAlteracao}
                          onChange={(e) => store.updateAttributeLicitante('obsMotivoAlteracao', e)}
                          placeholder="Informe o motivo da mudança"
                          disabled={alreadyModified}
                          hideRemaningChars={alreadyModified}
                        />
                      </FormField>
                    </>
                  )}
                </div>
              </Fieldset>
            </div>
          </form>
        </Dialog>
      </>
    );
  };

  const renderLotesItens = () => {
    const { showDesconto, showEspecificacao, readOnly, labelLicitante, hideFracasso, disableFracasso } = props;
    const decimalPlaces = store?.termoReferencia?.tresCasasDecimais ? 3 : 2;
    const forms = [
      {
        label: (
          <span className="p-text-capitalize">
            {labelLicitante}es <span className={'lowercase'}>e respectivos itens</span>
          </span>
        ),
        step: 0,
        body: (
          <div className="p-col-12">
            {!store.isLoteUnico &&
              store?.lotes?.map((lote, idx) => {
                const status = store.statusLote(lote);
                return (
                  <Lote
                    className={classNames({
                      'panel-warning': 'warning' === status,
                      'panel-error': 'error' === status,
                      'panel-check': 'check' === status,
                      'panel-failed': 'failed' === status,
                    })}
                    lote={lote}
                    collapsed={idx > 3}
                    showDesconto={showDesconto}
                    showEspecificacao={showEspecificacao}
                    updateAttributeItem={(lote, item, attribute, e) =>
                      store.updateAttributeItem(lote, item, attribute, e, () => store.setValorTotal(lote, item))
                    }
                    setDesconto={(lote, item, e) => store.setDesconto(lote, item, e)}
                    setQuantidade={(lote, item, e) => store.setQuantidade(lote, item, e)}
                    setLicitanteLote={(lote, e) => store.setLicitanteLote(lote, e.value)}
                    removeLicitanteLote={(lote) => store.removeLicitanteLote(lote)}
                    licitantes={store?.licitantes}
                    decimalPlaces={decimalPlaces}
                    labelLicitante={labelLicitante}
                    setLoteItemFracassado={(lote) => store.setLoteItemFracassado(lote, false)}
                    setLoteItemNaoFracassado={(lote) => store.setLoteItemNaoFracassado(lote)}
                    fracassado={store.checkLoteFracassado(lote)}
                    hideFracasso={hideFracasso}
                    disableFracasso={disableFracasso}
                  />
                );
              })}
            {store.isLoteUnico && (
              <LoteUnico
                lote={store.getLoteUnico()}
                showDesconto={showDesconto}
                showEspecificacao={showEspecificacao}
                updateAttributeItem={(lote, item, attribute, e) =>
                  store.updateAttributeItem(lote, item, attribute, e, () => store.setValorTotal(lote, item))
                }
                setDesconto={(lote, item, e) => store.setDesconto(lote, item, e)}
                setQuantidade={(lote, item, e) => store.setQuantidade(lote, item, e)}
                setLicitanteLote={(lote, e) => store.setLicitanteLote(lote, e)}
                removeLicitanteLote={(lote) => store.removeLicitanteLote(lote)}
                licitantes={store?.licitantes}
                decimalPlaces={decimalPlaces}
                labelLicitante={labelLicitante}
                setLoteItemFracassado={(item) => store.setLoteItemFracassado(item)}
                hideFracasso={hideFracasso}
                disableFracasso={disableFracasso}
              />
            )}
          </div>
        ),
      },
      {
        label: 'Resumo',
        step: 1,
        body: (
          <div className="p-col-12">
            {!store.isLoteUnico && [
              ...store
                .getVencedoresAgrupados()
                ?.map((vencedor) => (
                  <Resumo
                    vencedor={vencedor}
                    decimalPlaces={decimalPlaces}
                    showDesconto={showDesconto}
                    showEspecificacao={showEspecificacao}
                    labelLicitante={labelLicitante}
                  />
                )),
              store.getLotesFracassadosAgrupados()?.length !== 0 && (
                <Resumo lotesFracassados={store.getLotesFracassadosAgrupados()?.flatMap((obj) => obj.lotes)} />
              ),
            ]}
            {store.isLoteUnico && [
              ...store
                .getVencedoresAgrupados()
                ?.map((vencedor) => (
                  <ResumoLoteUnico
                    vencedor={vencedor}
                    decimalPlaces={decimalPlaces}
                    showDesconto={showDesconto}
                    showEspecificacao={showEspecificacao}
                    labelLicitante={labelLicitante}
                  />
                )),
              store.getLotesFracassadosAgrupados()?.length !== 0 && (
                <ResumoLoteUnico
                  itensFracassados={store
                    .getLotesFracassadosAgrupados()
                    ?.flatMap((obj) => obj.lotes)
                    ?.flatMap((lote) => lote.itens)}
                />
              ),
            ]}
            {store.getVencedoresAgrupados()?.length === 0 && store.getLotesFracassadosAgrupados()?.length === 0 && (
              <span>Não há dados a serem exibidos.</span>
            )}
          </div>
        ),
      },
    ];
    return (
      <>
        {store.termoReferencia && (
          <Fieldset className="p-col-12" legend="Lotes e Itens">
            {!readOnly ? (
              <>
                <Steps
                  model={forms}
                  activeIndex={activeIndexLotesItens}
                  onSelect={(e) => setActiveIndexLotesItens(e.index)}
                  readOnly={false}
                />
                {forms.find((item) => item.step === activeIndexLotesItens).body}
              </>
            ) : (
              forms.find((item) => item.step === 1).body
            )}
          </Fieldset>
        )}
      </>
    );
  };

  const renderTableVencedores = () => {
    const { reqModificacao, valueFuncReqModificacao } = props;

    const columns = [
      {
        style: { width: reqModificacao ? '20%' : '50%' },
        field: 'licitante.nome',
        header: 'Nome',
        body: ({ licitante, newLicitante }) =>
          reqModificacao
            ? valueFuncReqModificacao(licitante, newLicitante, (value) => value?.nome ?? value)
            : licitante?.nome,
      },
      {
        style: { width: '160px' },
        field: 'licitante.cpfCnpj',
        header: 'CPF/CNPJ',
        body: ({ licitante, newLicitante }) =>
          reqModificacao
            ? valueFuncReqModificacao(licitante, newLicitante, (value) => value?.cpfCnpj ?? value)
            : licitante?.cpfCnpj,
      },
      reqModificacao && {
        style: { width: '25%', textOverflow: 'ellipsis' },
        field: 'motivoAlteracao',
        header: 'Motivo da Alteração',
        body: ({ motivoAlteracao, newMotivoAlteracao }) =>
          valueFuncReqModificacao(motivoAlteracao, newMotivoAlteracao, (value) => {
            const newValue = getValueByKey(value, DadosEstaticosService.getMotivoAlteracao());
            return isValueValid(newValue) && newValue !== '-' ? newValue : value;
          }),
      },
      reqModificacao && {
        style: { width: '20%', textOverflow: 'ellipsis' },
        field: 'obsMotivoAlteracao',
        header: 'Observação',
        body: ({ obsMotivoAlteracao, newObsMotivoAlteracao }) =>
          valueFuncReqModificacao(obsMotivoAlteracao, newObsMotivoAlteracao),
      },
      reqModificacao && {
        style: { width: '10%' },
        field: 'valor',
        header: 'Valor',
        body: ({ valor, newValor }) =>
          reqModificacao ? valueFuncReqModificacao(valor, newValor, getValue, false, true) : getValueMoney(valor),
      },
      reqModificacao && {
        style: { width: '15%' },
        field: 'modificado',
        header: 'Modificação',
        body: ({ modificado }) => {
          if (modificado) {
            const { label, color } = DadosEstaticosService.getLabelRequisicaoModificacao()[modificado];
            return (
              <span
                className="product-badge p-d-flex p-jc-center"
                style={{
                  background: color,
                  borderRadius: 5,
                  color: 'white',
                  textAlign: 'center',
                  width: '80%',
                  maxWidth: '90px',
                }}
              >
                {label}
              </span>
            );
          }
        },
      },
      !reqModificacao &&
        !readOnly && {
          style: { width: '110px' },
          body: (vencedor) => {
            return (
              <>
                <FcButton
                  type="button"
                  icon="pi pi-pencil"
                  className="p-button-success p-mr-1"
                  onClick={() => store.editVencedor(vencedor)}
                />
                <FcButton
                  type="button"
                  icon="pi pi-trash"
                  className="p-button-danger p-mr-1"
                  onClick={() => store.removeVencedor(vencedor)}
                />
              </>
            );
          },
        },
    ];

    const { labelLicitante } = props;

    return (
      <DataTable rowHover value={store.vencedores} emptyMessage={`Nenhum ${labelLicitante} adicionado`}>
        {columns.map((c, idx) => {
          return <Column key={`field-${idx}`} {...c} />;
        })}
      </DataTable>
    );
  };

  const { labelLicitante, readOnly, entidadeAntiga } = props;
  return (
    <div className="p-grid">
      {entidadeAntiga && !readOnly && (
        <div className="p-col-3">
          <FcButton
            type="button"
            label={`Adicionar ${labelLicitante.charAt(0).toUpperCase() + labelLicitante.slice(1)}`}
            icon="pi pi-plus"
            disabled={!store.licitantes?.length > 0}
            onClick={() => {
              store.toggleDialogVencedores();
            }}
          />
        </div>
      )}
      {entidadeAntiga && <div className="p-col-12">{renderTableVencedores()}</div>}
      {!entidadeAntiga && renderLotesItens()}
      {renderDialogVencedores()}
    </div>
  );
});

export default Vencedor;
