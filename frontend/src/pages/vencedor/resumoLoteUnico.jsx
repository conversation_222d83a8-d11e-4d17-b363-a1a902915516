import React, { useState } from 'react';

import { observer } from 'mobx-react';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { getNumberFractionDigits, getValueMoney, getValue } from 'fc/utils/utils';
import Panel from './panel';
import classNames from 'classnames';

const ResumoLoteUnico = observer((props) => {
  const [collapsed, setCollapsed] = useState(false);

  const { showDesconto, showEspecificacao, vencedor, decimalPlaces, labelLicitante, itensFracassados } = props;

  const toggleTitulo = () => {
    setCollapsed(!collapsed);
  };

  const _renderDataTable = () => {
    const columns = [
      {
        header: 'Código',
        body: (item) => getValue(item.itemLote?.materialDetalhamento?.codigo),
      },
      {
        header: 'Item',
        style: { width: '30%' },
        body: (item) => `${item.numero ? item.numero + ' - ' : ''} ${getValue(item.materialDetalhamento?.pdm?.nome)}`,
      },
      {
        header: 'Marca/Modelo',
        field: 'marcaModelo',
        body: (item) => getValue(item.marcaModelo),
      },
      {
        header: 'Descrição Complementar',
        style: { width: '20%' },
        body: (item) => getValue(item.itemLote?.descricaoComplementar),
      },
      {
        header: 'Unidade de Medida',
        body: (item) => getValue(item.itemLote?.unidadeMedida?.textUnidadeMedida),
      },
      showEspecificacao && {
        header: 'Especificação',
        field: 'especificacao',
        body: (item) => getValue(item.especificacao),
      },
      {
        header: 'Quantidade',
        field: 'quantidade',
        body: (item) => getNumberFractionDigits(item.quantidade, 2),
      },
      {
        header: 'Valor Negociado',
        field: 'valorUnitario',
        body: (item) => getValueMoney(item.valorUnitario, decimalPlaces),
      },
      showDesconto && {
        header: 'Desconto(%)',
        field: 'desconto',
        body: (item) => getNumberFractionDigits(item.desconto, 2),
      },
      {
        header: 'Valor Total',
        field: 'valorTotal',
        body: (item) => getValueMoney(item.valorTotal, decimalPlaces, 0),
      },
    ];

    const itens = vencedor?.lotes
      ?.map((lote) => {
        let item = null;
        if (lote?.itens?.length > 0) {
          item = lote.itens[0];
        }
        return item;
      })
      .filter((item) => item);
    return (
      <>
        <DataTable rowHover value={itens} emptyMessage={`Nenhum item adicionado`} style={{ width: '100%' }}>
          {columns.map((c, idx) => {
            return <Column key={`field-${idx}`} {...c} />;
          })}
        </DataTable>
      </>
    );
  };

  const _renderDataTableFracassado = () => {
    const columnsFracassadas = [
      {
        header: 'Item',
        style: { width: '30%' },
        body: (item) => `${item.numero ? item.numero + ' - ' : ''} ${getValue(item.materialDetalhamento?.pdm?.nome)}`,
      },
      {
        header: 'Quantidade',
        field: 'quantidade',
        body: (item) => getNumberFractionDigits(item.quantidade, 2),
      },
      {
        header: 'Valor Unitário Estimado',
        field: 'valorUnitarioEstimado',
        body: (item) => getValueMoney(item.valorUnitarioEstimado, decimalPlaces),
      },
      {
        header: 'Valor Total',
        field: 'valorTotal',
        body: (item) => getValueMoney(item.quantidade * item.valorUnitarioEstimado, decimalPlaces),
      },
    ];
    return (
      <>
        <DataTable rowHover value={itensFracassados} emptyMessage={`Nenhum item fracassado`} style={{ width: '100%' }}>
          {columnsFracassadas.map((c, idx) => {
            return <Column key={`field-${idx}`} {...c} />;
          })}
        </DataTable>
      </>
    );
  };

  const _renderHeader = () => {
    return (
      <>
        <div className="flex-left pointer" onClick={() => toggleTitulo()}>
          <div className="info-lote">
            <span className="feedback">
              <i className={classNames('p-m-2', { 'pi pi-angle-right': collapsed, 'pi pi-angle-down': !collapsed })} />
            </span>
            <strong className="p-ml-2" onClick={() => toggleTitulo()}>
              {vencedor?.licitante?.nome || (itensFracassados && 'Itens Fracassados')}
            </strong>
          </div>
        </div>
        <div className="flex-right p-mr-5 sm:w-26rem lg:w-17rem xl:w-30rem">
          <div>
            <strong className="p-mr-5">Total</strong>
            <span className="p-ml-2 text-disabled">
              {(vencedor && getValueMoney(vencedor.valor, decimalPlaces)) ||
                (itensFracassados &&
                  getValueMoney(
                    itensFracassados.map((i) => i.quantidade * i.valorUnitarioEstimado).reduce((a, b) => a + b, 0),
                    decimalPlaces,
                    2
                  ))}
            </span>
          </div>
        </div>
      </>
    );
  };

  let content = <span className="p-text-capitalize">Erro ao renderizar o {labelLicitante}</span>;
  if (vencedor) {
    content = (
      <div className="p-mt-2">
        <Panel header={_renderHeader()} content={_renderDataTable()} collapsed={collapsed} />
      </div>
    );
  }
  if (itensFracassados) {
    content = (
      <div className="p-mt-2">
        <Panel header={_renderHeader()} content={_renderDataTableFracassado()} collapsed={collapsed} />
      </div>
    );
  }
  return content;
});

export default ResumoLoteUnico;
