import React, { useState } from 'react';

import './style.scss';
import { observer } from 'mobx-react';
import classNames from 'classnames';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { InputText } from 'primereact/inputtext';
import InputMonetary from 'fc/components/InputMonetary';
import FcButton from 'fc/components/FcButton';
import { Tag } from 'primereact/tag';
import { getNumberFractionDigits, getValue, getValueMoney } from 'fc/utils/utils';
import Panel from './panel';
import { Dropdown } from 'primereact/dropdown';
import Tooltip from 'fc/components/Tooltip';
import { Dialog } from 'primereact/dialog';
import { InputTextarea } from 'primereact/inputtextarea';
import { ConfirmDialog } from 'primereact/confirmdialog';
import { InputNumber } from 'primereact/inputnumber';

const Lote = observer((props) => {
  const [collapsed, setCollapsed] = useState(props.collapsed);
  const [editing, setEditing] = useState({});
  const [dialogVisible, setDialogVisible] = useState(false);
  const [loteFracassadoDialogVisible, setLoteFracassadoDialogVisible] = useState(false);
  const [loteNaoFracassadoDialogVisible, setLoteNaoFracassadoDialogVisible] = useState(false);
  const [obsItem, setObsItem] = useState('');
  const [itemSelected, setItemSelected] = useState({});
  const [itemError, setItemError] = useState({});

  const toggleLote = () => {
    setCollapsed(!collapsed);
  };

  const enableEdit = (item, field) => {
    if (!item.preenchido) {
      const itemId = item.id;
      const newEditing = editing[itemId] ?? {};
      if (!newEditing[itemId]) {
        newEditing[itemId] = {};
      }
      newEditing[itemId][field] = true;
      setEditing(newEditing);
    }
  };

  const handleKey = (key) => {
    if (key === 'Enter') {
      setEditing({});
    }
  };

  const _renderDialogLoteFracassado = () => {
    const { setLoteItemFracassado } = props;
    return (
      <ConfirmDialog
        header={<strong>Fracassar Lote</strong>}
        message={
          <strong>
            Você está indicando o lote como{' '}
            <span style={{ color: 'rgba(255, 0, 0, 1)', textDecorationLine: 'underline' }}>fracassado</span>. Deseja
            continuar?
          </strong>
        }
        visible={loteFracassadoDialogVisible}
        onHide={() => {
          setLoteFracassadoDialogVisible(false);
        }}
        accept={() => {
          setLoteItemFracassado(lote, false);
          setLoteFracassadoDialogVisible(false);
        }}
      />
    );
  };

  const _renderDialogLoteNaoFracassado = () => {
    const { setLoteItemNaoFracassado } = props;
    return (
      <ConfirmDialog
        header={<strong>Reverter Lote Fracassado</strong>}
        message={
          <strong>
            Você <span style={{ color: 'rgba(255, 0, 0, 1)', textDecorationLine: 'underline' }}>não está mais</span>{' '}
            indicando o lote como{' '}
            <span style={{ color: 'rgba(255, 0, 0, 1)', textDecorationLine: 'underline' }}>fracassado</span>. Deseja
            continuar?
          </strong>
        }
        visible={loteNaoFracassadoDialogVisible}
        onHide={() => {
          setLoteNaoFracassadoDialogVisible(false);
        }}
        accept={() => {
          setLoteItemNaoFracassado(lote, false);
          setLoteNaoFracassadoDialogVisible(false);
        }}
      />
    );
  };

  const _renderDialogObs = () => {
    const { lote } = props;
    return (
      <Dialog
        header="Observação"
        className="dialog-obs"
        visible={dialogVisible}
        onHide={() => setDialogVisible(false)}
        footer={
          <div>
            <FcButton
              label="Cancelar"
              icon="pi pi-times"
              className="p-button-danger"
              onClick={() => setDialogVisible(false)}
            />
            <FcButton
              label="Salvar"
              icon="pi pi-check"
              onClick={() => {
                props.updateAttributeItem(lote, itemSelected, 'observacao', obsItem);
                setDialogVisible(false);
              }}
            />
          </div>
        }
      >
        <div className="p-fluid p-formgrid p-grid">
          <InputTextarea
            onChange={(e) => setObsItem(e.target.value)}
            rows={4}
            value={obsItem}
            placeholder="Descreva a Observação"
          />
        </div>
      </Dialog>
    );
  };

  const _renderDataTable = () => {
    const {
      updateAttributeItem,
      setQuantidade,
      setDesconto,
      showEspecificacao,
      decimalPlaces,
      showDesconto,
      lote,
      fracassado,
    } = props;

    const columns = [
      {
        header: 'Código',
        body: (item) => getValue(item.itemLote?.materialDetalhamento?.codigo),
      },
      {
        header: 'Item',
        style: { width: '30%' },
        body: (item) => `${item.numero ? item.numero + ' - ' : ''} ${getValue(item.materialDetalhamento?.pdm?.nome)}`,
      },
      {
        header: (
          <span>
            Marca/Modelo<span className="p-error"> *</span>
          </span>
        ),
        body: (item) =>
          editing[item.id]?.marcaModelo || !item.preenchido ? (
            <InputText
              className={itemError[item.id] ? 'p-invalid p-error' : ''}
              onChange={(e) => updateAttributeItem(lote, item, 'marcaModelo', e)}
              placeholder="Informe a Marca/Modelo"
              value={item.marcaModelo}
              onKeyDown={({ key }) => handleKey(key)}
            />
          ) : (
            <span
              className={classNames({ pointer: !item.preenchido, 'p-pr-5 p-pl-5': !item.marcaModelo })}
              onClick={() => enableEdit(item, 'marcaModelo')}
            >
              {getValue(item.marcaModelo)}
            </span>
          ),
      },
      {
        header: 'Descrição Complementar',
        style: { width: '20%' },
        body: (item) => `${getValue(item.descricaoComplementar)}`,
      },
      {
        header: 'Unidade de Medida',
        body: (item) => getValue(item.itemLote?.unidadeMedida?.textUnidadeMedida),
      },
      showEspecificacao && {
        header: 'Especificação',
        body: (item) =>
          !editing[item.id]?.especificacao && !item.preenchido ? (
            <InputText
              onChange={(e) => updateAttributeItem(lote, item, 'especificacao', e)}
              placeholder="Informe a Especificação"
              value={item.especificacao}
              onKeyDown={({ key }) => handleKey(key)}
            />
          ) : (
            <span
              className={classNames('p-pr-5', { pointer: !item.preenchido, 'p-pr-5 p-pl-5': !item.especificacao })}
              onClick={() => enableEdit(item, 'especificacao')}
            >
              {getValue(item.especificacao)}
            </span>
          ),
      },
      {
        header: 'Quantidade',
        body: (item) =>
          !editing[item.id]?.quantidade && !item.preenchido ? (
            <InputMonetary
              value={item.quantidade}
              onChange={(e) => setQuantidade(lote, item, e)}
              min={0}
              max={item.quantidadeDisponivel}
              onKeyDown={({ key }) => handleKey(key)}
            />
          ) : (
            <span className={classNames({ pointer: !item.preenchido })} onClick={() => enableEdit(item, 'quantidade')}>
              {getNumberFractionDigits(item.quantidade)}
            </span>
          ),
      },
      {
        header: 'Valor Negociado',
        body: (item) =>
          !editing[item.id]?.valorNegociado && !item.preenchido ? (
            <InputNumber
              value={item.valorUnitario}
              onChange={(e) => handleInputValorNegociadoChange(e, item)}
              decimalPlaces={decimalPlaces}
              min={0}
              onKeyDown={({ key }) => handleKey(key)}
              mode="currency"
              currency="BRL"
              locale="pt-BR"
            />
          ) : (
            <span
              className={classNames({ pointer: !item.preenchido })}
              onClick={() => enableEdit(item, 'valorNegociado')}
            >
              {getValueMoney(item.valorUnitario, decimalPlaces)}
            </span>
          ),
      },
      showDesconto && {
        header: 'Desconto(%)',
        body: (item) =>
          !editing[item.id]?.desconto && !item.preenchido ? (
            <InputMonetary
              onChange={(e) => setDesconto(lote, item, e)}
              placeholder="Desconto"
              value={item.desconto}
              min={0}
              max={100}
              onKeyDown={({ key }) => handleKey(key)}
            />
          ) : (
            <span className={classNames({ pointer: !item.preenchido })} onClick={() => enableEdit(item, 'desconto')}>
              {getNumberFractionDigits(item.desconto)}
            </span>
          ),
      },
      {
        header: 'Valor Total',
        body: (item) => <span className="text-disabled">{getValueMoney(item.valorTotal, decimalPlaces, 0)}</span>,
      },
      {
        header: 'Status',
        body: (item) =>
          fracassado ? (
            <Tag severity="warning" value="Fracassado" rounded />
          ) : item.preenchido ? (
            <Tag severity="success" value="Preenchido" rounded />
          ) : (
            <Tag severity="danger" value="Pendente" rounded />
          ),
      },
      !fracassado && {
        header: 'Ações',
        body: (item) => (
          <>
            {!item.preenchido && (
              <FcButton
                type="button"
                icon="pi pi-check"
                className="p-button-text toggle-button"
                onClick={() => {
                  if (item.marcaModelo) {
                    setEditing({});
                    updateAttributeItem(lote, item, 'preenchido', true);
                    setItemError((oldState) => {
                      oldState[item.id] = false;
                      return { ...oldState };
                    });
                  } else {
                    setItemError((oldState) => {
                      oldState[item.id] = true;
                      return { ...oldState };
                    });

                    setEditing((oldState) => {
                      if (!oldState[item.id]) {
                        oldState[item.id] = {};
                      }
                      oldState[item.id].marcaModelo = true;
                      return { ...oldState };
                    });
                  }
                }}
              />
            )}
            {item.fracassado ||
              (item.preenchido && (
                <FcButton
                  type="button"
                  icon="pi pi-pencil"
                  className="p-button-text toggle-button"
                  onClick={() => {
                    setEditing({});
                    updateAttributeItem(lote, item, 'preenchido', false);
                  }}
                />
              ))}
            {!item.fracassado && (
              <FcButton
                type="button"
                icon="pi pi-comment"
                className="p-button-text toggle-button"
                onClick={() => {
                  setItemSelected(item);
                  setObsItem(item.observacao);
                  setEditing({});
                  setDialogVisible(true);
                }}
              />
            )}
          </>
        ),
      },
    ];
    return (
      <DataTable
        rowHover
        value={lote.itens}
        emptyMessage="Nenhum item disponível"
        style={{ maxWidth: '100%' }}
        className="p-datatable-sm "
      >
        {columns.map((c, idx) => {
          return <Column key={`field-${idx}`} {...c} />;
        })}
      </DataTable>
    );
  };

  const handleInputValorNegociadoChange = (e, item) => {
    const value = e.value !== undefined && !isNaN(e.value) ? e.value : null;
    store.updateAttributeItem(item.itemLote?.id, item.licitante.id, 'valorUnitario', value);
  };

  const _renderHeader = () => {
    const { className, lote, licitantes, setLicitanteLote, labelLicitante, fracassado, hideFracasso, disableFracasso } =
      props;
    return (
      <>
        <div className="flex-left pointer" onClick={() => toggleLote()}>
          <div className="info-lote">
            <span className="feedback">
              <i className={classNames('p-m-2', { 'pi pi-angle-right': collapsed, 'pi pi-angle-down': !collapsed })} />
            </span>
            <strong className="p-ml-2" onClick={() => toggleLote()}>
              {lote.nome}
            </strong>
          </div>
        </div>
        <div className="flex-right p-mr-2" style={{ alignItems: 'center' }}>
          <div style={{ marginRight: '15px' }}>
            {!fracassado && (
              <>
                <strong className="p-mr-3 p-text-capitalize">{labelLicitante}</strong>
                <Dropdown
                  className="sm:w-26rem lg:w-17rem xl:w-30rem"
                  value={lote?.licitante}
                  onChange={(e) => setLicitanteLote(lote, e)}
                  optionLabel="nome"
                  options={licitantes}
                  emptyMessage="Não há licitantes disponíveis"
                  placeholder={`Selecione um Licitante`}
                />
              </>
            )}
          </div>
          {!hideFracasso &&
            (!fracassado ? (
              <FcButton
                label="Sinalizar Lote Fracassado"
                type="button"
                icon="pi pi-thumbs-down"
                className="p-button-raised p-button-danger"
                onClick={() => {
                  setLoteFracassadoDialogVisible(true);
                }}
                disabled={disableFracasso}
              />
            ) : (
              <FcButton
                label="Reverter Lote Fracassado"
                type="button"
                icon="pi pi-replay"
                className="p-button-raised p-button-success"
                onClick={() => {
                  setLoteNaoFracassadoDialogVisible(true);
                }}
                disabled={disableFracasso}
              />
            ))}
          <div className="feedback">
            {className?.includes('panel-check') && (
              <span className={`circle check`}>
                <Tooltip value="Preenchido" sideOffset={10} delayDuration={100}>
                  <i className="pi pi-check" />
                </Tooltip>
              </span>
            )}
            {className?.includes('panel-warning') && (
              <span className="circle warning">
                <Tooltip value="Parcialmente Preenchido" sideOffset={10} delayDuration={100}>
                  <span className="default">!</span>
                </Tooltip>
              </span>
            )}
            {className?.includes('panel-error') && (
              <span className="circle error">
                <Tooltip value="Não Preenchido" sideOffset={10} delayDuration={100}>
                  <i className="pi pi-times" />
                </Tooltip>
              </span>
            )}
          </div>
        </div>
      </>
    );
  };

  const { lote, className } = props;
  let content = <>Erro ao renderizar o lote</>;
  if (lote) {
    content = (
      <>
        <Panel className={className} header={_renderHeader()} content={_renderDataTable()} collapsed={collapsed} />
        {_renderDialogObs()}
        {_renderDialogLoteFracassado()}
        {_renderDialogLoteNaoFracassado()}
      </>
    );
  }
  return content;
});

export default Lote;
