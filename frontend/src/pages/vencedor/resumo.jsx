import React, { useState } from 'react';

import { observer } from 'mobx-react';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { getNumberFractionDigits, getValueMoney, getValue } from 'fc/utils/utils';
import Panel from './panel';
import classNames from 'classnames';

const Resumo = observer((props) => {
  const [collapsed, setCollapsed] = useState(false);
  const [expandedLotes, setExpandedLotes] = useState([]);

  const { vencedor, decimalPlaces, labelLicitante, lotesFracassados } = props;

  const toggleTitulo = () => {
    setCollapsed(!collapsed);
  };

  const renderItens = (lote) => {
    const { showDesconto, showEspecificacao, decimalPlaces } = props;
    const columns = [
      {
        header: 'Código',
        body: (item) => getValue(item.itemLote?.materialDetalhamento?.codigo),
      },
      {
        header: 'Item',
        style: { width: '30%' },
        body: (item) => `${item.numero ? item.numero + ' - ' : ''} ${getValue(item.materialDetalhamento?.pdm?.nome)}`,
      },
      {
        header: 'Marca/Modelo',
        field: 'marcaModelo',
        body: (item) => getValue(item.marcaModelo),
      },
      {
        header: 'Descrição Complementar',
        body: (item) => getValue(item.itemLote?.descricaoComplementar),
      },
      {
        header: 'Unidade de Medida',
        body: (item) => getValue(item.itemLote?.unidadeMedida?.textUnidadeMedida),
      },
      showEspecificacao && {
        header: 'Especificação',
        field: 'especificacao',
        body: (item) => getValue(item.especificacao),
      },
      {
        header: 'Quantidade',
        field: 'quantidade',
        body: (item) => getNumberFractionDigits(item.quantidade, 2),
      },
      {
        header: 'Valor Negociado',
        field: 'valorUnitario',
        body: (item) => getValueMoney(item.valorUnitario, decimalPlaces),
      },
      showDesconto && {
        header: 'Desconto(%)',
        field: 'desconto',
        body: (item) => getNumberFractionDigits(item.desconto, 2),
      },
      {
        header: 'Valor Total',
        field: 'valorTotal',
        body: (item) => getValueMoney(item.valorTotal, decimalPlaces, 0),
      },
    ];

    return (
      <DataTable rowHover value={lote.itens} emptyMessage={`Nenhum item adicionado`} style={{ width: '100%' }}>
        {columns.map((c, idx) => {
          return <Column key={`field-${idx}`} {...c} />;
        })}
      </DataTable>
    );
  };

  const renderLoteFracassado = (lote) => {
    const columnsFracassadas = [
      {
        header: 'Código',
        body: (item) => getValue(item.itemLote?.materialDetalhamento?.codigo),
      },
      {
        header: 'Item',
        style: { width: '30%' },
        body: (item) => `${item.numero ? item.numero + ' - ' : ''} ${getValue(item.materialDetalhamento?.pdm?.nome)}`,
      },
      {
        header: 'Descrição Complementar',
        style: { width: '20%' },
        body: (item) => `${getValue(item.descricaoComplementar)}`,
      },
      {
        header: 'Unidade de Medida',
        body: (item) => getValue(item.itemLote?.unidadeMedida?.textUnidadeMedida),
      },
      {
        header: 'Quantidade',
        field: 'quantidade',
        body: (item) => getNumberFractionDigits(item.quantidade, 2),
      },
      {
        header: 'Valor Unitário Estimado',
        field: 'valorUnitarioEstimado',
        body: (item) => getValueMoney(item.valorUnitarioEstimado, decimalPlaces),
      },
      {
        header: 'Valor Total',
        field: 'valorTotal',
        body: (item) => getValueMoney(item.quantidade * item.valorUnitarioEstimado, decimalPlaces, 0),
      },
    ];

    return (
      <>
        <DataTable rowHover value={lote.itens} emptyMessage={`Nenhum lote fracassado`} style={{ width: '100%' }}>
          {columnsFracassadas.map((c, idx) => {
            return <Column key={`field-${idx}`} {...c} />;
          })}
        </DataTable>
      </>
    );
  };

  const _renderDataTable = () => {
    const columnsLotes = [
      {
        field: 'nome',
        header: 'Lote',
        style: { width: '60%' },
      },
      {
        field: 'valor',
        header: 'Valor',
        style: { width: '40%' },
        body: (lote) => getValueMoney(lote.valor, decimalPlaces),
      },
    ];
    return (
      <DataTable
        rowHover
        value={vencedor.lotes}
        expandedRows={expandedLotes}
        onRowToggle={(e) => setExpandedLotes(e.data)}
        rowExpansionTemplate={(row) => renderItens(row)}
        emptyMessage="Nenhum lote disponível"
      >
        <Column expander style={{ width: '3em' }} />
        {columnsLotes.map((c, idx) => {
          return <Column key={`field-${idx}`} {...c} />;
        })}
      </DataTable>
    );
  };

  const _renderDataTableFracassado = () => {
    const columnsLotes = [
      {
        field: 'nome',
        header: 'Lote',
        style: { width: '60%' },
      },
      {
        field: 'valor',
        header: 'Valor',
        style: { width: '40%' },
        body: (lote) =>
          getValueMoney(
            lote.itens.map((i) => i.quantidade * i.valorUnitarioEstimado).reduce((a, b) => a + b, 0),
            decimalPlaces
          ),
      },
    ];
    return (
      <DataTable
        rowHover
        value={lotesFracassados}
        expandedRows={expandedLotes}
        onRowToggle={(e) => setExpandedLotes(e.data)}
        rowExpansionTemplate={(row) => renderLoteFracassado(row)}
        emptyMessage="Nenhum lote disponível"
      >
        <Column expander style={{ width: '3em' }} />
        {columnsLotes.map((c, idx) => {
          return <Column key={`field-${idx}`} {...c} />;
        })}
      </DataTable>
    );
  };

  const _renderHeader = () => {
    return (
      <>
        <div className="flex-left pointer" onClick={() => toggleTitulo()}>
          <div className="info-lote">
            <span className="feedback">
              <i className={classNames('p-m-2', { 'pi pi-angle-right': collapsed, 'pi pi-angle-down': !collapsed })} />
            </span>
            <strong className="p-ml-2" onClick={() => toggleTitulo()}>
              {vencedor?.licitante?.nome || (lotesFracassados && 'Lotes Fracassados')}
            </strong>
          </div>
        </div>
        <div className="flex-left p-mr-5 sm:w-26rem lg:w-17rem xl:w-30rem">
          <div>
            <strong className="p-mr-5">Total</strong>
            <span className="p-ml-2 text-disabled">
              {(vencedor && getValueMoney(vencedor.valor, decimalPlaces)) ||
                (lotesFracassados &&
                  getValueMoney(
                    lotesFracassados
                      .map((l) => l.itens.map((i) => i.quantidade * i.valorUnitarioEstimado).reduce((a, b) => a + b, 0))
                      .reduce((a, b) => a + b, 0),
                    decimalPlaces
                  ))}
            </span>
          </div>
        </div>
      </>
    );
  };

  let content = <span className="p-text-capitalize">Erro ao renderizar o {labelLicitante}</span>;
  if (vencedor) {
    content = (
      <div className="p-mt-2">
        <Panel header={_renderHeader()} content={_renderDataTable()} collapsed={collapsed} />
      </div>
    );
  }
  if (lotesFracassados) {
    content = (
      <div className="p-mt-2">
        <Panel header={_renderHeader()} content={_renderDataTableFracassado()} collapsed={collapsed} />
      </div>
    );
  }
  return content;
});

export default Resumo;
