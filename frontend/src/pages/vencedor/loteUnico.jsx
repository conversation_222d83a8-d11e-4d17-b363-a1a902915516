import React, { useEffect, useState } from 'react';

import './style.scss';
import { observer } from 'mobx-react';
import { InputText } from 'primereact/inputtext';
import FcButton from 'fc/components/FcButton';
import { Dropdown } from 'primereact/dropdown';
import { Dialog } from 'primereact/dialog';
import { InputTextarea } from 'primereact/inputtextarea';
import { PickList } from 'primereact/picklist';
import { getNumberFractionDigits, getValueMoney, showNotification, getValue } from 'fc/utils/utils';
import Tooltip from 'fc/components/Tooltip';
import classNames from 'classnames';
import InputMonetary from 'fc/components/InputMonetary';
import { Tag } from 'primereact/tag';
import { DataTable } from 'primereact/datatable';
import { Column } from 'react-virtualized';
import Panel from './panel';
import { ConfirmDialog } from 'primereact/confirmdialog';
import { InputNumber } from 'primereact/inputnumber';

const LoteUnico = observer((props) => {
  const [collapsed, setCollapsed] = useState({});
  const [editing, setEditing] = useState({});
  const [dialogVisible, setDialogVisible] = useState(false);
  const [itemFracassadoPickListVisible, setItemFracassadoPickListVisible] = useState(false);
  const [itemFracassadoDialogVisible, setItemFracassadoDialogVisible] = useState(false);
  const [obsItem, setObsItem] = useState('');
  const [itemSelected, setItemSelected] = useState({});
  const [itemError, setItemError] = useState({});

  const [vencedorLoteUnico, setVencedorLoteUnico] = useState({});
  const [sourceFilter, setSourceFilter] = useState('');
  const [targetFilter, setTargetFilter] = useState('');
  const [lotesFracassados, setLotesFracassados] = useState([]);

  const {
    lote,
    licitantes,
    setLicitanteLote,
    removeLicitanteLote,
    labelLicitante,
    updateAttributeItem,
    setQuantidade,
    setDesconto,
    showEspecificacao,
    decimalPlaces,
    showDesconto,
    setLoteItemFracassado,
    hideFracasso,
    disableFracasso,
  } = props;

  useEffect(() => {
    const lotes = [];
    props.lote.itens.forEach((item) => item.fracassado && lotes.push(item.lote));
    itemFracassadoPickListVisible && setLotesFracassados(lotes);
  }, [itemFracassadoPickListVisible]);

  const toggleLote = (id) => {
    const newCollpsed = { ...collapsed };
    newCollpsed[id] = !collapsed[id];
    setCollapsed(newCollpsed);
  };

  const enableEdit = (item, field) => {
    if (!item.preenchido) {
      const itemId = item.id;
      const newEditing = editing[itemId] ?? {};
      if (!newEditing[itemId]) {
        newEditing[itemId] = {};
      }
      newEditing[itemId][field] = true;
      setEditing(newEditing);
    }
  };

  const handleKey = (key) => {
    if (key === 'Enter') {
      setEditing({});
    }
  };

  const _renderDialogObs = () => {
    return (
      <Dialog
        header="Observação"
        className="dialog-obs"
        visible={dialogVisible}
        onHide={() => setDialogVisible(false)}
        footer={
          <div>
            <FcButton
              label="Cancelar"
              icon="pi pi-times"
              className="p-button-danger"
              onClick={() => setDialogVisible(false)}
            />
            <FcButton
              label="Salvar"
              icon="pi pi-check"
              onClick={() => {
                props.updateAttributeItem(itemSelected.lote, itemSelected, 'observacao', obsItem);
                setDialogVisible(false);
              }}
            />
          </div>
        }
      >
        <div className="p-fluid p-formgrid p-grid">
          <InputTextarea
            onChange={(e) => setObsItem(e.target.value)}
            rows={4}
            value={obsItem}
            placeholder="Descreva a Observação"
          />
        </div>
      </Dialog>
    );
  };

  const _renderDialogItensFracassados = () => {
    return (
      <ConfirmDialog
        header={<strong>Fracassar Itens</strong>}
        message={
          <strong>
            Você está indicando os itens como{' '}
            <span style={{ color: 'rgba(255, 0, 0, 1)', textDecorationLine: 'underline' }}>fracassados</span> ou
            tornando-os{' '}
            <span style={{ color: 'rgba(77, 167, 59, 1)', textDecorationLine: 'underline' }}>disponíveis</span>{' '}
            novamente.Tem certeza?
          </strong>
        }
        visible={itemFracassadoDialogVisible}
        onHide={() => {
          setItemFracassadoDialogVisible(false);
        }}
        accept={() => {
          setLoteItemFracassado(lotesFracassados);
          setLotesFracassados([]);
          setItemFracassadoDialogVisible(false);
          setItemFracassadoPickListVisible(false);
        }}
      />
    );
  };

  const checkItemFracassado = (item) => {
    return lotesFracassados?.find((l) => item.id === l.id) ? true : false;
  };

  const _renderPickListItensFracassados = () => {
    const itemTemplate = (item) => {
      return (
        <div className="flex flex-wrap p-2 align-items-center gap-3">
          <div className="flex-1 flex flex-column gap-2">
            <span className="font-bold">
              {item.numero ? item.numero + ' - ' : ''}
              {getValue(item.materialDetalhamento?.pdm?.nome)}
            </span>
          </div>
        </div>
      );
    };
    return (
      <Dialog
        header="Selecione os itens que você deseja fracassar"
        visible={itemFracassadoPickListVisible}
        modal
        footer={
          <div className="p-mt-2">
            <span className="p-mr-1 p-d-inline p-d-flex align-items-center">
              <FcButton
                label="Cancelar"
                type="button"
                className="p-ml-auto p-button-secondary p-mr-2"
                onClick={() => {
                  setItemFracassadoPickListVisible(false);
                }}
              />
              <FcButton
                label="Confirmar"
                onClick={() => {
                  setItemFracassadoDialogVisible(true);
                }}
              />
            </span>
          </div>
        }
        style={{ width: '80vw' }}
        onHide={() => setItemFracassadoPickListVisible(false)}
        draggable={false}
        resizable={false}
      >
        <div className="p-fluid p-formgrid p-grid">
          <span className="p-field p-col-6" style={{ paddingRight: '30px' }}>
            <span className="p-input-icon-left">
              <i className="pi pi-search" />
              <InputText value={sourceFilter} onChange={(e) => setSourceFilter(e.target.value?.toLocaleLowerCase())} />
            </span>
          </span>
          <span className="p-field p-col-6" style={{ paddingLeft: '30px' }}>
            <span className="p-input-icon-left">
              <i className="pi pi-search" />
              <InputText value={targetFilter} onChange={(e) => setTargetFilter(e.target.value?.toLocaleLowerCase())} />
            </span>
          </span>
        </div>
        <PickList
          source={lote?.itens?.filter(
            (item) =>
              !item.lote?.licitante &&
              !checkItemFracassado(item) &&
              (!sourceFilter ||
                item.materialDetalhamento?.pdm?.nome?.toLocaleLowerCase()?.includes(sourceFilter) ||
                sourceFilter == item.numero)
          )}
          target={lote?.itens?.filter(
            (item) =>
              checkItemFracassado(item) &&
              (!targetFilter ||
                item.materialDetalhamento?.pdm?.nome?.descricaoDetalhamento?.pdm?.nome
                  ?.toLocaleLowerCase()
                  ?.includes(targetFilter) ||
                targetFilter == item.numero)
          )}
          onChange={(e) => {
            setLotesFracassados(e.target?.map((value) => value.lote));
          }}
          itemTemplate={itemTemplate}
          sourceHeader="Itens Disponíveis"
          targetHeader="Itens Fracassados"
          showSourceControls={false}
          showTargetControls={false}
          sourceStyle={{ height: '342px' }}
          targetStyle={{ height: '342px' }}
        />
      </Dialog>
    );
  };

  const _renderVencedoresItens = () => {
    const itemTemplate = (item) => {
      return (
        <div className="flex flex-wrap p-2 align-items-center gap-3">
          <div className="flex-1 flex flex-column gap-2">
            <span className="font-bold">
              {item.numero ? item.numero + ' - ' : ''}
              {getValue(item.materialDetalhamento?.pdm?.nome)}
            </span>
          </div>
        </div>
      );
    };
    return (
      <div>
        <div className="p-fluid p-formgrid p-grid">
          <div className="p-field p-col-6">
            <strong className="p-mr-5 p-text-capitalize">{labelLicitante}</strong>
            <Dropdown
              className="sm:w-26rem lg:w-17rem xl:w-30rem"
              value={vencedorLoteUnico}
              onChange={(e) => setVencedorLoteUnico(e.value)}
              optionLabel="nome"
              options={licitantes}
              emptyMessage="Não há licitantes disponíveis"
              placeholder={`Selecione um Licitante`}
            />
          </div>
          <div className="p-field p-col-6" style={{ paddingLeft: '30px', paddingTop: '24px' }}>
            {!hideFracasso && (
              <FcButton
                label="Sinalizar Itens Fracassados"
                tooltip="Sinalizar Itens Fracassados"
                icon="pi pi-thumbs-down"
                type="button"
                className="p-button-raised p-button-danger"
                onClick={() => {
                  setItemFracassadoPickListVisible(true);
                }}
                disabled={disableFracasso}
              />
            )}
          </div>
          <span className="p-field p-col-6" style={{ paddingRight: '30px' }}>
            <span className="p-input-icon-left">
              <i className="pi pi-search" />
              <InputText value={sourceFilter} onChange={(e) => setSourceFilter(e.target.value?.toLocaleLowerCase())} />
            </span>
          </span>
          <span className="p-field p-col-6" style={{ paddingLeft: '30px' }}>
            <span className="p-input-icon-left">
              <i className="pi pi-search" />
              <InputText value={targetFilter} onChange={(e) => setTargetFilter(e.target.value?.toLocaleLowerCase())} />
            </span>
          </span>
        </div>
        <PickList
          source={lote?.itens?.filter(
            (item) =>
              !item.lote?.licitante &&
              !item.fracassado &&
              (!sourceFilter ||
                item.materialDetalhamento?.pdm?.nome?.toLocaleLowerCase()?.includes(sourceFilter) ||
                sourceFilter == item.numero)
          )}
          target={lote.itens?.filter(
            (item) =>
              vencedorLoteUnico?.id &&
              item.lote?.licitante?.id === vencedorLoteUnico?.id &&
              (!targetFilter ||
                item.materialDetalhamento?.pdm?.nome?.descricaoDetalhamento?.pdm?.nome
                  ?.toLocaleLowerCase()
                  ?.includes(targetFilter) ||
                targetFilter == item.numero)
          )}
          onChange={(e) => {
            if (vencedorLoteUnico?.id) {
              e.target?.forEach((item) => setLicitanteLote(item.lote, vencedorLoteUnico));
              e.source?.forEach((item) => {
                updateAttributeItem(item.lote, item, 'preenchido', false);
                removeLicitanteLote(item.lote, vencedorLoteUnico);
              });
            } else {
              showNotification(
                'warn',
                null,
                <span>
                  Selecione um <span className="p-text-capitalize">{labelLicitante}</span>
                </span>
              );
            }
          }}
          itemTemplate={itemTemplate}
          sourceHeader="Itens Disponíveis"
          targetHeader="Itens Adicionados"
          showSourceControls={false}
          showTargetControls={false}
          sourceStyle={{ height: '342px' }}
          targetStyle={{ height: '342px' }}
        />
      </div>
    );
  };

  const _renderDataTable = (itens) => {
    const columns = [
      {
        header: 'Código',
        body: (item) => getValue(item.itemLote?.materialDetalhamento?.codigo),
      },
      {
        header: 'Item',
        style: { width: '30%' },
        body: (item) => `${item.numero ? item.numero + ' - ' : ''} ${getValue(item.materialDetalhamento?.pdm?.nome)}`,
      },
      {
        header: (
          <span>
            Marca/Modelo<span className="p-error"> *</span>
          </span>
        ),
        body: (item) =>
          editing[item.id]?.marcaModelo || !item.preenchido ? (
            <InputText
              className={itemError[item.id] ? 'p-invalid p-error' : ''}
              onChange={(e) => updateAttributeItem(item.lote, item, 'marcaModelo', e)}
              placeholder="Informe a Marca/Modelo"
              value={item.marcaModelo}
              onKeyDown={({ key }) => handleKey(key)}
            />
          ) : (
            <span
              className={classNames({ pointer: !item.preenchido, 'p-pr-5 p-pl-5': !item.marcaModelo })}
              onClick={() => enableEdit(item, 'marcaModelo')}
            >
              {getValue(item.marcaModelo)}
            </span>
          ),
      },
      {
        header: 'Descrição Complementar',
        style: { width: '20%' },
        body: (item) => `${getValue(item.descricaoComplementar)}`,
      },
      {
        header: 'Unidade de Medida',
        body: (item) => getValue(item.itemLote?.unidadeMedida?.textUnidadeMedida),
      },
      showEspecificacao && {
        header: 'Especificação',
        body: (item) =>
          !editing[item.id]?.especificacao && !item.preenchido ? (
            <InputText
              onChange={(e) => updateAttributeItem(item.lote, item, 'especificacao', e)}
              placeholder="Informe a Especificação"
              value={item.especificacao}
              onKeyDown={({ key }) => handleKey(key)}
            />
          ) : (
            <span
              className={classNames({ pointer: !item.preenchido, 'p-pr-5 p-pl-5': !item.especificacao })}
              onClick={() => enableEdit(item, 'especificacao')}
            >
              {getValue(item.especificacao)}
            </span>
          ),
      },
      {
        header: 'Quantidade',
        body: (item) =>
          !editing[item.id]?.quantidade && !item.preenchido ? (
            <InputMonetary
              value={item.quantidade}
              onChange={(e) => setQuantidade(item.lote, item, e)}
              min={0}
              max={item.quantidadeDisponivel}
              onKeyDown={({ key }) => handleKey(key)}
            />
          ) : (
            <span className={classNames({ pointer: !item.preenchido })} onClick={() => enableEdit(item, 'quantidade')}>
              {getNumberFractionDigits(item.quantidade, 2)}
            </span>
          ),
      },
      {
        header: 'Valor Negociado',
        body: (item) =>
          !editing[item.id]?.valorNegociado && !item.preenchido ? (
            <InputNumber
              value={item.valorUnitario}
              onChange={(e) => handleInputValorNegociadoChange(e, item)}
              decimalPlaces={decimalPlaces}
              min={0}
              onKeyDown={({ key }) => handleKey(key)}
              mode="currency"
              currency="BRL"
              locale="pt-BR"
            />
          ) : (
            <span
              className={classNames({ pointer: !item.preenchido })}
              onClick={() => enableEdit(item, 'valorNegociado')}
            >
              {getValueMoney(item.valorUnitario, decimalPlaces)}
            </span>
          ),
      },
      showDesconto && {
        header: 'Desconto(%)',
        body: (item) =>
          !editing[item.id]?.desconto && !item.preenchido ? (
            <InputMonetary
              onChange={(e) => setDesconto(item.lote, item, e)}
              placeholder="Desconto"
              value={item.desconto}
              decimalPlaces={2}
              min={0}
              max={100}
              onKeyDown={({ key }) => handleKey(key)}
            />
          ) : (
            <span className={classNames({ pointer: !item.preenchido })} onClick={() => enableEdit(item, 'desconto')}>
              {getNumberFractionDigits(item.desconto, 2)}
            </span>
          ),
      },
      {
        header: 'Valor Total',
        body: (item) => <span className="text-disabled">{getValueMoney(item.valorTotal, decimalPlaces, 0)}</span>,
      },
      {
        header: 'Status',
        body: (item) =>
          item.preenchido ? (
            <Tag severity="success" value="Preenchido" rounded />
          ) : (
            <Tag severity="danger" value="Pendente" rounded />
          ),
      },
      {
        header: 'Ações',
        body: (item) => (
          <>
            {!item.preenchido && (
              <FcButton
                type="button"
                icon="pi pi-check"
                className="p-button-text toggle-button"
                onClick={() => {
                  if (item.marcaModelo) {
                    setEditing({});
                    updateAttributeItem(item.lote, item, 'preenchido', true);
                    setItemError((oldState) => {
                      oldState[item.id] = false;
                      return { ...oldState };
                    });
                  } else {
                    setItemError((oldState) => {
                      oldState[item.id] = true;
                      return { ...oldState };
                    });

                    setEditing((oldState) => {
                      if (!oldState[item.id]) {
                        oldState[item.id] = {};
                      }
                      oldState[item.id].marcaModelo = true;
                      return { ...oldState };
                    });
                  }
                }}
              />
            )}
            {item.preenchido && (
              <FcButton
                type="button"
                icon="pi pi-pencil"
                className="p-button-text toggle-button"
                onClick={() => {
                  setEditing({});
                  updateAttributeItem(item.lote, item, 'preenchido', false);
                }}
              />
            )}
            <FcButton
              type="button"
              icon="pi pi-comment"
              className="p-button-text toggle-button"
              onClick={() => {
                setItemSelected(item);
                setObsItem(item.observacao);
                setEditing({});
                setDialogVisible(true);
              }}
            />
          </>
        ),
      },
    ];
    return (
      <DataTable
        rowHover
        value={itens}
        emptyMessage="Nenhum item disponível"
        style={{ maxWidth: '100%' }}
        className="p-datatable-sm "
      >
        {columns.map((c, idx) => {
          return <Column key={`field-${idx}`} {...c} />;
        })}
      </DataTable>
    );
  };

  const handleInputValorNegociadoChange = (e, item) => {
    const value = e.value !== undefined && !isNaN(e.value) ? e.value : null;
    store.updateAttributeItem(item.itemLote?.id, item.licitante.id, 'valorUnitario', value);
  };

  const _renderHeader = (vencedor, className) => {
    return (
      <>
        <div className="flex-left pointer" onClick={() => toggleLote(vencedor.id)}>
          <div className="info-lote">
            <span className="feedback">
              <i
                className={classNames('p-m-2', {
                  'pi pi-angle-right': collapsed[vencedor.id],
                  'pi pi-angle-down': !collapsed[vencedor.id],
                })}
              />
            </span>
            <strong className="p-ml-2" onClick={() => toggleLote(vencedor.id)}>
              {vencedor.nome}
            </strong>
          </div>
        </div>
        <div className="flex-right p-mr-2">
          <div className="feedback">
            {className?.includes('panel-check') && (
              <span className={`circle check`}>
                <Tooltip value="Preenchido" sideOffset={10} delayDuration={100}>
                  <i className="pi pi-check" />
                </Tooltip>
              </span>
            )}
            {className?.includes('panel-warning') && (
              <span className="circle warning">
                <Tooltip value="Parcialmente Preenchido" sideOffset={10} delayDuration={100}>
                  <span className="default">!</span>
                </Tooltip>
              </span>
            )}
            {className?.includes('panel-error') && (
              <span className="circle error">
                <Tooltip value="Não Preenchido" sideOffset={10} delayDuration={100}>
                  <i className="pi pi-times" />
                </Tooltip>
              </span>
            )}
          </div>
        </div>
      </>
    );
  };

  const _renderItens = () => {
    const vencedores = licitantes?.filter((licitante) =>
      lote.itens?.map((item) => item.lote?.licitante?.id).includes(licitante?.id)
    );
    return (
      <div className="p-mt-2">
        {vencedores?.map((venc) => {
          const itens = lote.itens?.filter((item) => item.lote?.licitante?.id === venc.id);
          const className = itens.filter((item) => !item.preenchido)?.length === 0 ? 'panel-check' : 'panel-warning';
          return (
            <Panel
              className={className}
              header={_renderHeader(venc, className)}
              content={_renderDataTable(itens)}
              collapsed={collapsed[venc.id]}
            />
          );
        })}
      </div>
    );
  };

  let content = <>Erro ao renderizar o lote</>;
  if (lote) {
    content = (
      <>
        {_renderVencedoresItens()}
        {_renderItens()}
        {_renderDialogObs()}
        {_renderPickListItensFracassados()}
        {_renderDialogItensFracassados()}
      </>
    );
  }
  return content;
});

export default LoteUnico;
