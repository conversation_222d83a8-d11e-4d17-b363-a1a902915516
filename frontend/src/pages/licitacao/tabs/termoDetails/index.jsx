import { observer } from 'mobx-react';
import { InputText } from 'primereact/inputtext';
import { Sidebar } from 'primereact/sidebar';
import React from 'react';
import PropTypes from 'prop-types';
import FcButton from 'fc/components/FcButton';
import { formatList } from 'fc/utils/utils';
import IndexBase from 'fc/stores/IndexBase';
import DialogContent from 'fc/components/SelectDialog/DialogContent';
import GerenciamentoTermoIndexStore from '~/stores/gerenciamentoTermo/indexStore';
import TermoReferenciaDetails from '~/pages/gerenciamentoTermo/indexDetail';
import { Message } from 'primereact/message';
import { ConfirmDialog } from 'primereact/confirmdialog';
import { Dialog } from 'primereact/dialog';

@observer
class TermoSelectDetails extends React.Component {
  constructor(props) {
    super(props);
    this.store = new GerenciamentoTermoIndexStore();

    this.state = {
      visibleSideBar: false,
      selectedTermo: this.props.value,
      visiblePopup: false,
      itensInvalidos: false,
    };
  }

  componentDidMount() {
    this.store.loadSecoes();
  }

  _showSideBar(value) {
    this.setState({ visibleSideBar: value });
  }

  _onChangeSelectedTermo(event) {
    if (event.value) {
      if (event.value.lotes.length != 0) {
        this.setState({ selectedTermo: event.value });
      }
    }
  }

  renderConfirmPopup(callback) {
    this.onChangeCallback = callback;
    this.setState({ visiblePopup: true });
  }

  _renderConfirmDialog() {
    const { confirmChange } = this.props;
    return (
      <ConfirmDialog
        visible={this.state.visiblePopup}
        onHide={() => this.setState({ visiblePopup: false })}
        accept={() => {
          this.onChangeCallback && this.onChangeCallback();
          confirmChange.callback && confirmChange.callback();
        }}
        message={confirmChange?.message ?? ''}
        icon="pi pi-exclamation-triangle"
      />
    );
  }

  _renderDialog() {
    return (
      <DialogContent
        indexStore={this.props.indexStore}
        columns={this.props.dialogColumns}
        onSelectRow={(e) => this._onChangeSelectedTermo(e)}
        dismiss={() => {}}
        selectedElements={this.state.selectedTermo}
        searchFields={this.props.searchFields}
        filterSuggest={this.props.filterSuggest}
        radioMode={this.props.radioMode}
      />
    );
  }

  _renderTermoDetails() {
    return this.props.value || this.state.selectedTermo ? (
      <div key={this.state.selectedTermo?.id}>
        <div className="p-col-12">
          <b>
            <h2>Detalhes</h2>
          </b>
        </div>
        <TermoReferenciaDetails
          id={this.state.selectedTermo?.id ?? this.props.value.id}
          store={this.store}
          formatList={formatList}
          collapsedSections
          disabledEditButton
        />
      </div>
    ) : (
      <Message
        severity="info"
        text="Selecione um Termo de Referência para exibir os detalhes."
        style={{ width: '100%' }}
      />
    );
  }

  _confirmChange() {
    if (this.props.confirmChange && this.props.value.id != this.state.selectedTermo.id) {
      this.renderConfirmPopup(() => {
        this.props.onChange(this.state.selectedTermo);
        this._showSideBar(false);
      });
    } else {
      this.props.onChange(this.state.selectedTermo);
      this._showSideBar(false);
    }
  }

  _renderItensDepreciadosSuspensosWarning() {
    return (
      <Dialog
        header="Termo de Referência Inválido"
        visible={this.state.itensInvalidos}
        onHide={() => {
          this.setState({ itensInvalidos: false });
        }}
        style={{ width: '50vw', textAlign: 'center' }}
      >
        <p>
          O Termo de Referência selecionado possui itens <strong>suspensos</strong> ou <strong>depreciados</strong>.
          Substitua os itens antes de continuar.
        </p>
      </Dialog>
    );
  }

  _renderSideBar() {
    return (
      <Sidebar visible={this.state.visibleSideBar} onHide={() => this._showSideBar(false)} position="right" fullScreen>
        <div className="p-formgrid p-grid" style={{ paddingBottom: '4em' }}>
          <div className="p-col-12">
            <b>
              <h2>Termo de Referência</h2>
            </b>
          </div>
          <div className="p-col-12">{this._renderDialog()}</div>
          <div className="p-col-12 mt-3">{this._renderTermoDetails()}</div>
        </div>
        <div
          className="p-col-12"
          style={{ position: 'fixed', width: '100em', height: '4em', bottom: 0, right: 0, backgroundColor: 'white' }}
        >
          <div className="p-mt-2 p-mr-2">
            <span className="p-mr-1 p-d-inline p-d-flex">
              <FcButton
                label="Cancelar"
                type="button"
                className="p-ml-auto p-button-secondary p-mr-2"
                onClick={() => this._showSideBar(false)}
                loading={this.store.loading}
              />
              <FcButton
                label="Confirmar"
                disabled={!this.state.selectedTermo || this.state.selectedTermo === this.props.value}
                type="button"
                onClick={() => {
                  this.store.isItensTermoDepreciadosSuspensos(this.state.selectedTermo.lotes)
                    ? this.setState({ itensInvalidos: true })
                    : this._confirmChange();
                }}
              />
              {this.state.itensInvalidos && this._renderItensDepreciadosSuspensosWarning()}
            </span>
          </div>
        </div>
      </Sidebar>
    );
  }

  render() {
    return (
      <div className="p-formgrid p-grid p-align-center">
        <div className="p-col-10">
          <InputText value={this.props.value ? this.props.value[this.props.label] : this.props.emptyMessage} disabled />
        </div>
        <div className="p-col-2">
          <FcButton
            id="search-btn"
            type="button"
            style={{ width: '100%', height: '100%' }}
            icon="pi pi-search"
            onClick={() => this._showSideBar(true)}
            disabled={this.props.disabledComponent}
          />
        </div>
        {this.state.visibleSideBar && this._renderSideBar()}
        {this.state.visiblePopup && this._renderConfirmDialog()}
      </div>
    );
  }
}

TermoSelectDetails.defaultProps = {
  emptyMessage: 'Nenhum registro selecionado',
  disabled: false,
  disabledComponent: false,
  onChange: undefined,
  radioMode: false,
};

TermoSelectDetails.propTypes = {
  id: PropTypes.string,
  value: PropTypes.any,
  label: PropTypes.string,
  onChange: PropTypes.func,
  indexStore: PropTypes.objectOf(IndexBase).isRequired,
  dialogColumns: PropTypes.array,
  searchFields: PropTypes.array,
  filterSuggest: PropTypes.array,
  emptyMessage: PropTypes.string,
  disabled: PropTypes.bool,
  disabledComponent: PropTypes.bool,
  radioMode: PropTypes.bool,
  confirmChange: PropTypes.func,
};

export default TermoSelectDetails;
