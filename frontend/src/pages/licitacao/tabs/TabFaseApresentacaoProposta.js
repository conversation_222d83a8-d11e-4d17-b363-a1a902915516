import React from 'react';
import { observer } from 'mobx-react';
import PropTypes from 'prop-types';
import UrlRouter from '~/constants/UrlRouter';
import GenericFormPage from 'fc/pages/GenericFormPage';
import PermissionProxy from 'fc/components/PermissionProxy';
import FcButton from 'fc/components/FcButton';
import { getValueByKey, isValueValid } from 'fc/utils/utils';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import AccessPermission from '~/constants/AccessPermission';
import AppStore from 'fc/stores/AppStore';
import ApresentacaoForm from '~/stores/licitacao/tabs/apresentacaoForm';
import LicitanteFormPage from '~/pages/licitante/form';
import { Message } from 'primereact/message';
import { Dialog } from 'primereact/dialog';
import { InputTextarea } from 'primereact/inputtextarea';
import { Fieldset } from 'primereact/fieldset';
import RequisicaoModificacaoFormStore from '~/stores/requisicaoModificacao/formStore';
import SelectDialog from 'fc/components/SelectDialog';
import LicitanteIndexStore from '~/stores/licitante/indexStore';
import FormField from 'fc/components/FormField';
import MultipleFileUploader from 'fc/components/MultipleFileUploader';
import ArquivoDetailPage from '../boardLicitacoes/arquivoDetail';
import Vencedores from '~/pages/vencedores';

@observer
class TabFaseApresentacaoProposta extends GenericFormPage {
  requisicaoModificacaoStore;
  constructor(props) {
    super(props, UrlRouter.cadastrosConsulta.licitacao.index, AccessPermission.licitacao);
    this.store = new ApresentacaoForm();
    this.requisicaoModificacaoStore = new RequisicaoModificacaoFormStore();

    this.state = {
      visibleDialogReqMod: false,
    };
    this._toggleDialogReqMod = this._toggleDialogReqMod.bind(this);
    this.renderDialogRequisicaoModificacao = this.renderDialogRequisicaoModificacao.bind(this);
  }

  componentDidMount() {
    const { id } = this.props;
    this.store.initialize(id);
  }

  isDisabledAvancar() {
    const errorsMessages = {
      vencedores: { message: 'Por favor, adicione vencedores antes de avançar para a próxima etapa.', disabled: true },
      fracassadosDesertos: {
        message:
          'Para apontar toda a Licitação como Fracassada/Deserta utilize-se da funcionalidade de Finalização presente nas ações da licitação a partir da listagem principal.',
        disabled: true,
      },
      quantidade: {
        message: 'Alguns itens possuem quantidades remanescentes não vencidas ou desertos.',
        disabled: true,
      },
      preenchido: { message: 'Itens vencidos ou desertos precisam ser preenchidos.', disabled: true },
      valorUnitario: {
        message:
          this.store.licitacao?.lei === 'LEI_N_8666'
            ? 'O valor dos vencedores não deve ser superior ao valor estimado.'
            : 'O valor unitário dos itens não deve ser superior ao valor estimado.',
        disabled: true,
      },
    };

    if (this.store.isLegislacaoAntiga && !this.store.licitacao?.vencedores?.length) {
      return errorsMessages.vencedores;
    } else if (!this.store.isLegislacaoAntiga) {
      if (this.store.vencedoresStore.allItensFracassadosOrDesertos && !this.store.licitacao?.vencedores?.length) {
        return errorsMessages.fracassadosDesertos;
      } else if (!this.store.licitacao?.vencedores?.length) {
        return errorsMessages.vencedores;
      } else if (this.store.vencedoresStore.hasItensNotFilleds) {
        return errorsMessages.preenchido;
      } else if (this.store.vencedoresStore.hasQuantityNotAssign) {
        return errorsMessages.quantidade;
      } else if (this.store.licitacao?.vencedores?.some((i) => i.itemLote?.valorUnitarioEstimado < i.valorUnitario)) {
        return errorsMessages.valorUnitario;
      }

      const valorEstimado = this.store.licitacao?.valorEstimado || 0;
      const valorAdjudicadoTotal = this.store.calcularValorAdjudicado();

      if (valorAdjudicadoTotal > valorEstimado) {
        return errorsMessages.valorUnitario;
      } else {
        return { message: '', disabled: false };
      }
    } else if (this.store.isLegislacaoAntiga) {
      const valorEstimado = this.store.licitacao?.valorEstimado || 0;
      const valorAdjudicadoTotal = this.store.calcularValorAdjudicado();
      if (valorAdjudicadoTotal > valorEstimado) {
        return errorsMessages.valorUnitario;
      } else {
        return { message: '', disabled: false };
      }
    } else {
      return { message: '', disabled: false };
    }
  }

  _goBack() {
    if (this.props.editMode) {
      this.props.disableEditMode();
      this.props.callback && this.props.callback(this.store.licitacao.id);
    } else {
      this.props.history.push(this.goBackUrl);
    }
  }

  renderActionButtons() {
    const hasWritePermission = !this.getWritePermission() || AppStore.hasPermission(this.getWritePermission());
    const disabledAvancar = this.isDisabledAvancar();
    return (
      <div className="p-mt-10 form-actions">
        <div className="p-mt-2">
          <span className="p-mr-1 p-d-inline p-d-flex align-items-center justify-content-end">
            {disabledAvancar.disabled && (
              <Message className="p-ml-auto p-mr-1" severity="warn" text={disabledAvancar.message} />
            )}
            <FcButton
              label="Voltar"
              type="button"
              className="p-ml-2 p-button-secondary p-mr-2"
              onClick={() => this._goBack()}
              loading={this.store.loading}
            />
            {!this.props.readOnly && hasWritePermission && !this.props.isRequisicaoModificacao && (
              <FcButton
                loading={this.store.loading}
                label="Salvar"
                type="button"
                disabled={disabledAvancar.disabled}
                onClick={() => {
                  if (this.store.validateSubmittedFiles(this.store.arquivoLicitacaoList)) {
                    this.store.salvarVencedorLicitacao(this.props.action, this._goBack);
                  }
                }}
              />
            )}
            {!this.props.readOnly && hasWritePermission && this.props.isRequisicaoModificacao && (
              <FcButton
                label="Enviar Requisição"
                onClick={() => {
                  if (this.store.validateSubmittedFiles(this.store.arquivoLicitacaoList)) {
                    this._toggleDialogReqMod();
                  }
                }}
                loading={this.store.loading}
                disabled={disabledAvancar.disabled}
              />
            )}
          </span>
        </div>
      </div>
    );
  }

  _toggleDialogReqMod() {
    this.setState((oldState) => ({ visibleDialogReqMod: !oldState.visibleDialogReqMod }));
  }

  validadeDialogInputs() {
    if (this.requisicaoModificacaoStore.justificativaJurisdicionado) {
      this.setState({ errorDialogValue: false });
    } else {
      this.setState({ errorDialogValue: true });
    }
  }

  renderFooter() {
    return (
      <div>
        <FcButton
          label="Cancelar"
          icon="pi pi-times"
          onClick={() => this._toggleDialogReqMod()}
          className="p-button-text"
        />
        <FcButton
          label="Enviar Requisição"
          icon="pi pi-check"
          loading={this.requisicaoModificacaoStore.loading}
          onClick={() => {
            if (this.requisicaoModificacaoStore.justificativaJurisdicionado) {
              this.submitFormData();
              this._toggleDialogReqMod();
            } else {
              !this.state.errorDialogValue && this.setState({ errorDialogValue: true });
            }
          }}
        />
      </div>
    );
  }

  renderDialogRequisicaoModificacao() {
    return (
      <Dialog
        header="Justificativa Requisição"
        visible={this.state.visibleDialogReqMod}
        style={{ width: '50vw' }}
        footer={this.renderFooter()}
        onHide={() => this._toggleDialogReqMod()}
      >
        <div className="p-fluid p-formgrid p-grid">
          <FormField columns={12} attribute="justificativa" label="Justificativa">
            <InputTextarea
              className={this.state.errorDialogValue ? 'p-invalid p-error' : ''}
              value={this.requisicaoModificacaoStore.justificativaJurisdicionado}
              onChange={(e) => {
                this.requisicaoModificacaoStore.updateAttribute('jurisdicionado', e);
                isValueValid(this.requisicaoModificacaoStore.justificativaJurisdicionado?.length) &&
                  this.setState({ errorDialogValue: false });
              }}
              placeholder="Descreva a justificativa"
            />
          </FormField>
          <div className="p-col-6">
            {this._getFieldErrorMessage(this.state.errorDialogValue, 'Por favor, preencha o campo')}
          </div>
        </div>
      </Dialog>
    );
  }

  _renderMultipleFileUploader() {
    return (
      <MultipleFileUploader
        downloadOnly={this.props.readOnly}
        store={this.store.fileStore}
        onChangeFiles={(files) => this.store.setArquivoLicitacaoList(files)}
        fileTypes={DadosEstaticosService.getTipoArquivoLicitacao()}
        accept=".pdf, .xls, .xlsx"
        filterTypes={{
          included: this.store.getTypeFilesByPhase(),
          filter: {
            column: 'fase',
            values: ['APRESENTACAO_PROPOSTAS_LANCES'],
          },
        }}
      />
    );
  }

  _getFieldErrorMessage(rendered, message) {
    if (rendered) {
      return <small className="p-error">{message}</small>;
    }
    return null;
  }

  submitFormData(e) {
    e && e.preventDefault();
    const execution = () => {
      const filesTypeByPhase = DadosEstaticosService.getTipoArquivoLicitacao()
        .filter((arq) => arq.fase.includes('APRESENTACAO_PROPOSTAS_LANCES'))
        .map((arq) => arq.value);

      const files = this.store.arquivoLicitacaoList.map((arquivo) => {
        if (filesTypeByPhase.includes(arquivo.tipo) && !arquivo.fase) {
          arquivo.fase = 'APRESENTACAO_PROPOSTAS_LANCES';
        }
        return arquivo;
      });

      const cadastroLicitacaoDTO = {
        licitacao: this.store.licitacao,
        arquivosLicitacao: files,
      };
      !this.props.isRequisicaoModificacao && this.store.salvarVencedorLicitacao(this._goBack);

      this.requisicaoModificacaoStore.recuperarEntidade(this.props.id);
      const requisicaoModificacaoDTO = {
        cadastroLicitacaoDTO: cadastroLicitacaoDTO,
        idLicitacao: cadastroLicitacaoDTO.licitacao?.id,
        idEntidade: cadastroLicitacaoDTO.licitacao?.entidade?.id,
        faseLicitacao: 'APRESENTACAO_PROPOSTAS_LANCES',
      };
      this.props.isRequisicaoModificacao &&
        this.requisicaoModificacaoStore.justificativaJurisdicionado &&
        this.requisicaoModificacaoStore.enviarRequisicaoLicitacao(requisicaoModificacaoDTO, this._goBack);
    };

    if (this.state.submitted) {
      execution();
    } else {
      this.setState({ submitted: true }, execution);
    }
  }

  render() {
    const { submitFormData } = this;

    const columnsLicitante = [
      {
        field: 'nome',
        header: 'Nome',
        sortable: true,
      },
      {
        field: 'cpfCnpj',
        header: 'CPF/CNPJ',
        sortable: true,
      },
      {
        field: 'pessoaFisica',
        header: 'Tipo de Pessoa',
        body: ({ pessoaFisica }) => getValueByKey(pessoaFisica, DadosEstaticosService.getTipoPessoa()),
      },
      {
        field: 'internacional',
        header: 'Pessoa/Empresa Internacional',
        body: ({ internacional }) => getValueByKey(internacional, DadosEstaticosService.getSimNao()),
      },
    ];

    const columnsParticipantes = [
      {
        style: { width: '30%' },
        field: 'nome',
        header: 'Nome',
      },
      {
        style: { width: '30%' },
        field: 'cpfCnpj',
        header: 'CPF/CNPJ',
      },
      {
        field: 'pessoaFisica',
        header: 'Tipo de Pessoa',
        body: ({ pessoaFisica }) => getValueByKey(pessoaFisica, DadosEstaticosService.getTipoPessoa()),
        style: { width: '20%' },
      },
      {
        field: 'internacional',
        header: 'Pessoa/Empresa Internacional',
        body: ({ internacional }) => getValueByKey(internacional, DadosEstaticosService.getSimNao()),
        style: { width: '20%' },
      },
      !this.props.readOnly && {
        style: { width: '15%' },
        body: (rowData) => {
          return (
            <FcButton
              icon="pi pi-trash"
              className="p-button-sm p-button-danger"
              type="button"
              onClick={() => {
                this.store.removeLicitante(rowData);
              }}
            />
          );
        },
      },
    ];

    const licitanteForm = (props) => {
      return <LicitanteFormPage history={history} action="new" closeMethod={props.closeMethod} index="licitacao" />;
    };

    let content;
    if (this.store.licitacao) {
      content = (
        <>
          <div className={`${this.props.editMode ? '' : 'card page form-action-buttons'}`}>
            <form onSubmit={submitFormData}>
              {this.props.readOnly ? (
                <Fieldset legend="AVISO">
                  <h6 style={{ color: '#dd0303' }}>
                    {'LICITAÇÃO ' +
                      this.store.licitacao.statusOcorrenciaAtual +
                      ' (FINALIZADA), DISPONÍVEL APENAS PARA CONFERÊNCIA.'}
                  </h6>
                </Fieldset>
              ) : (
                this.props.isRequisicaoModificacao && (
                  <Fieldset legend="AVISO">
                    <h6 style={{ color: '#dd0303' }}>
                      A EDIÇÃO DESTA LICITAÇÃO ESTARÁ SUJEITA A RATIFICAÇÃO PELA EQUIPE DE AUDITORIA DO TCE
                    </h6>
                  </Fieldset>
                )
              )}
              <br />
              <div>
                {!this.store.isLegislacaoAntiga && !this.store.licitacao?.termoReferencia ? (
                  <div className="p-fluid p-formgrid p-grid">
                    <div className="p-field p-col-12">
                      <Message
                        severity="error"
                        text="A licitação não possui um Termo de Referência associado. Por favor, selecione um Termo de Referência na fase preparatória!"
                      />
                    </div>
                  </div>
                ) : (
                  <>
                    <div className="p-col-12 p-fluid p-formgrid p-grid ">
                      <div className="p-col-12">
                        <SelectDialog
                          value={this.store.licitacao.licitantes}
                          indexStore={new LicitanteIndexStore()}
                          dialogColumns={columnsLicitante}
                          searchFields={['nome', 'cpfCnpj', 'pessoaFisica', 'internacional']}
                          canCreate
                          headerDialog="Licitante"
                          isMultiple
                          selectedElements={this.store.licitacao.licitantes}
                          onSelectMultiple={(e) => {
                            this.store.updateAttributeLicitacao('licitantes', e);
                            this.store.vencedoresStore.setLicitantes(e);
                          }}
                          readOnly={this.props.readOnly}
                          onDesselect={(e) => this.store.validaRemocaoLicitante(e)}
                          errorMessageToDesselect="Licitante cadastrado como vencedor não pode ser removido da lista de participantes"
                          formPage={licitanteForm}
                          labelButtonCreate="Adicionar licitantes"
                          selectedKeydElementsList={this.store.licitanteListKeyed}
                          columnsSelectMultiple={columnsParticipantes}
                        />
                      </div>
                    </div>
                    <div className="p-col-12">
                      {this.store.vencedoresStore && (
                        <Vencedores
                          readOnly={this.props.readOnly}
                          store={this.store.vencedoresStore}
                          labelLicitante="vencedor"
                          entidadeAntiga={this.store.isLegislacaoAntiga}
                          showDesconto={this.store.licitacao?.tiposLicitacao
                            ?.map((t) => t.nome?.toUpperCase())
                            ?.includes('MAIOR DESCONTO')}
                          showEspecificacao={this.store.licitacao?.naturezasDoObjeto?.includes('COMPRAS')}
                          hideFracasso={false}
                          disableFracasso={
                            this.store.licitacao.fase?.includes('APRESENTACAO_PROPOSTAS_LANCES') ||
                            this.store.licitacao.fase?.includes('FINALIZACAO')
                          }
                          hideDeserto={false}
                          disableDeserto={
                            this.store.licitacao.fase?.includes('APRESENTACAO_PROPOSTAS_LANCES') ||
                            this.store.licitacao.fase?.includes('FINALIZACAO')
                          }
                          hideTipoAdjudicacao
                        />
                      )}
                    </div>

                    <div className="p-field p-col-12">
                      <div className="p-fluid p-formgrid p-grid">
                        <span className="p-mt-5 p-ml-4">
                          <b>Arquivos</b>
                        </span>
                      </div>
                      <div>
                        <ArquivoDetailPage
                          countDownloadRequest
                          licitacao={this.store.licitacao}
                          activeIndex={2}
                          labelFase={'Apresentação de Propostas e Lances'}
                          action={this.props.action}
                          multipleFileUploader={this._renderMultipleFileUploader()}
                        />
                      </div>
                    </div>
                  </>
                )}
              </div>
            </form>
            {this.renderActionButtons()}
            {this.renderDialogRequisicaoModificacao()}
          </div>
        </>
      );
    } else {
      content = (
        <div className={`${this.props.editMode ? '' : 'card page '}form-action-buttons`}>
          <i
            className="pi pi-spin pi-spinner"
            style={{
              marginTop: '20px',
              marginLeft: 'calc(50% - 20px)',
              fontSize: '2em',
            }}
          ></i>
        </div>
      );
    }
    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        {content}
      </PermissionProxy>
    );
  }
}

TabFaseApresentacaoProposta.propTypes = {
  id: PropTypes.any,
  isRequisicaoModificacao: PropTypes.bool,
  action: PropTypes.string,
  editMode: PropTypes.bool,
  disableEditMode: PropTypes.func,
  callback: PropTypes.func,
  readOnly: PropTypes.bool,
};

export default TabFaseApresentacaoProposta;
