import React from 'react';
import PropTypes from 'prop-types';
import { observer } from 'mobx-react';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import InputNumber from 'fc/components/InputNumber';
import FormField from 'fc/components/FormField';
import { InputMask } from 'primereact/inputmask';
import AsyncDropdown from 'fc/components/AsyncDropdown';
import moment from 'moment';
import SelectDialog from 'fc/components/SelectDialog';
import { DATE_FORMAT, DATE_PARSE_FORMAT } from 'fc/utils/date';
import GenericFormPage from 'fc/pages/GenericFormPage';
import LicitacaoFormStore from '~/stores/licitacao/formStore';
import UrlRouter from '~/constants/UrlRouter';
import AsyncMultiselect from 'fc/components/AsyncMultiselect';
import { InputText } from 'primereact/inputtext';
import InputMonetary from 'fc/components/InputMonetary';
import { Divider } from 'primereact/divider';
import FcButton from 'fc/components/FcButton';
import AppStore from 'fc/stores/AppStore';
import { showNotification, getValueDate, getValueByKey, isValueValid } from 'fc/utils/utils';
import { Dialog } from 'primereact/dialog';
import FcInputTextarea from 'fc/components/FcInputTextarea';
import { SelectButton } from 'primereact/selectbutton';
import PareceristaFormPage from '../formParecerista';
import PareceristaIndexStore from '~/stores/parecerista/indexStore';
import Obra from '~/domains/Obra';
import { InputTextarea } from 'primereact/inputtextarea';
import { Dropdown } from 'primereact/dropdown';
import ComissaoIndexStore from '~/stores/comissao/indexStore';
import ComissaoFormPage from '../formComissao';
import ObraMapContainer from './ObraMapContainer';
import PermissionProxy from 'fc/components/PermissionProxy';
import { AutoComplete } from 'primereact/autocomplete';
import AccessPermission from '~/constants/AccessPermission';
import MultipleFileUploader from 'fc/components/MultipleFileUploader';
import ArquivoDetailPage from '../boardLicitacoes/arquivoDetail';
import FcMultiSelect from 'fc/components/FcMultiSelect';
import { ConfirmDialog } from 'primereact/confirmdialog';
import PreparatoriaForm from '~/stores/licitacao/tabs/preparatoriaForm';
import FcDropdown from 'fc/components/FcDropdown';
import { Fieldset } from 'primereact/fieldset';
import { Checkbox } from 'primereact/checkbox';
import FcCalendar from 'fc/components/FcCalendar';
import { RadioButton } from 'primereact/radiobutton';

@observer
class TabFasePreparatoria extends GenericFormPage {
  constructor(props) {
    super(props, UrlRouter.cadastrosConsulta.licitacao.index, AccessPermission.licitacao);
    this.store = new LicitacaoFormStore();

    this.state = {
      ...this.state,
      showFonteRecurso: false,
      showNaturezaObjeto: false,
      position: 'center',
      visibleDialogReqMod: false,
      errorDialogValue: false,
      visibleDialogObras: false,
      submitted: false,
      currentLesgilacao: undefined,
      dialogVisibility: false,
      disableTermoReferencia: false,
    };
    this.onClick = this.onClick.bind(this);
    this.onHide = this.onHide.bind(this);
    this._toggleDialogReqMod = this._toggleDialogReqMod.bind(this);
    this._renderDialogObras = this._renderDialogObras.bind(this);
    this._toggleDialogObras = this._toggleDialogObras.bind(this);
    this._onCreateStateObra = this._onCreateStateObra.bind(this);
    this._onCreate = this._onCreate.bind(this);
    this._onDelete = this._onDelete.bind(this);
  }

  componentDidMount() {
    const { id } = this.props;
    const initialize = () =>
      this.store.initialize(
        id,
        {
          fontesDeRecurso: [],
          naturezasDoObjeto: [],
          lei: 'LEI_N_14133',
          fase: 'PREPARATORIA',
          tipoAdjudicacao: 'ITEM',
        },
        () => {
          this.store.carregaAnos();
          this.store.object?.obra && this.store.carregarEdificacaoObra();
          this.store.carregarTiposLicitacaoModalidades(() => this.forceUpdate());
          this.store.carregarFontesRecursos(() => this.forceUpdate());
        }
      );

    this.store.arquivoLicitacaoStore = new PreparatoriaForm(this.props.action);
    if (id) {
      this.store.arquivoLicitacaoStore.initialize(id, initialize);
    } else {
      initialize();
    }

    this.props.action === 'edit' && this.store.arquivoLicitacaoStore.loadFases();
    this.store.setEntidadeContexto(AppStore.getContextEntity()?.id);
  }

  _goBack() {
    if (this.props.editMode) {
      this.props.disableEditMode();
      this.props.callback && this.props.callback(this.store.object.id);
    } else {
      this.props.history.push(this.goBackUrl);
    }
  }

  itemTemplate(item) {
    return (
      <div className="natureza-item">
        <div className="natureza-container">{item.text}</div>
      </div>
    );
  }

  _onCreateStateObra(event) {
    const layerType = event?.layerType;
    const coordinates = [];
    this.store?.updateStateObraAttribute('tipoCamadaObraDTO', layerType);
    if (['polyline', 'polygon'].includes(layerType)) {
      const latLngs = 'polyline' === layerType ? event.layer.getLatLngs() : event.layer.getLatLngs()[0];
      latLngs.forEach((element) => {
        const coor = [];
        coor.push(element.lat);
        coor.push(element.lng);
        coordinates.push(coor);
      });
      this.store?.updateStateObraAttribute('coordenadasObraDTO', latLngs);
    } else {
      coordinates.push(event.layer.getLatLng().lat);
      coordinates.push(event.layer.getLatLng().lng);
      this.store?.updateStateObraAttribute('coordenadasObraDTO', [event.layer.getLatLng()]);
    }
    this.store?.updateStateObraAttribute('coordenadas', coordinates.join(','));
  }

  _onCreate() {
    const { coordenadas, coordenadasObraDTO, tipoCamadaObraDTO } = this.store.stateObra;
    if (coordenadas) {
      this.store.updateObraDTOAtt('tipoCamada', tipoCamadaObraDTO);
      this.store.updateObraDTOAtt('coordenadas', coordenadasObraDTO);
      this.store.updateAttribute('coordenadas', coordenadas);
    }
  }

  _onDelete() {
    this.store.updateAttribute('coordenadas', undefined);
  }

  onClick(name, position) {
    let state = {
      [`${name}`]: true,
    };

    if (position) {
      state = {
        ...state,
        position,
      };
    }

    this.setState(state);
  }

  onHide(name) {
    this.setState({
      [`${name}`]: false,
    });
  }

  getDateAttributeValue(value) {
    return value ? moment(value).toDate() : value;
  }

  renderActionButtons() {
    const hasWritePermission = !this.getWritePermission() || AppStore.hasPermission(this.getWritePermission());
    const isFasePreparatoria = this.store.object.fase === 'PREPARATORIA';

    return (
      <div className="p-mt-10 form-actions">
        <div className="p-mt-2">
          <span className="p-mr-1 p-d-inline p-d-flex align-items-center">
            <FcButton
              label="Voltar"
              type="button"
              className="p-ml-auto p-button-secondary p-mr-2"
              onClick={() => this._goBack()}
              loading={this.store.loading}
            />
            {!this.props.readOnly && hasWritePermission && isFasePreparatoria && (
              <FcButton label="Salvar" type="submit" loading={this.store.loading} />
            )}
            {!this.props.readOnly && hasWritePermission && !isFasePreparatoria && (
              <FcButton
                label="Enviar Requisição"
                type="button"
                onClick={() => {
                  if (this.store.validateSubmittedFiles(this.store.arquivoLicitacaoStore.arquivoLicitacaoList)) {
                    this._toggleDialogReqMod();
                  }
                }}
                loading={this.store.loading}
              />
            )}
          </span>
        </div>
      </div>
    );
  }

  _getModalidadeByNaturezas() {
    if (this.store.object.naturezasDoObjeto?.length) {
      if (
        this.store.object.naturezasDoObjeto?.includes('BENS_SERVICOS_ESPECIAIS') ||
        this.store.object.naturezasDoObjeto?.includes('OBRAS')
      ) {
        return this.store.modalidades?.filter((modalidade) =>
          ['Concorrência Eletrônica', 'Concorrência Presencial'].includes(modalidade.nome)
        );
      } else {
        return this.store.modalidades;
      }
    } else {
      return this.store.modalidades;
    }
  }

  renderDialogRequisicaoModificacao() {
    const { getRule } = this.store;
    return (
      <Dialog
        header="Justificativa Requisição"
        visible={this.state.visibleDialogReqMod}
        style={{ width: '50vw' }}
        footer={this.renderFooter()}
        onHide={() => this._toggleDialogReqMod()}
      >
        <div className="p-fluid p-formgrid p-grid">
          <FormField columns={12} attribute="justificativa" label="Justificativa" rule={getRule('justificativa')}>
            <FcInputTextarea
              className={this.state.errorDialogValue ? 'p-invalid p-error' : ''}
              value={this.props.reqModificacaoStore.justificativaJurisdicionado}
              onChange={(e) => {
                this.props.reqModificacaoStore.updateAttribute('jurisdicionado', e);
                isValueValid(this.props.reqModificacaoStore.justificativaJurisdicionado?.length) &&
                  this.setState({ errorDialogValue: false });
              }}
              placeholder="Descreva a justificativa"
            />
          </FormField>
          <div className="p-col-6">
            {this._getFieldErrorMessage(this.state.errorDialogValue, 'Por favor, preencha o campo')}
          </div>
        </div>
      </Dialog>
    );
  }

  _toggleDialogReqMod() {
    this.setState((oldState) => ({ visibleDialogReqMod: !oldState.visibleDialogReqMod }));
  }

  _toggleDialogObras() {
    this.store.resetStateObra();
    this.setState((oldState) => ({ visibleDialogObras: !oldState.visibleDialogObras }));
  }

  validadeDialogInputs() {
    if (this.props.reqModificacaoStore.justificativaJurisdicionado) {
      this.setState({ errorDialogValue: false });
    } else {
      this.setState({ errorDialogValue: true });
    }
  }

  renderFooterObras() {
    return (
      <div>
        <FcButton
          label="Confirmar"
          icon="pi pi-check"
          onClick={() => {
            this._onCreate();
            this._toggleDialogObras();
          }}
          className="p-button-text"
        />
      </div>
    );
  }

  renderFooter() {
    return (
      <div>
        <FcButton
          label="Cancelar"
          icon="pi pi-times"
          onClick={() => this._toggleDialogReqMod()}
          className="p-button-text"
        />
        <FcButton
          label="Enviar Requisição"
          icon="pi pi-check"
          onClick={() => {
            if (this.props.reqModificacaoStore.justificativaJurisdicionado) {
              this.submitFormData();
              this._toggleDialogReqMod();
            } else {
              !this.state.errorDialogValue && this.setState({ errorDialogValue: true });
            }
          }}
        />
      </div>
    );
  }

  _renderDialogObras() {
    return (
      <Dialog
        header="Cadastrar Localização de Obra"
        visible={this.state.visibleDialogObras}
        style={{ width: '80%' }}
        footer={this.renderFooterObras()}
        onHide={() => this._toggleDialogObras()}
      >
        <ObraMapContainer
          hasEdificacao={!!this.store.object?.obra?.edificacao?.localizacao}
          tipoSelecao={this.store.object.tipoSelecao}
          previousSelectedObra={this.store.object?.obra?.edificacao?.localizacao}
          selectedObra={this.store.obraObject}
          onCreated={this._onCreateStateObra}
          onDeleted={this._onDelete}
        />
      </Dialog>
    );
  }

  _getFieldErrorMessage(rendered, message) {
    if (rendered) {
      return <small className="p-error">{message}</small>;
    }
    return null;
  }

  submitFormData(e) {
    e && e.preventDefault();
    const execution = () => {
      if (!this.store.rules.hasError) {
        if (this.store.validateSubmittedFiles(this.store.arquivoLicitacaoStore.arquivoLicitacaoList)) {
          const isFasePreparatoria = this.store.object.fase === 'PREPARATORIA';

          const filesTypeByPhase = DadosEstaticosService.getTipoArquivoLicitacao()
            .filter((arq) => arq.fase.includes('PREPARATORIA'))
            .map((arq) => arq.value);

          const files = this.store.arquivoLicitacaoStore.arquivoLicitacaoList.map((arquivo) => {
            if (filesTypeByPhase.includes(arquivo.tipo) && !arquivo.fase) {
              arquivo.fase = 'PREPARATORIA';
            }
            return arquivo;
          });

          const cadastroLicitacaoDTO = {
            licitacao: this.store.object,
            arquivosLicitacao: files,
            obra: this.store.obraObject && Object.keys(this.store.obraObject).length > 0 ? this.store.obraObject : null,
            edificacao: this.store.edificacao,
          };

          isFasePreparatoria && this.store.cadastrarLicitacao(cadastroLicitacaoDTO, this._goBack);

          const requisicaoModificacaoDTO = {
            cadastroLicitacaoDTO: cadastroLicitacaoDTO,
            idLicitacao: cadastroLicitacaoDTO.licitacao?.id,
            idEntidade: AppStore.getContextEntity()?.id ?? cadastroLicitacaoDTO.licitacao?.entidade?.id,
            faseLicitacao: 'PREPARATORIA',
          };

          !isFasePreparatoria &&
            this.props.reqModificacaoStore.justificativaJurisdicionado &&
            this.props.reqModificacaoStore.enviarRequisicaoLicitacao(requisicaoModificacaoDTO, this._goBack);
        }
      } else {
        showNotification('error', null, 'Verifique os campos do formulário!');
      }
    };

    if (this.state.submitted) {
      execution();
    } else {
      this.setState({ submitted: true }, execution);
    }
  }

  updateTermo(value) {
    let valorEstimado = 0;
    value.lotes.forEach((lote) => {
      lote.itens.forEach((item) => {
        if (item.quantidade && item.valorUnitarioEstimado) {
          valorEstimado += item.quantidade * item.valorUnitarioEstimado;
        }
      });
    });

    this.store.updateAttribute('vencedores', []);
    this.store.updateAttribute('valorEstimado', valorEstimado);
    this.store.updateAttribute('termoReferencia', value);
  }

  _naturezaHasObra(selectedNaturezas) {
    return (
      selectedNaturezas && selectedNaturezas.some((natureza) => ['OBRAS', 'SERVICOS_DE_ENGENHARIA'].includes(natureza))
    );
  }

  _toggleDialogVisibility() {
    this.setState({ dialogVisibility: !this.state.dialogVisibility });
  }

  _renderDialog() {
    return (
      <ConfirmDialog
        visible={this.state.dialogVisibility}
        message="Ao trocar de legislação, os arquivos selecionados serão removidos do sistema. Deseja continuar e aplicar a mudança na legislação, removendo os arquivos selecionados?"
        header="Atualização da Legislação"
        onHide={() => {
          this._toggleDialogVisibility();
        }}
        accept={() => {
          this.store.arquivoLicitacaoStore.setArquivoLicitacaoList([]);
          this.store.arquivoLicitacaoStore.fileStore.removeAllFiles();
          this.store.updateAttribute('lei', this.state.currentLesgilacao);
          this.store.arquivoLicitacaoStore.loadFases(this.state.currentLesgilacao);
          this.store.updateAttribute('termoReferencia', null);
          this.store.updateAttribute('legislacaoOutros', '');
          this.store.updateAttribute('tiposLicitacao', null);
          this.store.carregarTiposLicitacaoModalidades();
          this.setState({ currentLesgilacao: undefined });
          this.forceUpdate();
        }}
      />
    );
  }

  _renderMultipleFileUploader() {
    return (
      <MultipleFileUploader
        downloadOnly={this.props.readOnly}
        store={this.store.arquivoLicitacaoStore.fileStore}
        onChangeFiles={(files) => this.store.arquivoLicitacaoStore.setArquivoLicitacaoList(files)}
        fileTypes={DadosEstaticosService.getTipoArquivoLicitacao()}
        filterTypes={{
          included: this.store.getTypeFilesByPhase(),
          filter: {
            column: 'fase',
            values: ['PREPARATORIA'],
          },
        }}
      />
    );
  }

  render() {
    const { submitted } = this.state;
    const { getRule, updateAttributeCheckbox } = this.store;

    const columnsParecerista = [
      {
        field: 'nome',
        header: 'Nome',
        sortable: true,
      },
      {
        field: 'cpfCnpj',
        header: 'CPF/CNPJ',
        sortable: true,
      },
    ];

    const pareceristaForm = (props) => {
      return <PareceristaFormPage action="new" closeMethod={props.closeMethod} />;
    };

    const columnsComissao = [
      {
        field: 'numero',
        header: 'Número',
        sortable: true,
      },
      {
        field: 'tipo',
        header: 'Tipo',
        sortable: true,
        body: ({ tipo }) => getValueByKey(tipo, DadosEstaticosService.getTipoComissao()),
      },
      {
        field: 'tipoConjunto',
        header: 'Conjunto',
        sortable: true,
        body: ({ tipoConjunto }) => getValueByKey(tipoConjunto, DadosEstaticosService.getTipoConjuntoComissao()),
      },
      {
        field: 'dataVigenciaInicial',
        header: 'Início da Vigência',
        sortable: true,
        body: ({ dataVigenciaInicial }) => getValueDate(dataVigenciaInicial, DATE_FORMAT, DATE_PARSE_FORMAT),
      },
      {
        field: 'dataVigenciaFinal',
        header: 'Fim da Vigência',
        sortable: true,
        body: ({ dataVigenciaFinal }) => getValueDate(dataVigenciaFinal, DATE_FORMAT, DATE_PARSE_FORMAT),
      },
    ];

    const comissaoForm = (props) => {
      return <ComissaoFormPage action="new" closeMethod={props.closeMethod} />;
    };

    if (this.store.object && !this.store.loadingCoordinates) {
      return (
        <>
          <div className={`${this.props.editMode ? '' : 'card page form-action-buttons'}`}>
            <form onSubmit={this.submitFormData}>
              {this.props.readOnly && (
                <>
                  <Fieldset legend="AVISO">
                    <h6 style={{ color: '#dd0303' }}>
                      {'LICITAÇÃO ' +
                        this.store?.object?.statusOcorrenciaAtual +
                        ' (FINALIZADA), DISPONÍVEL APENAS PARA CONFERÊNCIA.'}
                    </h6>
                  </Fieldset>
                </>
              )}
              {!this.props.readOnly && this.store.object.fase !== 'PREPARATORIA' && (
                <Fieldset legend="AVISO">
                  <h6 style={{ color: '#dd0303' }}>
                    A EDIÇÃO DESTA LICITAÇÃO ESTARÁ SUJEITA A RATIFICAÇÃO PELA EQUIPE DE AUDITORIA DO TCE
                  </h6>
                </Fieldset>
              )}
              <br />
              <div className="p-fluid p-formgrid p-grid" style={{ alignItems: 'center' }}>
                {!this.store.object?.processoMigrado && (
                  <FormField
                    columns={4}
                    attribute="lei"
                    label="A licitação será regida por qual legislação?"
                    rule={getRule('lei')}
                    submitted={submitted}
                  >
                    <SelectButton
                      disabled={this.props.action === 'edit'}
                      optionLabel="text"
                      optionValue="value"
                      value={this.store.object.lei}
                      options={DadosEstaticosService.getTipoLicitacaoLei14133()}
                      onChange={(e) => {
                        if (e.value) {
                          if (this.store.arquivoLicitacaoStore.arquivoLicitacaoList?.length) {
                            this.setState({ currentLesgilacao: e.value });
                            this._toggleDialogVisibility();
                          } else {
                            this.store.updateAttribute('lei', e.value);
                            this.store.arquivoLicitacaoStore.loadFases(e.value);
                            this.store.updateAttribute('srp', null);
                            this.store.updateAttribute('termoReferencia', null);
                            this.store.updateAttribute('legislacaoOutros', '');
                            this.store.updateAttribute('tiposLicitacao', null);
                            this.store.carregarTiposLicitacaoModalidades();
                            this.forceUpdate();
                          }
                        }
                      }}
                    />
                  </FormField>
                )}
                {this.state.dialogVisibility && this._renderDialog()}
                {this.store.object?.lei === 'OUTRA' && (
                  <>
                    <FormField
                      rule={getRule('legislacaoOutros')}
                      columns={4}
                      attribute="legislacaoOutros"
                      label="Outra Lei"
                      infoTooltip="Informe sob qual lei o processo está sendo criado"
                      submitted={submitted}
                    >
                      <InputText
                        disabled={this.props.action === 'edit'}
                        value={this.store.object.legislacaoOutros}
                        placeholder="Informe a legislação"
                        rows={4}
                        onChange={(e) => this.store.updateAttribute('legislacaoOutros', e)}
                      />
                    </FormField>
                  </>
                )}
                {this.store.object?.lei === 'LEI_N_8666' && (
                  <div style={{ paddingTop: '1.2rem' }}>
                    <FormField
                      columns={12}
                      attribute="srp"
                      label="Sistema de Registro de Preços (SRP)"
                      checkbox
                      submitted={submitted}
                    >
                      <Checkbox
                        inputId="srp"
                        checked={this.store.object.srp}
                        onChange={(e) => updateAttributeCheckbox('srp', e)}
                        id="srp"
                        style={{ height: this.props.action === 'edit' ? '1.6rem' : 'auto' }}
                      />
                    </FormField>
                  </div>
                )}
                {!this.store.object.processoMigrado && (
                  <FormField
                    columns={4}
                    attribute="participacaoExclusiva"
                    label="Destinado exclusivamente à participação de microempresas e empresas de pequeno porte?"
                    rule={getRule('participacaoExclusiva')}
                    submitted={submitted}
                  >
                    <div className="p-field-radiobutton p-dir-row">
                      <div className="p-field-radiobutton p-col mb-0">
                        <RadioButton
                          inputId="participacaoExclusiva"
                          name="participacaoExclusiva"
                          value={true}
                          onChange={(e) => this.store.updateAttribute('participacaoExclusiva', e.value)}
                          checked={this.store.object.participacaoExclusiva}
                        />
                        <label htmlFor="participacaoExclusiva">Sim</label>
                      </div>
                      <div className="p-field-radiobutton p-col mb-0">
                        <RadioButton
                          inputId="semparticipacaoExclusiva"
                          name="senParticipacaoExclusiva"
                          value={false}
                          onChange={(e) => this.store.updateAttribute('participacaoExclusiva', e.value)}
                          checked={this.store.object.participacaoExclusiva === false}
                        />
                        <label htmlFor="senParticipacaoExclusiva">Não</label>
                      </div>
                    </div>
                  </FormField>
                )}
              </div>
              <div className="p-fluid p-formgrid p-grid" style={{ alignItems: 'center' }}>
                {this.store.object.fase === 'PREPARATORIA' && (
                  <PermissionProxy resourcePermissions={'admin'} hideOnFail>
                    <FormField
                      columns={3}
                      label="Entidade"
                      rule={getRule('entidade')}
                      submitted={submitted}
                      attribute="entidade"
                    >
                      <AutoComplete
                        disabled={this.props.readOnly}
                        value={this.store.object.entidade}
                        suggestions={this.store.entidadesFiltradas}
                        completeMethod={(e) => this.store.getEntidadesFiltradas(e, () => this.forceUpdate())}
                        field="nome"
                        onChange={(e) => this.store.updateAttribute('entidade', e.value)}
                        aria-label="Entidade"
                        dropdownAriaLabel="Selecione a entidade"
                      />
                    </FormField>
                  </PermissionProxy>
                )}
                {!AppStore.getContextEntity() && (
                  <FormField
                    columns={3}
                    attribute="entidade"
                    label="Entidade"
                    rule={getRule('entidade')}
                    submitted={submitted}
                  >
                    <AsyncDropdown
                      onChange={this.store.updateAttribute}
                      value={this.store.object.entidade?.id}
                      placeholder="Selecione a entidade"
                      store={this.store.entidadeStore}
                      disabled={AppStore.getContextEntity() || this.props.readOnly}
                    />
                  </FormField>
                )}
                <FormField columns={3} attribute="numero" label="Número" rule={getRule('numero')} submitted={submitted}>
                  <InputNumber
                    keyfilter={new RegExp('^[0-9]+$')}
                    onChange={(e) => this.store.updateAttribute('numero', e)}
                    placeholder="Informe o número"
                    value={this.store.object.numero}
                    leadingZeros
                    disabled={this.props.readOnly}
                  />
                </FormField>
                <FormField columns={3} attribute="ano" label="Ano" rule={getRule('ano')} submitted={submitted}>
                  {this.store.object?.ano < 2016 ? (
                    <InputMask
                      mask={'9999'}
                      placeholder="Informe o ano"
                      value={this.store.object.ano}
                      id="anoLicitacao"
                      disabled
                    />
                  ) : (
                    <Dropdown
                      {...this.validateField('ano')}
                      onChange={(e) => this.store.updateAttribute('ano', e)}
                      placeholder="Informe o ano"
                      value={this.store.object?.ano}
                      id="anoLicitacao"
                      optionLabel="text"
                      optionValue="value"
                      options={this.store.anos}
                      disabled={this.props.readOnly}
                    />
                  )}
                </FormField>

                <FormField
                  columns={4}
                  attribute="dataAbertura"
                  label="Data e Hora de Abertura"
                  rule={getRule('dataAbertura')}
                  submitted={submitted}
                >
                  <FcCalendar
                    value={this.getDateAttributeValue(this.store.object.dataAbertura)}
                    onChange={(e) => this.store.updateAttributeDateWithHours('dataAbertura', e)}
                    showTime
                    hourFormat="24"
                    showIcon
                    disabled={this.props.readOnly}
                    disabledDays={[0, 6]}
                    mask="99/99/9999 99:99"
                  />
                </FormField>
                <FormField
                  columns={4}
                  attribute="numeroProcessoAdm"
                  label="Número do Processo Administrativo"
                  rule={getRule('numeroProcessoAdm')}
                  submitted={submitted}
                >
                  <InputText
                    onChange={(e) => {
                      const value = new RegExp('([0-9]*([.,-/]?[0-9]*))*').exec(e.target.value);
                      this.store.updateAttribute('numeroProcessoAdm', value[0]);
                    }}
                    placeholder="Informe o número do processo"
                    rows={4}
                    disabled={this.props.readOnly}
                    value={this.store.object.numeroProcessoAdm}
                  />
                </FormField>

                <FormField
                  columns={4}
                  attribute="valorEstimado"
                  label="Valor Total Estimado"
                  rule={getRule('valorEstimado')}
                  submitted={submitted}
                >
                  <InputMonetary
                    onChange={(e) => this.store.updateAttribute('valorEstimado', e)}
                    placeholder="R$"
                    mode="currency"
                    value={this.store.object.valorEstimado}
                    disabled={this.store.object?.lei !== 'LEI_N_8666'}
                    decimalPlaces={this.store.object?.termoReferencia?.tresCasasDecimais ? 3 : 2}
                  />
                </FormField>
                <FormField columns={6} attribute="objeto" label="Objeto" rule={getRule('objeto')} submitted={submitted}>
                  <FcInputTextarea
                    onChange={(e) => this.store.updateAttribute('objeto', e)}
                    placeholder="Informe o objeto"
                    rows={4}
                    disabled={this.props.readOnly}
                    value={this.store.object.objeto}
                  />
                </FormField>
                <FormField
                  columns={6}
                  attribute="regenciaLegal"
                  label="Fundamentação Legal"
                  rule={getRule('regenciaLegal')}
                  infoTooltip="Caso deseje, adicione a lei complementar ou decreto legal."
                  submitted={submitted}
                >
                  <FcInputTextarea
                    onChange={(e) => this.store.updateAttribute('regenciaLegal', e)}
                    placeholder="Informe a fundamentação legal"
                    rows={4}
                    disabled={this.props.readOnly}
                    value={this.store.object.regenciaLegal}
                  />
                </FormField>
                <div className="p-field p-col-4">
                  <FormField
                    columns={12}
                    attribute="naturezasDoObjeto"
                    label="Naturezas do Objeto"
                    rule={getRule('naturezasDoObjeto')}
                    submitted={submitted}
                  >
                    <FcMultiSelect
                      disabled={this.props.readOnly}
                      onChange={(e) => {
                        const naturezasSelecionadas = e?.target?.value;
                        const legislacao = this.store.object.lei;
                        const hasObra = naturezasSelecionadas?.includes('obra');
                        this.store.updateAttribute('naturezasDoObjeto', naturezasSelecionadas);

                        if (this._naturezaHasObra(naturezasSelecionadas) && !this.store.object.obra) {
                          this.store.updateAttribute('tipoSelecao', 'PONTO');
                          this.store.updateAttribute('obra', new Obra());
                          this.store.updateObraAttribute('finalizada', false);
                          this.store.arquivoLicitacaoStore.loadFases(legislacao, naturezasSelecionadas);
                        } else if (!this._naturezaHasObra(naturezasSelecionadas) && this.store.object.obra) {
                          this.store.updateAttribute('obra', undefined);
                          this.store.updateAttribute('tipoObra', undefined);
                          this.store.updateAttribute('categoria', undefined);
                          this.store.updateAttribute('coordenadas', '');
                          this.store.initializeObraDTO();
                          this.store.arquivoLicitacaoStore.loadFases(legislacao);
                        } else if (!hasObra) {
                          this.store.arquivoLicitacaoStore.loadFases(legislacao, naturezasSelecionadas);
                        }
                      }}
                      placeholder="Selecione as naturezas do objeto"
                      value={this.store.object.naturezasDoObjeto}
                      options={DadosEstaticosService.getNaturezaObjetoLicitacao()}
                      optionValue="value"
                      optionLabel="text"
                      filterBy="text"
                      filter
                      selectedItemsLabel="{} itens selecionados"
                      showClear
                      showOverlay
                    />
                  </FormField>
                </div>
                <div className="p-field p-col-4">
                  <FormField
                    columns={12}
                    attribute="fontesDeRecurso"
                    label="Fontes de Recurso"
                    rule={getRule('fontesDeRecurso')}
                    submitted={submitted}
                  >
                    <FcMultiSelect
                      placeholder="Selecione as fontes de recurso"
                      value={this.store.object.fontesDeRecurso}
                      onChange={(e) => this.store.updateAttribute('fontesDeRecurso', e)}
                      options={this.store.fontesRecursos}
                      showOverlay
                      optionLabel="nome"
                      filterBy="nome"
                      filter
                      selectedItemsLabel="{} itens selecionados"
                      showClear
                      disabled={this.props.readOnly}
                    />
                  </FormField>
                </div>
                <div className="p-field p-col-4">
                  <FormField
                    columns={12}
                    attribute="orgaosParticipantes"
                    label="Órgãos Participantes"
                    rule={getRule('orgaosParticipantes')}
                    submitted={submitted}
                  >
                    <AsyncMultiselect
                      placeholder="Selecione os órgãos participantes"
                      value={this.store.object?.orgaosParticipantes}
                      onChange={(e) => this.store.updateAttribute('orgaosParticipantes', e)}
                      store={this.store.orgaosParticipantesStore}
                      label="nome"
                      showOverlay
                      disabled={this.props.readOnly}
                    />
                  </FormField>
                </div>
                {this.store.object.naturezasDoObjeto &&
                  ['OBRAS', 'SERVICOS_DE_ENGENHARIA'].some((natureza) =>
                    this.store.object.naturezasDoObjeto.includes(natureza)
                  ) && (
                    <>
                      <FormField
                        columns={6}
                        attribute="tipoObra"
                        label="Tipo da Obra"
                        rule={getRule('tipoObra')}
                        submitted={submitted}
                      >
                        <AsyncDropdown
                          onChange={(_, v) => {
                            this.store.updateObraAttribute('tipo', v);
                            this.store.updateAttribute('tipoObra', v);
                            this.forceUpdate();
                          }}
                          value={this.store.object.obra?.tipo?.id}
                          placeholder="Selecione o tipo da obra"
                          store={this.store.obraTipoStore}
                          disabled={this.props.readOnly}
                        />
                      </FormField>
                      <FormField
                        columns={6}
                        attribute="categoria"
                        label="Categoria da Obra"
                        rule={getRule('categoria')}
                        submitted={submitted}
                      >
                        <AsyncDropdown
                          onChange={(_, v) => {
                            this.store.updateObraAttribute('categoria', v);
                            this.store.updateAttribute('categoria', v);
                            this.forceUpdate();
                          }}
                          value={this.store.object.obra?.categoria?.id}
                          placeholder="Selecione a categoria da obra"
                          store={this.store.obraCategoriaStore}
                          disabled={this.props.readOnly}
                        />
                      </FormField>

                      <FormField
                        columns={6}
                        attribute="tipoSelecao"
                        label="Tipo Seleção"
                        rule={getRule('tipoSelecao')}
                        submitted={submitted}
                      >
                        <div className="flex gap-2">
                          <FcDropdown
                            inOrder
                            {...this.validateField('tipoSelecao')}
                            onChange={(e) => this.store.updateAttribute('tipoSelecao', e)}
                            placeholder="Selecione o tipo de seleção"
                            value={this.store.object?.tipoSelecao}
                            id="tipoSelecao"
                            optionLabel="text"
                            optionValue="value"
                            disabled={this.props.readOnly}
                            options={DadosEstaticosService.getTipoSelecaoMapa()}
                            className="w-6"
                          />
                          <FcButton
                            label="Selecionar no Mapa"
                            type="button"
                            className="p-button-secondary w-6"
                            onClick={() => this._toggleDialogObras()}
                            loading={this.store.loading}
                            disabled={!this.store.object.tipoSelecao || this.props.readOnly}
                          />
                        </div>
                      </FormField>

                      <FormField
                        columns={6}
                        attribute="coordenadas"
                        label="Coordenadas Geográficas"
                        rule={getRule('coordenadas')}
                        submitted={submitted}
                      >
                        <InputTextarea
                          onChange={(e) => updateAttribute('coordenadas', e)}
                          placeholder=""
                          value={this.store.object.coordenadas}
                          disabled
                          rows={4}
                        />
                      </FormField>
                    </>
                  )}
                <FormField
                  rule={getRule('comissao')}
                  columns={6}
                  label="A licitação será conduzida por?"
                  submitted={submitted}
                >
                  <SelectDialog
                    value={this.store.object.comissao}
                    label={null}
                    indexStore={new ComissaoIndexStore()}
                    onChange={(e) => {
                      this.store.updateAttribute('comissao', e);
                      this.store.updateAttribute('pregoeiro', null);
                      this.forceUpdate();
                    }}
                    headerDialog="Comissão"
                    emptyMessage="Selecione a comissão"
                    nullMessage={
                      this.store.object.comissao &&
                      this.store.object.comissao.numero +
                        ' - ' +
                        getValueByKey(this.store.object.comissao.tipo, DadosEstaticosService.getTipoComissao()) +
                        ' - ' +
                        getValueByKey(
                          this.store.object.comissao.tipoConjunto,
                          DadosEstaticosService.getTipoConjuntoComissao()
                        )
                    }
                    dialogColumns={columnsComissao}
                    filterSuggest={this.store.comissaoFilterSuggest()}
                    searchFields={['numero']}
                    canCreate
                    formPage={comissaoForm}
                    disabledComponent={this.props.readOnly}
                  />
                </FormField>

                <FormField
                  rule={getRule('pregoeiro')}
                  columns={6}
                  attribute="pregoeiro"
                  label={this.store.labelPregoeiro}
                  submitted={submitted}
                >
                  <Dropdown
                    onChange={(e) =>
                      this.store.updateAttributePregoeiro('pregoeiro', e, this.store?.object?.comissao?.membros)
                    }
                    placeholder={'Selecione o ' + this.store.labelPregoeiro?.toLowerCase()}
                    value={this.store.object?.pregoeiro?.id ?? null}
                    id="pregoeiro"
                    optionLabel="nome"
                    optionValue="id"
                    emptyMessage="Nenhuma comissão foi selecionada"
                    options={this.store?.object?.comissao?.membros ?? null}
                    disabled={this.props.readOnly}
                  />
                </FormField>

                <FormField columns={6} label="Parecerista Jurídico" submitted={submitted}>
                  <SelectDialog
                    value={this.store.object.parecerista}
                    label="nome"
                    indexStore={new PareceristaIndexStore()}
                    onChange={(e) => {
                      this.store.updateAttribute('parecerista', e);
                      this.forceUpdate();
                    }}
                    headerDialog="Parecerista"
                    emptyMessage="Selecione o parecerista"
                    nullMessage="Parecerista sem Nome"
                    dialogColumns={columnsParecerista}
                    searchFields={['nome', 'cpfCnpj']}
                    canCreate
                    formPage={pareceristaForm}
                    disabledComponent={this.props.readOnly}
                  />
                </FormField>

                <FormField
                  columns={6}
                  label="Os pontos mencionados no parecer foram acatados/justificados?"
                  submitted={submitted}
                >
                  <SelectButton
                    disabled={this.props.readOnly}
                    id="form-value"
                    optionLabel="text"
                    optionValue="value"
                    value={this.store.object.acatadoJustificado}
                    options={DadosEstaticosService.getSituacaoParecer()}
                    onChange={(e) => {
                      this.store.updateAttribute('acatadoJustificado', e);
                      this.forceUpdate();
                    }}
                  />
                </FormField>
                {this.store.object.lei === 'LEI_N_14133' || this.store.object.lei === 'OUTRA' ? (
                  <FormField
                    columns={6}
                    attribute="tiposLicitacao"
                    label="Critérios de Julgamento"
                    rule={getRule('tiposLicitacao')}
                    submitted={submitted}
                  >
                    <FcMultiSelect
                      onChange={(e) => this.store.updateAttribute('tiposLicitacao', e)}
                      placeholder="Selecione os critérios de julgamento"
                      value={this.store.object.tiposLicitacao}
                      options={this.store.tiposLicitacao}
                      optionLabel="nome"
                      filterBy="nome"
                      filter
                      selectedItemsLabel="{} itens selecionados"
                      showClear
                      showOverlay
                      disabled={this.props.readOnly}
                    />
                  </FormField>
                ) : (
                  <FormField
                    columns={6}
                    attribute="tipo"
                    label="Critérios de Julgamento"
                    rule={getRule('tipo')}
                    submitted={submitted}
                  >
                    <FcDropdown
                      inOrder
                      id="tipo"
                      options={this.store.tiposLicitacao}
                      optionLabel="nome"
                      value={this.store.object.tipo}
                      onChange={(e) => this.store.updateAttribute('tipo', e)}
                      placeholder="Selecione os critérios de julgamento"
                      showClear
                      showFilterClear
                      filter
                      emptyMessage="Nenhum Registro Encontrado"
                      emptyFilterMessage="Nenhum Registro Encontrado"
                      disabled={this.props.readOnly}
                    />
                  </FormField>
                )}

                <Divider />
                <div className="p-col-12">
                  <div className="p-grid">
                    <span className="p-mt-5 p-ml-4">
                      <b>Arquivos</b>
                    </span>
                  </div>
                  <div>
                    <ArquivoDetailPage
                      countDownloadRequest
                      licitacao={this.store.object}
                      activeIndex={0}
                      labelFase={'Preparatória'}
                      action={this.props.action}
                      multipleFileUploader={this._renderMultipleFileUploader()}
                    />
                  </div>
                </div>
              </div>
              {this.renderActionButtons()}
            </form>
            {this.renderDialogRequisicaoModificacao()}
            {this._renderDialogObras()}
          </div>
        </>
      );
    } else {
      return (
        <div className={`${this.props.editMode ? '' : 'card page '}form-action-buttons`}>
          <i
            className="pi pi-spin pi-spinner"
            style={{
              marginTop: '20px',
              marginLeft: 'calc(50% - 20px)',
              fontSize: '2em',
            }}
          ></i>
        </div>
      );
    }
  }
}

TabFasePreparatoria.propTypes = {
  id: PropTypes.any,
  action: PropTypes.any,
  history: PropTypes.any,
  reqModificacaoStore: PropTypes.any,
  isRequisicaoModificacao: PropTypes.bool,
  editMode: PropTypes.bool,
  disableEditMode: PropTypes.func,
  callback: PropTypes.func,
  readOnly: PropTypes.bool,
};

export default TabFasePreparatoria;
