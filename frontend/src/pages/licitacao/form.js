import React from 'react';
import { observer } from 'mobx-react';
import PropTypes from 'prop-types';
import UrlRouter from '~/constants/UrlRouter';
import AppBreadCrumb from 'fc/components/AppBreadCrumb';
import FcButton from 'fc/components/FcButton';
import TabFasePreparatoria from './tabs/TabFasePreparatoria';
import TabFaseApresentacaoProposta from './tabs/TabFaseApresentacaoProposta';
import TabFaseDivulgacaoEdital from './tabs/TabFaseDivulgacaoEdital';
import TabFinalizacao from './tabs/TabFaseFinalizacao';
import GenericForm from '~/stores/licitacao/genericForm';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import { getValueByKey } from 'fc/utils/utils';
import RequisicaoModificacaoFormStore from '~/stores/requisicaoModificacao/formStore';
import { ConfirmDialog } from 'primereact/confirmdialog';
import { getValueDate } from 'fc/utils/utils';
import { DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS } from 'fc/utils/date';

@observer
class LicitacaoFormPage extends React.Component {
  store;
  constructor(props) {
    super(props);

    this.state = {
      ...this.state,
      phaseKey: '',
      licitacaoAtual: undefined,
      showPreparatoriaDialog: false,
      dataAberturaPreparatoria: undefined,
    };

    this.store = new GenericForm();

    this.setPhase = this.setPhase.bind(this);
  }

  componentDidMount() {
    if (this.props.action !== 'new') {
      this.store.initialize(this.props.id, () => {
        this.selectPhase();
        this.store.checkDataCadastro(this.store.licitacao.fase, this.state.phaseKey);
      });
    } else {
      this.store.initialize(undefined, () => this.setPhase('preparatoria'));
    }
  }

  selectPhase() {
    const fases = DadosEstaticosService.getFasesLicitacao();
    const indexFaseAtual = fases.map((object) => object.value).indexOf(this.store.licitacao.fase);
    if (this.props.action === 'edit') {
      const indexFaseSolicitada = fases.map((object) => object.phaseKey).indexOf(this.props.fase);
      if (indexFaseSolicitada <= indexFaseAtual) {
        this.setPhase(this.props.fase);
      }
    } else if (this.props.action === 'next') {
      if (indexFaseAtual + 1 < fases.length) {
        this.setPhase(fases[indexFaseAtual + 1].phaseKey);
      }
    }
  }

  _goBack() {
    this.props.history.push(UrlRouter.cadastrosConsulta.licitacao.index);
  }

  render() {
    const breacrumbItems = [
      { label: 'Licitações', url: UrlRouter.cadastrosConsulta.licitacao.index },
      { label: this.props.action === 'new' ? 'Novo' : 'Editar' },
    ];

    const { action } = this.props;

    const phaseTabs = {
      preparatoria: (
        <TabFasePreparatoria
          readOnly={this.store.getLicitacaoFinalizada}
          action={this.props.action}
          id={this.props.id}
          history={this.props.history}
          isRequisicaoModificacao={this.store.enableReqPreparatoria}
          reqModificacaoStore={new RequisicaoModificacaoFormStore()}
          editMode={this.props.editMode}
          disableEditMode={this.props.disableEditMode}
          callback={this.props.callback}
        />
      ),
      divulgacaoEditalLicitacao: (
        <TabFaseDivulgacaoEdital
          readOnly={this.store.getLicitacaoFinalizada}
          action={this.props.action}
          id={this.props.id}
          history={this.props.history}
          isRequisicaoModificacao={this.store.enableReqPreparatoria}
          editMode={this.props.editMode}
          disableEditMode={this.props.disableEditMode}
          callback={this.props.callback}
        />
      ),
      apresentacao: (
        <TabFaseApresentacaoProposta
          readOnly={this.store.getLicitacaoFinalizada}
          action={this.props.action}
          id={this.props.id}
          history={this.props.history}
          isRequisicaoModificacao={this.store.enableReqPreparatoria}
          editMode={this.props.editMode}
          disableEditMode={this.props.disableEditMode}
          callback={this.props.callback}
        />
      ),
      finalizacao: (
        <TabFinalizacao
          readOnly={this.store.getLicitacaoFinalizada}
          action={this.props.action}
          id={this.props.id}
          history={this.props.history}
          isRequisicaoModificacao={this.store.enableReqPreparatoria}
          editMode={this.props.editMode}
          disableEditMode={this.props.disableEditMode}
          callback={this.props.callback}
        />
      ),
    };
    if (this.store.licitacao) {
      const faseValue = getValueByKey(
        this.store.licitacao.fase,
        DadosEstaticosService.getFasesLicitacaoNum(),
        'text',
        'value'
      );
      const faseText = getValueByKey(
        this.state.phaseKey,
        DadosEstaticosService.getFasesLicitacao(),
        'phaseKey',
        'text'
      );
      return (
        <>
          {!this.props.editMode && <AppBreadCrumb items={breacrumbItems} />}
          <div
            className={`${this.props.editMode ? '' : 'card page '}p-d-flex p-ai-center p-jc-between
 p-mt-2`}
          >
            <div className="p-mt-3">
              <h4>{this.props.action === 'new' ? 'Fase Preparatória' : faseText}</h4>
            </div>
            <div className="flex gap-2">
              <FcButton
                tooltip="Voltar"
                className="p-button-raised p-button-danger"
                icon="pi pi-arrow-left"
                onClick={() => (this.props.editMode ? this.props.disableEditMode() : this._goBack())}
              />
              <FcButton
                tooltip="Preparatória"
                icon="pi pi-file-o"
                className="p-button-raised"
                onClick={() => this.setPhase('preparatoria')}
                disabled={action === 'new' && this.state.phaseKey != ''}
              />
              <FcButton
                tooltip="Divulgação e Publicação da Licitação"
                icon="pi pi-book"
                className="p-button-raised p-button-warning"
                onClick={() => this.onClickCheckFase(this.store.licitacao)}
                disabled={
                  (this.store.getLicitacaoFinalizada && action === 'edit' && faseValue < 2) ||
                  (this.store.licitacao.naturezaOcorrencia === 'SUSPENDER' && action === 'edit' && faseValue < 2) ||
                  (action === 'new' && this.state.phaseKey != '') ||
                  (action === 'edit' && faseValue < 1) ||
                  (action === 'next' && faseValue < 1)
                }
              />
              <FcButton
                tooltip="Apresentação de Propostas e Lances"
                icon="pi pi-shield"
                className="p-button-raised p-button-help"
                onClick={() => this.setPhase('apresentacao')}
                disabled={
                  (this.store.getLicitacaoFinalizada && action === 'edit' && faseValue < 3) ||
                  (this.store.licitacao.naturezaOcorrencia === 'SUSPENDER' && action === 'edit' && faseValue < 3) ||
                  (action === 'new' && this.state.phaseKey != '') ||
                  (action === 'edit' && faseValue < 2) ||
                  (action === 'next' && faseValue < 2)
                }
              />
              <FcButton
                tooltip="Finalização"
                icon="pi pi-flag"
                style={{ background: 'darkcyan' }}
                className="p-button-raised"
                onClick={() => this.setPhase('finalizacao')}
                disabled={
                  (this.store.getLicitacaoFinalizada && action === 'edit' && faseValue < 4) ||
                  (this.store.licitacao.naturezaOcorrencia === 'SUSPENDER' && action === 'edit' && faseValue < 4) ||
                  (action === 'new' && this.state.phaseKey != '') ||
                  (action === 'edit' && faseValue < 3) ||
                  (action === 'next' && faseValue < 3)
                }
              />
            </div>
          </div>
          <div className="current phase">{this.state.phaseKey && phaseTabs[this.state.phaseKey]}</div>
          {this.state.showPreparatoriaDialog && this._renderDialogConfirmacaoPreparatoria()}
        </>
      );
    } else {
      return (
        <div className="card page">
          <i className="pi pi-spin pi-spinner" style={{ fontSize: '2em' }}></i>
        </div>
      );
    }
  }

  setPhase(phase = '') {
    this.setState({ phaseKey: phase }, () => this.store.checkDataCadastro(this.store.licitacao.fase, phase));
  }

  _renderDialogConfirmacaoPreparatoria() {
    const dataAbertura = this.state.dataAberturaPreparatoria;
    return (
      <ConfirmDialog
        blockScroll
        visible={this.state.showPreparatoriaDialog}
        draggable={false}
        message={
          <div>
            <p>Confira os seguintes dados da licitação selecionada:</p>
            <ul>
              <li>Entidade: {this.state.licitacaoAtual.entidade.nome}</li>
              <li>
                Data e Hora de Abertura:{' '}
                {getValueDate(dataAbertura, DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS)}
              </li>
            </ul>
            <p>As informações acima estão corretas?</p>
          </div>
        }
        header="Confirmação"
        icon="pi pi-user preview-item"
        acceptClassName="p-button-info"
        onHide={() => this.closeDialogPreparatoriaVisibility()}
        accept={() => {
          this.setPhase('divulgacaoEditalLicitacao');
        }}
      />
    );
  }

  onClickCheckFase(licitacao) {
    licitacao?.fase === 'PREPARATORIA'
      ? this.openDialogPreparatoriaVisibility(licitacao)
      : this.setPhase('divulgacaoEditalLicitacao');
  }

  openDialogPreparatoriaVisibility(licitacao) {
    this.setState({
      dataAberturaPreparatoria: licitacao.dataAbertura,
      showPreparatoriaDialog: !this.state.showPreparatoriaDialog,
      licitacaoAtual: licitacao,
    });
  }

  closeDialogPreparatoriaVisibility() {
    this.setState({
      dataAberturaPreparatoria: undefined,
      showPreparatoriaDialog: !this.state.showPreparatoriaDialog,
      licitacaoAtual: undefined,
    });
  }
}

LicitacaoFormPage.propTypes = {
  id: PropTypes.any,
  fase: PropTypes.any,
  action: PropTypes.any,
  history: PropTypes.any,
  tipoProcesso: PropTypes.any,
  editMode: PropTypes.bool,
  disableEditMode: PropTypes.func,
  callback: PropTypes.func,
};

export default LicitacaoFormPage;
