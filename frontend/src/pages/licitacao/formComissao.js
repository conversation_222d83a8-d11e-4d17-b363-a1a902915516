import React from 'react';
import { observer } from 'mobx-react';
import PropTypes from 'prop-types';
import { InputText } from 'primereact/inputtext';
import UrlRouter from '~/constants/UrlRouter';
import GenericFormPage from 'fc/pages/GenericFormPage';
import FormField from 'fc/components/FormField';
import AccessPermission from '~/constants/AccessPermission';
import PermissionProxy from 'fc/components/PermissionProxy';
import ComissaoFormStore from '~/stores/comissao/formStore';
import { RadioButton } from 'primereact/radiobutton';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import AsyncDropdown from 'fc/components/AsyncDropdown';
import AppStore from 'fc/stores/AppStore';
import moment from 'moment';
import AsyncPickList from 'fc/components/AsyncPicklist';
import { Divider } from 'primereact/divider';
import FcButton from 'fc/components/FcButton';
import MultipleFileUploader from 'fc/components/MultipleFileUploader';
import { showNotification } from 'fc/utils/utils';
import { ConfirmDialog } from 'primereact/confirmdialog';
import FcCalendar from 'fc/components/FcCalendar';

@observer
class ComissaoFormPage extends GenericFormPage {
  constructor(props) {
    super(props, UrlRouter.administracao.comissao.index, AccessPermission.comissao);
    this.store = new ComissaoFormStore();
    this.state = { isConfirmDialogVisible: false };
  }

  componentDidMount() {
    const { id } = this.props;
    this.store.initialize(
      id,
      {
        tipo: DadosEstaticosService.getTipoComissao()[0].value,
        arquivosTemporarios: [],
        entidade: AppStore.getContextEntity(),
        membros: [],
        tipoConjunto: DadosEstaticosService.getTipoConjuntoComissao()[0].value,
      },
      () => {
        this.store.recuperarArquivos(id);
        this.store.setPessoaStore();
      }
    );
  }

  _goBack(comissao) {
    this.props.closeMethod(comissao);
  }

  submitFormData(e) {
    e && e.preventDefault();
    const execution = () => {
      if (!this.store.rules.hasError) {
        if (!this.store.object.arquivosTemporarios?.some((file) => file.tipo == '')) {
          if (this.store.object?.membros?.length < 3) {
            this.setState({ isConfirmDialogVisible: true });
          } else {
            this.store.save(this._goBack, this.props.action);
          }
        } else {
          showNotification('error', null, 'O tipo dos arquivos é obrigatório!');
        }
      } else {
        showNotification('error', null, 'Verifique os campos do formulário!');
      }
    };

    if (this.state.submitted) {
      execution();
    } else {
      this.setState({ submitted: true }, execution);
    }
  }

  renderActionButtons() {
    return (
      <div className="p-mt-10 form-actions-dialog">
        <Divider />
        <span className="p-d-flex">
          <FcButton
            label="Voltar"
            type="button"
            className="p-ml-auto p-button-secondary p-mr-2"
            onClick={() => this._goBack()}
            loading={this.store.loading}
          />
          <FcButton label="Salvar" type="button" onClick={this.submitFormData} loading={this.store.loading} />
        </span>
      </div>
    );
  }

  _renderConfirmSave() {
    return (
      <ConfirmDialog
        blockScroll
        visible={this.state.isConfirmDialogVisible}
        message={
          <label style={{ color: '#dd0303' }}>
            De acordo com a Lei nº 14.333/2021 (art. 8º, §§ 1º e 2º; art. 32, §1º, XI; art. 37, §1º), a comissão
            permanente ou especial deverá ser composta de, no mínimo, 03 (três) membros. A comissão preenchida não
            atende ao requisito, deseja continuar?
          </label>
        }
        header="Atenção"
        icon="pi pi-exclamation-triangle"
        accept={() => {
          this.store.save(this._goBack, this.props.action);
          this.setState({ isConfirmDialogVisible: false });
        }}
        onHide={() => this.setState({ isConfirmDialogVisible: false })}
      />
    );
  }

  render() {
    const { submitted } = this.state;
    const { updateAttribute, getRule } = this.store;
    const { submitFormData } = this;

    let content;

    if (this.store.object) {
      content = (
        <>
          <div>
            <form onSubmit={submitFormData}>
              <div className="p-fluid p-formgrid p-grid">
                <FormField
                  columns={12}
                  attribute="tipoConjunto"
                  label="Tipo de Conjunto da Comissão"
                  rule={getRule('tipoConjunto')}
                  submitted={submitted}
                >
                  <div className="p-field-radiobutton p-dir-row">
                    {DadosEstaticosService.getTipoConjuntoComissao().map((tipoConjunto, idx) => {
                      return (
                        <div className="p-field-radiobutton p-col" key={`tipoConjunto-${idx}`}>
                          <RadioButton
                            inputId={`tipoConjunto-${idx}`}
                            name={`tipoConjunto-${idx}`}
                            value={tipoConjunto}
                            onChange={(e) => this.store.updateAttribute('tipoConjunto', e.value.value)}
                            checked={this.store.object.tipoConjunto === tipoConjunto.value}
                          />
                          <label htmlFor={`tipoConjunto-${idx}`}>{tipoConjunto.text}</label>
                        </div>
                      );
                    })}
                  </div>
                </FormField>
                <FormField
                  columns={12}
                  attribute="entidade"
                  label="Entidade"
                  rule={getRule('entidade')}
                  submitted={submitted}
                >
                  <AsyncDropdown
                    id="entidade"
                    onChange={(e, v) => {
                      this.store.updateAttribute(e, v);
                      this.store.setPessoaStore(v.id);
                    }}
                    value={this.store.object.entidade ? this.store.object.entidade.id : undefined}
                    placeholder="Selecione uma entidade"
                    store={this.store.entidadeStore}
                    disabled={this.props.action === 'new' && AppStore.getContextEntity()}
                  />
                </FormField>

                <FormField columns={6} attribute="numero" label="Número" rule={getRule('numero')} submitted={submitted}>
                  <InputText
                    onChange={(e) => updateAttribute('numero', e)}
                    placeholder="Informe o número"
                    value={this.store.object.numero}
                  />
                </FormField>

                <FormField
                  columns={6}
                  attribute="tipo"
                  label="Tipo da Comissão"
                  rule={getRule('tipo')}
                  submitted={submitted}
                >
                  <div className="p-field-radiobutton p-dir-row">
                    {DadosEstaticosService.getTipoComissao().map((tipo, idx) => {
                      return (
                        <div className="p-field-radiobutton p-col" key={`tipo-${idx}`}>
                          <RadioButton
                            inputId={`tipo-${idx}`}
                            name={`tipo-${idx}`}
                            value={tipo}
                            onChange={(e) => this.store.updateAttribute('tipo', e.value.value)}
                            checked={this.store.object.tipo === tipo.value}
                          />
                          <label htmlFor={`tipo-${idx}`}>{tipo.text}</label>
                        </div>
                      );
                    })}
                  </div>
                </FormField>

                <FormField
                  columns={6}
                  attribute="dataVigenciaInicial"
                  label="Início da Vigência"
                  rule={getRule('dataVigenciaInicial')}
                  submitted={submitted}
                >
                  <FcCalendar
                    value={
                      this.store.object?.dataVigenciaInicial ? moment(this.store.object.dataVigenciaInicial)._d : null
                    }
                    onChange={(e) => updateAttribute('dataVigenciaInicial', e)}
                    id="dataVigenciaInicial"
                    showIcon
                    mask="99/99/9999"
                  />
                </FormField>

                <FormField
                  columns={6}
                  attribute="dataVigenciaFinal"
                  label="Fim da Vigência"
                  rule={getRule('dataVigenciaFinal')}
                  submitted={submitted}
                >
                  <FcCalendar
                    value={this.store.object?.dataVigenciaFinal ? moment(this.store.object.dataVigenciaFinal)._d : null}
                    onChange={(e) => updateAttribute('dataVigenciaFinal', e)}
                    id="dataVigenciaFinal"
                    showIcon
                    mask="99/99/9999"
                  />
                </FormField>

                <FormField columns={12} attribute="arquivos" label="Arquivos" submitted={submitted}>
                  <MultipleFileUploader
                    store={this.store.fileStore}
                    fileTypes={DadosEstaticosService.getTipoArquivoComissao()}
                    onChangeFiles={(files) => this.store.updateAttribute('arquivosTemporarios', files)}
                  />
                </FormField>
                {this.store.object?.entidade && this.store.pessoaStore && (
                  <FormField
                    columns={12}
                    attribute="membros"
                    label="Membros"
                    submitted={submitted}
                    rule={getRule('membros')}
                    infoTooltip={
                      this.store.object.tipoConjunto === 'CONTRATACAO'
                        ? 'Incluir todos os membros da comissão, inclusive o/a Presidente'
                        : 'Incluir todos os membros da equipe, inclusive o/a Agente de Contratação e/ou Pregoeiro(a)'
                    }
                  >
                    <AsyncPickList
                      value={this.store.object?.membros}
                      sourceHeader="Pessoas Disponíveis"
                      targetHeader="Membros Escolhidos"
                      onChange={(e) => updateAttribute('membros', e)}
                      store={this.store.pessoaStore}
                    />
                  </FormField>
                )}
              </div>
              {this.renderActionButtons()}
              {this._renderConfirmSave()}
            </form>
          </div>
        </>
      );
    } else {
      content = (
        <div className="card page">
          <i
            className="pi pi-spin pi-spinner"
            style={{
              marginTop: '20px',
              marginLeft: 'calc(50% - 20px)',
              fontSize: '2em',
            }}
          ></i>
        </div>
      );
    }
    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        {content}
      </PermissionProxy>
    );
  }
}

ComissaoFormPage.propTypes = {
  id: PropTypes.any,
  action: PropTypes.any,
  history: PropTypes.any,
};

export default ComissaoFormPage;
