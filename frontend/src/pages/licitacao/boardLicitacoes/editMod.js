import React from 'react';
import PropTypes from 'prop-types';
import LicitacaoDetailPage from './detalhes/IndexDetail';
import UrlRouter from '~/constants/UrlRouter';
import FcButton from 'fc/components/FcButton';
import PermissionProxy from 'fc/components/PermissionProxy';
import AccessPermission from '~/constants/AccessPermission';
import { checkUserGroup, showNotification, getValueDate } from 'fc/utils/utils';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import OcorrenciaSuspenderFormPage from '~/pages/ocorrenciaLicitacao/ocorrenciaSuspenderDialog';
import OcorrenciaProrrogarFormPage from '~/pages/ocorrenciaLicitacao/ocorrenciaProrrogarDialog';
import OcorrenciaReabrirFormPage from '~/pages/ocorrenciaLicitacao/ocorrenciaReabrirDialog';
import OcorrenciaFinalizarFormPage from '~/pages/ocorrenciaLicitacao/ocorrenciaFinalizarDialog';
import RequisicaoRemocao from '~/pages/requisicaoRemocao/requisicaoRemocaoModal';
import Form from '../form';
import { observer } from 'mobx-react';
import OcorrenciaContinuarFormPage from '~/pages/ocorrenciaLicitacao/ocorrenciaContinuarDialog';
import { ConfirmDialog } from 'primereact/confirmdialog';
import moment from 'moment';
import { DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS } from 'fc/utils/date';

@observer
class EditMod extends React.Component {
  constructor(props) {
    super(props);
    this.store = props.store;
    this._disableEditMode = this._disableEditMode.bind(this);
    this.switchSuspenderDialog = this.switchSuspenderDialog.bind(this);
    this.switchProrrogarDialog = this.switchProrrogarDialog.bind(this);
    this.switchReabrirDialog = this.switchReabrirDialog.bind(this);
    this.switchFinalizarDialog = this.switchFinalizarDialog.bind(this);
    this.switchContinuarDialog = this.switchContinuarDialog.bind(this);
    this._closeRequisicaoRemocaoModal = this._closeRequisicaoRemocaoModal.bind(this);
    this.state = {
      licitacaoAtual: undefined,
      editMod: false,
      fase: undefined,
      selectedRow: undefined,
      action: '',
      showSuspenderDialog: false,
      showProrrogarDialog: false,
      showReabrirDialog: false,
      showContinuarDialog: false,
      showFinalizarDialog: false,
      showRemoverDialog: false,
      showRequisicaoRemocao: false,
      showPreparatoriaDialog: false,
      idRemove: undefined,
      dataAberturaPreparatoria: undefined,
    };
  }

  switchSuspenderDialog() {
    this.setState({
      showSuspenderDialog: !this.state.showSuspenderDialog,
    });
  }

  switchProrrogarDialog() {
    this.setState({
      showProrrogarDialog: !this.state.showProrrogarDialog,
    });
  }

  switchReabrirDialog() {
    this.setState({
      showReabrirDialog: !this.state.showReabrirDialog,
    });
  }

  switchContinuarDialog() {
    this.setState({
      showContinuarDialog: !this.state.showContinuarDialog,
    });
  }

  switchFinalizarDialog() {
    this.setState({
      showFinalizarDialog: !this.state.showFinalizarDialog,
    });
  }
  openDialogPreparatoriaVisibility(licitacao) {
    this.setState({
      dataAberturaPreparatoria: licitacao.dataAbertura,
      showPreparatoriaDialog: !this.state.showPreparatoriaDialog,
      licitacaoAtual: licitacao,
    });
  }

  closeDialogPreparatoriaVisibility() {
    this.setState({
      dataAberturaPreparatoria: undefined,
      showPreparatoriaDialog: !this.state.showPreparatoriaDialog,
      licitacaoAtual: undefined,
    });
  }

  _closeRemoverDialog() {
    this.setState({ showRemoverDialog: false });
  }

  _handleRemocao(licitacao) {
    if (licitacao?.fase === 'PREPARATORIA') {
      this.setState({ showRemoverDialog: true, idRemove: licitacao?.id });
    } else {
      this._handleRemocaoRequisicaoModal(licitacao);
    }
  }

  _handleRemocaoRequisicaoModal(licitacao) {
    this.setState({ showRequisicaoRemocao: true, selectedRow: licitacao });
  }

  _closeRequisicaoRemocaoModal() {
    this.setState({ showRequisicaoRemocao: false, selectedRow: null });
  }

  _disableEditMode() {
    this.setState({ editMod: false });
  }

  _renderDialogRequisicaoRemocao() {
    return (
      <RequisicaoRemocao
        history={this.props.history}
        processo={this.props.licitacao}
        visibleDialog={this.state.showRequisicaoRemocao}
        closeDialog={this._closeRequisicaoRemocaoModal}
        updateTable={this.props.updateDataTable}
        tipoProcesso="licitacao"
      />
    );
  }

  _handleProximaFase(licitacao) {
    switch (licitacao.fase) {
      case 'PREPARATORIA':
        this.openDialogPreparatoriaVisibility(licitacao);
        break;
      case 'DIVULGACAO_PUBLICACAO_LICITACAO':
        this._faseApresentacaoPropostasLances(licitacao);
        break;
      default:
        this.setState({ fase: '', editMod: true, action: 'next' });
    }
  }

  _renderDialogConfirmacaoPreparatoria() {
    const dataAbertura = this.state.dataAberturaPreparatoria;
    return (
      <ConfirmDialog
        blockScroll
        visible={this.state.showPreparatoriaDialog}
        draggable={false}
        message={
          <div>
            <p>Confira os seguintes dados da licitação selecionada:</p>
            <ul>
              <li>Entidade: {this.state.licitacaoAtual.entidade.nome}</li>
              <li>
                Data e Hora de Abertura:{' '}
                {getValueDate(dataAbertura, DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS)}
              </li>
            </ul>
            <p>As informações acima estão corretas?</p>
          </div>
        }
        header="Confirmação"
        icon="pi pi-user preview-item"
        acceptClassName="p-button-info"
        onHide={() => this.closeDialogPreparatoriaVisibility()}
        accept={() => {
          this.setState({ fase: '', editMod: true, action: 'next' });
        }}
      />
    );
  }

  _faseApresentacaoPropostasLances(licitacao) {
    const dataAbertura = moment(licitacao.dataAbertura);
    const dataAtual = moment();
    if (dataAtual.isBefore(dataAbertura)) {
      showNotification(
        'warn',
        null,
        'Não é possível avançar para a próxima fase sem atingir a data de abertura da licitação'
      );
    } else {
      this.setState({ fase: '', editMod: true, action: 'next' });
    }
  }

  confirmRemove() {
    const { onRemove, updateDataTable } = this.props;
    const { deleteRow } = this.props.store;
    return (
      <ConfirmDialog
        blockScroll
        visible={this.state.showRemoverDialog}
        message="Você realmente deseja remover a licitação selecionada?"
        header="Remover licitação"
        icon="pi pi-exclamation-triangle"
        acceptClassName="p-button-danger"
        accept={() => {
          deleteRow(this.state.idRemove, () => {
            onRemove(this.state.idRemove);
            updateDataTable();
          });
        }}
        onHide={() => this._closeRemoverDialog()}
      />
    );
  }

  disableEditButton(faseLicitacao, faseBotao) {
    const fases = DadosEstaticosService.getFasesLicitacao().map((object) => object.value);
    const indexAtual = fases.indexOf(faseLicitacao);
    return indexAtual === -1 || indexAtual < fases.indexOf(faseBotao);
  }

  pushUrlToHistory(url) {
    url && this.props.history.push(url);
  }

  _renderActionButtons(item) {
    const isAuditor = checkUserGroup('Auditor');
    if (item) {
      let buttons = [
        {
          icon: 'pi pi-file',
          tooltip: 'Preparatória',
          style: { backgroundColor: '#3f51b5' },
          disabled: this.disableEditButton(item.fase, 'PREPARATORIA'),
          permissionsAttributes: [AccessPermission.licitacao.writePermission],
          onClick: () => this.setState({ fase: 'preparatoria', editMod: true, action: 'edit' }),
        },
        {
          icon: 'pi pi-book',
          tooltip: 'Divulgação e Publicação da Licitação',
          style: { backgroundColor: '#fbc02d' },
          disabled: this.disableEditButton(item.fase, 'DIVULGACAO_PUBLICACAO_LICITACAO'),
          permissionsAttributes: [AccessPermission.licitacao.writePermission],
          onClick: () => this.setState({ fase: 'divulgacaoEditalLicitacao', editMod: true, action: 'edit' }),
        },
        {
          icon: 'pi pi-shield',
          tooltip: 'Apresentação de Propostas e Lances',
          style: { backgroundColor: '#9c27b0' },
          disabled: this.disableEditButton(item.fase, 'APRESENTACAO_PROPOSTAS_LANCES'),
          permissionsAttributes: [AccessPermission.licitacao.writePermission],
          onClick: () => this.setState({ fase: 'apresentacao', editMod: true, action: 'edit' }),
        },
        {
          icon: 'pi pi-flag',
          tooltip: 'Finalização',
          style: { backgroundColor: 'darkcyan' },
          disabled: this.disableEditButton(item.fase, 'FINALIZACAO'),
          permissionsAttributes: [AccessPermission.licitacao.writePermission],
          onClick: () => this.setState({ fase: 'finalizacao', editMod: true, action: 'edit' }),
        },
        {
          icon: 'pi pi-arrow-right',
          tooltip: 'Próxima Fase',
          style: { backgroundColor: '#64748B' },
          onClick: () => this._handleProximaFase(item),
          disabled:
            item.fase === 'FINALIZACAO' ||
            item.ultimaOcorrencia == 'FINALIZAR' ||
            item.naturezaOcorrencia === 'SUSPENDER',
          permissionsAttributes: [AccessPermission.licitacao.writePermission],
        },
        {
          icon: 'pi pi-clock',
          tooltip: 'Suspender Licitação',
          disabled:
            item.status === 'REMOVIDA' ||
            item.naturezaOcorrencia === 'SUSPENDER' ||
            item.naturezaOcorrencia === 'FINALIZAR' ||
            item.fase === 'PREPARATORIA',
          style: { backgroundColor: '#f97316' },
          onClick: () => this.switchSuspenderDialog(),
          permissionsAttributes: [AccessPermission.ocorrenciaLicitacao.writePermission],
        },
        {
          icon: 'pi pi-calendar-plus',
          tooltip: 'Prorrogar Licitação',
          disabled:
            item.status === 'REMOVIDA' ||
            item.fase !== 'DIVULGACAO_PUBLICACAO_LICITACAO' ||
            item.naturezaOcorrencia === 'FINALIZAR' ||
            item.fase === 'APRESENTACAO_PROPOSTAS_LANCES',
          style: { backgroundColor: '#617C4D' },
          onClick: () => this.switchProrrogarDialog(),
          permissionsAttributes: [AccessPermission.ocorrenciaLicitacao.writePermission],
        },
        {
          icon: 'pi pi-folder-open',
          tooltip: 'Reabrir Licitação',
          disabled:
            (item.naturezaOcorrencia !== 'SUSPENDER' &&
              item.statusOcorrenciaAtual !== 'FRACASSADA' &&
              item.statusOcorrenciaAtual !== 'DESERTA' &&
              item.statusOcorrenciaAtual === 'REVOGADA' &&
              item.fase !== 'APRESENTACAO_PROPOSTAS_LANCES') ||
            (item.fase === 'DIVULGACAO_PUBLICACAO_LICITACAO' &&
              item.statusOcorrenciaAtual !== 'FRACASSADA' &&
              item.statusOcorrenciaAtual !== 'DESERTA') ||
            item.qtdContratosAssociados >= 1 ||
            item.fase === 'PREPARATORIA',
          style: { backgroundColor: '#048BA8' },
          onClick: () => this.switchReabrirDialog(),
          permissionsAttributes: [AccessPermission.ocorrenciaLicitacao.writePermission],
        },
        {
          icon: 'pi pi-caret-right',
          tooltip: 'Continuar Licitação',
          disabled: item.naturezaOcorrencia !== 'SUSPENDER' || item.fase === 'DIVULGACAO_PUBLICACAO_LICITACAO',
          style: { backgroundColor: '#70AC42' },
          onClick: () => this.switchContinuarDialog(),
          permissionsAttributes: [AccessPermission.ocorrenciaLicitacao.writePermission],
        },
        {
          icon: 'pi pi-check-square',
          tooltip: 'Finalizar Licitação',
          disabled:
            item.status === 'REMOVIDA' ||
            item.naturezaOcorrencia === 'FINALIZAR' ||
            item.fase === 'PREPARATORIA' ||
            item.qtdContratosAssociados >= 1,
          style: { backgroundColor: '#022B3A' },
          onClick: () => this.switchFinalizarDialog(),
          permissionsAttributes: [AccessPermission.ocorrenciaLicitacao.writePermission],
        },
        {
          icon: 'pi pi-plus',
          tooltip: 'Criar Contrato',
          style: { backgroundColor: '#6495ED' },
          disabled:
            item.fase !== 'FINALIZACAO' ||
            item.naturezaOcorrencia === 'SUSPENDER' ||
            (item.naturezaOcorrencia == 'FINALIZAR' &&
              ['FRACASSADA', 'REVOGADA', 'DESERTA', 'ANULADA'].includes(item.statusOcorrenciaAtual)),
          onClick: () =>
            this.pushUrlToHistory(
              UrlRouter.cadastrosConsulta.contrato.defaultLicitacao.replace(':idLicitacao', item.id)
            ),
          permissionsAttributes: [AccessPermission.ocorrenciaLicitacao.writePermission],
        },
        {
          icon: 'pi pi-trash',
          tooltip: 'Excluir',
          className: 'p-button-danger',
          onClick: () => this._handleRemocao(item),
          permissionsAttributes: [AccessPermission.licitacao.writePermission],
        },
      ];
      const idRequisicaoModificacaoLicitacao = item.idRequisicaoModificacao;
      const idRequisicaoModificacaoTermo = item.termoReferencia?.idRequisicaoModificacao;

      if (idRequisicaoModificacaoLicitacao) {
        buttons = [
          {
            icon: 'pi pi-exclamation-triangle',
            tooltip: 'Requisição de modificação pendente',
            style: { backgroundColor: '#c89800', marginLeft: '3px' },
            onClick: () =>
              this.pushUrlToHistory(
                isAuditor
                  ? UrlRouter.auditoria.requisicaoModificacao.indexDetail.replace(':id', item.idRequisicaoModificacao)
                  : UrlRouter.administracao.requisicaoModificacao.indexDetail.replace(
                      ':id',
                      item.idRequisicaoModificacao
                    )
              ),
          },
        ];
      }

      if (idRequisicaoModificacaoTermo) {
        buttons = [
          {
            icon: 'pi pi-exclamation-triangle',
            tooltip: 'Termo de Referência com Requisição de modificação pendente',
            style: { backgroundColor: '#c89800', marginLeft: '3px' },
            onClick: () =>
              this.pushUrlToHistory(
                isAuditor
                  ? UrlRouter.auditoria.requisicaoModificacao.indexDetail.replace(':id', idRequisicaoModificacaoTermo)
                  : UrlRouter.administracao.requisicaoModificacao.indexDetail.replace(
                      ':id',
                      idRequisicaoModificacaoTermo
                    )
              ),
          },
        ];
      }

      return (
        <div
          style={{
            float: 'right',
            marginBottom: '1.23rem',
          }}
          className="flex gap-2"
        >
          {buttons.map((propsButton) => {
            const button = <FcButton tooltipOptions={{ position: 'bottom' }} {...propsButton} />;
            if (propsButton.permissionsAttributes)
              return (
                <PermissionProxy resourcePermissions={propsButton.permissionsAttributes}>{button}</PermissionProxy>
              );
            else return button;
          })}
        </div>
      );
    }
  }

  render() {
    return (
      <div>
        {!this.state.editMod && this._renderActionButtons(this.props.licitacao)}
        {this.state.editMod ? (
          <Form
            id={this.props.licitacao.id}
            fase={this.state.fase}
            history={this.props.history}
            action={this.state.action}
            editMode
            disableEditMode={this._disableEditMode}
            callback={this.props.updateDataTable}
          />
        ) : (
          <LicitacaoDetailPage
            key={this.props.index}
            countDownloadRequest
            id={this.props.licitacao.id}
            onDetailContratoAssociado={this.props.onDetailContratoAssociado}
            onDetailEdital={this.props.onDetailEdital}
          />
        )}
        {this.state.showSuspenderDialog && (
          <OcorrenciaSuspenderFormPage
            showSuspenderDialog={this.state.showSuspenderDialog}
            switchSuspenderDialog={this.switchSuspenderDialog}
            idLicitacao={this.props.licitacao?.id}
            openDate={this.props.licitacao?.dataAbertura}
            reload={this.props.updateDataTable}
          />
        )}
        {this.state.showProrrogarDialog && (
          <OcorrenciaProrrogarFormPage
            showProrrogarDialog={this.state.showProrrogarDialog}
            switchProrrogarDialog={this.switchProrrogarDialog}
            idLicitacao={this.props.licitacao?.id}
            openDate={this.props.licitacao?.dataAbertura}
            reload={this.props.updateDataTable}
          />
        )}
        {this.state.showReabrirDialog && (
          <OcorrenciaReabrirFormPage
            showReabrirDialog={this.state.showReabrirDialog}
            switchReabrirDialog={this.switchReabrirDialog}
            idLicitacao={this.props.licitacao?.id}
            reload={this.props.updateDataTable}
            faseLicitacao={this.props.licitacao?.fase}
          />
        )}
        {this.state.showContinuarDialog && (
          <OcorrenciaContinuarFormPage
            showContinuarDialog={this.state.showContinuarDialog}
            switchContinuarDialog={this.switchContinuarDialog}
            idLicitacao={this.props.licitacao?.id}
            reload={this.props.updateDataTable}
          />
        )}
        {this.state.showFinalizarDialog && (
          <OcorrenciaFinalizarFormPage
            showFinalizarDialog={this.state.showFinalizarDialog}
            switchFinalizarDialog={this.switchFinalizarDialog}
            idLicitacao={this.props.licitacao?.id}
            openDate={this.props.licitacao?.dataAbertura}
            reload={this.props.updateDataTable}
            licitantes={this.props.licitacao?.licitantes}
            faseLicitacao={this.props.licitacao?.fase}
            licitacaoSuspensa={this.props.licitacao?.naturezaOcorrencia === 'SUSPENDER'}
          />
        )}
        {this.state.showRequisicaoRemocao && this._renderDialogRequisicaoRemocao()}
        {this.state.showRemoverDialog && this.confirmRemove(this.state.idRemove)}
        {this.state.showPreparatoriaDialog && this._renderDialogConfirmacaoPreparatoria()}
      </div>
    );
  }
}

EditMod.propTypes = {
  store: PropTypes.any,
  licitacao: PropTypes.object,
  updateDataTable: PropTypes.func,
  index: PropTypes.number,
  history: PropTypes.any,
  onDetailContratoAssociado: PropTypes.func,
  onRemove: PropTypes.func,
  onDetailEdital: PropTypes.func,
};

export default EditMod;
