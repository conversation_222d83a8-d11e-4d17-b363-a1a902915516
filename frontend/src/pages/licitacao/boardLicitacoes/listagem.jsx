import React from 'react';
import './style.scss';
import UrlRouter from '~/constants/UrlRouter';
import FcButton from 'fc/components/FcButton';
import { observer, PropTypes } from 'mobx-react';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import AdvancedSearch from 'fc/components/AdvancedSearch';
import { PrimeIcons } from 'primereact/api';
import AccessPermission from '~/constants/AccessPermission';
import PermissionProxy from 'fc/components/PermissionProxy';
import LazyScroller from './lazyScroller';
import { ConfirmDialog } from 'primereact/confirmdialog';
import AppStore from 'fc/stores/AppStore';

@observer
class BoardLicitacoesListagemPage extends React.Component {
  constructor(props) {
    super(props);
    this.store = this.props.boardStore;

    this.store.setRefresh(() => this._refreshLazys());

    const initialState = {
      reloadLazys: false,
      collapsedRays: {
        showEntidadeDialog: false,
        preparatoria: false,
        divulgacaoEditalLicitacao: false,
        apresentacao: false,
        finalizacao: false,
      },
    };

    this.state = initialState;

    this.pushUrlToHistory = this.pushUrlToHistory.bind(this);
    this._toggleDetails = this._toggleDetails.bind(this);
    this.toggleCollapseRay = this.toggleCollapseRay.bind(this);
  }

  componentDidMount() {
    const { id } = this.props.match.params;
    if (id) this.store.getLicitacaoById(id, (item) => this._toggleDetails(item));
    if (localStorage.getItem(window.location.hash)) {
      this.setState({ collapsedRays: JSON.parse(localStorage.getItem(window.location.hash)) });
    }
  }

  refreshPage() {
    this.pushUrlToHistory(UrlRouter.cadastrosConsulta.licitacao.index);
    document.location.reload(true);
  }

  _refreshLazys() {
    this.setState({ reloadLazys: !this.state.reloadLazys });
  }

  _renderHeader(ray, collapsed = false) {
    return (
      <>
        <FcButton
          icon={`pi ${collapsed ? 'pi-angle-down' : 'pi-angle-right'}`}
          className=" p-button-text toggle-button"
          onClick={() => this._toggleCollapse(ray.value)}
        />
        {ray.text}
      </>
    );
  }

  _toggleDetails(item, callback) {
    this.props.onDetail(item);
    callback && callback();
  }

  pushUrlToHistory(url) {
    url && this.props.history.push(url);
  }

  disableEditButton(faseLicitacao, faseBotao) {
    const fases = DadosEstaticosService.getFasesLicitacao().map((object) => object.value);
    const indexAtual = fases.indexOf(faseLicitacao);
    return indexAtual === -1 || indexAtual < fases.indexOf(faseBotao);
  }

  _getLazyScrollerSortFields() {
    return [
      { label: 'Data de Cadastro', value: 'dataCadastro' },
      { label: 'Número', value: 'numero' },
      { label: 'Valor', value: 'valorEstimado' },
      { label: 'Data de Alteração', value: 'updatedAt' },
    ];
  }

  toggleCollapseRay(phaseKey) {
    this.setState((oldState) => {
      const { collapsedRays } = oldState;
      collapsedRays[phaseKey] = !collapsedRays[phaseKey];

      localStorage.setItem(window.location.hash, JSON.stringify(collapsedRays));
      return { collapsedRays: collapsedRays };
    });
  }

  openDialogEntidadeVisibility() {
    this.setState({
      showEntidadeDialog: !this.state.showEntidadeDialog,
    });
  }

  closeDialogEntidadeVisibility() {
    this.setState({
      showEntidadeDialog: !this.state.showEntidadeDialog,
    });
  }

  _renderDialogConfirmacaoEntidade() {
    const message = `O novo registro criado será associado à entidade selecionada: ${
      AppStore.getContextEntity().nome
    }. Deseja continuar?`;
    return (
      <ConfirmDialog
        blockScroll
        visible={this.state.showEntidadeDialog}
        message={message}
        header="Confirmação"
        icon="pi pi-user preview-item"
        acceptClassName="p-button-info"
        draggable={false}
        onHide={() => this.closeDialogEntidadeVisibility()}
        accept={() => {
          this.pushUrlToHistory(UrlRouter.cadastrosConsulta.licitacao.novo);
        }}
      />
    );
  }

  render() {
    const idLicitacao = this.props.match.params.idLicitacao;
    const { collapsedRays } = this.state;

    return (
      <>
        <AdvancedSearch
          searchParams={this.store.getAdvancedSearchParams()}
          store={this.store}
          searchFields={['numeroAnoLicitacao', 'entidade', 'orgao', 'objeto']}
          filterSuggest={this.store.getFilterSuggest(idLicitacao)}
          useOr
        />
        <PermissionProxy resourcePermissions={[AccessPermission.licitacao.writePermission]}>
          <FcButton
            className="p-button"
            label="Nova Licitação"
            icon={PrimeIcons.PLUS}
            onClick={() => this.openDialogEntidadeVisibility()}
          />
        </PermissionProxy>
        <div className="licitacoes-page-container">
          <div className="lici-rays-container">
            {DadosEstaticosService.getFasesLicitacao().map((ray) => (
              <>
                <LazyScroller
                  history={this.props.history}
                  phaseKey={ray}
                  refresh={this.state.reloadLazys}
                  service={this.store.service}
                  advancedSearchParams={this.store.advancedSearchParams}
                  sortOptions={this._getLazyScrollerSortFields()}
                  store={this.store}
                  _toggleDetails={this._toggleDetails}
                  collapseRay={collapsedRays[ray.phaseKey]}
                  toggleCollapseRay={() => this.toggleCollapseRay(ray.phaseKey)}
                />
              </>
            ))}
          </div>
          {this.state.showEntidadeDialog && this._renderDialogConfirmacaoEntidade()}
        </div>
      </>
    );
  }
}

BoardLicitacoesListagemPage.propTypes = {
  boardStore: PropTypes.any,
  history: PropTypes.any,
  match: PropTypes.any,
  onDetail: PropTypes.func,
};

export default BoardLicitacoesListagemPage;
