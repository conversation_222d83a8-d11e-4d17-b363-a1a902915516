import React from 'react';
import '~/pages/licitacao/boardLicitacoes/style.scss';
import { observer } from 'mobx-react';
import PropTypes from 'prop-types';
import { Panel } from 'primereact/panel';
import classNames from 'classnames';
import { Skeleton } from 'primereact/skeleton';
import FcButton from 'fc/components/FcButton';
import { OverlayPanel } from 'primereact/overlaypanel';
import { Dropdown } from 'primereact/dropdown';
import { SelectButton } from 'primereact/selectbutton';
import { Card } from 'primereact/card';
import { Tag } from 'primereact/tag';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import { getLightenColor, getValueByKey, getValueDate, getValueMoney } from 'fc/utils/utils';
import LazyScrollerStore from '~/stores/boardLicitacoes/lazyScroller/lazyScrollerStore';
import { DATE_FORMAT, DATE_PARSE_FORMAT } from 'fc/utils/date';
import Tooltip from 'fc/components/Tooltip';
import { checkUserGroup } from 'fc/utils/utils';
import UrlRouter from '~/constants/UrlRouter';

@observer
class LazyScroller extends React.Component {
  constructor(props) {
    super(props);
    this.store = new LazyScrollerStore(props.service, props.phaseKey, props.advancedSearchParams);

    this.state = {
      prevY: 0,
      motivoOcorrencia: '',
    };

    this._getItemTemplate = this._getItemTemplate.bind(this);
    this._renderRayContent = this._renderRayContent.bind(this);
  }

  componentDidMount() {
    const options = {
      root: null,
      rootMargin: '0px',
      threshold: 1.0,
    };

    this.observer = new IntersectionObserver(this._handleObserver.bind(this), options);
    this.observer.observe(this.loadingRef);
  }

  componentDidUpdate(prevProps) {
    if (prevProps.refresh != this.props.refresh) {
      this.store.resetStates(this.props.advancedSearchParams);
      this.store.loadContent();
    }
    if (!this.props.collapseRay) {
      this.observer.observe(this.loadingRef);
    }
  }

  _renderDataAbertura(fase, licitacao) {
    if (fase != 'PREPARATORIA') {
      return (
        <Tooltip value="Data de Abertura" sideOffset={0}>
          <div style={{ cursor: 'default' }}>
            <Tag
              icon="pi pi-calendar"
              className="p-d-flex tags"
              style={{
                backgroundColor: getLightenColor('#4da73b', 0.7),
                color: '#4da73b',
                border: `1px solid #4da73b`,
              }}
            >
              <span>{getValueDate(licitacao.dataAbertura, DATE_FORMAT, DATE_PARSE_FORMAT)}</span>
            </Tag>
          </div>
        </Tooltip>
      );
    }
  }

  _renderRayContent(licitacao) {
    const {
      numero,
      ano,
      objeto,
      entidade,
      statusOcorrenciaAtual,
      naturezaOcorrencia,
      fase,
      idRequisicaoModificacao,
      termoReferencia,
    } = licitacao;

    const isAuditor = checkUserGroup('Auditor');
    const isJurisdicionado = checkUserGroup('Jurisdicionado');
    const hasValorAdjudicado = licitacao?.valorAdjudicado;
    const subTitle = `${numero} / ${ano}`;
    const titleEntityContext = isJurisdicionado ? subTitle : `${entidade ? entidade.nome : ''}`;

    return (
      <>
        <div
          onClick={() =>
            licitacao.obra
              ? this.props.store.carregarEdificacaoObra(licitacao, (licitacaoObra) =>
                  this.props._toggleDetails(licitacaoObra)
                )
              : this.props._toggleDetails(licitacao)
          }
        >
          <Card className="licitacao-content-card" key={licitacao.numero + '-' + licitacao.id}>
            <div className="title">
              <Tooltip value={titleEntityContext}>
                <div className="title mb-0 pb-0">{titleEntityContext}</div>
              </Tooltip>
              {idRequisicaoModificacao && (
                <FcButton
                  icon="pi pi-exclamation-triangle"
                  tooltip="Requisição de modificação pendente"
                  style={{ backgroundColor: '#c89800' }}
                  onClick={() =>
                    this.props.history.push(
                      isAuditor
                        ? UrlRouter.auditoria.requisicaoModificacao.indexDetail.replace(':id', idRequisicaoModificacao)
                        : UrlRouter.administracao.requisicaoModificacao.indexDetail.replace(
                            ':id',
                            idRequisicaoModificacao
                          )
                    )
                  }
                />
              )}
              {termoReferencia?.idRequisicaoModificacao && (
                <FcButton
                  icon="pi pi-exclamation-triangle"
                  tooltip="Termo de Referência com Requisição de modificação pendente"
                  style={{ backgroundColor: '#c89800' }}
                  onClick={() =>
                    this.props.history.push(
                      isAuditor
                        ? UrlRouter.auditoria.requisicaoModificacao.indexDetail.replace(
                            ':id',
                            termoReferencia?.idRequisicaoModificacao
                          )
                        : UrlRouter.administracao.requisicaoModificacao.indexDetail.replace(
                            ':id',
                            termoReferencia?.idRequisicaoModificacao
                          )
                    )
                  }
                />
              )}
            </div>
            {!isJurisdicionado && (
              <div className="subtitle text-base font-bold" style={{ paddingBottom: '1rem' }}>
                {subTitle}
              </div>
            )}
            <div className="desc">{objeto}</div>
            <div className="p-d-flex p-flex-wrap p-ai-end tags">
              {((fase === 'DIVULGACAO_PUBLICACAO_LICITACAO' && naturezaOcorrencia === 'PRORROGAR') ||
                (fase === 'DIVULGACAO_PUBLICACAO_LICITACAO' && naturezaOcorrencia === 'REABRIR') ||
                naturezaOcorrencia === 'SUSPENDER' ||
                naturezaOcorrencia === 'FINALIZAR') && (
                <Tag
                  key={'key' + licitacao.id + '-' + 'value'}
                  value={
                    this.props.store.tagList[naturezaOcorrencia].label === 'Finalizada'
                      ? getValueByKey(statusOcorrenciaAtual, DadosEstaticosService.getStatusOcorrenciaLicitacao())
                      : this.props.store.tagList[naturezaOcorrencia].label
                  }
                  style={{
                    backgroundColor: this.props.store.tagList[naturezaOcorrencia].backgroundColor,
                    color: this.props.store.tagList[naturezaOcorrencia].fontColor,
                  }}
                />
              )}
              <Tooltip value={hasValorAdjudicado ? 'Valor adjudicado' : 'Valor estimado'} sideOffset={0}>
                <div style={{ cursor: 'default' }}>
                  <Tag
                    icon="pi pi-money-bill"
                    className="p-d-flex"
                    style={
                      hasValorAdjudicado
                        ? {
                            backgroundColor: getLightenColor('#1e3a8a', 0.7),
                            color: '#1e3a8a',
                            border: `1px solid #1e3a8a`,
                          }
                        : {
                            backgroundColor: getLightenColor('#2F83DC', 0.7),
                            color: '#2F83DC',
                            border: `1px solid #2F83DC`,
                          }
                    }
                  >
                    <span>
                      {getValueMoney(hasValorAdjudicado ? licitacao?.valorAdjudicado : licitacao?.valorEstimado)}
                    </span>
                  </Tag>
                </div>
              </Tooltip>
              {this._renderDataAbertura(licitacao?.fase, licitacao)}
            </div>
          </Card>
        </div>
      </>
    );
  }

  _renderButton(icon, onClick) {
    return <FcButton icon={icon} style={{ backgroundColor: 'rgba(0, 0, 0, 0)' }} onClick={onClick} />;
  }

  _renderSelectButton() {
    return (
      <div style={{ marginLeft: '5px' }}>
        <b>Ordem</b>
        <SelectButton
          style={{ marginTop: '5px' }}
          options={[
            { icon: 'pi pi-sort-amount-down', value: 'desc' },
            { icon: 'pi pi-sort-amount-up', value: 'asc' },
          ]}
          value={this.store.orderBy}
          onChange={(e) => {
            this.store.orderBy = e.value;
          }}
          itemTemplate={this._getItemTemplate}
        />
      </div>
    );
  }

  _renderApplyButton(op, disableCond, loadContentParam) {
    return (
      <FcButton
        className="p-ml-auto p-button-primary p-mr-2"
        label="Aplicar"
        optionLabel="value"
        style={{ marginTop: '5px' }}
        disabled={!disableCond || !this.store.orderBy}
        onClick={(e) => {
          this.store.resetStates(this.props.advancedSearchParams);
          loadContentParam ? this.store.loadContent({}, loadContentParam) : this.store.loadContent();
          op.hide(e);
        }}
      />
    );
  }

  _renderHeader(ray, collapsed = false) {
    const motivosOcorrencia = DadosEstaticosService.getMotivosOcorrenciaFilter();
    const showOverlayNaoFinalizados = !collapsed && ray.text !== 'Finalizados';
    const showOverlayFinalizados = !collapsed && ray.text === 'Finalizados';

    return (
      <div className="p-d-flex p-jc-between p-ai-center">
        <div className="overflow-hidden text-overflow-ellipsis">
          <FcButton
            icon={`pi ${collapsed ? 'pi-angle-down' : 'pi-angle-right'}`}
            className="p-button-text toggle-button"
            onClick={() => this.props.toggleCollapseRay()}
          />
          <span className={`ray-name-${ray.phaseKey}`}>{ray.text}</span>
        </div>

        {showOverlayNaoFinalizados && this._renderButton('pi pi-sort-alt', (e) => this.op.toggle(e))}

        <OverlayPanel style={{ width: '400px' }} ref={(el) => (this.op = el)} showCloseIcon>
          <div className="p-d-flex p-jc-between p-ai-center">
            <div>
              <b style={{ display: 'block' }}>Campo</b>
              <Dropdown
                style={{ marginTop: '5px', minWidth: '15rem' }}
                optionLabel="label"
                optionValue="value"
                options={this.props.sortOptions}
                placeholder="Selecione um campo"
                onChange={(e) => {
                  this.store.column = e.value;
                }}
                value={this.store.column}
              />
            </div>
            {this._renderSelectButton()}
          </div>
          {this._renderApplyButton(this.op, this.store.column)}
        </OverlayPanel>

        {showOverlayFinalizados && (
          <div>
            {this._renderButton('pi pi-filter-fill', (e) => this.op2.toggle(e))}
            {this._renderButton('pi pi-sort-alt', (e) => this.op.toggle(e))}
          </div>
        )}
        <OverlayPanel style={{ width: '400px' }} ref={(el) => (this.op2 = el)} showCloseIcon>
          <div className="p-d-flex p-jc-between p-ai-center">
            <div>
              <b style={{ display: 'block' }}>Motivo da ocorrência</b>
              <Dropdown
                style={{ marginTop: '5px', minWidth: '15rem' }}
                options={motivosOcorrencia}
                placeholder="Selecione um campo"
                onChange={(e) => {
                  this.setState({ motivoOcorrencia: e.value.toUpperCase() });
                }}
                value={this.state.motivoOcorrencia.charAt(0) + this.state.motivoOcorrencia.slice(1).toLowerCase()}
              />
            </div>
            {this._renderSelectButton()}
          </div>
          {this._renderApplyButton(this.op2, this.store.column, this.state.motivoOcorrencia)}
        </OverlayPanel>
      </div>
    );
  }

  _getItemTemplate(option) {
    return <i style={{ marginLeft: '33%' }} className={option.icon} />;
  }

  _handleObserver(entities) {
    const y = entities[0].boundingClientRect.y;
    if (this.state.prevY > y) {
      if (this.store.page > 1 && !this.store.blockRequests) {
        this.state.motivoOcorrencia
          ? this.store.loadContent({}, this.state.motivoOcorrencia)
          : this.store.loadContent();
      }
    }

    this.setState({ prevY: y });
  }

  render() {
    return (
      <>
        {this.props.collapseRay ? (
          <div className={classNames('lici-ray-collapsed', `ray-${this.props.phaseKey.value}-collapsed`)}>
            <span className="p-panel-content p-component">{this._renderHeader(this.props.phaseKey, true)}</span>
          </div>
        ) : (
          <Panel
            collapsed={this.props.collapseRay}
            header={this._renderHeader(this.props.phaseKey)}
            className={classNames('lici-ray', `ray-${this.props.phaseKey.value}`)}
          >
            {this.store.data.map((item) => {
              return this._renderRayContent(item);
            })}
            <div
              ref={(loadingRef) => (this.loadingRef = loadingRef)}
              className="p-d-flex p-ai-center p-p-2"
              style={{ height: '20px' }}
            >
              {this.store.loading && <Skeleton width={'100%'} height="1rem" />}
            </div>
          </Panel>
        )}
      </>
    );
  }
}

LazyScroller.propTypes = {
  history: PropTypes.any,
  phaseKey: PropTypes.string,
  refresh: PropTypes.bool,
  service: PropTypes.any,
  advancedSearchParams: PropTypes.any,
  sortOptions: PropTypes.array,
  store: PropTypes.any,
  _toggleDetails: PropTypes.func,
  collapseRay: PropTypes.bool,
  toggleCollapseRay: PropTypes.func,
};

export default LazyScroller;
