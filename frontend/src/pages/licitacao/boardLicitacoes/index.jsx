import React from 'react';
import './style.scss';
import { observer, PropTypes } from 'mobx-react';
import AccessPermission from '~/constants/AccessPermission';
import PermissionProxy from 'fc/components/PermissionProxy';
import AppBreadCrumb from 'fc/components/AppBreadCrumb';
import BoardLicitacoesListagemPage from './listagem';
import { getValue } from 'fc/utils/utils';
import EditMod from './editMod';
import BoardLicitacoesIndexStore from '~/stores/boardLicitacoes/indexStore';
import FcCloseableTabView from 'fc/components/FcCloseableTabView';
import ContratoDetailPage from '~/pages/contrato/detalhes/indexDetail';
import AditivoDetailPage from '~/pages/contrato/aditivos/indexDetail';
import SentencaEditalDetail from '~/pages/analisarEditais/sentencaEdital/formDetail';

@observer
class BoardLicitacoesIndexPage extends React.Component {
  vencedorStore;
  constructor(props) {
    super(props);

    this.store = new BoardLicitacoesIndexStore();

    this.state = {
      data: [
        {
          id: 1,
          header: 'Listagem',
          closeable: false,
          content: (
            <BoardLicitacoesListagemPage
              {...props}
              boardStore={this.store}
              onDetail={(licitacao) => this.onDetail(licitacao)}
            />
          ),
        },
      ],
      count: 2,
      activeTabIndex: 0,
    };

    this._updateDatatable = this._updateDatatable.bind(this);
    this._updateHeaderTab = this._updateHeaderTab.bind(this);
    this.handleCloseTab = this.handleCloseTab.bind(this);
    this.getTitleLicitacao = this.getTitleLicitacao.bind(this);
    this.setActiveTabIndex = this.setActiveTabIndex.bind(this);
    this.onDetail = this.onDetail.bind(this);
    this.onDetailContratoAssociado = this.onDetailContratoAssociado.bind(this);
    this.onDetailAditivo = this.onDetailAditivo.bind(this);
    this.closeDeletedTab = this.closeDeletedTab.bind(this);
  }

  _updateDatatable(idLicitacao) {
    this.store.reloadTableData(() => this._updateHeaderTab(idLicitacao));
  }

  _updateHeaderTab(idLicitacao) {
    this.store.getLicitacaoById(idLicitacao, (licitacao) => {
      const dataUpdate = this.state.data.map((tab) => {
        if (tab.idLicitacao === idLicitacao) {
          tab.id = this.state.count;
          tab.header = this.getTitleLicitacao(licitacao);
          tab.content = (
            <EditMod
              licitacao={licitacao}
              index={this.state.count}
              history={this.props.history}
              updateDataTable={this._updateDatatable}
              store={this.store}
              onRemove={this.closeDeletedTab}
            />
          );
        }
        return tab;
      });

      this.setState({ data: dataUpdate, count: this.state.count + 1 });
    });
  }

  closeDeletedTab(idLicitacao) {
    const deletedTab = this.state.data.find((tab) => tab.idLicitacao === idLicitacao);
    this.setState((prevState) => {
      return { data: prevState.data.filter((t) => t.id !== deletedTab.id), activeTabIndex: 0 };
    });
  }

  onDetail(licitacao) {
    const existingTab = this.state.data.find((tab) => tab.idLicitacao === licitacao.id);
    if (existingTab) {
      this.setActiveTabIndex(existingTab);
    } else {
      const newLicitacao = {
        id: this.state.count,
        idLicitacao: licitacao.id,
        header: this.getTitleLicitacao(licitacao),
        badgeCount: licitacao.qtdContratosAssociados,
        closeable: true,
        content: (
          <div>
            <EditMod
              licitacao={licitacao}
              index={this.state.count}
              history={this.props.history}
              updateDataTable={this._updateDatatable}
              store={this.store}
              onDetailContratoAssociado={this.onDetailContratoAssociado}
              onRemove={this.closeDeletedTab}
              onDetailEdital={(edital) => this.onDetailEdital(edital)}
            />
          </div>
        ),
      };

      this.setState({ data: [...this.state.data, newLicitacao], count: this.state.count + 1 });
    }
  }

  onDetailEdital(edital) {
    const existingTab = this.state.data.find((tab) => tab.idEdital === edital.id);
    if (existingTab) {
      this.setActiveTabIndex(existingTab);
    } else {
      const newEditalDetail = {
        id: this.state.count,
        idEdital: edital.id,
        header: getValue(edital?.titulo),
        closeable: true,
        content: <SentencaEditalDetail idEdital={edital.id} />,
      };
      this.setState({ data: [...this.state.data, newEditalDetail], count: this.state.count + 1 });
    }
  }

  onDetailContratoAssociado(contrato) {
    const existingTab = this.state.data.find((tab) => tab.idContrato === contrato.id);
    if (existingTab) {
      this.setActiveTabIndex(existingTab);
    } else {
      const newContratoAssociado = {
        id: this.state.count,
        idContrato: contrato.id,
        header: `Contrato - ${contrato.numeroContrato}`,
        closeable: true,
        content: <ContratoDetailPage id={contrato.id} onDetailAditivo={(aditivo) => this.onDetailAditivo(aditivo)} />,
      };
      this.setState({ data: [...this.state.data, newContratoAssociado], count: this.state.count + 1 });
    }
  }

  onDetailAditivo(aditivo) {
    const existingTab = this.state.data.find((tab) => tab.idAditivo === aditivo.id);
    if (existingTab) {
      this.setActiveTabIndex(existingTab);
    } else {
      const newAditivo = {
        id: this.state.count,
        idAditivo: aditivo.id,
        header: 'Aditivo - ' + aditivo.numero + ' - Contr. ' + aditivo.contrato.numero,
        closeable: true,
        content: <AditivoDetailPage aditivo={aditivo} />,
      };
      this.setState({ data: [...this.state.data, newAditivo], count: this.state.count + 1 });
    }
  }

  getTitleLicitacao(licitacao) {
    const { numero, ano } = licitacao;
    return `${numero} / ${ano}`;
  }

  setActiveTabIndex(tab) {
    this.setState({ activeTabIndex: this.state.data.indexOf(tab) });
  }

  handleCloseTab(tab) {
    this.setState((prevState) => {
      return { data: prevState.data.filter((t) => t.id !== tab.id) };
    });
  }

  render() {
    const breacrumbItems = [{ label: 'Licitações' }];
    return (
      <PermissionProxy
        resourcePermissions={[
          AccessPermission.boardLicitacoes.readPermission,
          AccessPermission.licitacao.writePermission,
        ]}
        blockOnFail
      >
        <AppBreadCrumb items={breacrumbItems} />
        <div className="card page index-table form-tab-actions-buttons">
          <FcCloseableTabView
            tabs={this.state.data}
            activeTabIndex={this.state.activeTabIndex}
            onChangeTab={this.setActiveTabIndex}
            onClose={this.handleCloseTab}
            fixedFirstTab
          />
        </div>
      </PermissionProxy>
    );
  }
}

BoardLicitacoesIndexPage.propTypes = {
  history: PropTypes.any,
  match: PropTypes.any,
  qtdContratosAssociados: PropTypes.number,
};

export default BoardLicitacoesIndexPage;
