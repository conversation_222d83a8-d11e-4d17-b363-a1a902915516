.licitacoes-page-container {
  width: 100%;
  height: 100%;
  overflow: hidden;

  .lici-rays-container {
    white-space: nowrap;
    display: flex;
    overflow: auto;
    margin-top: 9px;
    gap: 6px !important;

    .lici-ray {
      display: inline-block;
      vertical-align: top;
      resize: horizontal;
      overflow: auto;
      width: 100%;
      min-width: 300px;

      .p-panel-content {
        height: calc(100vh - 350px) !important;
        overflow-y: auto;
        padding: 5px;
        background-color: #f3f3f3;
      }

      .licitacao-content-card {
        margin-bottom: 5px;
        cursor: pointer;
      }

      .p-card-content {
        padding: 0;

        .title {
          font-weight: bold;
          margin-bottom: 8px;
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
          justify-content: space-between;
          align-items: center;
          display: flex;
        }

        .title:hover {
          text-decoration: underline;
        }

        .desc {
          text-align: justify;
          font-size: 0.9em;
          width: 100%;
          max-height: 4.5em;
          line-height: 1.5em;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 3;
          word-wrap: break-word;
          white-space: pre-wrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .tags {
          margin-top: 5px;

          .p-tag {
            margin-right: 5px !important;
          }
        }
      }
    }

    .ray-PREPARATORIA {
      .p-panel-header {
        background-color: #3f51b5;
        color: #ffffff;
        padding: 0.2rem;

        .p-panel-title {
          overflow: hidden;
          text-overflow: ellipsis;
          width: 100%;
        }
      }

      .p-panel-content {
        background-color: #dee2f8 !important;
      }
    }

    .ray-DIVULGACAO_PUBLICACAO_LICITACAO {
      .p-panel-header {
        background-color: #fbc02d;
        color: #000000;
        padding: 0.2rem;

        .p-panel-title {
          overflow: hidden;
          text-overflow: ellipsis;
          width: 100%;
        }
      }

      .p-panel-content {
        background-color: #f6edd6;
      }
    }

    .ray-APRESENTACAO_PROPOSTAS_LANCES {
      .p-panel-header {
        background-color: #9c27b0;
        color: #ffffff;
        padding: 0.2rem;

        .p-panel-title {
          overflow: hidden;
          text-overflow: ellipsis;
          width: 100%;
        }
      }

      .title {
        width: 100%;
        text-overflow: ellipsis;
      }

      .p-panel-content {
        background-color: #f0dcf4;
      }
    }

    .ray-FINALIZACAO {
      .p-panel-header {
        background-color: #008b8b;
        color: #ffffff;
        padding: 0.2rem;

        .p-panel-title {
          overflow: hidden;
          text-overflow: ellipsis;
          width: 100%;
        }
      }

      .p-panel-content {
        background-color: #cdeaea;
      }
    }

    .lici-ray-collapsed {
      display: inline-block;
      vertical-align: top;
      min-width: 40px;
      height: calc(100vh - 309px) !important;
      background-color: #f3f3f3;
      border-radius: 3px;
      border: 1px solid #dee2e6;
      overflow: hidden;

      .p-panel-content {
        align-items: center;
        text-align: center;
        -webkit-writing-mode: vertical-rl;
        -moz-writing-mode: vertical-rl;
        -ms-writing-mode: vertical-rl;
        writing-mode: vertical-rl;
        font-weight: bold;
        color: #495057;
        padding: 0.7rem;
      }

      .toggle-button {
        margin-bottom: 10px;
      }
    }

    .ray-PREPARATORIA-collapsed {
      background-color: #3f51b5;

      .p-panel-content {
        color: #ffffff;
      }
    }

    .ray-DIVULGACAO_PUBLICACAO_LICITACAO-collapsed {
      background-color: #fbc02d;
    }

    .ray-APRESENTACAO_PROPOSTAS_LANCES-collapsed {
      background-color: #9c27b0;

      .p-panel-content {
        color: #ffffff;
      }
    }

    .ray-FINALIZACAO-collapsed {
      background-color: #008b8b;

      .p-panel-content {
        color: #ffffff;
      }
    }
  }

  .toggle-button {
    width: 1.1rem !important;
    height: 1.1rem !important;
    margin-right: 10px;
    color: inherit;
  }
}

.advanced-search-wrapper {
  white-space: nowrap;

  .p-card-body {
    padding-top: 0;
    padding-bottom: 0;
  }

  .p-autocomplete-multiple-container.p-component.p-inputtext {
    width: 100% !important;
  }

  .input-field {
    width: calc(100% - 155px);
    display: inline-block;
  }

  .data-type-select-field {
    display: inline-block;
    width: 113px;
    margin-right: 10px;
  }
}
