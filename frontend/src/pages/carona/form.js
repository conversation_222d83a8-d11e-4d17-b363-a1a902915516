import React from 'react';
import { observer } from 'mobx-react';
import PropTypes from 'prop-types';
import CaronaFormStore from '~/stores/carona/formStore';
import UrlRouter from '~/constants/UrlRouter';
import GenericFormPage from 'fc/pages/GenericFormPage';
import AppBreadCrumb from 'fc/components/AppBreadCrumb';
import AccessPermission from '~/constants/AccessPermission';
import PermissionProxy from 'fc/components/PermissionProxy';
import AppStore from 'fc/stores/AppStore';
import FormDadosBasicos from './Abas/abaFormDadosBasicos';
import { Steps } from 'primereact/steps';
import FormFornecedores from './Abas/abaFormFornecedores';
import FormItens from './Abas/abaFormItens';
import FormResumo from './Abas/abaResumo';
import FcButton from 'fc/components/FcButton';
import { Message } from 'primereact/message';
import classNames from 'classnames';
import <PERSON>Field from 'fc/components/FormField';
import { Dialog } from 'primereact/dialog';
import FcInputTextarea from 'fc/components/FcInputTextarea';
import RequisicaoModificacaoFormStore from '~/stores/requisicaoModificacao/formStore';
import { isValueValid, showNotification } from 'fc/utils/utils';
import { ProgressSpinner } from 'primereact/progressspinner';
import { ConfirmDialog } from 'primereact/confirmdialog';

@observer
class CaronaFormPage extends GenericFormPage {
  constructor(props) {
    super(props, UrlRouter.cadastrosConsulta.carona.index, AccessPermission.carona);

    this.store = new CaronaFormStore();
    this.reqModificacaoStore = new RequisicaoModificacaoFormStore();
    this.state = {
      activeIndexForm: 0,
      visibleDialogReqMod: false,
      errorDialogValue: false,
      isConfirmDialogVisibleDataAdesao: false,
    };

    this._toggleDialogReqMod = this._toggleDialogReqMod.bind(this);
    this.renderDialogRequisicaoModificacao = this.renderDialogRequisicaoModificacao.bind(this);
  }

  toggleShowConfirmDialogDataAdesao() {
    this.setState((prevState) => ({ isConfirmDialogVisibleDataAdesao: !prevState.isConfirmDialogVisibleDataAdesao }));
  }

  componentDidMount() {
    const { id } = this.props;
    const initialize = () =>
      this.store.initialize(
        id,
        {
          processoEntidadeOrigem: true,
          entidade: AppStore.getContextEntity(),
          tipoAdjudicacao: 'ITEM',
        },
        () => {
          if (id) {
            this.store.updateLicitantes();
            this.store.checkDataCadastro();
            this.store.recuperaVencedores();
            this.store.updateNomeEntidadeOrigem();
            this.store.loadTipos();
          }
          this.forceUpdate();
        }
      );
    if (id) {
      this.store.initializeArquivos(id, initialize);
    } else {
      initialize();
    }
  }

  onBack = () => {
    this.setState({ activeIndexForm: this.state.activeIndexForm - 1 });
  };

  onNext = () => {
    this.setState({ activeIndexForm: this.state.activeIndexForm + 1 });
  };

  isDisabledAvancar() {
    const { activeIndexForm } = this.state;
    const errorsMessages = {
      message: '',
      disabled: true,
    };

    switch (activeIndexForm) {
      case 0:
        if (!this.store.validaDadosBasicos()) {
          errorsMessages.message =
            'É necessário preencher todos os campos obrigatórios antes de avançar para a próxima etapa.';
        } else {
          errorsMessages.disabled = false;
        }
        break;
      case 1:
        if (!this.store.object?.detentores?.length && !this.store.object?.licitantes?.length) {
          errorsMessages.message =
            'É necessário adicionar ao menos um fornecedor antes de avançar para a próxima etapa.';
        } else {
          errorsMessages.disabled = false;
        }
        break;
      case 2:
        if (this.store.object.processoEntidadeOrigem && this.store.object?.licitacao?.termoReferencia) {
          if (!this.store.isProcessoMigrado() && this.store.caronaVencedorStore.hasLicitantesNotAssign) {
            errorsMessages.message = 'É necessário adicionar todos os fornecedores.';
          } else if (!this.store.isProcessoMigrado() && this.store.caronaVencedorStore.hasItensNotFilleds) {
            errorsMessages.message = 'Itens vencidos precisam ser preenchidos.';
          } else {
            errorsMessages.disabled = false;
          }
        } else {
          if (!this.store.isProcessoMigrado() && this.store.detentoresStore.hasLicitantesNotAssign) {
            errorsMessages.message = 'É necessário adicionar todos os fornecedores.';
          } else if (!this.store.isProcessoMigrado() && this.store.detentoresStore.hasItensNotFilleds) {
            errorsMessages.message = 'Itens vencidos precisam ser preenchidos.';
          } else if (this.store.object?.detentores?.some((i) => i.itemLote?.valorUnitarioEstimado < i.valorUnitario)) {
            errorsMessages.message = 'O valor unitário dos itens não deve ser superior ao valor adjudicado.';
          } else if (!this.store.isProcessoMigrado() && this.store.detentoresStore.hasQuantityNotAssign) {
            errorsMessages.message = 'Alguns itens possuem quantidades remanescentes não vencidas.';
          } else {
            errorsMessages.disabled = false;
          }
        }
        break;
      case 3:
        errorsMessages.disabled = false;
        break;
    }
    return errorsMessages;
  }

  onChangeStep(step, action = 'avancar') {
    if (step < 0) {
      this.store.toggleShowConfirmDialog();
    } else {
      if (step < this.state.activeIndexForm) {
        this.setState({ activeIndexForm: step });
      } else {
        if (this.store.validateSubmittedFiles(this.store.arquivoCaronaList)) {
          if (this.store.dataAdesaoIsValid() && action !== 'voltar' && step === 1 && this.store.dialogOpen) {
            this.toggleShowConfirmDialogDataAdesao();
            this.store.dialogOpen = false;
          } else {
            (action !== 'avancar' || !this.isDisabledAvancar().disabled) && this.setState({ activeIndexForm: step });
          }
        }
      }
    }
  }

  _toggleDialogReqMod() {
    this.setState((oldState) => ({ visibleDialogReqMod: !oldState.visibleDialogReqMod }));
  }

  validarForm(callback) {
    if (this.store.validateSubmittedFiles(this.store.arquivoCaronaList)) {
      if (this.state.activeIndexForm === 0) {
        this.store.validaArquivos(callback);
      } else {
        callback && callback();
      }
    }
  }

  renderActionButtons() {
    const { activeIndexForm } = this.state;
    const hasWritePermission = !this.getWritePermission() || AppStore.hasPermission(this.getWritePermission());

    const disabledAvancar = this.isDisabledAvancar();
    return (
      <div className="p-mt-10 form-actions">
        <div className="p-mt-2">
          <span className="p-mr-1 p-d-inline p-d-flex align-items-center justify-content-end">
            {disabledAvancar.disabled && (
              <Message className="p-ml-auto p-mr-2" severity="warn" text={disabledAvancar.message} />
            )}
            <FcButton
              label="Voltar"
              type="button"
              className={classNames('p-button-secondary p-mr-2', { 'p-ml-auto': !disabledAvancar.disabled })}
              onClick={() => this.onChangeStep(activeIndexForm - 1, 'voltar')}
              loading={this.store.loading}
            />
            {activeIndexForm < 3 && (
              <FcButton
                label="Avançar"
                type="button"
                onClick={() => this.validarForm(() => this.onChangeStep(activeIndexForm + 1))}
                disabled={disabledAvancar.disabled}
              />
            )}
            {hasWritePermission && activeIndexForm === 3 && !this.store.enableReqMod && (
              <FcButton label="Salvar" type="submit" loading={this.store.loading} />
            )}
            {hasWritePermission && activeIndexForm === 3 && this.store.enableReqMod && (
              <FcButton
                label="Enviar Requisição"
                type="button"
                onClick={() => this._toggleDialogReqMod()}
                loading={this.reqModificacaoStore.loading}
              />
            )}
          </span>
        </div>
      </div>
    );
  }

  renderDialogDataAdesao() {
    const { activeIndexForm } = this.state;

    return (
      <ConfirmDialog
        visible={this.state.isConfirmDialogVisibleDataAdesao}
        message={
          <label style={{ color: '#dd0303' }}>
            Data de Adesão posterior à Data de validade da ata. Deseja prosseguir com o cadastro?
          </label>
        }
        accept={() => this.setState({ activeIndexForm: activeIndexForm + 1 })}
        reject={() => (this.store.dialogOpen = true)}
        header="Atenção"
        icon="pi pi-exclamation-triangle"
        onHide={() => this.toggleShowConfirmDialogDataAdesao()}
      />
    );
  }

  renderDialogReqModFooter() {
    return (
      <div>
        <FcButton
          label="Cancelar"
          icon="pi pi-times"
          onClick={() => this._toggleDialogReqMod()}
          className="p-button-text"
        />
        <FcButton
          label="Enviar Requisição"
          icon="pi pi-check"
          loading={this.reqModificacaoStore.loading}
          onClick={() => {
            if (this.reqModificacaoStore.justificativaJurisdicionado) {
              this.submitFormData();
              this._toggleDialogReqMod();
            } else {
              !this.state.errorDialogValue && this.setState({ errorDialogValue: true });
            }
          }}
        />
      </div>
    );
  }

  _getFieldErrorMessage(rendered, message) {
    if (rendered) {
      return <small className="p-error">{message}</small>;
    }
    return null;
  }

  renderDialogRequisicaoModificacao() {
    const { getRule } = this.store;
    return (
      <Dialog
        header="Justificativa Requisição"
        visible={this.state.visibleDialogReqMod}
        style={{ width: '50vw' }}
        footer={this.renderDialogReqModFooter()}
        onHide={() => this._toggleDialogReqMod()}
      >
        <div className="p-fluid p-formgrid p-grid">
          <FormField columns={12} attribute="justificativa" rule={getRule('justificativa')} label="Justificativa">
            <FcInputTextarea
              className={this.state.errorDialogValue ? 'p-invalid p-error' : ''}
              value={this.reqModificacaoStore.justificativaJurisdicionado}
              onChange={(e) => {
                this.reqModificacaoStore.updateAttribute('jurisdicionado', e);
                isValueValid(this.reqModificacaoStore.justificativaJurisdicionado?.length) &&
                  this.setState({ errorDialogValue: false });
              }}
              placeholder="Descreva a justificativa"
            />
          </FormField>
          <div className="p-col-6">
            {this._getFieldErrorMessage(this.state.errorDialogValue, 'Por favor, preencha o campo')}
          </div>
        </div>
      </Dialog>
    );
  }

  submitFormData(e) {
    let filtros = {};
    if (this.store.object?.processoMigrado) {
      filtros = { tipoCarona: 'ANTIGA' };
    } else {
      filtros = {
        lei: this.store.object.lei,
        tipoOrgao: this.store.object?.processoEntidadeOrigem ? 'ORGAO_INTERNO' : 'ORGAO_NAO_INTERNO',
        naturezaObjeto:
          this.store.object?.naturezasDoObjeto && this.store.object?.naturezasDoObjeto.includes('OBRAS')
            ? 'NATUREZAOBJ_OBRA'
            : undefined,
      };
    }
    e && e.preventDefault();
    const execution = () => {
      if (!this.store.rules.hasError) {
        !this.store.enableReqMod && this.store.save(this._goBack, this.props.action);
        const caronaDTO = {
          carona: { ...this.store.object },
          arquivosCarona: this.store.arquivoCaronaList,
          filtros: filtros,
        };
        this.store.enableReqMod &&
          this.reqModificacaoStore.justificativaJurisdicionado &&
          this.reqModificacaoStore.enviarRequisicaoCarona(caronaDTO, this._goBack);
      } else {
        showNotification('error', null, 'Verifique os campos do formulário!');
      }
    };

    if (this.state.submitted) {
      execution();
    } else {
      this.setState({ submitted: true }, execution);
    }
  }

  render() {
    const { submitFormData } = this;
    const { submitted } = this.state;

    const breacrumbItems = [
      { label: 'Adesão/Carona', url: UrlRouter.cadastrosConsulta.carona.index },
      { label: this.props.action === 'new' ? 'Novo' : 'Editar' },
    ];

    if (this.store.object) {
      const forms = [
        {
          label: 'Dados Básicos',
          step: 0,
          body: <FormDadosBasicos key="dadosBasicos" store={this.store} submitted={submitted} />,
        },
        {
          label: 'Fornecedor/Prestador',
          step: 1,
          body: (
            <FormFornecedores
              key="detentores"
              submitted={submitted}
              store={this.store}
              disabled={this.getWritePermission() && !AppStore.hasPermission(this.getWritePermission())}
            />
          ),
        },
        {
          label: 'Itens',
          step: 2,
          body: <FormItens key="itens" store={this.store} action={this.props.action} />,
        },
        {
          label: 'Resumo',
          step: 3,
          body: <FormResumo key="resumo" store={this.store} action={this.props.action} history={this.props.history} />,
        },
      ];
      return (
        <PermissionProxy resourcePermissions={this.getWritePermission()} blockOnFail>
          <AppBreadCrumb items={breacrumbItems} />
          <div className="card page form-action-buttons">
            <form onSubmit={submitFormData}>
              <Steps
                model={forms}
                activeIndex={this.state.activeIndexForm}
                onSelect={(e) => {
                  if (this.store.validaDadosBasicos()) {
                    this.validarForm(() => this.onChangeStep(e.index));
                  }
                }}
                readOnly={false}
              />
              {forms.find((item) => item.step === this.state.activeIndexForm).body}
              {this.renderActionButtons()}
              {this.store.isConfirmDialogVisible && this.confirmDiscardChanges()}
              {this.state.isConfirmDialogVisibleDataAdesao && this.renderDialogDataAdesao()}
            </form>
            {this.renderDialogRequisicaoModificacao()}
          </div>
        </PermissionProxy>
      );
    } else {
      return (
        <div className="card page">
          <AppBreadCrumb items={breacrumbItems} />
          <div className="p-d-inline p-d-flex align-items-center">
            <ProgressSpinner />
          </div>
        </div>
      );
    }
  }
}

CaronaFormPage.propTypes = {
  id: PropTypes.any,
  action: PropTypes.any,
  history: PropTypes.any,
};

export default CaronaFormPage;
