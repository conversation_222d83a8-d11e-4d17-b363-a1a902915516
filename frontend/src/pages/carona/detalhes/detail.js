import React from 'react';
import PropTypes from 'prop-types';
import { Link } from 'react-router-dom';
import { observer } from 'mobx-react';
import { Divider } from 'primereact/divider';
import { getValue<PERSON><PERSON>, getValue<PERSON><PERSON><PERSON><PERSON>, getValueDate, generateFullURL } from 'fc/utils/utils';
import { DATE_FORMAT, DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT, DATE_PARSE_FORMAT_WITH_HOURS } from 'fc/utils/date';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import Vencedores from '~/pages/vencedores';
import './style.scss';
import AppStore from 'fc/stores/AppStore';
import FcCloseableTabView from 'fc/components/FcCloseableTabView';
import ScrollMenu from 'fc/components/ScrollMenu';
import classNames from 'classnames';
import ItensCarona from '../Abas/components/caronaVencedor';
import MultipleFileUploader from 'fc/components/MultipleFileUploader';
import AccessPermission from '~/constants/AccessPermission';
import UrlRouter from '~/constants/UrlRouter';
import GerenciamentoTermoFormStore from '~/stores/gerenciamentoTermo/formStore';
import FcButton from 'fc/components/FcButton';

@observer
class CaronaDetailPage extends React.Component {
  termoReferenciaStore;
  constructor(props) {
    super(props);
    this.state = { numeroDownloads: 0, activeTabIndex: 0 };
    this.termoReferenciaStore = new GerenciamentoTermoFormStore();
  }

  componentDidMount() {
    const { caronaStore, idTermo, idLicitacaoOrigem } = this.props;
    if (caronaStore) {
      this.setState({ numeroDownloads: caronaStore.object?.numeroDownloads ?? 0 });
    }
    if (idTermo) {
      this.termoReferenciaStore.initialize(idTermo, {}, () => {
        this.termoReferenciaStore.recuperarArquivos(idTermo);
      });
    }
    if (idLicitacaoOrigem) {
      caronaStore.carregarArquivosLicitacaoById(this.props.idLicitacaoOrigem, this.props.caronaStore.object.lei);
    }
    if (caronaStore?.idCarona) {
      caronaStore.hasAlert(caronaStore.idCarona);
    }
  }

  getValueStyle() {
    return { fontSize: AppStore.layout === 'extenso' ? '1.26rem' : '1.1rem' };
  }

  getValueLayoutClassNames() {
    const tipoLayout = AppStore.layout;
    return { 'py-0': tipoLayout === 'compacto', 'py-2': tipoLayout === 'default' || tipoLayout === 'extenso' };
  }

  _renderValue(label, value, col = 12, type = 'value', url = '#') {
    return (
      <div
        style={this.getValueStyle()}
        className={classNames(
          `details-set details-display flex align-items-center p-col-12 p-md-${col} p-sm-${col >= 6 ? col : 6}`,
          this.getValueLayoutClassNames()
        )}
      >
        <div
          style={{ color: 'rgb(0 0 0 / 50%)', paddingLeft: 0 }}
          className="xl:col-2 lg:col-3 md:col-4 sm:col-4 details-attributte drawer-content-label"
        >
          {label}
        </div>

        {type == 'value' && (
          <div
            style={{ paddingLeft: 0 }}
            className={`xl:col-10 lg:col-9 md:col-8 sm:col-8 details-value p-text-justify pb-1`}
          >
            {value ?? '-'}
          </div>
        )}

        {type == 'list' && (
          <div style={{ display: 'block', paddingLeft: 0 }}>
            {value?.map((value) => (
              <div className={`details-value p-text-justify`}>{value ?? '-'}</div>
            ))}
          </div>
        )}

        {type == 'link' && (
          <div
            style={{ paddingLeft: 0 }}
            className={`xl:col-10 lg:col-9 md:col-8 sm:col-8 details-value p-text-justify`}
          >
            {
              <Link target="_blank" rel="noreferrer" style={{ color: '#3F51B5' }} to={url}>
                <u>{value}</u>
              </Link>
            }
          </div>
        )}
      </div>
    );
  }

  _renderDivider(label) {
    return (
      <div style={{ flexBasis: 'auto', display: 'flex' }}>
        <h5
          style={{
            padding: '7px',
            whiteSpace: 'nowrap',
            width: 'auto',
            color: '#333333',
            alignContent: 'left',
            marginRight: '5px',
            marginTop: '11px',
          }}
        >
          {label}
        </h5>
        <Divider />
      </div>
    );
  }

  _renderAlert() {
    const { caronaStore } = this.props;
    if (caronaStore.hasPermissionAlerta() && caronaStore.idAlerta) {
      return (
        <FcButton
          icon="pi pi-bell"
          label="Histórico de Alertas"
          className="p-button-danger my-3"
          onClick={() => {
            window.open(generateFullURL(UrlRouter.alerta.editar.replace(':id', caronaStore.idAlerta)), '_blank');
          }}
        />
      );
    }
  }

  _renderTabs() {
    const { caronaStore, countDownloadRequest, idLicitacaoOrigem } = this.props;

    const tabs = [];
    tabs.push({ id: 0, header: 'Fornecedor/Prestador', content: this._renderTabFornecedorPrestador() });
    tabs.push({
      id: 1,
      header: 'Arquivos',
      content: (
        <MultipleFileUploader
          fileTypes={DadosEstaticosService.getTipoArquivoCarona()}
          countDownloadRequest={countDownloadRequest}
          fileTypesToCount={['EDITAL']}
          downloadOnly
          store={caronaStore.fileStore}
          onDownload={(tipo) => {
            if (tipo === 'EDITAL') {
              countDownloadRequest && this.setState({ numeroDownloads: this.state.numeroDownloads + 1 });
            }
          }}
        />
      ),
    });
    if (this.props.idTermo) {
      tabs.push({
        id: 2,
        header: 'Arquivos do Termo de Referência',
        content: (
          <MultipleFileUploader
            downloadOnly
            store={this.termoReferenciaStore?.fileStore}
            fileTypes={DadosEstaticosService.getTipoArquivoTermoReferencia()}
            accept=".pdf, .xls, .xlsx"
          />
        ),
      });
    }
    if (idLicitacaoOrigem) {
      tabs.push({
        id: 2,
        header: 'Arquivos da Licitação de Origem',
        content: (
          <MultipleFileUploader
            downloadOnly
            store={caronaStore.fileStoreLicitacao}
            fileTypes={DadosEstaticosService.getTipoArquivoLicitacao()}
          />
        ),
      });
    }
    return (
      <FcCloseableTabView
        tabs={tabs}
        activeTabIndex={this.state.activeTabIndex}
        onChangeTab={(tab) => this.setState({ activeTabIndex: tabs.indexOf(tab) })}
      />
    );
  }

  _renderTabFornecedorPrestador() {
    const { caronaStore } = this.props;
    return (
      <>
        {caronaStore.detentoresStore && (
          <Vencedores
            readOnly
            store={caronaStore.detentoresStore}
            labelLicitante="fornecedor"
            entidadeAntiga={caronaStore.isProcessoMigrado()}
          />
        )}
        {caronaStore.caronaVencedorStore && (
          <ItensCarona
            readOnly
            store={caronaStore.caronaVencedorStore}
            showDesconto={caronaStore.object?.licitacao?.tiposLicitacao
              ?.map((t) => t.nome?.toUpperCase())
              ?.includes('MAIOR DESCONTO')}
            showEspecificacao={caronaStore.object?.licitacao?.naturezasDoObjeto?.includes('COMPRAS')}
          />
        )}
      </>
    );
  }

  renderDadosBasicos() {
    const { caronaStore, showGerenciamento, countDownloadRequest } = this.props;
    const { numeroDownloads } = this.state;
    const carona = caronaStore.object;
    const { idCarona } = caronaStore;

    return (
      <div className="p-fluid p-formgrid">
        <div className="relative">
          <div className="scroll-menu">
            <ScrollMenu
              title="sumário"
              layoutPosition="left"
              offsetTopOnScroll={50}
              links={[
                { id: `detalhes-${idCarona}`, label: 'Detalhes' },
                { id: `informacoesLegais-${idCarona}`, label: 'Informações Legais' },
                { id: `recursos-${idCarona}`, label: 'Recursos' },
                { id: `gerenciamento-${idCarona}`, label: 'Gerenciamento' },
              ]}
            />
          </div>
        </div>
        <div id={`detalhes-${idCarona}`}>{this._renderDivider('Detalhes')}</div>
        {!carona.migrado && this._renderValue('Número do Processo Administrativo', carona.numeroProcessoAdministrativo)}
        {carona.migrado &&
          this._renderValue('Número do Processo Administrativo (Aderente)', carona.numeroProcessoGerenciadorAta)}
        {!carona.migrado && this._renderValue('Número da Adesão/Carona', carona.numeroProcessoGerenciadorAta)}
        {this._renderValue('Ano', carona.anoCarona)}
        {this._renderValue('Valor', getValueMoney(carona.valor, carona.termoReferencia?.tresCasasDecimais ? 3 : 2))}
        {this._renderValue('Objeto', carona.objeto)}
        {this._renderValue('Data da Adesão/Carona', getValueDate(carona.dataAdesao, DATE_FORMAT, DATE_PARSE_FORMAT))}
        {this._renderValue(
          'Naturezas do Objeto',
          carona?.naturezasDoObjeto
            ?.map((n) => getValueByKey(n, DadosEstaticosService.getNaturezaObjetoLicitacao()))
            .join(', ')
        )}
        <div id={`informacoesLegais-${idCarona}`}>{this._renderDivider('Informações Legais')}</div>
        {this._renderValue('Fundamentação Legal', carona?.fundamentacaoLegal)}
        {this._renderValue(
          'Órgão Gerenciador da Ata',
          carona.gerenciadorAta ??
            carona.entidadeOrigem?.nome ??
            carona.entidadeOrigemExterna?.nomeEntidadeExterna ??
            carona.entidadeOrigemExterna?.nome
        )}
        {this._renderValue(
          'Número do Pregão ou Concorrência de origem',
          carona.entidadeOrigemExterna
            ? carona.numeroLicitacaoEntidadeExterna
            : carona.licitacao
            ? `${carona.licitacao?.numero}/${carona.licitacao?.ano}`
            : null
        )}
        {carona.entidadeOrigemExterna && carona.termoReferencia && (
          <>
            {AppStore.hasPermission([
              AccessPermission.gerenciamentoTermo.readPermission,
              AccessPermission.gerenciamentoTermo.writePermission,
            ])
              ? this._renderValue(
                  'Termo de Referência',
                  carona.termoReferencia?.identificadorProcesso,
                  12,
                  'link',
                  UrlRouter.termoReferencia.gerenciamentoTermos.detalhe.replace(':idTermo', carona.termoReferencia.id)
                )
              : this._renderValue('Termo de Referência', carona.termoReferencia?.identificadorProcesso)}
          </>
        )}
        {this._renderValue(
          'Data de Validade da Ata',
          getValueDate(carona.dataValidadeAta, DATE_FORMAT, DATE_PARSE_FORMAT)
        )}
        {this._renderValue('Responsável pela Adesão/Carona', carona.responsavelAdesao)}
        {countDownloadRequest && this._renderValue('Número de Downloads', numeroDownloads)}
        <div id={`recursos-${idCarona}`}>{this._renderDivider('Recursos')}</div>

        {this._renderValue('Fontes de Recurso', carona?.fontesDeRecurso?.map((fonte) => fonte.descricao).join('; '))}
        {showGerenciamento && (
          <>
            <div id={`gerenciamento-${idCarona}`}>{this._renderDivider('Gerenciamento')}</div>
            {carona.id && this._renderValue('Cadastrado por', carona.usuario ? carona.usuario.nome : null)}
            {carona.id &&
              this._renderValue(
                'Data/Hora',
                getValueDate(carona.dataCadastro, DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS)
              )}
            {carona.id &&
              this._renderValue('Alterado por', caronaStore.ultimaAlteracao ? caronaStore.ultimaAlteracao?.nome : null)}
            {carona.id &&
              this._renderValue(
                'Data/Hora',
                getValueDate(caronaStore.ultimaAlteracao?.data, DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS)
              )}
          </>
        )}
      </div>
    );
  }

  render() {
    return (
      <div>
        {this.renderDadosBasicos()}
        <Divider style={{ marginBottom: `1px` }} />
        {this._renderAlert()}
        {this._renderTabs()}
      </div>
    );
  }
}

CaronaDetailPage.defaultProps = {
  countDownloadRequest: false,
  showGerenciamento: true,
};

CaronaDetailPage.propTypes = {
  caronaStore: PropTypes.any,
  countDownloadRequest: PropTypes.bool,
  showGerenciamento: PropTypes.bool,
  idTermo: PropTypes.number,
  idLicitacaoOrigem: PropTypes.number,
};

export default CaronaDetailPage;
