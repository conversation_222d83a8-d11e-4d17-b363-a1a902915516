import React from 'react';
import PropTypes from 'prop-types';
import { observer } from 'mobx-react';
import CaronaFormStore from '~/stores/carona/formStore';
import { ProgressSpinner } from 'primereact/progressspinner';
import CaronaDetailPage from './detail';

@observer
class CaronaIndexDetailPage extends React.Component {
  constructor(props) {
    super(props);
    this.caronaStore = new CaronaFormStore();
  }

  componentDidMount() {
    const { id } = this.props;
    if (id) {
      this.caronaStore.initialize(id, {}, () => {
        this.caronaStore.initizalizeDetentorStore();
        this.caronaStore.initializeArquivos(id);
        this.caronaStore.carregaUltimaAlteracao(id);
        this.caronaStore.initializeTdaCarona(id);
      });
    }
  }

  render() {
    const carona = this.caronaStore?.object;
    let content = <></>;
    if (this.caronaStore.loading) {
      content = (
        <div className="p-d-inline p-d-flex align-items-center">
          <ProgressSpinner />
        </div>
      );
    } else if (carona) {
      content = (
        <CaronaDetailPage
          caronaStore={this.caronaStore}
          idTermo={carona.termoReferencia?.id}
          idLicitacaoOrigem={carona.licitacao?.id}
          countDownloadRequest
        />
      );
    } else {
      content = <div>Erro ao exibir detalhes da Carona.</div>;
    }

    return content;
  }
}

CaronaIndexDetailPage.propTypes = {
  id: PropTypes.number,
};

export default CaronaIndexDetailPage;
