import React from 'react';
import { observer } from 'mobx-react';
import CaronaIndexStore from '~/stores/carona/indexStore';
import FcButton from 'fc/components/FcButton';
import { PrimeIcons } from 'primereact/api';
import {
  checkUserGroup,
  generateFullURL,
  getValue,
  getValueByKey,
  getValueDate,
  getValueMoney,
  hasPermissionProxy,
} from 'fc/utils/utils';
import { DATE_FORMAT, DATE_PARSE_FORMAT } from 'fc/utils/date';
import UrlRouter from '~/constants/UrlRouter';
import GenericIndexPage from 'fc/pages/GenericIndexPage';
import AdvancedSearch from 'fc/components/AdvancedSearch';
import { getValueElipsis } from 'fc/utils/utils';
import AccessPermission from '~/constants/AccessPermission';
import PermissionProxy from 'fc/components/PermissionProxy';
import RequisicaoRemocao from '~/pages/requisicaoRemocao/requisicaoRemocaoModal';
import CardList from 'fc/components/CardList';
import { ConfirmDialog } from 'primereact/confirmdialog';
import AppStore from 'fc/stores/AppStore';
import AnulacaoRevogacaoModal from '../AnulacaoRevogacao/AnulacaoRevogacaoModal';
import DadosEstaticosService from '~/services/DadosEstaticosService';

@observer
class CaronaListagemPage extends GenericIndexPage {
  constructor(props) {
    super(props, AccessPermission.carona);
    this.store = new CaronaIndexStore();
    this.state = {
      idRemove: null,
      selectedRow: null,
      showEntidadeDialog: false,
      showRequisicaoRemocao: false,
      showAnularRevogar: false,
    };

    this._closeRequisicaoRemocaoModal = this._closeRequisicaoRemocaoModal.bind(this);
    this._closeAnularRevogarModal = this._closeAnularRevogarModal.bind(this);
    this._updateDatatable = this._updateDatatable.bind(this);
  }

  componentDidMount() {
    const { id } = this.props.match?.params || {};
    if (id) this.store.getById(id, (carona) => this.props.onDetail(carona));
  }

  _handleRemocaoRequisicaoModal(carona) {
    this.setState({ showRequisicaoRemocao: true, selectedRow: carona });
  }

  _handleAnularRevogarModal(dispensa) {
    this.setState({ showAnularRevogar: true, selectedRow: dispensa });
  }

  _closeRequisicaoRemocaoModal() {
    this.setState({ showRequisicaoRemocao: false, selectedRow: null });
  }

  _closeAnularRevogarModal() {
    this.setState({ showAnularRevogar: false, selectedRow: null });
  }

  _updateDatatable() {
    this.store.reloadTableData(() => this.forceUpdate());
  }

  _renderDialogRequisicaoRemocao() {
    return (
      <RequisicaoRemocao
        history={this.props.history}
        processo={this.state.selectedRow}
        visibleDialog={this.state.showRequisicaoRemocao}
        closeDialog={this._closeRequisicaoRemocaoModal}
        updateTable={this._updateDatatable}
        tipoProcesso="carona"
      />
    );
  }

  _renderDialogAnularRevogar() {
    return (
      <AnulacaoRevogacaoModal
        showAnularRevogar={this.state.showAnularRevogar}
        closeDialog={this._closeAnularRevogarModal}
        selectedRow={this.state.selectedRow}
        getWritePermission={this.getWritePermission}
        tipoProcessoAssociado="CARONA"
        updateTable={this._updateDatatable}
      />
    );
  }

  getCardEllipsisOptions(cardData) {
    const isAuditor = checkUserGroup('Auditor');
    const items = [];

    if (
      (cardData.idRequisicaoModificacao || cardData.anulacaoRevogacao?.idRequisicaoModificacao) &&
      hasPermissionProxy(AccessPermission.requisicaoModificacao.writePermission)
    ) {
      const itemToAdd = {
        label: 'Requisição de modificação pendente',
        icon: 'pi pi-exclamation-triangle',
      };

      const idRequisicaoCarona =
        cardData.idRequisicaoModificacao ?? cardData.anulacaoRevogacao?.idRequisicaoModificacao;

      if (isAuditor) {
        itemToAdd.url = generateFullURL(
          UrlRouter.auditoria.requisicaoModificacao.indexDetail.replace(':id', idRequisicaoCarona)
        );
      } else {
        itemToAdd.url = generateFullURL(
          UrlRouter.administracao.requisicaoModificacao.indexDetail.replace(':id', idRequisicaoCarona)
        );
      }

      items.push(itemToAdd);
    } else {
      if (hasPermissionProxy(this.getReadPermission())) {
        items.push({
          label: 'Detalhes',
          icon: 'pi pi-eye',
          command: () => this.props.onDetail(cardData),
        });
      }

      if (hasPermissionProxy(this.getWritePermission())) {
        items.push({
          label: this.store.isRequisicaoModificacao(cardData.dataCadastro)
            ? 'Criar Requisição de Modificação'
            : 'Editar',
          icon: 'pi pi-pencil',
          disabled: this._disabledEditarCarona(cardData),
          url: this.store.isRequisicaoModificacao(cardData.dataCadastro)
            ? generateFullURL(
                UrlRouter.administracao.requisicaoModificacao.carona.requisitar.replace(':id', cardData.id)
              )
            : generateFullURL(UrlRouter.cadastrosConsulta.carona.editar.replace(':id', cardData.id)),
        });
      }

      if (hasPermissionProxy(this.getWritePermission())) {
        items.push({
          label: 'Remover',
          icon: 'pi pi-trash',
          command: () => this._handleRemocaoRequisicaoModal(cardData),
        });
      }

      if (hasPermissionProxy(this.getWritePermission())) {
        items.push({
          label: 'Anular/Revogar',
          icon: 'pi pi-times',
          command: () => this._handleAnularRevogarModal(cardData),
        });
      }
    }

    return items;
  }

  renderCardTitle(cardData) {
    const isJurisdicionado = checkUserGroup('Jurisdicionado');
    const orgaoGerenciadorAta =
      cardData.gerenciadorAta ??
      cardData.entidadeOrigemExterna?.nomeEntidadeExterna ??
      cardData.entidadeOrigemExterna?.nome ??
      cardData.entidadeOrigem?.nome;
    return isJurisdicionado
      ? getValue(orgaoGerenciadorAta)?.toUpperCase()
      : `${cardData?.entidade?.nome}${orgaoGerenciadorAta ? ' - ' + orgaoGerenciadorAta : ''}`.toUpperCase();
  }

  _disabledEditarCarona(carona) {
    let disabled = false;
    if (carona?.licitacao?.naturezaOcorrencia == 'SUSPENDER') {
      disabled = true;
    } else if (carona?.licitacao?.naturezaOcorrencia == 'REABRIR' && carona?.licitacao?.fase !== 'FINALIZACAO') {
      disabled = true;
    } else if (
      carona?.licitacao?.statusOcorrenciaAtual == 'ANULADA' ||
      carona?.licitacao?.statusOcorrenciaAtual == 'REVOGADA'
    ) {
      disabled = true;
    }
    return disabled;
  }

  openDialogEntidadeVisibility() {
    this.setState({
      showEntidadeDialog: !this.state.showEntidadeDialog,
    });
  }

  closeDialogEntidadeVisibility() {
    this.setState({
      showEntidadeDialog: !this.state.showEntidadeDialog,
    });
  }

  _renderDialogConfirmacaoEntidade() {
    const message = `O novo registro criado será associado à entidade selecionada: ${
      AppStore.getContextEntity().nome
    }. Deseja continuar?`;
    return (
      <ConfirmDialog
        blockScroll
        visible={this.state.showEntidadeDialog}
        message={message}
        header="Confirmação"
        icon="pi pi-user preview-item"
        acceptClassName="p-button-info"
        draggable={false}
        onHide={() => this.closeDialogEntidadeVisibility()}
        accept={() => {
          this.pushUrlToHistory(UrlRouter.cadastrosConsulta.carona.novo);
        }}
      />
    );
  }

  render() {
    const fields = [
      {
        field: 'entidadeOrigem',
        label: 'Entidade de Origem',
        value: 'title',
        sortable: true,
        body: this.renderCardTitle,
      },
      {
        field: 'numeroProcessoGerenciadorAta',
        label: 'Número da Adesão/Carona',
        value: 'subtitle',
        sortable: true,
        body: (cardData) => {
          const numeroProcesso = cardData?.numeroProcessoGerenciadorAta;
          const ano = cardData?.anoCarona;
          return numeroProcesso && numeroProcesso.includes('/')
            ? `Carona: ${numeroProcesso}`
            : `Carona: ${numeroProcesso}/${ano}`;
        },
      },
      {
        value: 'iconLabel',
        body: (rowData) => {
          if (rowData?.licitacao?.naturezaOcorrencia == 'SUSPENDER') {
            return [
              {
                value: 'Suspensa',
                toolTip: 'Licitação Suspensa',
                icon: 'pi pi-exclamation-triangle',
                color: '#ef4444',
              },
            ];
          } else if (
            rowData?.licitacao?.naturezaOcorrencia == 'REABRIR' &&
            rowData?.licitacao?.fase !== 'FINALIZACAO'
          ) {
            return [
              {
                value: 'Bloqueada',
                toolTip: 'Licitação Reaberta',
                icon: 'pi pi-exclamation-triangle',
                color: '#ef4444',
              },
            ];
          } else if (
            rowData?.licitacao?.statusOcorrenciaAtual == 'ANULADA' ||
            rowData?.licitacao?.statusOcorrenciaAtual == 'REVOGADA'
          ) {
            return [
              {
                value: 'Bloqueada',
                toolTip: 'Licitação Anulada/Revogada',
                icon: 'pi pi-exclamation-triangle',
                color: '#ef4444',
              },
            ];
          } else if (rowData?.licitacao?.naturezaOcorrencia == 'REABRIR' && !rowData.detentores?.length) {
            return [
              {
                value: 'Inválida',
                toolTip:
                  'A adesão/carrona está inválida, pois a licitação foi reaberta. Novos detentores precisam ser cadastrados.',
                icon: 'pi pi-exclamation-triangle',
                color: '#f59e0b',
              },
            ];
          } else {
            return [];
          }
        },
      },
      {
        field: 'dataAdesao',
        label: 'Data de Adesão',
        value: 'iconLabel',
        color: '#38AAAD',
        icon: 'pi pi-calendar',
        body: ({ dataAdesao }) => getValueDate(dataAdesao, DATE_FORMAT, DATE_PARSE_FORMAT),
      },
      {
        field: 'valor',
        label: 'Valor',
        sortable: true,
        value: 'iconLabel',
        color: '#2F83DC',
        icon: 'pi pi-money-bill',
        body: (rowData) => getValueMoney(rowData.valor, rowData?.termoReferencia?.tresCasasDecimais ? 3 : 2),
      },
      {
        value: 'iconLabel',
        body: (cardData) => {
          if (cardData?.detentores) {
            const idsUnicos = new Set();
            const licitantes = cardData?.detentores
              .map((f) => f.licitante)
              .filter((licitante) => {
                if (idsUnicos.has(licitante.id)) {
                  return false;
                } else {
                  idsUnicos.add(licitante.id);
                  return true;
                }
              });

            const detentoresLabels = licitantes.map((licitante) => ({
              value: licitante?.nome,
              toolTip: 'Fornecedor',
              icon: 'pi pi-user-plus',
              color: '#722ed1',
            }));

            return detentoresLabels;
          }
        },
      },
      {
        label: 'Entidade Externa',
        field: 'entidadeExterna',
        value: 'iconLabel',
        color: '#c63737',
        icon: 'pi pi-external-link',
        sortable: true,
        body: (cardData) => !cardData?.processoEntidadeOrigem && 'Entidade Externa',
      },
      {
        field: 'objeto',
        label: 'Objeto',
        value: 'mainContent',
        sortable: true,
        body: (rowData) => {
          return getValueElipsis(rowData.objeto, 80);
        },
      },
      {
        field: 'anulacaoRevogacao',
        label: 'Anulado/Revogado',
        value: 'iconLabel',
        showIfExists: true,
        color: '#b83b20',
        icon: 'pi pi-money-bill',
        body: ({ anulacaoRevogacao }) =>
          getValueByKey(anulacaoRevogacao?.tipoOcorrencia, DadosEstaticosService.getValueAnuladoRevogado()),
        sortable: true,
      },
      {
        label: 'Ações',
        value: 'ellipsisItems',
        body: (cardData) => this.getCardEllipsisOptions(cardData),
      },
    ];

    const exportFields = [
      {
        field: 'numeroProcessoGerenciadorAta',
        header: 'Número da Adesão/Carona',
      },
      {
        field: 'dataAdesao',
        header: 'Data de Adesão',
        body: ({ dataAdesao }) => getValueDate(dataAdesao, DATE_FORMAT, DATE_PARSE_FORMAT),
      },
      {
        field: 'entidadeOrigem',
        header: 'Entidade de Origem',
        body: this.renderCardTitle,
      },
      {
        field: 'objeto',
        header: 'Objeto',
        body: (rowData) => {
          return getValueElipsis(rowData.objeto, 80);
        },
      },
      {
        field: 'valor',
        header: 'Valor',
        body: (rowData) => getValueMoney(rowData.valor, rowData?.termoReferencia?.tresCasasDecimais ? 3 : 2),
      },
    ];

    const header = (
      <div className="table-header flex justify-content-start">
        <PermissionProxy resourcePermissions={this.getWritePermission()}>
          <FcButton
            className="p-button"
            label="Novo"
            style={{ marginBottom: '5px', marginRight: '5px' }}
            icon={PrimeIcons.PLUS}
            onClick={() => this.openDialogEntidadeVisibility()}
          />
        </PermissionProxy>

        {this.renderTableDataExport(exportFields, 'carona', true)}
      </div>
    );

    return (
      <>
        <AdvancedSearch
          searchParams={this.store.getAdvancedSearchParams()}
          store={this.store}
          searchFields={['numeroProcessoGerenciadorAta', 'objeto']}
          filterSuggest={this.store.getFilterSuggest()}
          useOr
        />
        <CardList
          fields={fields}
          store={this.store}
          header={header}
          onTitleClick={(cardData) => {
            if (hasPermissionProxy(this.getReadPermission())) {
              return {
                command: () => this.props.onDetail(cardData),
              };
            }
          }}
          labelsLimit={100}
        />
        {this.store.isConfirmDialogVisible && this.confirmRemove(this.state.idRemove)}
        {this._renderDialogRequisicaoRemocao()}
        {this.state.showEntidadeDialog && this._renderDialogConfirmacaoEntidade()}
        {this.state.showAnularRevogar && this._renderDialogAnularRevogar()}
      </>
    );
  }
}

export default CaronaListagemPage;
