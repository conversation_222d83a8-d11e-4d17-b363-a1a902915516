import React from 'react';
import { observer } from 'mobx-react';
import PropTypes from 'prop-types';
import SelectDialog from 'fc/components/SelectDialog';
import FormField from 'fc/components/FormField';
import FcButton from 'fc/components/FcButton';
import SearchTypes from 'fc/components/AdvancedSearch/SearchTypes';
import AsyncDropDownStore from 'fc/stores/AsyncDropdownStore';
import Licitante from '~/domains/Licitante';
import LicitanteService from '~/services/LicitanteService';
import { Fieldset } from 'primereact/fieldset';
import { Divider } from 'primereact/divider';
import LicitanteFormPage from '~/pages/licitante/form';
import LicitanteIndexStore from '~/stores/licitante/indexStore';
import { getValue } from 'fc/utils/utils';
import { confirmDialog } from 'primereact/confirmdialog';

@observer
class FormFornecedores extends React.Component {
  constructor(props) {
    super(props);
    this.store = props.store;

    this.state = {
      detentorRemoveSelected: undefined,
      showConfirmDialog: false,
    };
  }

  getFilterSuggest() {
    const carona = this.store.object;
    const { processoEntidadeOrigem, licitacao } = carona;

    const filterSuggest = [];
    if (processoEntidadeOrigem && licitacao) {
      const idsUnicos = new Set();
      const licitantes = licitacao?.vencedores
        ?.map((f) => f.licitante)
        .filter((licitante) => {
          if (idsUnicos.has(licitante.id)) {
            return false;
          } else {
            idsUnicos.add(licitante.id);
            return true;
          }
        });
      licitantes?.forEach((l) =>
        filterSuggest.push({
          id: '',
          field: 'licitante',
          operator: 'EQUAL_TO',
          value: l,
          formatted: '',
          fixed: true,
          completeParam: {
            field: 'licitante',
            label: 'Licitante',
            type: SearchTypes.ASYNC_QUERY,
            store: new AsyncDropDownStore(Licitante, LicitanteService, 'nome', 'id'),
          },
        })
      );
    }
    return filterSuggest;
  }

  confirmRemoveDetentor() {
    confirmDialog({
      message: 'Fornecedor já associado, deseja realmente remover?',
      header: 'Confirmação',
      icon: 'pi pi-exclamation-triangle',
      accept: () => {
        this.store.removeDetentor(this.state.detentorRemoveSelected);
        this.setState({ showConfirmDialog: false, detentorRemoveSelected: undefined });
      },
      reject: () => {
        this.setState({ showConfirmDialog: false, detentorRemoveSelected: undefined });
      },
    });
  }

  render() {
    const { submitted } = this.props;

    const licitanteForm = (props) => {
      return <LicitanteFormPage history={history} action="new" closeMethod={props.closeMethod} index="carona" />;
    };

    const columnsLicitantes = [
      {
        field: 'nome',
        header: 'Nome',
        sortable: true,
      },
      {
        field: 'endereco',
        header: 'Endereço',
        sortable: true,
        body: ({ licitante }) => getValue(licitante?.endereco),
      },
      {
        field: 'cpfCnpj',
        header: 'CPF/CNPJ',
        sortable: true,
        style: { width: '180px' },
      },
    ];

    const columnsFornecedores = [
      {
        field: 'licitante.nome',
        header: 'Nome',
        sortable: true,
      },
      {
        field: 'licitante.endereco',
        header: 'Endereço',
        sortable: true,
        body: ({ licitante }) => getValue(licitante?.endereco),
      },
      {
        field: 'licitante.cpfCnpj',
        header: 'CPF/CNPJ',
        sortable: true,
        style: { width: '180px' },
      },
    ];

    if (this.store.object) {
      return (
        <>
          <div className="p-col-12">
            {this.store.enableReqMod && (
              <Fieldset legend="AVISO">
                <h6 style={{ color: '#dd0303' }}>
                  A EDIÇÃO DESTA CARONA ESTARÁ SUJEITA A RATIFICAÇÃO PELA EQUIPE DE AUDITORIA DO TCE
                </h6>
              </Fieldset>
            )}
          </div>

          <div className="p-fluid p-formgrid p-grid">
            <Divider />
            <FormField columns={12} attribute="detentores" label="Fornecedor/Prestador da Ata" submitted={submitted}>
              {this.store.object.entidadeOrigemExterna ? (
                <SelectDialog
                  label="nome"
                  indexStore={new LicitanteIndexStore()}
                  headerDialog="Fornecedor/Prestador"
                  nullMessage="Fornecedor/Prestador sem nome"
                  dialogColumns={columnsLicitantes}
                  searchFields={['nome']}
                  labelButtonCreate="Adicionar"
                  isMultiple
                  canCreate
                  formPage={licitanteForm}
                  selectedElements={this.store.object.licitantes}
                  selectedKeydElementsList={this.store.licitanteListKeyed}
                  onSelectMultiple={(e) => this.store.updateAttribute('licitantes', e)}
                  columnsSelectMultiple={[
                    {
                      field: 'nome',
                      header: 'Nome',
                      sortable: true,
                    },
                    {
                      field: 'endereco',
                      header: 'Endereço',
                      sortable: true,
                      body: ({ endereco }) => getValue(endereco),
                    },
                    {
                      field: 'cpfCnpj',
                      header: 'CPF/CNPJ',
                      sortable: true,
                      style: { width: '180px' },
                    },
                    {
                      style: { width: '110px', textAlign: 'center' },
                      body: (licitante) => (
                        <FcButton
                          type="button"
                          icon="pi pi-trash"
                          tooltip="Remover"
                          className="p-button-sm p-button-danger"
                          onClick={() => this.store.removeLicitante(licitante)}
                        />
                      ),
                    },
                  ]}
                  disabled={this.props.disabled}
                />
              ) : (
                <SelectDialog
                  label="nome"
                  indexStore={this.store.vencedoresIndexStore}
                  headerDialog="Fornecedor/Prestador"
                  nullMessage="Fornecedor/Prestador sem nome"
                  dialogColumns={columnsFornecedores}
                  searchFields={['licitante']}
                  labelButtonCreate="Adicionar"
                  isMultiple
                  canCreate
                  disabledCreate={true}
                  filterSuggest={this.getFilterSuggest()}
                  selectedElements={this.store.object.vencedores}
                  selectedKeydElementsList={this.store.object.vencedores}
                  onSelectMultiple={(e) => this.store.setLicitantes(e)}
                  columnsSelectMultiple={[
                    {
                      field: 'licitante.nome',
                      header: 'Nome',
                      sortable: true,
                    },
                    {
                      field: 'licitante.endereco',
                      header: 'Endereço',
                      sortable: true,
                      body: (detentor) => getValue(detentor?.licitante?.endereco),
                    },
                    {
                      field: 'licitante.cpfCnpj',
                      header: 'CPF/CNPJ',
                      sortable: true,
                      style: { width: '180px' },
                    },
                    {
                      style: { width: '110px', textAlign: 'center' },
                      body: (detentor) => (
                        <FcButton
                          type="button"
                          icon="pi pi-trash"
                          tooltip="Remover"
                          className="p-button-sm p-button-danger"
                          onClick={() => {
                            if (this.store.checkDetentorIsVencedor(detentor)) {
                              this.setState({ detentorRemoveSelected: detentor, showConfirmDialog: true }, () =>
                                this.confirmRemoveDetentor()
                              );
                            } else {
                              this.store.removeDetentor(detentor);
                            }
                          }}
                        />
                      ),
                    },
                  ]}
                  disabled={this.props.disabled}
                />
              )}
            </FormField>
          </div>
        </>
      );
    } else {
      return (
        <div className="card page">
          <i className="pi pi-spin pi-spinner" style={{ fontSize: '2em' }}></i>
        </div>
      );
    }
  }
}

FormFornecedores.propTypes = {
  store: PropTypes.any,
  submitted: PropTypes.any,
  disabled: PropTypes.bool,
};

export default FormFornecedores;
