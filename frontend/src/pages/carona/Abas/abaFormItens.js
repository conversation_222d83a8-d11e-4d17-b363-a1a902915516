import React from 'react';
import { observer } from 'mobx-react';
import PropTypes from 'prop-types';
import { Fieldset } from 'primereact/fieldset';
import { Divider } from 'primereact/divider';
import ItensCarona from './components/caronaVencedor';
import Vencedores from '~/pages/vencedores';

@observer
class FormItens extends React.Component {
  constructor(props) {
    super(props);
    this.store = props.store;
  }

  componentDidMount() {
    this.store.initizalizeDetentorStore();
  }

  getComponentItens() {
    if (this.store.object.processoEntidadeOrigem && this.store.object?.licitacao?.termoReferencia) {
      return (
        <ItensCarona
          store={this.store.caronaVencedorStore}
          showDesconto={this.store.object?.licitacao?.tiposLicitacao
            ?.map((t) => t.nome?.toUpperCase())
            ?.includes('MAIOR DESCONTO')}
          showEspecificacao={this.store.object?.licitacao?.naturezasDoObjeto?.includes('COMPRAS')}
        />
      );
    } else {
      return (
        <Vencedores
          store={this.store.detentoresStore}
          labelLicitante="Fornecedor Prestador"
          processoMigrado={this.store.isProcessoMigrado()}
          entidadeAntiga={this.store.isProcessoMigrado()}
          hideDeserto
          hideFracasso
        />
      );
    }
  }

  render() {
    if (this.store.object) {
      return (
        <>
          <div className="p-col-12">
            {this.store.enableReqMod && (
              <Fieldset legend="AVISO">
                <h6 style={{ color: '#dd0303' }}>
                  A EDIÇÃO DESTA CARONA ESTARÁ SUJEITA A RATIFICAÇÃO PELA EQUIPE DE AUDITORIA DO TCE
                </h6>
              </Fieldset>
            )}
          </div>
          <Divider />
          <div className="p-col-12">{this.getComponentItens()}</div>
        </>
      );
    } else {
      return (
        <div className="card page">
          <i className="pi pi-spin pi-spinner" style={{ fontSize: '2em' }}></i>
        </div>
      );
    }
  }
}

FormItens.propTypes = {
  store: PropTypes.any,
  action: PropTypes.string,
};

export default FormItens;
