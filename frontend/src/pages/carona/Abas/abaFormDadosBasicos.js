import React from 'react';
import { observer } from 'mobx-react';
import PropTypes from 'prop-types';
import { InputText } from 'primereact/inputtext';
import Form<PERSON>ield from 'fc/components/FormField';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import SelectDialog from 'fc/components/SelectDialog';
import moment from 'moment';
import { DATE_FORMAT_ONLY_YEAR, DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS } from 'fc/utils/date';
import { getValueByKey, getValueDate, getValueMoney, getValue, isValueValid } from 'fc/utils/utils';
import CaronaEntidadeExternaFormPage from './formEntidadeExterna';
import FcInputTextarea from 'fc/components/FcInputTextarea';
import InputMonetary from 'fc/components/InputMonetary';
import EntidadeInternaExternaIndexStore from '~/stores/entidadeInternaExterna/indexStore';
import { Fieldset } from 'primereact/fieldset';
import MultipleFileUploader from 'fc/components/MultipleFileUploader';
import PermissionProxy from 'fc/components/PermissionProxy';
import { AutoComplete } from 'primereact/autocomplete';
import { Divider } from 'primereact/divider';
import TermoSelectDetails from '~/pages/licitacao/tabs/termoDetails';
import FcButton from 'fc/components/FcButton';
import FcMultiSelect from 'fc/components/FcMultiSelect';
import { SelectButton } from 'primereact/selectbutton';
import { Dialog } from 'primereact/dialog';
import { ConfirmDialog } from 'primereact/confirmdialog';
import { Dropdown } from 'primereact/dropdown';
import FcCalendar from 'fc/components/FcCalendar';

@observer
class FormDadosBasicos extends React.Component {
  constructor(props) {
    super(props);
    this.store = props.store;
    this.state = {
      entidade: null,
      arquivosLicitacaoModalVisible: false,
      arquivosCaronaDialogVisible: false,
      currentCallback: undefined,
    };
  }

  componentDidMount() {
    this.store.getPessoasResponsaveis();
    this.store.carregarFontesRecursos(() => this.forceUpdate());
  }

  renderDialogArquivosCarona() {
    return (
      <ConfirmDialog
        visible={this.state.arquivosCaronaDialogVisible}
        message="Ao trocar de Origem do Processo, de Legislação ou de Licitação, os arquivos selecionados serão removidos do sistema. Deseja continuar e aplicar a mudança realizada, removendo os arquivos selecionados?"
        header="Atualização"
        onHide={() => {
          this.setState({ arquivosCaronaDialogVisible: false });
        }}
        accept={() => {
          this.store.setArquivoCaronaList([]);
          this.store.fileStore.removeAllFiles();
          this.state.currentCallback();
          this.setState({ currentCallback: undefined });
        }}
      />
    );
  }

  handleLoadTiposArquivos(callback) {
    this.setState({ arquivosCaronaDialogVisible: true, currentCallback: callback });
  }

  renderDialogArquivosLicitacao() {
    return (
      <Dialog
        header="Arquivos da Licitação de Origem"
        style={{ width: '80vw', minHeight: '26vw' }}
        visible={this.state.arquivosLicitacaoModalVisible}
        onHide={() => this.setState({ arquivosLicitacaoModalVisible: false })}
      >
        {this.renderArquivosLicitacao()}
      </Dialog>
    );
  }

  renderArquivosLicitacao() {
    this.store.carregarArquivosLicitacao();

    return (
      <MultipleFileUploader
        fileTypes={DadosEstaticosService.getTipoArquivoLicitacao()}
        downloadOnly
        store={this.store.fileStoreLicitacao}
      />
    );
  }

  handleUpdateEntidadeOrigem(entidade) {
    this.store.updateEntidadeOrigem(entidade);
  }

  handleChangeUpdateProcessoEntidadeOrigem(e) {
    const { updateAttribute } = this.store;

    updateAttribute('processoEntidadeOrigem', e);
    updateAttribute('licitacao', undefined);
    updateAttribute('naturezasDoObjeto', []);
    updateAttribute('objeto', '');
    updateAttribute('entidadeOrigem', undefined);
    updateAttribute('entidadeOrigemExterna', undefined);
    if (!e?.target?.value) {
      updateAttribute('lei', 'LEI_N_14133');
    } else {
      e?.target?.value && updateAttribute('lei', undefined);
      e?.target?.value && updateAttribute('legislacaoOutros', undefined);
    }
    this.store.resetVencedores();
    this.store.loadTipos(() => this.forceUpdate());
  }

  handleChangeUpdateLicitacao(e) {
    const { updateAttribute } = this.store;

    updateAttribute('licitacao', e);
    this.store.updateAttribute('lei', e.value.lei);
    this.store.updateAttribute('legislacaoOutros', e.value.legislacaoOutros);
    this.store.resetVencedores();
    this.store.setLicitacaoFields(e.value);
    this.store.loadTipos(() => this.forceUpdate());
  }

  handleChangeUpdateLei(e) {
    this.store.updateAttribute('lei', e);
    this.store.updateAttribute('legislacaoOutros', '');
    e.target.value == 'LEI_N_8666' && this.store.updateAttribute('termoReferencia', undefined);
    this.store.loadTipos(() => this.forceUpdate());
  }

  render() {
    const { submitted } = this.props;
    const { getRule } = this.store;
    const { updateAttribute } = this.store;

    const columnsTermo = [
      {
        field: 'identificadorProcesso',
        header: 'Identificador',
        body: ({ identificadorProcesso, lotes }) => {
          return lotes?.length ? (
            <div>{getValue(identificadorProcesso)}</div>
          ) : (
            <div className="actions p-d-flex align-items-center">
              <FcButton
                icon="pi pi-exclamation-triangle"
                tooltip="Termo não permite associação, pois não possui itens/lotes cadastrados."
                className="p-button-sm p-button-danger p-button-text"
              />
              {getValue(identificadorProcesso)}
            </div>
          );
        },
        sortable: true,
      },
      {
        field: 'dataCadastro',
        header: 'Data de Cadastro',
        body: ({ dataCadastro }) => getValueDate(dataCadastro, DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS),
        sortable: true,
      },
    ];

    const columnsLicitacao = [
      {
        field: 'numero',
        header: 'Número da Licitação',
        sortable: true,
      },
      {
        field: 'lei',
        header: 'Legislação',
        body: ({ lei }) => getValueByKey(lei, DadosEstaticosService.getTipoLicitacaoLegislacao()),
        sortable: true,
      },
      {
        field: 'ano',
        header: 'Ano',
        body: ({ ano }) => getValueDate(ano, DATE_FORMAT_ONLY_YEAR),
        sortable: true,
      },
      {
        field: 'valorAdjudicado',
        header: 'Valor Adjudicado',
        body: ({ valorAdjudicado }) => getValueMoney(valorAdjudicado),
      },
    ];

    const columnsEntidadeOrigem = [
      {
        field: 'nome',
        header: 'Nome',
        sortable: true,
      },
      {
        field: 'esfera',
        header: 'Esfera',
        sortable: true,
        body: ({ esfera }) => `${getValue(esfera?.nome)}`,
      },
    ];

    const entidadeExternaForm = (props) => {
      return <CaronaEntidadeExternaFormPage action="new" closeMethod={props.closeMethod} />;
    };

    const isMigrado = this.store.object?.processoMigrado;
    const attributeNumProcesso = isMigrado ? 'numeroProcessoGerenciadorAta' : 'numeroProcessoAdministrativo';

    if (this.store.object) {
      const field = this.store.object.processoEntidadeOrigem ? 'entidadeOrigem' : 'entidadeOrigemExterna';
      const disabledNaturezasDoObjeto =
        (this.store.object.processoEntidadeOrigem && !this.store.object.licitacao) ||
        (this.store.object.licitacao && this.store.object.licitacao?.naturezasDoObjeto?.length <= 1);

      return (
        <>
          {this.store.enableReqMod && (
            <Fieldset legend="AVISO">
              <h6 style={{ color: '#dd0303' }}>
                A EDIÇÃO DESTA CARONA ESTARÁ SUJEITA A RATIFICAÇÃO PELA EQUIPE DE AUDITORIA DO TCE
              </h6>
            </Fieldset>
          )}

          <div className="p-fluid p-formgrid p-grid" style={{ alignItems: 'center' }}>
            <Divider />
            {this.store.enableReqMod && (
              <PermissionProxy resourcePermissions={'admin'} hideOnFail>
                <FormField
                  columns={4}
                  label="Entidade"
                  rule={getRule('entidade')}
                  submitted={submitted}
                  attribute="entidade"
                >
                  <AutoComplete
                    value={this.store.object.entidade}
                    suggestions={this.store.entidadesFiltradas}
                    completeMethod={(e) => this.store.getEntidadesFiltradas(e, () => this.forceUpdate())}
                    field="nome"
                    onChange={(e) => this.store.updateAttribute('entidade', e.value)}
                    aria-label="Entidade"
                    dropdownAriaLabel="Selecione a entidade"
                  />
                </FormField>
                <div id="blank-space" className="p-col-8" />
              </PermissionProxy>
            )}
            <FormField
              columns={12}
              attribute="processoEntidadeOrigem"
              label="Origem do Processo"
              rule={getRule('processoEntidadeOrigem')}
              submitted={submitted}
            >
              <SelectButton
                optionLabel="text"
                optionValue="value"
                style={{ maxWidth: '700px' }}
                value={this.store.object.processoEntidadeOrigem}
                options={DadosEstaticosService.getOrigemProcessoCarona()}
                onChange={(e) => {
                  if (isValueValid(e?.target?.value) && e?.target?.value !== this.store.object.processoEntidadeOrigem) {
                    if (this.store.arquivoCaronaList.length) {
                      const callback = () => {
                        this.handleChangeUpdateProcessoEntidadeOrigem(e);
                      };
                      this.handleLoadTiposArquivos(callback);
                    } else {
                      this.handleChangeUpdateProcessoEntidadeOrigem(e);
                    }
                  }
                }}
              />
            </FormField>
            {this.state.arquivosCaronaDialogVisible && this.renderDialogArquivosCarona()}
            <FormField
              columns={4}
              attribute={field}
              rule={getRule(field)}
              label="Órgão Gerenciador da Ata"
              submitted={submitted}
            >
              <SelectDialog
                value={this.store.getEntidadeOrigem()}
                label="nome"
                indexStore={new EntidadeInternaExternaIndexStore()}
                onChange={(e) => this.handleUpdateEntidadeOrigem(e.value)}
                headerDialog="Órgão Gerenciador da Ata"
                emptyMessage="Selecione o órgão gerenciador da ata"
                nullMessage="Órgão Gerenciador da Ata sem Nome"
                dialogColumns={columnsEntidadeOrigem}
                searchFields={['nome']}
                canCreate={this.store.object.origemProcesso === 'ORGAO_EXTERNO'}
                formPage={entidadeExternaForm}
                key={field}
                filterSuggest={this.store.getFilterSuggestOrgaoGerenciador()}
                disabled={this.store.object.processoMigrado}
                disabledComponent={this.store.object.processoMigrado}
              />
            </FormField>
            {this.store.object.processoEntidadeOrigem && (
              <FormField
                columns={4}
                attribute="licitacao"
                label="Nº da Licitação"
                rule={getRule('licitacao')}
                submitted={submitted}
              >
                <SelectDialog
                  value={this.store.object.licitacao}
                  label="numero"
                  indexStore={this.store.licitacaoSrpIndexStore}
                  onChange={(e) => {
                    if (this.store.arquivoCaronaList.length) {
                      const callback = () => {
                        this.handleChangeUpdateLicitacao(e);
                      };
                      this.handleLoadTiposArquivos(callback);
                    } else {
                      this.handleChangeUpdateLicitacao(e);
                    }
                  }}
                  headerDialog="Nº da Licitação"
                  emptyMessage="Selecione o nº da licitação"
                  nullMessage="Entidade sem licitação associada"
                  dialogColumns={columnsLicitacao}
                  searchFields={['numero', 'ano']}
                  filterSuggest={this.store.getFilterSuggestLicitacao()}
                  disabledComponent={this.store.enableReqMod || !this.store.object.entidadeOrigem}
                />
              </FormField>
            )}
            {this.state.arquivosCaronaDialogVisible && this.renderDialogArquivosCarona()}
            <FormField
              columns={4}
              attribute="lei"
              label="A Adesão/Carona será regida por qual legislação?"
              rule={getRule('lei')}
              submitted={submitted}
            >
              <SelectButton
                optionLabel="text"
                optionValue="value"
                value={this.store.object.lei}
                options={DadosEstaticosService.getTipoLicitacaoLei14133()}
                onChange={(e) => {
                  if (e.target.value && e.target.value !== this.store.object.lei) {
                    if (this.store.arquivoCaronaList.length) {
                      const callback = () => {
                        this.handleChangeUpdateLei(e);
                      };
                      this.handleLoadTiposArquivos(callback);
                    } else {
                      this.handleChangeUpdateLei(e);
                    }
                  }
                }}
                disabled={this.store.object.processoEntidadeOrigem}
              />
            </FormField>
            {this.state.arquivosCaronaDialogVisible && this.renderDialogArquivosCarona()}
            {this.store.object?.lei === 'OUTRA' && (
              <FormField
                rule={getRule('legislacaoOutros')}
                columns={4}
                attribute="legislacaoOutros"
                label="Outra Lei"
                infoTooltip="Informe sob qual lei o processo está sendo criado"
                submitted={submitted}
              >
                <InputText
                  value={this.store.object.legislacaoOutros}
                  placeholder="Informe a legislação"
                  rows={4}
                  onChange={(e) => this.store.updateAttribute('legislacaoOutros', e)}
                />
              </FormField>
            )}
            {this.store.object.entidadeOrigemExterna && (
              <FormField
                columns={4}
                attribute="numeroLicitacaoEntidadeExterna"
                label="Nº da Licitação"
                rule={getRule('numeroLicitacaoEntidadeExterna')}
                submitted={submitted}
              >
                <InputText
                  keyfilter={new RegExp('^[0-9]+$')}
                  onChange={(e) => updateAttribute('numeroLicitacaoEntidadeExterna', e)}
                  value={this.store.object.numeroLicitacaoEntidadeExterna}
                  placeholder="Informe o número"
                />
              </FormField>
            )}
            {!this.store.object.processoEntidadeOrigem && this.store.object.lei !== 'LEI_N_8666' && (
              <FormField
                columns={4}
                attribute="termoReferencia"
                label="Termo de Referência"
                rule={getRule('termoReferencia')}
                submitted={submitted}
              >
                <TermoSelectDetails
                  value={this.store.object.termoReferencia}
                  label="identificadorProcesso"
                  indexStore={this.store.termoIndexStore}
                  onChange={(e) => {
                    this.store.updateAttribute('termoReferencia', e);
                    this.store.updateAttribute('detentores', []);
                    this.store.caronaLicitanteStore.initializeNewTermo(this.store.object.termoReferencia);
                  }}
                  emptyMessage="Selecione o termo"
                  dialogColumns={columnsTermo}
                  searchFields={['identificadorProcesso']}
                  filterSuggest={this.store.getFilterSuggestTermoReferencia()}
                  disabledComponent={!this.store?.object?.entidade?.id || this.store.enableReqMod}
                  radioMode
                />
              </FormField>
            )}
            <FormField
              columns={4}
              attribute={attributeNumProcesso}
              label="Número do Processo Administrativo"
              infoTooltip="Deve ser informado um número de processo, que pode ser o número do processo do SEI (caso tenha), ou o número do Processo Administrativo que gerou a carona."
              rule={getRule(attributeNumProcesso)}
              submitted={submitted}
            >
              <InputText
                keyfilter={new RegExp('[0-9|.|[-]|[/]')}
                onChange={(e) => updateAttribute(attributeNumProcesso, e)}
                value={this.store.object[attributeNumProcesso]}
                placeholder="Informe o número"
              />
            </FormField>
            {!isMigrado && (
              <FormField
                columns={4}
                attribute="numeroProcessoGerenciadorAta"
                label="Número da Adesão/Carona"
                rule={getRule('numeroProcessoGerenciadorAta')}
                submitted={submitted}
              >
                <InputText
                  keyfilter={new RegExp('^[0-9]+$')}
                  onChange={(e) => updateAttribute('numeroProcessoGerenciadorAta', e)}
                  value={this.store.object.numeroProcessoGerenciadorAta}
                  placeholder="Informe o número"
                />
              </FormField>
            )}
            <FormField columns={4} attribute="anoCarona" label="Ano" rule={getRule('anoCarona')} submitted={submitted}>
              <Dropdown
                onChange={(e) => this.store.updateAttribute('anoCarona', e)}
                placeholder="Informe o ano"
                value={this.store.object?.anoCarona}
                id="anoCarona"
                optionLabel="text"
                optionValue="value"
                options={this.store.anos}
              />
            </FormField>
            <FormField
              columns={4}
              attribute="dataAdesao"
              label="Data da Adesão/Carona"
              rule={getRule('dataAdesao')}
              submitted={submitted}
            >
              <FcCalendar
                value={this.store.object.dataAdesao ? moment(this.store.object.dataAdesao)._d : null}
                onChange={(e) => {
                  updateAttribute('dataAdesao', e);
                  if (this.store.dataAdesaoIsValid()) {
                    this.store.dialog = true;
                  } else {
                    this.store.dialog = false;
                  }
                }}
                mask="99/99/9999"
                id="dataAdesao"
                showIcon
              />
            </FormField>
            <FormField
              columns={4}
              attribute="dataValidadeAta"
              label="Data de Validade da Ata"
              rule={getRule('dataValidadeAta')}
              submitted={submitted}
            >
              <FcCalendar
                value={this.store.object.dataValidadeAta ? moment(this.store.object.dataValidadeAta)._d : null}
                onChange={(e) => {
                  updateAttribute('dataValidadeAta', e);
                  if (this.store.dataAdesaoIsValid()) {
                    this.store.dialog = true;
                  } else {
                    this.store.dialog = false;
                  }
                }}
                mask="99/99/9999"
                id="dataValidadeAta"
                showIcon
              />
            </FormField>
            {isMigrado ? (
              <FormField
                columns={4}
                attribute="responsavelAdesao"
                label="Responsável pela Adesão/Carona"
                rule={getRule('responsavelAdesao')}
                submitted={submitted}
              >
                <InputText
                  keyfilter={new RegExp('[^0-9]')}
                  onChange={(e) => updateAttribute('responsavelAdesao', e)}
                  placeholder="Informe o(a) responsável pela adesão"
                  value={this.store.object.responsavelAdesao}
                />
              </FormField>
            ) : (
              <FormField
                columns={4}
                attribute="responsavelAdesaoCarona"
                label="Responsável pela Adesão/Carona"
                rule={getRule('idResponsavelAdesaoCarona')}
                submitted={submitted}
              >
                <Dropdown
                  id="responsavelAdesaoCarona"
                  optionLabel="nome"
                  optionValue="id"
                  value={this.store.object?.idResponsavelAdesaoCarona}
                  options={this.store.responsaveisCarona}
                  onChange={(e) => {
                    this.store.updateResponsavelCaronaAttribute('idResponsavelAdesaoCarona', e.value);
                  }}
                  placeholder="Selecione o(a) responsável pela adesão"
                  emptyMessage="Não há responsáveis disponíveis"
                />
              </FormField>
            )}
            {isMigrado && (
              <FormField columns={4} attribute="valor" label="Valor (R$)" rule={getRule('valor')} submitted={submitted}>
                <InputMonetary
                  onChange={(e) => updateAttribute('valor', e)}
                  placeholder="Informe o valor"
                  value={this.store.object.valor}
                />
              </FormField>
            )}
            <FormField
              columns={8}
              attribute="fontesDeRecurso"
              label="Fontes de Recurso"
              rule={getRule('fontesDeRecurso')}
              submitted={submitted}
            >
              <FcMultiSelect
                placeholder="Selecione as fontes de recurso"
                value={this.store.object.fontesDeRecurso}
                onChange={(e) => this.store.updateAttribute('fontesDeRecurso', e)}
                options={this.store.fontesRecursos}
                showOverlay
                optionLabel="nome"
                filterBy="nome"
                filter
                selectedItemsLabel="{} itens selecionados"
                showClear
              />
            </FormField>
            <FormField
              columns={6}
              attribute="naturezasDoObjeto"
              label="Naturezas do Objeto"
              rule={getRule('naturezasDoObjeto')}
              submitted={submitted}
            >
              <FcMultiSelect
                inOrder
                onChange={(e) => {
                  updateAttribute('naturezasDoObjeto', e);
                  this.store.loadTipos();
                }}
                placeholder="Naturezas do objeto"
                value={this.store.object.naturezasDoObjeto}
                id="naturezasDoObjeto"
                optionLabel="text"
                optionValue="value"
                options={DadosEstaticosService.getNaturezaObjetoLicitacao()}
                disabled={disabledNaturezasDoObjeto}
              />
            </FormField>
            <FormField
              columns={6}
              attribute="fundamentacaoLegal"
              label="Fundamentação Legal"
              rule={getRule('fundamentacaoLegal')}
              submitted={submitted}
            >
              <InputText
                rows={6}
                cols={30}
                autoResize
                onChange={(e) => updateAttribute('fundamentacaoLegal', e)}
                placeholder="Infome a Fundamentação Legal"
                value={this.store.object.fundamentacaoLegal}
              />
            </FormField>
            {this.store.object?.lei === 'OUTRA' ? (
              <div id="blank-space" className="p-col-4" />
            ) : (
              <div id="blank-space" className="p-col-8" />
            )}
            <FormField columns={6} attribute="objeto" label="Objeto" rule={getRule('objeto')} submitted={submitted}>
              <FcInputTextarea
                rows={6}
                cols={30}
                autoResize
                onChange={(e) => updateAttribute('objeto', e)}
                placeholder="Infome o objeto do processo"
                disabled={!!this.store.object.licitacao}
                value={this.store.object.objeto}
              />
            </FormField>
            <FormField
              columns={6}
              attribute="observacoes"
              label="Observações"
              rule={getRule('observacoes')}
              submitted={submitted}
            >
              <FcInputTextarea
                rows={6}
                cols={30}
                autoResize
                onChange={(e) => updateAttribute('observacoes', e)}
                placeholder="Informe as observações do processo"
                value={this.store.object.observacoes}
              />
            </FormField>
            {this.store.object?.licitacao?.id && (
              <FormField columns={12}>
                <div style={{ justifyContent: 'left', maxWidth: 'fit-content' }}>
                  <FcButton
                    label="Visualizar Arquivos da Licitação de Origem"
                    type="button"
                    icon="pi pi-external-link"
                    className="p-button-secondary"
                    onClick={() => this.setState({ arquivosLicitacaoModalVisible: true })}
                  />
                </div>
              </FormField>
            )}
            <FormField
              columns={12}
              attribute="arquivos"
              label="Arquivos"
              submitted={submitted}
              rule={getRule('arquivos')}
            >
              <MultipleFileUploader
                store={this.store.fileStore}
                onChangeFiles={(files) => this.store.setArquivosCaronaList(files)}
                filterTypes={{ excluded: ['RESCISAO'] }}
                fileTypes={DadosEstaticosService.getTipoArquivoCarona()}
                disabledUpload={!this.store.fileStore.tipoArquivoEnum?.length}
                accept=".pdf, .xls, .xlsx"
              />
            </FormField>
          </div>
          {this.state.isConfirmDialogEntidadeInternaVisible && this.renderConfirmDialogEntidadeInterna()}
          {this.state.arquivosLicitacaoModalVisible && this.renderDialogArquivosLicitacao()}
        </>
      );
    } else {
      return (
        <div className="card page">
          <i className="pi pi-spin pi-spinner" style={{ fontSize: '2em' }}></i>
        </div>
      );
    }
  }
}

FormDadosBasicos.propTypes = {
  store: PropTypes.any,
  submitted: PropTypes.any,
};

export default FormDadosBasicos;
