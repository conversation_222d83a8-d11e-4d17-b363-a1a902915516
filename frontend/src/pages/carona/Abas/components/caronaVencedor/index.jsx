import React, { useState } from 'react';

import './style.scss';
import { observer } from 'mobx-react';
import { PickList } from 'primereact/picklist';
import { Dropdown } from 'primereact/dropdown';
import { Tag } from 'primereact/tag';
import {
  getLightenColor,
  getNumberFractionDigits,
  getNumberUnitThousands,
  getValue,
  getValueMoney,
  isValueValid,
} from 'fc/utils/utils';
import Panel from './panel';
import { DataTable } from 'primereact/datatable';
import FcButton from 'fc/components/FcButton';
import InputMonetary from 'fc/components/InputMonetary';
import { Column } from 'react-virtualized';
import classNames from 'classnames';
import Tooltip from 'fc/components/Tooltip';
import { Dialog } from 'primereact/dialog';
import { InputTextarea } from 'primereact/inputtextarea';
import Resumo from './resumo';

const ItensCarona = observer((props) => {
  const store = props.store;
  const [editing, setEditing] = useState({});
  const [itemError, setItemError] = useState({});
  const [collapsed, setCollapsed] = useState({});
  const [obsItem, setObsItem] = useState('');
  const [itemSelected, setItemSelected] = useState({});
  const [dialogVisible, setDialogVisible] = useState(false);

  const itemTemplate = (item) => {
    const vencedorLicitacao = store.vencedores.find(
      (v) => v.itemLote.id == item.itemLote.id && v.licitante.id === item.licitante.id
    );

    let quantidadeDisponivel = item.quantidade;
    let quantidadeMaxima = store.getQuantidadeFifty(vencedorLicitacao.quantidade, item.itemLote.fracionario);

    return (
      <div className="flex flex-wrap p-2 align-items-center gap-3">
        <div className="flex-1 flex flex-column gap-2">
          <span className="font-bold">
            {item.itemLote.numero ? item.itemLote.numero + ' - ' : ''}
            {getValue(item.itemLote.materialDetalhamento?.pdm?.nome)}
          </span>
          <div className="flex gap-2">
            <Tag
              value={item.lote.nome}
              icon="pi pi-box"
              style={{
                backgroundColor: '#fbf7ff',
                color: '#8f48d2',
                border: '1px solid #8f48d2',
              }}
            />
            <Tag
              value={`Quantidade: ${getNumberUnitThousands(quantidadeDisponivel)}/${getNumberUnitThousands(
                quantidadeMaxima
              )}`}
              className="p-d-flex"
              style={{
                backgroundColor: getLightenColor('#2F83DC', 0.7),
                color: '#2F83DC',
                border: `1px solid #2F83DC`,
              }}
            />
          </div>
        </div>
      </div>
    );
  };

  const getOptionsLotesByVencedor = () => {
    const options = [];
    if (store.vencedor) {
      store.vencedor.itens?.forEach((i) => {
        if (!options.some((l) => l.id == i.lote.id)) {
          options.push({ ...i.lote });
        }
      });
    }
    return options;
  };

  const getSourceOptions = () => {
    if (store.vencedor) {
      const itens = [];
      store.vencedor.itens.forEach((i) => {
        if (!store.detentores.some((it) => it.itemLote.id == i.itemLote.id)) {
          if (store.lote) {
            if (store.lote && i.lote.id == store.lote.id) {
              const item = { ...i, quantidade: store.getQuantidadeFifty(i.quantidade, i.itemLote.fracionario) };
              itens.push(item);
            }
          } else {
            const item = { ...i, quantidade: store.getQuantidadeFifty(i.quantidade, i.itemLote.fracionario) };
            itens.push(item);
          }
        }
      });
      return itens;
    }
  };

  const getTargetOptions = () => {
    let vencedoresAssociados = [];
    store.detentores?.forEach((vencedor) => {
      if (vencedor.licitante.id == store.vencedor.licitante.id) {
        vencedoresAssociados.push(vencedor);
      }
    });
    return vencedoresAssociados;
  };

  const toggleLote = (id) => {
    const newCollpsed = { ...collapsed };
    newCollpsed[id] = !collapsed[id];
    setCollapsed(newCollpsed);
  };

  const _renderHeader = (vencedor, className) => {
    return (
      <>
        <div className="flex-left pointer" onClick={() => toggleLote(vencedor.licitante.id)}>
          <div className="info-lote">
            <span className="feedback">
              <i
                className={classNames('p-m-2', {
                  'pi pi-angle-right': collapsed[vencedor.licitante?.id],
                  'pi pi-angle-down': !collapsed[vencedor.licitante?.id],
                })}
              />
            </span>
            <strong className="p-ml-2" onClick={() => toggleLote(vencedor.licitante.id)}>
              {vencedor.licitante.nome}
            </strong>
          </div>
        </div>
        <div className="flex-right p-mr-2">
          <div className="feedback">
            {className?.includes('panel-check') && (
              <span className={`circle check`}>
                <Tooltip value="Preenchido" sideOffset={10} delayDuration={100}>
                  <i className="pi pi-check" />
                </Tooltip>
              </span>
            )}
            {className?.includes('panel-warning') && (
              <span className="circle warning">
                <Tooltip value="Parcialmente Preenchido" sideOffset={10} delayDuration={100}>
                  <span className="default">!</span>
                </Tooltip>
              </span>
            )}
            {className?.includes('panel-error') && (
              <span className="circle error">
                <Tooltip value="Não Preenchido" sideOffset={10} delayDuration={100}>
                  <i className="pi pi-times" />
                </Tooltip>
              </span>
            )}
          </div>
        </div>
      </>
    );
  };

  const handleKey = (key, item, field) => {
    if (key === 'Enter') {
      setEditing((oldState) => {
        if (!oldState[getId(item)]) {
          oldState[getId(item)] = {};
        }
        oldState[getId(item)][field] = true;
        return { ...oldState };
      });
    }
  };

  const getId = (item) => `item-${item?.itemLote?.id}-licitante-${item?.licitante?.id}`;

  const _validateRequiredFields = (item) => {
    const requiredFields = ['quantidade', 'valorUnitario'];
    const itemId = getId(item);
    const result = { ...itemError };
    let hasError = false;

    requiredFields.forEach((f) => {
      if (f === 'quantidade' || f === 'valorUnitario') {
        if (!isValueValid(item[f]) || item[f] <= 0) {
          result[itemId] = { ...result[itemId], [f]: true };
          hasError = true;
        } else {
          result[itemId] = { ...result[itemId], [f]: false };
        }
      } else if (!isValueValid(item[f])) {
        result[itemId] = { ...result[itemId], [f]: true };
        hasError = true;
      } else {
        result[itemId] = { ...result[itemId], [f]: false };
      }
    });

    if (hasError) {
      const fieldsError = result[itemId];
      const newEditingState = { ...editing };

      Object.keys(fieldsError).forEach((k) => {
        if (fieldsError[k]) {
          if (!newEditingState[itemId]) {
            newEditingState[itemId] = {};
          }
          newEditingState[itemId][k] = true;
        }
      });

      setEditing(newEditingState);
    }

    setItemError(result);

    return hasError;
  };

  const enableEdit = (item, field) => {
    if (!item.preenchido) {
      setEditing((oldState) => {
        if (!oldState[getId(item)]) {
          oldState[getId(item)] = {};
        }
        oldState[getId(item)][field] = true;
        return { ...oldState };
      });
    }
  };

  const renderResumo = () => {
    const { decimalPlaces, showDesconto, showEspecificacao } = props;
    return (
      <div className="flex flex-column">
        {store.itensGroupByDetentor?.map((v) => (
          <Resumo
            vencedor={v}
            decimalPlaces={decimalPlaces}
            showDesconto={showDesconto}
            showEspecificacao={showEspecificacao}
            labelLicitante="Fornecedor/Prestador"
          />
        ))}
      </div>
    );
  };

  const _renderDataTable = (itens) => {
    const columns = [
      {
        header: 'Lote',
        style: { width: '15%' },
        body: (item) => item.lote.nome,
      },
      {
        header: 'Código',
        body: (item) => getValue(item.itemLote?.materialDetalhamento?.codigo),
      },
      {
        header: 'Item',
        style: { width: '30%' },
        body: (item) =>
          `${item.itemLote.numero ? item.itemLote.numero + ' - ' : ''} ${getValue(
            item.itemLote.materialDetalhamento?.pdm?.nome
          )}`,
      },
      {
        header: 'Descrição Complementar',
        style: { width: '20%' },
        body: (item) => `${getValue(item.descricaoComplementar)}`,
      },
      {
        header: 'Unidade de Medida',
        body: (item) => getValue(item.itemLote?.unidadeMedida?.textUnidadeMedida),
      },
      {
        header: 'Marca/Modelo',
        body: (item) => <span className="text-disabled">{getValue(item.marcaModelo)}</span>,
      },
      props.showEspecificacao && {
        header: 'Especificação',
        body: (item) => <span className="text-disabled">{getValue(item.especificacao)}</span>,
      },
      {
        header: (
          <span>
            Quantidade<span className="p-error"> *</span>
          </span>
        ),
        body: (item) =>
          !editing[getId(item)]?.quantidade && !item.preenchido ? (
            <div className="flex flex-column gap-1">
              <InputMonetary
                className={itemError[getId(item)]?.quantidade ? 'p-invalid p-error' : ''}
                value={item.quantidade}
                onChange={(e) => {
                  store.updateAttributeItem(item.itemLote.id, item.licitante.id, 'quantidade', e);
                }}
                decimalPlaces={item.itemLote.fracionario ? 2 : 0}
                min={0}
                onKeyDown={({ key }) => handleKey(key, item, 'quantidade')}
              />
            </div>
          ) : (
            <span
              className={classNames({
                pointer: !item.preenchido,
                'p-pr-5 p-pl-5': !item.quantidade || !item.quantidade <= 0,
              })}
              onClick={() => enableEdit(item, 'quantidade')}
            >
              {getNumberUnitThousands(item.quantidade)}
            </span>
          ),
      },
      {
        header: 'Valor Negociado',
        body: (item) => <span className="text-disabled">{getValueMoney(item.valorUnitario)}</span>,
      },
      props.showDesconto && {
        header: 'Desconto(%)',
        body: (item) =>
          !editing[getId(item)]?.desconto && !item.preenchido ? (
            <InputMonetary
              onChange={(e) => store.setDescontoItem(item.itemLote?.id, item.licitante.id, e)}
              placeholder="Desconto"
              value={item.desconto}
              decimalPlaces={2}
              min={0}
              max={100}
              onKeyDown={({ key }) => handleKey(key, item, 'desconto')}
            />
          ) : (
            <span className={classNames({ pointer: !item.preenchido })} onClick={() => enableEdit(item, 'desconto')}>
              {getNumberFractionDigits(item.desconto)}
            </span>
          ),
      },
      {
        header: 'Valor Total',
        body: (item) => <span className="text-disabled">{getValueMoney(item.valor)}</span>,
      },
      {
        header: 'Status',
        body: (item) =>
          item.preenchido ? (
            <Tag severity="success" value="Preenchido" rounded />
          ) : (
            <Tag severity="danger" value="Pendente" rounded />
          ),
      },
      {
        header: 'Ações',
        body: (item) => (
          <>
            {!item.preenchido && (
              <FcButton
                type="button"
                icon="pi pi-check"
                className="p-button-text toggle-button"
                onClick={() => {
                  if (!_validateRequiredFields(item)) {
                    setEditing((oldState) => {
                      oldState[getId(item)] = {};
                      return { ...oldState };
                    });
                    store.updateAttributeItem(item.itemLote?.id, item.licitante.id, 'preenchido', true);
                    setItemError((oldState) => {
                      oldState[getId(item)] = false;
                      return { ...oldState };
                    });
                  }
                }}
              />
            )}
            {item.preenchido && (
              <FcButton
                type="button"
                icon="pi pi-pencil"
                className="p-button-text toggle-button"
                onClick={() => {
                  setEditing((oldState) => {
                    oldState[getId(item)] = {};
                    return { ...oldState };
                  });
                  store.updateAttributeItem(item.itemLote?.id, item.licitante.id, 'preenchido', false);
                }}
              />
            )}
            <FcButton
              type="button"
              icon="pi pi-comment"
              className="p-button-text toggle-button"
              onClick={() => {
                setItemSelected(item);
                setObsItem(item.observacao);
                setEditing({});
                setDialogVisible(true);
              }}
            />
          </>
        ),
      },
    ];
    return (
      <DataTable
        rowHover
        value={itens}
        emptyMessage="Nenhum item disponível"
        style={{ maxWidth: '100%' }}
        className="p-datatable-sm "
      >
        {columns.map((c, idx) => {
          return <Column key={`field-${idx}`} {...c} />;
        })}
      </DataTable>
    );
  };

  const _renderItensVencedores = () => {
    return (
      <div className="p-mt-2">
        {store.itensGroupByDetentor?.map((venc) => {
          const itens = venc.itens;
          const className = itens.filter((item) => !item.preenchido)?.length === 0 ? 'panel-check' : 'panel-warning';
          return (
            <Panel
              className={className}
              header={_renderHeader(venc, className)}
              content={_renderDataTable(itens)}
              collapsed={collapsed[venc.licitante?.id]}
            />
          );
        })}
      </div>
    );
  };

  const _renderDialogObs = () => {
    return (
      <Dialog
        header="Observação"
        className="dialog-obs"
        visible={dialogVisible}
        onHide={() => setDialogVisible(false)}
        footer={
          <div>
            <FcButton
              label="Cancelar"
              icon="pi pi-times"
              className="p-button-danger"
              onClick={() => setDialogVisible(false)}
            />
            <FcButton
              label="Salvar"
              icon="pi pi-check"
              onClick={() => {
                store.updateAttributeItem(itemSelected.itemLote?.id, itemSelected.licitante.id, 'observacao', obsItem);
                setDialogVisible(false);
              }}
            />
          </div>
        }
      >
        <div className="p-fluid p-formgrid p-grid">
          <InputTextarea
            onChange={(e) => setObsItem(e.target.value)}
            rows={4}
            value={obsItem}
            placeholder="Descreva a Observação"
          />
        </div>
      </Dialog>
    );
  };

  return (
    <div>
      {!props.readOnly ? (
        <>
          <div className="p-fluid p-formgrid p-grid">
            <div className="p-field p-col-3">
              <strong className=" p-text-capitalize">Fornecedor/Prestador</strong>
              <Dropdown
                value={
                  store.vencedor
                    ? store.vencedor
                    : store.vencedoresGrouped?.length
                    ? store.vencedoresGrouped[0]
                    : undefined
                }
                onChange={(e) => {
                  store.setVencedor(e.value);
                }}
                optionLabel="licitante.nome"
                options={store.vencedoresGrouped}
                emptyMessage="Não há licitantes disponíveis"
                placeholder="Selecione um Licitante"
              />
            </div>
            <div className="p-field p-col-3" style={{ paddingRight: '30px' }}>
              <strong className=" p-text-capitalize">Lote</strong>
              <Dropdown
                id="dropdown-lote"
                value={store.lote}
                onChange={(e) => store.setLote(e.value)}
                optionLabel="nome"
                options={getOptionsLotesByVencedor()}
                emptyMessage="Não há lotes disponíveis"
                placeholder="Selecione um lote"
              />
            </div>
          </div>
          <PickList
            source={getSourceOptions()}
            target={getTargetOptions()}
            itemTemplate={itemTemplate}
            sourceStyle={{ height: '342px' }}
            targetStyle={{ height: '342px' }}
            showSourceControls={false}
            showTargetControls={false}
            onChange={(e) => store.onChangePickList(e)}
            dataKey="id"
            sourceHeader="Itens Disponíveis"
            targetHeader="Itens Adicionados"
          />
          {_renderItensVencedores()}
          {_renderDialogObs()}
        </>
      ) : (
        renderResumo()
      )}
    </div>
  );
});

export default ItensCarona;
