import React from 'react';
import { observer } from 'mobx-react';
import AppBreadCrumb from 'fc/components/AppBreadCrumb';
import GenericIndexPage from 'fc/pages/GenericIndexPage';
import AccessPermission from '~/constants/AccessPermission';
import PermissionProxy from 'fc/components/PermissionProxy';
import CaronaListagemPage from './listagem';
import CaronaIndexDetailPage from './detalhes/indexDetail';
import FcCloseableTabView from 'fc/components/FcCloseableTabView';

@observer
class CaronaIndexPage extends GenericIndexPage {
  constructor(props) {
    super(props, AccessPermission.carona);
    this.state = {
      data: [
        {
          id: 1,
          header: 'Listagem',
          closeable: false,
          content: <CaronaListagemPage {...props} onDetail={(carona) => this.onDetail(carona)} />,
        },
      ],
      count: 2,
      activeTabIndex: 0,
    };

    this.setActiveTabIndex = this.setActiveTabIndex.bind(this);
    this.handleCloseTab = this.handleCloseTab.bind(this);
    this.onDetail = this.onDetail.bind(this);
  }

  onDetail(carona) {
    const existingTab = this.state.data.find((tab) => tab.idCarona === carona.id);
    if (existingTab) {
      this.setActiveTabIndex(existingTab);
    } else {
      const newCarona = {
        id: carona.id,
        idCarona: carona.id,
        header: carona?.numeroProcessoGerenciadorAta.includes('/')
          ? carona?.numeroProcessoGerenciadorAta
          : `${carona?.numeroProcessoGerenciadorAta}/${carona?.anoCarona}`,
        closeable: true,
        content: <CaronaIndexDetailPage id={carona.id} />,
      };
      this.setState({ data: [...this.state.data, newCarona], count: this.state.count + 1 });
    }
  }

  setActiveTabIndex(tab) {
    this.setState({ activeTabIndex: this.state.data.indexOf(tab) });
  }

  handleCloseTab(tab) {
    this.setState((prevState) => {
      return { data: prevState.data.filter((t) => t.id !== tab.id) };
    });
  }

  render() {
    const breacrumbItems = [{ label: 'Adesão/Carona' }];
    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        <AppBreadCrumb items={breacrumbItems} />
        <div className="card page index-table">
          <FcCloseableTabView
            tabs={this.state.data}
            activeTabIndex={this.state.activeTabIndex}
            onChangeTab={this.setActiveTabIndex}
            onClose={this.handleCloseTab}
            fixedFirstTab
          />
        </div>
      </PermissionProxy>
    );
  }
}

CaronaIndexPage.displayName = 'CaronaIndexPage';

export default CaronaIndexPage;
