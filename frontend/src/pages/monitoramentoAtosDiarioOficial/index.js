import React from 'react';
import { observer } from 'mobx-react';
import IndexDataTable from 'fc/components/IndexDataTable';
import FcButton from 'fc/components/FcButton';
import { getValue, getValueByKey, getValueDate } from 'fc/utils/utils';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import AppBreadCrumb from 'fc/components/AppBreadCrumb';
import GenericIndexPage from 'fc/pages/GenericIndexPage';
import AdvancedSearch from 'fc/components/AdvancedSearch';
import AccessPermission from '~/constants/AccessPermission';
import PermissionProxy from 'fc/components/PermissionProxy';
import { DATE_FORMAT, DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT, DATE_PARSE_FORMAT_WITH_HOURS } from 'fc/utils/date';
import MonitoramentoAtosDiarioOficialViewIndexStore from '~/stores/monitoramentoAtosDiarioOficial/indexStore';
import { Dialog } from 'primereact/dialog';
import { Column } from 'primereact/column';
import { DataTable } from 'primereact/datatable';

@observer
class MonitoramentoAtosDiarioOficialViewIndexPage extends GenericIndexPage {
  constructor(props) {
    super(props, AccessPermission.monitoramentoAtosDiarioOficialView);

    this.state = {
      visibleDialogAtosLicitacao: false,
      expandedRows: null,
    };
    this.store = new MonitoramentoAtosDiarioOficialViewIndexStore();

    this._showDialogDetalhesAtosLicitacao = this._showDialogDetalhesAtosLicitacao.bind(this);
    this._renderRowDetails = this._renderRowDetails.bind(this);
  }

  _getTaggedValuesFromText(text) {
    return <span dangerouslySetInnerHTML={{ __html: text }} />;
  }

  _getProcessingStatusValue(status) {
    const color = status === 'EM_EXTRACAO' ? 'red' : status === 'PROCESSADO' ? 'green' : 'yellow';
    return (
      <div style={{ display: 'inline-flex' }}>
        <div>
          <i
            className="pi pi-circle"
            style={{
              fontSize: '0.5rem',
              marginRight: '5px',
              verticalAlign: 'middle',
              color: color,
              backgroundColor: color,
              borderRadius: '50%',
              marginBottom: '3px',
            }}
          />
        </div>
        <div>
          <span>{getValueByKey(status, DadosEstaticosService.getStatusMonitoramentoAtosDiarioOficial())}</span>
        </div>
      </div>
    );
  }

  _showDialogDetalhesAtosLicitacao() {
    this.setState({ visibleDialogAtosLicitacao: !this.state.visibleDialogAtosLicitacao });
  }

  _renderColumnsAtos(columns) {
    return columns.map((col) => <Column key={`col-${col.field}`} {...col} />);
  }

  _renderValue(label, value, col = 12) {
    return (
      <div className={`p-col-${col}`}>
        <div className="p-col-12 drawer-content-label" style={{ fontWeight: 'bold' }}>
          {label}
        </div>
        <div className={`p-col-12`}>{value ?? '-'}</div>
      </div>
    );
  }

  _renderRowDetails(rowdata) {
    return (
      <div className="card">
        <div className="p-grid">
          <div className="p-grid p-col-12">
            <h5 className="p-col-8" style={{ margin: '0 0 0 0' }}>
              Detalhes do Ato
            </h5>
          </div>
          <div className="p-grid p-col-12">
            {this._renderValue(
              <>Data da Classificação </>,
              getValueDate(rowdata.dataClassificacao, DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS),
              2
            )}
            {this._renderValue(
              <>Status </>,
              getValueByKey(rowdata.status, DadosEstaticosService.getStatusProcessamentoAtosLicDiarioOficial()),
              1
            )}
          </div>
          <div className="p-grid p-col-12">
            {this._renderValue(<>Ente </>, getValue(rowdata.ente), 4)}
            {this._renderValue(<>Órgão </>, getValue(rowdata.orgao), 4)}
            {this._renderValue(<>Identificação </>, getValue(rowdata.identificacao), 4)}
          </div>
          <div className="p-grid p-col-12">{this._renderValue(<>Objeto </>, getValue(rowdata.objeto), 12)}</div>
          <div className="p-grid p-col-12">
            {this._renderValue(<>Texto </>, this._getTaggedValuesFromText(rowdata.texto), 12)}
          </div>
        </div>
      </div>
    );
  }

  _renderDetalhesAtosLicitacaoDialog() {
    const columns = [
      {
        field: 'id',
        header: 'Ato de Licitação',
        body: ({ id }) => `Ato de Licitação - ${id}`,
      },
      {
        field: 'dataClassificacao',
        header: 'Data da Classificação',
        body: ({ dataClassificacao }) =>
          getValueDate(dataClassificacao, DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS),
      },
      {
        field: 'ente',
        header: 'Ente',
      },
    ];
    const header = (
      <div style={{ width: '100%', textAlign: 'left' }}>
        <h4 style={{ margin: 0 }}>Atos de Licitação</h4>
      </div>
    );

    return (
      <Dialog
        header={header}
        visible={this.state.visibleDialogAtosLicitacao}
        style={{ width: '80vw' }}
        onHide={() => this.setState({ visibleDialogAtosLicitacao: false, expandedRows: null })}
        draggable={false}
        resizable={false}
        dismissableMask
      >
        <div>
          <DataTable
            rowHover
            key={'atos-table'}
            expandedRows={this.state.expandedRows}
            onRowToggle={(e) => this.setState({ expandedRows: e.data })}
            rowExpansionTemplate={this._renderRowDetails}
            responsiveLayout="scroll"
            tabIndex={'id'}
            value={this.store.atosLicitacao}
            className="p-datatable-sm"
            paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport"
            currentPageReportTemplate="{first} - {last} de {totalRecords} registros"
            paginator
            rows={10}
            emptyMessage="Nenhum ato encontrado"
          >
            <Column expander style={{ width: '3em' }} />

            {this._renderColumnsAtos(columns)}
          </DataTable>
        </div>
      </Dialog>
    );
  }

  render() {
    const columns = [
      {
        field: 'dataPublicacao',
        header: 'Data da Publicação',
        body: ({ dataPublicacao }) => getValueDate(dataPublicacao, DATE_FORMAT, DATE_PARSE_FORMAT),
        sortable: true,
      },
      {
        field: 'quantidadeAtos',
        header: 'Qtd. Atos',
        sortable: true,
      },
      {
        field: 'quantidadeAtosLicitacao',
        header: 'Qtd. de Atos de Licitação',
        sortable: true,
      },
      {
        field: 'status',
        header: 'Status do Processamento',

        sortable: true,
        body: ({ status }) => this._getProcessingStatusValue(status),
      },
      {
        style: { width: '165px' },
        body: (rowdata) => {
          return (
            <div className="actions p-d-flex p-jc-end">
              <PermissionProxy resourcePermissions={this.getReadPermission()}>
                <FcButton
                  icon="pi pi-eye"
                  tooltip="Visualizar atos de licitações"
                  className="p-button-sm p-button-success p-mr-2"
                  onClick={() => {
                    this.store.carregarAtosLicitacao(rowdata.id, () => this._showDialogDetalhesAtosLicitacao());
                  }}
                  disabled={rowdata.status !== 'PROCESSADO' || rowdata.quantidadeAtosLicitacao == 0}
                />
              </PermissionProxy>
            </div>
          );
        },
      },
    ];

    const { listKey, loading } = this.store;
    const { getDefaultTableProps } = this;

    const breacrumbItems = [{ label: 'Monitoramento Atos Diário Oficial' }];

    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        <AppBreadCrumb items={breacrumbItems} />
        <div className="card page index-table">
          <AdvancedSearch
            searchParams={this.store.getAdvancedSearchParams()}
            store={this.store}
            searchFields={['quantidadeAtos', 'quantidadeAtosLicitacao']}
            useOr
          />
          <IndexDataTable columns={columns} value={listKey} loading={loading} {...getDefaultTableProps()} />
          {this.state.visibleDialogAtosLicitacao && this._renderDetalhesAtosLicitacaoDialog()}
        </div>
      </PermissionProxy>
    );
  }
}

MonitoramentoAtosDiarioOficialViewIndexPage.displayName = 'MonitoramentoAtosDiarioOficialViewIndexPage';

export default MonitoramentoAtosDiarioOficialViewIndexPage;
