import React from 'react';
import { observer } from 'mobx-react';
import GenericIndexPage from 'fc/pages/GenericIndexPage';
import AppBreadCrumb from 'fc/components/AppBreadCrumb';
import AccessPermission from '~/constants/AccessPermission';
import PermissionProxy from 'fc/components/PermissionProxy';
import AnalisarEditaisListagemPage from './listagem';
import FcCloseableTabView from 'fc/components/FcCloseableTabView';
import SentencaEditalDetail from './sentencaEdital/formDetail';
import { getValue } from 'fc/utils/utils';

@observer
class AnalisarEditaisIndexPage extends GenericIndexPage {
  constructor(props) {
    super(props, AccessPermission.edital);

    this.setActiveTabIndex = this.setActiveTabIndex.bind(this);
    this.handleCloseTab = this.handleCloseTab.bind(this);
    this.onDetail = this.onDetail.bind(this);

    this.state = {
      data: [
        {
          id: 1,
          header: 'Listagem',
          content: <AnalisarEditaisListagemPage {...props} onDetail={(edital) => this.onDetail(edital)} />,
        },
      ],
      count: 2,
      activeTabIndex: 0,
    };
  }

  onDetail(edital) {
    const existingTab = this.state.data.find((tab) => tab.idEdital === edital.id);
    if (existingTab) {
      this.setActiveTabIndex(existingTab);
    } else {
      const newEditalDetail = {
        id: this.state.count,
        idEdital: edital.id,
        header: getValue(edital?.titulo),
        closeable: true,
        content: <SentencaEditalDetail idEdital={edital.id} />,
      };
      this.setState({ data: [...this.state.data, newEditalDetail], count: this.state.count + 1 });
    }
  }

  setActiveTabIndex(tab) {
    this.setState({ activeTabIndex: this.state.data.indexOf(tab) });
  }

  handleCloseTab(tab) {
    this.setState((prevState) => {
      return { data: prevState.data.filter((t) => t.id !== tab.id) };
    });
  }

  render() {
    const breacrumbItems = [{ label: 'Análise de Editais' }];

    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        <AppBreadCrumb items={breacrumbItems} />
        <div className="card page index-table">
          <FcCloseableTabView
            tabs={this.state.data}
            activeTabIndex={this.state.activeTabIndex}
            onChangeTab={this.setActiveTabIndex}
            onClose={this.handleCloseTab}
            fixedFirstTab
          />
        </div>
      </PermissionProxy>
    );
  }
}

AnalisarEditaisIndexPage.displayName = 'AnalisarEditaisIndexPage';

export default AnalisarEditaisIndexPage;
