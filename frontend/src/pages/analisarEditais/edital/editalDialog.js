import React from 'react';
import { observer } from 'mobx-react';
import PropTypes from 'prop-types';
import GenericFormPage from 'fc/pages/GenericFormPage';
import <PERSON>Field from 'fc/components/FormField';
import AccessPermission from '~/constants/AccessPermission';
import { Dialog } from 'primereact/dialog';
import EditalFormStore from '~/stores/edital/formStore';
import { InputText } from 'primereact/inputtext';
import MultipleFileUploader from 'fc/components/MultipleFileUploader';
import { showNotification } from 'fc/utils/utils';
import AppStore from 'fc/stores/AppStore';
import FcButton from 'fc/components/FcButton';
import FcCalendar from 'fc/components/FcCalendar';

@observer
class EditalDialog extends GenericFormPage {
  constructor(props) {
    super(props, AccessPermission.edital);
    this.store = new EditalFormStore();
  }

  submitFormData(e) {
    e && e.preventDefault();
    const execution = () => {
      if (!this.store.rules.hasError) {
        this.store.save(() => {
          this.props.updateTable();
          this.props.closeDialog();
        });
      } else {
        showNotification('error', null, 'Verifique os campos do formulário!');
      }
    };

    if (this.state.submitted) {
      execution();
    } else {
      this.setState({ submitted: true }, execution);
    }
  }

  renderActionButtons() {
    const hasWritePermission = !this.getWritePermission() || AppStore.hasPermission(this.getWritePermission());
    return (
      <div className="p-mt-2">
        <span className="p-mr-1 p-d-inline p-d-flex align-items-center">
          <FcButton
            label="Voltar"
            type="button"
            className="p-ml-auto p-button-secondary p-mr-2"
            onClick={() => this.props.closeDialog()}
            loading={this.store.loading}
          />
          {hasWritePermission && <FcButton label="Analisar" type="submit" loading={this.store.loading} />}
        </span>
      </div>
    );
  }

  render() {
    const { submitted } = this.state;
    const { visible, closeDialog } = this.props;
    const { getRule, updateAttribute } = this.store;
    const { submitFormData } = this;

    return (
      <Dialog
        header="Analisar Edital"
        visible={visible}
        modal
        style={{ minWidth: '80vw', maxWidth: '800px' }}
        onHide={closeDialog}
        draggable={false}
        resizable={false}
        dismissableMask
      >
        {this.store.object && (
          <div>
            <form onSubmit={submitFormData}>
              <div className="p-fluid p-formgrid p-grid">
                <FormField columns={6} attribute="titulo" label="Título" submitted={submitted} rule={getRule('titulo')}>
                  <InputText
                    onChange={(e) => updateAttribute('titulo', e)}
                    placeholder="Informe o título do edital"
                    value={this.store.object?.titulo}
                  />
                </FormField>
                <FormField
                  columns={6}
                  attribute="dataPublicacao"
                  label="Data da Publicação"
                  submitted={submitted}
                  rule={getRule('dataPublicacao')}
                >
                  <FcCalendar
                    value={this.getDateAttributeValue(this.store.object?.dataPublicacao)}
                    onChange={(e) => updateAttribute('dataPublicacao', e)}
                    showIcon
                    mask="99/99/9999"
                  />
                </FormField>
                <FormField columns={12}>
                  <MultipleFileUploader
                    store={this.store.fileStore}
                    showFileType={false}
                    onChangeFiles={(files) => this.store.setFileList(files)}
                    accept=".docx, .pdf"
                    view={false}
                    multi={false}
                  />
                </FormField>
              </div>
              {this.renderActionButtons()}
            </form>
          </div>
        )}
      </Dialog>
    );
  }
}

EditalDialog.propTypes = {
  history: PropTypes.any,
  closeDialog: PropTypes.func.isRequired,
  updateTable: PropTypes.func.isRequired,
  visible: PropTypes.bool.isRequired,
};

export default EditalDialog;
