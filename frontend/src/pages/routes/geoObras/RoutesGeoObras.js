import NotFound from 'fc/pages/NotFound';
import { Route, Switch } from 'react-router';
import UrlRouter from '~/constants/UrlRouter';
import RoutesObras from '~/pages/routes/geoObras/RoutesObras';
import RoutesTipoObra from './RoutesTipoObra';
import RoutesAcompanhamento from './RoutesAcompanhamento';

const RoutesGeoObras = () => {
  return (
    <Switch>
      <Route path={UrlRouter.geoObras.tipoObra.index} component={RoutesTipoObra} />
      <Route path={UrlRouter.geoObras.obra.index} component={RoutesObras} />
      <Route path={UrlRouter.geoObras.acompanhamento.index} component={RoutesAcompanhamento} />
      <Route component={NotFound} />
    </Switch>
  );
};

export default RoutesGeoObras;
