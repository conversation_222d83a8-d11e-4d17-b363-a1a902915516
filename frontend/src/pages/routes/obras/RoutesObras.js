import { Switch, Route } from 'react-router';
import NotFound from 'fc/pages/NotFound';
import UrlRouter from '~/constants/UrlRouter';
import ObraIndexPage from '~/pages/obras';
import RoutesTipoObra from './RoutesTipoObra';
import RoutesCadastroObras from './RoutesCadastroObras';
import RoutesSicroSinapi from './RoutesSicroSinapi';
import RoutesAcompanhamento from './RoutesAcompanhamento';
import MapaObrasIndexPage from '~/pages/geoObras/mapa';

const RoutesObras = () => {
  return (
    <Switch>
      <Route path={UrlRouter.obra.tipoObra.index} component={RoutesTipoObra} />
      <Route path={UrlRouter.obra.cadastro.index} component={RoutesCadastroObras} />
      <Route path={UrlRouter.obra.acompanhamento.index} component={RoutesAcompanhamento} />
      <Route path={UrlRouter.obra.mapa.index} component={MapaObrasIndexPage} />
      <Route path={UrlRouter.obra.medicao.index} component={ObraIndexPage} />
      <Route path={UrlRouter.obra.relatorioObra.index} component={RoutesSicroSinapi} />
      <Route component={NotFound} />
    </Switch>
  );
};

export default RoutesObras;
