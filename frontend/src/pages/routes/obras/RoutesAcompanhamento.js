import NotFound from 'fc/pages/NotFound';
import { Route, Switch } from 'react-router';
import UrlRouter from '~/constants/UrlRouter';
import AcompanhamentoIndexPage from '~/pages/geoObras/acompanhamento';

const RoutesAcompanhamento = () => {
  return (
    <Switch>
      <Route path={UrlRouter.obra.acompanhamento.index} exact component={AcompanhamentoIndexPage} />
      <Route component={NotFound} />
    </Switch>
  );
};

export default RoutesAcompanhamento;
