import NotFound from 'fc/pages/NotFound';
import { Route, Switch } from 'react-router';
import UrlRouter from '~/constants/UrlRouter';
import ObraIndexPage from '~/pages/geoObras/obra';
import EditObra from '~/pages/geoObras/obra/edit';
import NewObra from '~/pages/geoObras/obra/new';

const RoutesCadastroObras = () => {
  return (
    <Switch>
      <Route path={UrlRouter.obra.cadastro.index} exact component={ObraIndexPage} />
      <Route path={UrlRouter.obra.cadastro.editar} exact component={EditObra} />
      <Route path={UrlRouter.obra.cadastro.novo} exact component={NewObra} />
      <Route component={NotFound} />
    </Switch>
  );
};

export default RoutesCadastroObras;
