import { Switch, Route } from 'react-router';
import UrlRouter from '~/constants/UrlRouter';
import ConsultaProcessoIndexPage from '~/pages/consultaProcesso';
import NotFound from 'fc/pages/NotFound';

const RoutesConsultaProcesso = () => {
  return (
    <Switch>
      <Route path={UrlRouter.cadastrosConsulta.consultaProcesso.index} exact component={ConsultaProcessoIndexPage} />
      <Route component={NotFound} />
    </Switch>
  );
};

export default RoutesConsultaProcesso;
