import { Switch, Route } from 'react-router';
import UrlRouter from '~/constants/UrlRouter';
import NotFound from 'fc/pages/NotFound';
import ContratoIndexPage from '~/pages/contrato/index';
import AditivoContratoIndexPage from '~/pages/contrato/aditivos/index';
import NewContrato from '~/pages/contrato/new';
import EditContrato from '~/pages/contrato/edit';
import NewAditivoContrato from '~/pages/contrato/aditivos/new';
import EditAditivoContrato from '~/pages/contrato/aditivos/edit';
import EditEmpenhoContrato from '~/pages/contrato/empenhos/edit';
import NewEmpenhoContrato from '~/pages/contrato/empenhos/new';
import EmpenhoContratoIndexPage from '~/pages/contrato/empenhos';

const RoutesContrato = () => {
  return (
    <Switch>
      <Route path={UrlRouter.cadastrosConsulta.contrato.index} exact component={ContratoIndexPage} />
      <Route path={UrlRouter.cadastrosConsulta.contrato.novo} exact component={NewContrato} />
      <Route path={UrlRouter.cadastrosConsulta.contrato.editar} exact component={EditContrato} />
      <Route path={UrlRouter.cadastrosConsulta.contrato.detalhe} exact component={ContratoIndexPage} />
      <Route path={UrlRouter.administracao.requisicaoModificacao.contrato.requisitar} exact component={EditContrato} />
      <Route path={UrlRouter.cadastrosConsulta.contrato.defaultLicitacao} exact component={NewContrato} />
      <Route path={UrlRouter.cadastrosConsulta.contrato.aditivo.index} exact component={AditivoContratoIndexPage} />
      <Route path={UrlRouter.cadastrosConsulta.contrato.aditivo.novo} exact component={NewAditivoContrato} />
      <Route path={UrlRouter.cadastrosConsulta.contrato.aditivo.editar} exact component={EditAditivoContrato} />
      <Route
        path={UrlRouter.administracao.requisicaoModificacao.aditivo.requisitar}
        exact
        component={EditAditivoContrato}
      />
      <Route path={UrlRouter.cadastrosConsulta.contrato.empenho.index} exact component={EmpenhoContratoIndexPage} />
      <Route path={UrlRouter.cadastrosConsulta.contrato.empenho.novo} exact component={NewEmpenhoContrato} />
      <Route path={UrlRouter.cadastrosConsulta.contrato.empenho.editar} exact component={EditEmpenhoContrato} />
      <Route component={NotFound} />
    </Switch>
  );
};

export default RoutesContrato;
