import { Switch, Route } from 'react-router';
import UrlRouter from '~/constants/UrlRouter';
import NotFound from 'fc/pages/NotFound';
import RoutesLicitacao from './RoutesLicitacao';
import RoutesConsultaProcesso from './RoutesConsultaProcesso';
import RoutesCarona from './RoutesCarona';
import RoutesDispensa from './RoutesDispensa';
import RoutesContrato from './RoutesContrato';
import RoutesInexigibilidade from './RoutesInexigibilidade';
import RoutesParecerista from './RoutesParecerista';
import RoutesCredenciamento from './RoutesCredenciamento';

const RoutesCadastrosConsulta = () => {
  return (
    <Switch>
      <Route path={UrlRouter.cadastrosConsulta.carona.index} component={RoutesCarona} />
      <Route path={UrlRouter.cadastrosConsulta.licitacao.index} component={RoutesLicitacao} />
      <Route path={UrlRouter.cadastrosConsulta.inexigibilidade.index} component={RoutesInexigibilidade} />
      <Route path={UrlRouter.cadastrosConsulta.credenciamento.index} component={RoutesCredenciamento} />
      <Route path={UrlRouter.cadastrosConsulta.dispensa.index} component={RoutesDispensa} />
      <Route path={UrlRouter.cadastrosConsulta.consultaProcesso.index} component={RoutesConsultaProcesso} />
      <Route path={UrlRouter.cadastrosConsulta.contrato.index} component={RoutesContrato} />
      <Route path={UrlRouter.cadastrosConsulta.parecerista.index} component={RoutesParecerista} />
      <Route component={NotFound} />
    </Switch>
  );
};

export default RoutesCadastrosConsulta;
