import { Switch, Route } from 'react-router';
import UrlRouter from '~/constants/UrlRouter';
import NotFound from 'fc/pages/NotFound';
import RoutesGrupoUsuario from './RoutesGrupoUsuario';
import RoutesUsuario from './RoutesUsuario';
import RoutesConfiguracoes from './RoutesConfiguracoes';
import RoutesFuncaoRisco from './RoutesFuncaoRisco';

const RoutesSeguranca = () => {
  return (
    <Switch>
      <Route path={UrlRouter.seguranca.usuario.index} component={RoutesUsuario} />
      <Route path={UrlRouter.seguranca.grupoUsuario.index} component={RoutesGrupoUsuario} />
      <Route path={UrlRouter.seguranca.configuracoes.editar} component={RoutesConfiguracoes} />
      <Route path={UrlRouter.seguranca.funcaoRisco.index} component={RoutesFuncaoRisco} />
      <Route component={NotFound} />
    </Switch>
  );
};

export default RoutesSeguranca;
