import { Switch, Route } from 'react-router';
import NotFound from 'fc/pages/NotFound';
import UrlRouter from '~/constants/UrlRouter';
import RoutesSecaoChecklist from './RoutesSecaoChecklist';
import RoutesItemChecklist from './RoutesItemChecklist';
import RoutesAnaliseProcesso from './RoutesAnaliseProcesso';
import RoutesTda from './RoutesTda';
import RoutesAplicarChecklist from './RoutesAplicarChecklist';
import RoutesEmitirAlerta from './RoutesEmitirAlerta';
import RoutesHistoricoProcessos from './RoutesHistoricoProcessos';
import RoutesAlertasDafo from './RoutesAlertasDafo';
import RoutesAnaliseAutomatica from './RoutesAnaliseAutomatica';
import RoutesRequisicaoModificacao from './RoutesRequisicaoModificacao';
import RoutesAnalisarEditais from './RoutesAnalisarEditais';
import RoutesMonitoramentoAtosDiariosOficiais from './RoutesMonitoramentoAtosDiariosOficiais';
import RoutesMapeamentoAtosLicitacoes from './RoutesMapeamentoAtosLicitacoes';
import RoutesAlertasInspetor from './RoutesAlertasInspetor';
import RoutesPainelAtosLicitacoes from './RoutesPainelAtosLicitacoes';

const RoutesAuditoria = () => {
  return (
    <Switch>
      <Route path={UrlRouter.auditoria.secaoChecklist.index} component={RoutesSecaoChecklist} />
      <Route path={UrlRouter.auditoria.itemChecklist.index} component={RoutesItemChecklist} />
      <Route path={UrlRouter.auditoria.analiseProcesso.index} component={RoutesAnaliseProcesso} />
      <Route path={UrlRouter.auditoria.tda.default} component={RoutesTda} />
      <Route path={UrlRouter.auditoria.aplicarChecklist.default} component={RoutesAplicarChecklist} />
      <Route path={UrlRouter.auditoria.emitirAlerta.default} component={RoutesEmitirAlerta} />
      <Route path={UrlRouter.auditoria.historicoProcessos.index} component={RoutesHistoricoProcessos} />
      <Route path={UrlRouter.auditoria.requisicaoModificacao.index} component={RoutesRequisicaoModificacao} />
      <Route path={UrlRouter.auditoria.analisarAlertas.index} component={RoutesAlertasDafo} />
      <Route path={UrlRouter.auditoria.analiseAutomatica.index} component={RoutesAnaliseAutomatica} />
      <Route path={UrlRouter.auditoria.analisarEditais.index} component={RoutesAnalisarEditais} />
      <Route
        path={UrlRouter.auditoria.monitoramentoAtosDiarioOficial.index}
        component={RoutesMonitoramentoAtosDiariosOficiais}
      />
      <Route path={UrlRouter.auditoria.mapeamentoAtosLicitacoes.index} component={RoutesMapeamentoAtosLicitacoes} />
      <Route path={UrlRouter.auditoria.painelAtosLicitacoes.index} component={RoutesPainelAtosLicitacoes} />
      <Route path={UrlRouter.auditoria.alertasInspetor.index} component={RoutesAlertasInspetor} />
      <Route component={NotFound} />
    </Switch>
  );
};

export default RoutesAuditoria;
