import React from 'react';
import { observer } from 'mobx-react';
import PropTypes from 'prop-types';
import FeriadoFormStore from '../../stores/feriado/formStore';
import { InputText } from 'primereact/inputtext';
import UrlRouter from '../../constants/UrlRouter';
import GenericFormPage from 'fc/pages/GenericFormPage';
import AppBreadCrumb from 'fc/components/AppBreadCrumb';
import FormField from 'fc/components/FormField';
import DadosEstaticosService from '../../services/DadosEstaticosService';
import { RadioButton } from 'primereact/radiobutton';
import AccessPermission from '~/constants/AccessPermission';
import PermissionProxy from 'fc/components/PermissionProxy';
import FcDropdown from 'fc/components/FcDropdown';
import FcCalendar from 'fc/components/FcCalendar';

@observer
class FeriadoFormPage extends GenericFormPage {
  constructor(props) {
    super(props, UrlRouter.administracao.feriado.index, AccessPermission.feriado);

    this.store = new FeriadoFormStore();
  }

  componentDidMount() {
    const { id } = this.props;
    this.store.initialize(id, { fixo: false }, this.store.initDataFeriado);
  }

  render() {
    const { submitted } = this.state;
    const { getRule } = this.store;
    const { submitFormData } = this;
    const { updateAttribute, updateAttributeDataFeriado } = this.store;
    const { validateField, getFieldErrorMessage } = this;

    const breacrumbItems = [
      { label: 'Feriado', url: UrlRouter.administracao.feriado.index },
      { label: this.props.action === 'new' ? 'Novo' : 'Editar' },
    ];

    let content;
    if (this.store.object) {
      content = (
        <>
          <AppBreadCrumb items={breacrumbItems} />
          <div className="card page form-action-buttons">
            <form onSubmit={submitFormData}>
              <div className="p-fluid p-formgrid p-grid">
                <FormField columns={6} attribute="nome" label="Nome" rule={getRule('nome')} submitted={submitted}>
                  <InputText
                    onChange={(e) => this.store.updateAttribute('nome', e)}
                    placeholder="Informe o nome"
                    value={this.store.object.nome}
                    id="nome"
                  />
                </FormField>

                <FormField
                  columns={6}
                  attribute="dia"
                  label="Data do Feriado"
                  rule={getRule('dia')}
                  submitted={submitted}
                >
                  <FcCalendar
                    value={this.store.dataFeriado}
                    onChange={(e) => updateAttributeDataFeriado(e)}
                    id="dataFeriado"
                    showIcon
                    mask="99/99/9999"
                  />
                </FormField>

                <div className="p-field p-col-6">
                  <label {...validateField('tipo')} htmlFor="tipo">
                    Tipo do Feriado:
                  </label>
                  <FcDropdown
                    inOrder
                    {...validateField('tipo')}
                    onChange={(e) => updateAttribute('tipo', e)}
                    placeholder="Selecione o tipo do feriado"
                    value={this.store.object.tipo}
                    id="tipoFeriado"
                    optionLabel="text"
                    optionValue="value"
                    options={DadosEstaticosService.getTipoFeriado()}
                  />
                  {getFieldErrorMessage('tipo')}
                </div>

                <FormField columns={6} attribute="fixo" label="Fixo?" rule={getRule('fixo')} submitted={submitted}>
                  <div className="p-field-radiobutton p-dir-row">
                    <div className="p-field-radiobutton p-col">
                      <RadioButton
                        inputId="fixoSim"
                        name="fixoS"
                        value={true}
                        onChange={(e) => {
                          this.store.updateAttribute('fixo', e.value);
                          this.store.setDateAtributtes();
                        }}
                        checked={this.store.object.fixo === true}
                      />
                      <label htmlFor="fixoSim">Sim</label>
                    </div>

                    <div className="p-field-radiobutton p-col">
                      <RadioButton
                        inputId="fixoNao"
                        name="fixoN"
                        value={false}
                        onChange={(e) => this.store.updateAttribute('fixo', e.value)}
                        checked={this.store.object.fixo === false}
                      />
                      <label htmlFor="fixoNao">Não</label>
                    </div>
                  </div>
                </FormField>
              </div>
              {this.renderActionButtons()}
            </form>
          </div>
        </>
      );
    } else {
      content = (
        <div className="card page">
          <AppBreadCrumb items={breacrumbItems} />
          <i
            className="pi pi-spin pi-spinner"
            style={{
              marginTop: '20px',
              marginLeft: 'calc(50% - 20px)',
              fontSize: '2em',
            }}
          ></i>
        </div>
      );
    }
    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        {content}
      </PermissionProxy>
    );
  }
}

FeriadoFormPage.propTypes = {
  id: PropTypes.any,
  action: PropTypes.any,
  history: PropTypes.any,
};

export default FeriadoFormPage;
