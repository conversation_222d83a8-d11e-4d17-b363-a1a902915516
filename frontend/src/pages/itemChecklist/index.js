import React from 'react';
import { Link } from 'react-router-dom';
import { observer } from 'mobx-react';
import IndexDataTable from 'fc/components/IndexDataTable';
import FcButton from 'fc/components/FcButton';
import { PrimeIcons } from 'primereact/api';
import AppBreadCrumb from 'fc/components/AppBreadCrumb';
import UrlRouter from '~/constants/UrlRouter';
import GenericIndexPage from 'fc/pages/GenericIndexPage';
import AdvancedSearch from 'fc/components/AdvancedSearch';
import AccessPermission from '~/constants/AccessPermission';
import PermissionProxy from 'fc/components/PermissionProxy';
import ItemChecklistIndexStore from '~/stores/itemChecklist/indexStore';
import { getValue } from 'fc/utils/utils';

@observer
class ItemChecklistIndexPage extends GenericIndexPage {
  constructor(props) {
    super(props, AccessPermission.itemChecklist);
    this.store = new ItemChecklistIndexStore();
    this.state = {
      idRemove: null,
    };
  }

  render() {
    const columns = [
      {
        field: 'titulo',
        header: 'Títu<PERSON>',
        sortable: true,
        className: 'text-justify',
      },
      {
        field: 'secao',
        header: 'Seção',
        sortable: true,
        body: ({ secao }) => getValue(secao.titulo),
      },
      {
        field: 'legislacao',
        header: 'Legislação',
        sortable: true,
        className: 'text-justify',
      },
      {
        field: 'ordem',
        header: 'Ordem',
        sortable: true,
      },
      {
        style: { width: '165px' },
        body: (rowData) => {
          return (
            <div className="actions p-d-flex p-jc-end">
              <PermissionProxy resourcePermissions={this.getWritePermission()}>
                <FcButton
                  icon="pi pi-pencil"
                  className="p-button-sm p-button-success p-mr-2"
                  onClick={() =>
                    this.pushUrlToHistory(UrlRouter.auditoria.itemChecklist.editar.replace(':id', rowData.id))
                  }
                />
              </PermissionProxy>
              <PermissionProxy resourcePermissions={this.getWritePermission()}>
                <FcButton
                  icon="pi pi-trash"
                  className="p-button-sm p-button-danger"
                  onClick={() => {
                    this.setState({ idRemove: rowData.id });
                    this.store.toggleShowConfirmDialog();
                  }}
                />
              </PermissionProxy>
            </div>
          );
        },
      },
    ];

    const header = (
      <div className="table-header p-grid p-dir-col">
        <Link to={UrlRouter.auditoria.itemChecklist.novo}>
          <FcButton
            className="p-button"
            label="Novo"
            style={{ marginBottom: '5px', marginRight: '5px' }}
            icon={PrimeIcons.PLUS}
          />
        </Link>
      </div>
    );

    const { listKey, loading } = this.store;
    const { getDefaultTableProps } = this;

    const breacrumbItems = [{ label: 'Checklist de Verificação' }];

    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        <AppBreadCrumb items={breacrumbItems} />
        <div className="card page index-table">
          <AdvancedSearch
            searchParams={this.store.getAdvancedSearchParams()}
            store={this.store}
            searchFields={['titulo']}
          />
          <IndexDataTable
            columns={columns}
            value={listKey}
            header={header}
            loading={loading}
            {...getDefaultTableProps()}
          />
          {this.store.isConfirmDialogVisible && this.confirmRemove(this.state.idRemove)}
        </div>
      </PermissionProxy>
    );
  }
}

ItemChecklistIndexPage.displayName = 'ItemChecklistIndexPage';

export default ItemChecklistIndexPage;
