import React from 'react';
import { observer } from 'mobx-react';
import PropTypes from 'prop-types';
import GenericFormPage from 'fc/pages/GenericFormPage';
import AppBreadCrumb from 'fc/components/AppBreadCrumb';
import PermissionProxy from 'fc/components/PermissionProxy';
import <PERSON><PERSON>ield from 'fc/components/FormField';
import { InputText } from 'primereact/inputtext';
import { InputNumber } from 'primereact/inputnumber';
import UrlRouter from '~/constants/UrlRouter';
import AccessPermission from '~/constants/AccessPermission';
import ItemChecklistFormStore from '~/stores/itemChecklist/formStore';
import SecaoChecklistIndexStore from '~/stores/secaoChecklist/indexStore';
import SelectDialog from 'fc/components/SelectDialog';
import SecaoChecklistFormPage from '../secaoChecklist/form';
import { Checkbox } from 'primereact/checkbox';

@observer
class ItemChecklistFormPage extends GenericFormPage {
  constructor(props) {
    super(props, UrlRouter.auditoria.itemChecklist.index, AccessPermission.itemChecklist);
    this.store = new ItemChecklistFormStore();
  }

  componentDidMount() {
    const { id } = this.props;
    this.store.initialize(id, { suspenso: false });
  }

  render() {
    const { submitted } = this.state;
    const { updateAttribute, getRule } = this.store;
    const { submitFormData } = this;

    const breacrumbItems = [
      { label: 'Checklist de Verificação', url: UrlRouter.auditoria.itemChecklist.index },
      { label: this.props.action === 'new' ? 'Novo' : 'Editar' },
    ];

    const secaoChecklistForm = (props) => {
      return <SecaoChecklistFormPage action="new" closeMethod={props.closeMethod} />;
    };

    const columnsSecaoChecklist = [{ field: 'titulo', label: 'Titulo', sortable: true }];

    let content = '';
    if (this.store.object) {
      content = (
        <>
          <AppBreadCrumb items={breacrumbItems} />
          <div className="card page form-action-buttons">
            <form onSubmit={submitFormData}>
              <div className="p-fluid p-formgrid p-grid">
                <FormField
                  columns={12}
                  attribute="titulo"
                  label="Título"
                  rule={getRule('titulo')}
                  submitted={submitted}
                >
                  <InputText
                    onChange={(e) => updateAttribute('titulo', e)}
                    placeholder="Informe o título"
                    value={this.store.object.titulo}
                  />
                </FormField>
                <FormField
                  columns={12}
                  attribute="descricaoRelatorioChecklist"
                  label="Descrição para Relatório Checklist"
                  rule={getRule('descricaoRelatorioChecklist')}
                  submitted={submitted}
                >
                  <InputText
                    onChange={(e) => updateAttribute('descricaoRelatorioChecklist', e)}
                    placeholder="Informe a descrição para relatório"
                    value={this.store.object.descricaoRelatorioChecklist}
                  />
                </FormField>
                <FormField
                  columns={6}
                  attribute="legislacao"
                  label="Legislação"
                  rule={getRule('legislacao')}
                  submitted={submitted}
                >
                  <InputText
                    onChange={(e) => updateAttribute('legislacao', e)}
                    placeholder="Informe a legislação"
                    value={this.store.object.legislacao}
                  />
                </FormField>
                <FormField columns={6} label="Seção de Checklist" submitted={submitted} rule={getRule('secao')}>
                  <SelectDialog
                    value={this.store.object.secao}
                    label="titulo"
                    indexStore={new SecaoChecklistIndexStore()}
                    onChange={(e) => {
                      this.store.updateAttribute('secao', e);
                      this.forceUpdate();
                    }}
                    headerDialog="Seção de Checklist"
                    emptyMessage="Selecione a seção de checklist"
                    nullMessage="Seção sem titulo"
                    dialogColumns={columnsSecaoChecklist}
                    searchFields={['titulo']}
                    canCreate
                    formPage={secaoChecklistForm}
                  />
                </FormField>
                <FormField columns={3} attribute="ordem" label="Ordem" rule={getRule('ordem')} submitted={submitted}>
                  <InputNumber
                    value={this.store.object.ordem}
                    onValueChange={(e) => updateAttribute('ordem', e)}
                    disabled
                  />
                </FormField>
                <FormField
                  columns={3}
                  attribute="suspenso"
                  label="Suspender"
                  rule={getRule('suspenso')}
                  submitted={submitted}
                  checkbox
                >
                  <Checkbox
                    style={{ marginTop: '33px' }}
                    onChange={(e) => updateAttribute('suspenso', e.checked)}
                    checked={this.store.object.suspenso}
                  />
                </FormField>
              </div>
              {this.renderActionButtons()}
            </form>
          </div>
        </>
      );
    } else {
      content = (
        <div className="card page">
          <AppBreadCrumb items={breacrumbItems} />
          <i className="pi pi-spin pi-spinner" style={{ fontSize: '2em' }}></i>
        </div>
      );
    }

    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        {content}
      </PermissionProxy>
    );
  }
}

ItemChecklistFormPage.propTypes = {
  id: PropTypes.any,
  action: PropTypes.any,
  history: PropTypes.any,
};

export default ItemChecklistFormPage;
