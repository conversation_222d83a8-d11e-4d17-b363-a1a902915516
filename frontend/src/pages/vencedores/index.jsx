import './style.scss';
import { observer } from 'mobx-react';
import { Column } from 'primereact/column';
import { DataTable } from 'primereact/datatable';
import React, { useState } from 'react';
import FcButton from 'fc/components/FcButton';
import { getV<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, getValue<PERSON><PERSON>, isValueValid } from 'fc/utils/utils';
import { Dialog } from 'primereact/dialog';
import { Fieldset } from 'primereact/fieldset';
import { InputText } from 'primereact/inputtext';
import InputMonetary from 'fc/components/InputMonetary';
import { Divider } from 'primereact/divider';
import FormField from 'fc/components/FormField';
import FcInputTextarea from 'fc/components/FcInputTextarea';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import LoteUnico from './loteUnico';
import FcDropdown from 'fc/components/FcDropdown';
import Lote from './lote';
import { SelectButton } from 'primereact/selectbutton';
import { ConfirmDialog } from 'primereact/confirmdialog';
import PropTypes from 'prop-types';
import VencedoresFormStore from '~/stores/vencedores/formStore';
import Resumo from './resumo';
import ResumoFracassados from './resumoFracassados';
import ResumoDesertos from './resumoDesertos';

const Vencedores = observer((props) => {
  const store = props.store;
  const [submitted, setSubmitted] = useState(false);
  const [tipoAdjudicacaoTemp, setTipoAdjudicacaoTemp] = useState(store.tipoAdjudicacao ?? 'ITEM');
  const [visibleConfirmDialogTipoAdjudicacao, setVisibleConfirmDialogTipoAdjudicacao] = useState(false);
  const [visibleDialogVencedores, setVisibleDialogVencedores] = useState(false);

  const renderFooter = () => {
    const { entidadeAntiga, readOnly } = props;
    return (
      <div className="p-mt-10 form-actions-dialog">
        <Divider />
        <span className="p-d-flex">
          <FcButton
            label={readOnly ? 'Voltar' : 'Cancelar'}
            type="button"
            className="p-ml-auto p-button-secondary p-mr-2"
            onClick={() => {
              setVisibleDialogVencedores(false);
            }}
          />
          {!readOnly && (
            <FcButton
              label={store.edit ? 'Salvar' : 'Adicionar'}
              disabled={!entidadeAntiga && !store?.vencedor[store.propItens]?.length > 0}
              onClick={() => {
                setSubmitted(true);
                store.adicionarVencedor(() => {
                  setVisibleDialogVencedores(false);
                  setSubmitted(false);
                });
              }}
            />
          )}
        </span>
      </div>
    );
  };

  const renderDialogVencedores = () => {
    const { labelLicitante, readOnly, reqModificacao } = props;
    const alreadyModified =
      !store.modifyVencedor && store.vencedor?.motivoAlteracao && store.vencedor?.obsMotivoAlteracao;

    return (
      <Dialog
        header={`Adicionar ${labelLicitante.charAt(0).toUpperCase() + labelLicitante.slice(1)}es`}
        visible={visibleDialogVencedores}
        style={{ width: '80vw' }}
        draggable={false}
        closable={false}
        footer={renderFooter()}
      >
        <form className="p-fluid p-formgrid p-grid">
          <div className="p-col-12">
            <Fieldset className="p-col-12" legend={labelLicitante.charAt(0).toUpperCase() + labelLicitante.slice(1)}>
              <div className="p-fluid p-formgrid p-grid">
                {store.vencedor && (
                  <FormField
                    columns={8}
                    attribute="licitante"
                    label="Licitante"
                    submitted={submitted}
                    rule={store.getRule('licitante')}
                  >
                    <div className="p-fluid p-grid p-formgrid">
                      <div className="p-col-10">
                        {store.edit && !store.modifyVencedor ? (
                          <InputText
                            value={
                              reqModificacao && store.vencedor.newLicitante
                                ? store.vencedor.newLicitante.nome?.trim()
                                : store.vencedor?.licitante?.nome
                            }
                            disabled
                          />
                        ) : (
                          <FcDropdown
                            value={store.vencedor.licitante}
                            onChange={(e) => store.updateLicitante(e.value)}
                            optionLabel="nome"
                            options={store.licitantes}
                            emptyMessage="Não há licitantes disponíveis"
                            placeholder={`Selecione um ${labelLicitante}`}
                          />
                        )}
                      </div>
                      {store.edit && !readOnly && (
                        <div className="p-col-2 align-items-center" style={{ display: 'flex' }}>
                          <FcButton
                            icon="pi pi-pencil"
                            className="p-button-sm p-button-success"
                            type="button"
                            onClick={() => store.changeModifyVencedor()}
                            disabled={alreadyModified}
                          />
                        </div>
                      )}
                    </div>
                  </FormField>
                )}
                {store.vencedor && !store.processoMigrado && (
                  <FormField
                    columns={3}
                    attribute="valor"
                    label="Valor"
                    submitted={submitted}
                    rule={store.getRule('valor')}
                  >
                    <div className="p-formgrid align-items-center">
                      <InputMonetary
                        placeholder="R$"
                        value={store.vencedor.valor}
                        onChange={(e) => store.updateAttributeLicitante('valor', e)}
                        decimalPlaces={2}
                        disabled={readOnly}
                        min={0}
                        max={store.vencedor.licitante?.valorMaximo}
                      />
                      {store.vencedor.licitante?.valorMaximo !== undefined && (
                        <div className="mt-2 ml-1" style={{ color: 'red' }}>
                          Valor máximo: {getValueMoney(store.vencedor.licitante?.valorMaximo)}
                        </div>
                      )}
                    </div>
                  </FormField>
                )}
                {(store.modifyVencedor || alreadyModified) && (
                  <>
                    <FormField
                      columns={12}
                      attribute="motivoAlteracao"
                      label="Motivo"
                      submitted={submitted}
                      rule={store.getRule('motivoAlteracao')}
                    >
                      <FcDropdown
                        inOrder
                        value={store.vencedor.motivoAlteracao}
                        onChange={(e) => store.updateAttributeLicitante('motivoAlteracao', e)}
                        optionLabel="text"
                        options={DadosEstaticosService.getMotivoAlteracao()}
                        placeholder="Selecione um motivo para a mudança"
                        disabled={alreadyModified}
                      />
                    </FormField>
                    <FormField
                      columns={12}
                      attribute="obsMotivoAlteracao"
                      label="Descrição"
                      submitted={submitted}
                      rule={store.getRule('obsMotivoAlteracao')}
                    >
                      <FcInputTextarea
                        rows={4}
                        value={store.vencedor.obsMotivoAlteracao}
                        onChange={(e) => store.updateAttributeLicitante('obsMotivoAlteracao', e)}
                        placeholder="Informe o motivo da mudança"
                        disabled={alreadyModified}
                        hideRemaningChars={alreadyModified}
                      />
                    </FormField>
                  </>
                )}
              </div>
            </Fieldset>
          </div>
        </form>
      </Dialog>
    );
  };

  const renderLotesItens = () => {
    const {
      showDesconto,
      showEspecificacao,
      readOnly,
      labelLicitante,
      hideFracasso,
      disableFracasso,
      hideDeserto,
      disableDeserto,
      hideTipoAdjudicacao,
    } = props;
    const decimalPlaces = store?.termoReferencia?.tresCasasDecimais ? 3 : 2;
    return (
      <>
        {store.termoReferencia && (
          <Fieldset
            className="p-col-12"
            legend={
              <b className="p-text-capitalize mb-3">
                {labelLicitante}es <span className={'lowercase'}>e respectivos itens</span>
              </b>
            }
          >
            {!readOnly ? (
              <div className="flex w-full flex-column">
                <div className="p-col-12 flex flex-column">
                  {!hideTipoAdjudicacao && (
                    <>
                      <b>
                        Adjudicado por: <span className="p-error"> *</span>
                      </b>
                      <SelectButton
                        options={DadosEstaticosService.getTipoAdjudicacao()}
                        optionLabel="text"
                        optionValue="value"
                        id="tipoAdjudicacao"
                        value={store.tipoAdjudicacao}
                        onChange={(e) => {
                          if (e?.target?.value && store.tipoAdjudicacao !== e.target.value) {
                            setTipoAdjudicacaoTemp(e.target.value);
                            if (store.vencedores.length > 0) {
                              setVisibleConfirmDialogTipoAdjudicacao(true);
                            } else {
                              store.resetAll();
                              if (store.tipoAdjudicacao == 'LOTE') {
                                store.updateTipoAdjudicacao('ITEM');
                                setTipoAdjudicacaoTemp(null);
                              } else {
                                store.updateTipoAdjudicacao('LOTE');
                                setTipoAdjudicacaoTemp(null);
                                store.initializeLotesLicitante();
                              }
                            }
                          }
                        }}
                      />
                    </>
                  )}
                  <ConfirmDialog
                    visible={visibleConfirmDialogTipoAdjudicacao}
                    onHide={() => {
                      setVisibleConfirmDialogTipoAdjudicacao(false);
                      setTipoAdjudicacaoTemp(null);
                    }}
                    message="Ao efetuar a substituição, todos os vencedores e lotes não selecionados serão perdidos. Você tem absoluta certeza disso?"
                    header="Confirmação"
                    icon="pi pi-exclamation-triangle"
                    accept={() => {
                      store.resetAll();
                      store.updateTipoAdjudicacao(tipoAdjudicacaoTemp);
                      setTipoAdjudicacaoTemp(null);
                      setVisibleConfirmDialogTipoAdjudicacao(false);

                      if (store.tipoAdjudicacao == 'LOTE') {
                        store.initializeLotesLicitante();
                      }
                    }}
                  />
                </div>
                <div className="p-col-12">
                  {store.tipoAdjudicacao === 'ITEM' ? (
                    <LoteUnico
                      showDesconto={showDesconto}
                      showEspecificacao={showEspecificacao}
                      decimalPlaces={decimalPlaces}
                      labelLicitante={labelLicitante}
                      hideFracasso={hideFracasso}
                      disableFracasso={disableFracasso}
                      hideDeserto={hideDeserto}
                      disableDeserto={disableDeserto}
                      store={store}
                    />
                  ) : (
                    store?.lotes?.map((lote, idx) => {
                      return (
                        <Lote
                          lote={lote}
                          collapsed={idx > 3}
                          key={idx}
                          showDesconto={showDesconto}
                          showEspecificacao={showEspecificacao}
                          decimalPlaces={decimalPlaces}
                          labelLicitante={labelLicitante}
                          hideFracasso={hideFracasso}
                          disableFracasso={disableFracasso}
                          hideDeserto={hideDeserto}
                          disableDeserto={disableDeserto}
                          store={store}
                        />
                      );
                    })
                  )}
                </div>
              </div>
            ) : (
              renderResumo()
            )}
          </Fieldset>
        )}
      </>
    );
  };

  const renderTableVencedores = () => {
    const { reqModificacao, valueFuncReqModificacao } = props;

    const columns = [
      {
        style: { width: reqModificacao ? '20%' : '50%' },
        field: 'licitante.nome',
        header: 'Nome',
        body: ({ licitante, newLicitante }) =>
          reqModificacao
            ? valueFuncReqModificacao(licitante, newLicitante, (value) => value?.nome ?? value)
            : licitante?.nome,
      },
      {
        style: { width: '160px' },
        field: 'licitante.cpfCnpj',
        header: 'CPF/CNPJ',
        body: ({ licitante, newLicitante }) =>
          reqModificacao
            ? valueFuncReqModificacao(licitante, newLicitante, (value) => value?.cpfCnpj ?? value)
            : licitante?.cpfCnpj,
      },
      reqModificacao && {
        style: { width: '25%', textOverflow: 'ellipsis' },
        field: 'motivoAlteracao',
        header: 'Motivo da Alteração',
        body: ({ motivoAlteracao, newMotivoAlteracao }) =>
          valueFuncReqModificacao(motivoAlteracao, newMotivoAlteracao, (value) => {
            const newValue = getValueByKey(value, DadosEstaticosService.getMotivoAlteracao());
            return isValueValid(newValue) && newValue !== '-' ? newValue : value;
          }),
      },
      reqModificacao && {
        style: { width: '20%', textOverflow: 'ellipsis' },
        field: 'obsMotivoAlteracao',
        header: 'Observação',
        body: ({ obsMotivoAlteracao, newObsMotivoAlteracao }) =>
          valueFuncReqModificacao(obsMotivoAlteracao, newObsMotivoAlteracao),
      },
      !store.processoMigrado && {
        style: { width: '10%' },
        field: 'valor',
        header: 'Valor',
        body: ({ valor, newValor }) =>
          reqModificacao ? valueFuncReqModificacao(valor, newValor) : getValueMoney(valor),
      },
      reqModificacao && {
        style: { width: '15%' },
        field: 'modificado',
        header: 'Modificação',
        body: ({ modificado }) => {
          if (modificado) {
            const { label, color } = DadosEstaticosService.getLabelRequisicaoModificacao()[modificado];
            return (
              <span
                className="product-badge p-d-flex p-jc-center"
                style={{
                  background: color,
                  borderRadius: 5,
                  color: 'white',
                  textAlign: 'center',
                  width: '80%',
                  maxWidth: '90px',
                }}
              >
                {label}
              </span>
            );
          }
        },
      },
      !reqModificacao &&
        !readOnly && {
          style: { width: '110px' },
          body: (vencedor) => {
            return (
              <>
                <FcButton
                  type="button"
                  icon="pi pi-pencil"
                  className="p-button-success p-mr-1"
                  onClick={() => {
                    store.editVencedor(vencedor, () => setVisibleDialogVencedores(true));
                  }}
                />
                <FcButton
                  type="button"
                  icon="pi pi-trash"
                  className="p-button-danger p-mr-1"
                  onClick={() => store.removeVencedor(vencedor)}
                />
              </>
            );
          },
        },
    ];

    const { labelLicitante } = props;

    return (
      <DataTable rowHover value={store.vencedores} emptyMessage={`Nenhum ${labelLicitante} adicionado`}>
        {columns.map((c, idx) => {
          return <Column key={`field-${idx}`} {...c} />;
        })}
      </DataTable>
    );
  };

  const renderResumo = () => {
    const { decimalPlaces, showDesconto, showEspecificacao } = props;
    return (
      <div className="flex flex-column">
        {store.itensGroupByVencedor?.map((v) => (
          <Resumo
            vencedor={v}
            decimalPlaces={decimalPlaces}
            showDesconto={showDesconto}
            showEspecificacao={showEspecificacao}
            labelLicitante={labelLicitante}
          />
        ))}
        <ResumoFracassados itensFracassados={store.lotesFracassados} />
        <ResumoDesertos itensDesertos={store.itensDesertos} />
      </div>
    );
  };

  const { labelLicitante, readOnly, entidadeAntiga } = props;

  return (
    <div className="p-grid">
      {entidadeAntiga && !readOnly && (
        <div className="p-col-3">
          <FcButton
            type="button"
            label={`Adicionar ${labelLicitante.charAt(0).toUpperCase() + labelLicitante.slice(1)}`}
            icon="pi pi-plus"
            disabled={!store.licitantes?.length > 0}
            onClick={() => {
              setVisibleDialogVencedores(true);
            }}
          />
        </div>
      )}
      {entidadeAntiga && <div className="p-col-12">{renderTableVencedores()}</div>}
      {!entidadeAntiga && renderLotesItens()}
      {renderDialogVencedores()}
    </div>
  );
});

Vencedores.propTypes = {
  readOnly: PropTypes.bool,
  store: PropTypes.objectOf(VencedoresFormStore).isRequired,
  labelLicitante: PropTypes.string,
  entidadeAntiga: PropTypes.bool,
  showDesconto: PropTypes.bool,
  showEspecificacao: PropTypes.bool,
  hideFracasso: PropTypes.bool,
  disableFracasso: PropTypes.bool,
  hideDeserto: PropTypes.bool,
  hideTipoAdjudicacao: PropTypes.bool,
};

export default Vencedores;
