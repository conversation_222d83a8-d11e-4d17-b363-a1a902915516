import React, { useState } from 'react';
import './style.scss';
import { observer } from 'mobx-react';
import classNames from 'classnames';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { InputText } from 'primereact/inputtext';
import InputMonetary from 'fc/components/InputMonetary';
import FcButton from 'fc/components/FcButton';
import { Tag } from 'primereact/tag';
import { getNumberFractionDigits, getNumberUnitThousands, getValue, getValueMoney, isValueValid } from 'fc/utils/utils';
import Panel from './panel';
import { Dropdown } from 'primereact/dropdown';
import Tooltip from 'fc/components/Tooltip';
import { Dialog } from 'primereact/dialog';
import { InputTextarea } from 'primereact/inputtextarea';
import { ConfirmDialog } from 'primereact/confirmdialog';
import PropTypes from 'prop-types';
import VencedoresFormStore from '~/stores/vencedores/formStore';
import { InputNumber } from 'primereact/inputnumber';

const Lote = observer((props) => {
  const [collapsed, setCollapsed] = useState(props.collapsed);
  const [editing, setEditing] = useState({});
  const [dialogVisible, setDialogVisible] = useState(false);
  const [loteFracassadoDialogVisible, setLoteFracassadoDialogVisible] = useState(false);
  const [loteDesertoDialogVisible, setLoteDesertoDialogVisible] = useState(false);
  const [loteNaoFracassadoDialogVisible, setLoteNaoFracassadoDialogVisible] = useState(false);
  const [loteNaoDesertoDialogVisible, setLoteNaoDesertoDialogVisible] = useState(false);

  const [obsItem, setObsItem] = useState('');
  const [itemSelected, setItemSelected] = useState({});
  const [itemError, setItemError] = useState({});

  const toggleLote = () => {
    setCollapsed(!collapsed);
  };

  const enableEdit = (item, field) => {
    if (!item.preenchido) {
      setEditing((oldState) => {
        if (!oldState[item.itemLote.id]) {
          oldState[item.itemLote.id] = {};
        }
        oldState[item.itemLote.id][field] = true;
        return { ...oldState };
      });
    }
  };

  const handleKey = (key, item, field) => {
    if (key === 'Enter') {
      setEditing((oldState) => {
        if (!oldState[item.itemLote.id]) {
          oldState[item.itemLote.id] = {};
        }
        oldState[item.itemLote.id][field] = true;
        return { ...oldState };
      });
    }
  };

  const _validateRequiredFields = (item) => {
    const requiredFields = ['marcaModelo', 'quantidade', 'valorUnitario'];
    const itemId = item.itemLote.id;
    const result = { ...itemError };
    let hasError = false;
    requiredFields.forEach((f) => {
      if (f == 'quantidade') {
        if (!isValueValid(item[f]) || item[f] <= 0) {
          result[itemId] = { ...result[itemId] };
          result[itemId][f] = true;
          hasError = true;
        } else {
          result[itemId] = { ...result[itemId] };
          result[itemId][f] = false;
        }
      } else if (!isValueValid(item[f])) {
        result[itemId] = { ...result[itemId] };
        result[itemId][f] = true;
        hasError = true;
      } else {
        result[itemId] = { ...result[itemId] };
        result[itemId][f] = false;
      }
    });
    setItemError(result);

    if (hasError) {
      const fieldsError = result[itemId];
      Object.keys(fieldsError).forEach((k) => {
        if (fieldsError[k]) {
          setEditing((oldState) => {
            if (!oldState[itemId]) {
              oldState[itemId] = {};
            }
            oldState[itemId][k] = true;
            return { ...oldState };
          });
        }
      });
    }
    return hasError;
  };

  const _renderDialogLoteFracassado = () => {
    return (
      <ConfirmDialog
        header={<strong>Fracassar Lote</strong>}
        message={
          <strong>
            Você está indicando o lote como{' '}
            <span style={{ color: 'rgba(255, 0, 0, 1)', textDecorationLine: 'underline' }}>fracassado</span>. Deseja
            continuar?
          </strong>
        }
        visible={loteFracassadoDialogVisible}
        onHide={() => {
          setLoteFracassadoDialogVisible(false);
        }}
        accept={() => {
          store.setLoteItemFracassado(lote);
          setLoteFracassadoDialogVisible(false);
        }}
      />
    );
  };

  const _renderDialogLoteNaoFracassado = () => {
    return (
      <ConfirmDialog
        header={<strong>Reverter Lote Fracassado</strong>}
        message={
          <strong>
            Você <span style={{ color: 'rgba(255, 0, 0, 1)', textDecorationLine: 'underline' }}>não está mais</span>{' '}
            indicando o lote como{' '}
            <span style={{ color: 'rgba(255, 0, 0, 1)', textDecorationLine: 'underline' }}>fracassado</span>. Deseja
            continuar?
          </strong>
        }
        visible={loteNaoFracassadoDialogVisible}
        onHide={() => {
          setLoteNaoFracassadoDialogVisible(false);
        }}
        accept={() => {
          store.setLoteItemNaoFracassado(lote);
          setLoteNaoFracassadoDialogVisible(false);
        }}
      />
    );
  };

  const _renderDialogLoteDeserto = () => {
    return (
      <ConfirmDialog
        header={<strong>Tornar Lote Deserto</strong>}
        message={
          <strong>
            Você está indicando o lote como{' '}
            <span style={{ color: 'rgba(255, 0, 0, 1)', textDecorationLine: 'underline' }}>deserto</span>. Deseja
            continuar?
          </strong>
        }
        visible={loteDesertoDialogVisible}
        onHide={() => {
          setLoteDesertoDialogVisible(false);
        }}
        accept={() => {
          store.setLoteItemDeserto(lote);
          setLoteDesertoDialogVisible(false);
        }}
      />
    );
  };

  const _renderDialogLoteNaoDeserto = () => {
    return (
      <ConfirmDialog
        header={<strong>Reverter Lote Deserto</strong>}
        message={
          <strong>
            Você <span style={{ color: 'rgba(255, 0, 0, 1)', textDecorationLine: 'underline' }}>não está mais</span>{' '}
            indicando o lote como{' '}
            <span style={{ color: 'rgba(255, 0, 0, 1)', textDecorationLine: 'underline' }}>deserto</span>. Deseja
            continuar?
          </strong>
        }
        visible={loteNaoDesertoDialogVisible}
        onHide={() => {
          setLoteNaoDesertoDialogVisible(false);
        }}
        accept={() => {
          store.setLoteItemNaoDeserto(lote);
          setLoteNaoDesertoDialogVisible(false);
        }}
      />
    );
  };

  const _renderDialogObs = () => {
    const { store } = props;
    return (
      <Dialog
        header="Observação"
        className="dialog-obs"
        visible={dialogVisible}
        onHide={() => setDialogVisible(false)}
        footer={
          <div>
            <FcButton
              label="Cancelar"
              icon="pi pi-times"
              className="p-button-danger"
              onClick={() => setDialogVisible(false)}
            />
            <FcButton
              label="Salvar"
              icon="pi pi-check"
              onClick={() => {
                store.updateAttributeItemLote(itemSelected.itemLote.id, 'observacao', obsItem);
                setDialogVisible(false);
              }}
            />
          </div>
        }
      >
        <div className="p-fluid p-formgrid p-grid">
          <InputTextarea
            onChange={(e) => setObsItem(e.target.value)}
            rows={4}
            value={obsItem}
            placeholder="Descreva a Observação"
          />
        </div>
      </Dialog>
    );
  };

  const _renderDataTable = () => {
    const { showEspecificacao, decimalPlaces, showDesconto, lote, store } = props;
    const loteCurrent = store.getLote(lote.id);
    const columns = [
      {
        header: 'Lote',
        style: { width: '15%' },
        body: (item) => store.getNomeLoteByitem(item),
      },
      {
        header: 'Código',
        body: (item) => getValue(item.itemLote?.materialDetalhamento?.codigo),
      },
      {
        header: 'Item',
        style: { width: '30%' },
        body: (item) =>
          `${item.itemLote.numero ? item.itemLote.numero + ' - ' : ''} ${getValue(
            item.itemLote.materialDetalhamento?.pdm?.nome
          )}`,
      },
      {
        header: 'Descrição Complementar',
        style: { width: '20%' },
        body: (item) => `${getValue(item.descricaoComplementar)}`,
      },
      {
        header: 'Unidade de Medida',
        body: (item) => getValue(item.itemLote?.unidadeMedida?.textUnidadeMedida),
      },
      !loteCurrent.fracassado &&
        !loteCurrent.deserto && {
          header: (
            <span>
              Marca/Modelo<span className="p-error"> *</span>
            </span>
          ),
          body: (item) =>
            editing[item.itemLote.id]?.marcaModelo || !item.preenchido ? (
              <InputText
                className={itemError[item.itemLote.id]?.marcaModelo ? 'p-invalid p-error' : ''}
                onChange={(e) => store.updateAttributeItemLote(item.itemLote.id, 'marcaModelo', e)}
                placeholder="Informe a Marca/Modelo"
                value={store.getItem(item.itemLote.id).marcaModelo}
                onKeyDown={({ key }) => handleKey(key, item, 'marcaModelo')}
              />
            ) : (
              <span
                className={classNames({
                  pointer: !store.getItem(item.itemLote.id).preenchido,
                  'p-pr-5 p-pl-5': !store.getItem(item.itemLote.id).marcaModelo,
                })}
                onClick={() => !loteCurrent.fracassado && !loteCurrent.deserto && enableEdit(item, 'marcaModelo')}
              >
                {getValue(store.getItem(item.itemLote.id).marcaModelo)}
              </span>
            ),
        },
      showEspecificacao &&
        !loteCurrent.fracassado &&
        !loteCurrent.deserto && {
          header: 'Especificação',
          body: (item) => {
            return !editing[item.itemLote.id]?.especificacao && !item.preenchido ? (
              <InputText
                onChange={(e) => store.updateAttributeItemLote(item.itemLote.id, 'especificacao', e)}
                placeholder="Informe a Especificação"
                value={store.getItem(item.itemLote.id).especificacao}
                onKeyDown={({ key }) => handleKey(key, item, 'especificacao')}
              />
            ) : (
              <span
                className={classNames('p-pr-5', {
                  pointer: !store.getItem(item.itemLote.id).preenchido,
                  'p-pr-5 p-pl-5': !store.getItem(item.itemLote.id).especificacao,
                })}
                onClick={() => !loteCurrent.fracassado && !loteCurrent.deserto && enableEdit(item, 'especificacao')}
              >
                {getValue(store.getItem(item.itemLote.id).especificacao)}
              </span>
            );
          },
        },
      {
        header: 'Quantidade',
        body: (item) =>
          store.tipoAdjudicacao != 'ITEM' ? (
            <span className="text-disabled">{getNumberUnitThousands(store.getItem(item.itemLote.id).quantidade)}</span>
          ) : !editing[item.itemLote.id]?.quantidade && !item.preenchido ? (
            <div className="flex flex-column gap-1">
              <InputMonetary
                className={itemError[item.itemLote.id]?.quantidade ? 'p-invalid p-error' : ''}
                value={store.getItem(item.itemLote.id).quantidade}
                onChange={(e) => store.updateAttributeItemLote(item.itemLote.id, 'quantidade', e)}
                min={0}
                max={item.itemLote.quantidade}
                decimalPlaces={item.itemLote.fracionario ? 2 : 0}
                onKeyDown={({ key }) => handleKey(key, item, 'quantidade')}
              />
              <span className="text-sm" style={{ color: '#1d4ed8' }}>
                Quantidade máxima: {getNumberUnitThousands(item.itemLote.quantidade)}
              </span>
            </div>
          ) : (
            <span
              className={classNames({ pointer: !store.getItem(item.itemLote.id).preenchido })}
              onClick={() => !loteCurrent.fracassado && !loteCurrent.deserto && enableEdit(item, 'quantidade')}
            >
              {getNumberUnitThousands(store.getItem(item.itemLote.id).quantidade)}
            </span>
          ),
      },
      !loteCurrent.fracassado &&
        !loteCurrent.deserto && {
          header: 'Valor Negociado',
          body: (item) =>
            !editing[item.itemLote.id]?.valorUnitario && !item.preenchido ? (
              <InputNumber
                value={store.getItem(item.itemLote.id).valorUnitario}
                onChange={(e) => handleInputValorNegociadoChange(e, item)}
                decimalPlaces={decimalPlaces}
                min={0}
                onKeyDown={({ key }) => handleKey(key, item, 'valorUnitario')}
                mode="currency"
                currency="BRL"
                locale="pt-BR"
              />
            ) : (
              <span
                className={classNames({ pointer: !store.getItem(item.itemLote.id).preenchido })}
                onClick={() => !loteCurrent.fracassado && !loteCurrent.deserto && enableEdit(item, 'valorUnitario')}
              >
                {getValueMoney(store.getItem(item.itemLote.id).valorUnitario, decimalPlaces)}
              </span>
            ),
        },
      showDesconto &&
        !loteCurrent.fracassado &&
        !loteCurrent.deserto && {
          header: 'Desconto(%)',
          body: (item) =>
            !editing[item.itemLote.id]?.desconto && !item.preenchido ? (
              <InputMonetary
                onChange={(e) => store.setDescontoLote(item.itemLote.id, e)}
                placeholder="Desconto"
                value={store.getItem(item.itemLote.id).desconto}
                decimalPlaces={2}
                min={0}
                max={100}
                onKeyDown={({ key }) => handleKey(key, item, 'desconto')}
              />
            ) : (
              <span
                className={classNames({ pointer: !store.getItem(item.itemLote.id).preenchido })}
                onClick={() => !loteCurrent.fracassado && !loteCurrent.deserto && enableEdit(item, 'desconto')}
              >
                {getNumberFractionDigits(store.getItem(item.itemLote.id).desconto, 2)}
              </span>
            ),
        },
      !loteCurrent.fracassado &&
        !loteCurrent.deserto && {
          header: 'Valor Total',
          body: (item) => (
            <span className="text-disabled">
              {getValueMoney(store.getItem(item.itemLote.id).valor, decimalPlaces, 0)}
            </span>
          ),
        },
      {
        header: 'Status',
        body: (item) => {
          if (loteCurrent.fracassado) return <Tag severity="warning" value="Fracassado" rounded />;
          else if (loteCurrent.deserto) return <Tag severity="warning" value="Deserto" rounded />;
          else if (store.getItem(item.itemLote.id).preenchido)
            return <Tag severity="success" value="Preenchido" rounded />;
          else {
            return <Tag severity="danger" value="Pendente" rounded />;
          }
        },
      },
      !loteCurrent.fracassado &&
        !loteCurrent.deserto && {
          header: 'Ações',
          body: (item) => (
            <>
              {!store.getItem(item.itemLote.id).preenchido ? (
                <FcButton
                  type="button"
                  icon="pi pi-check"
                  className="p-button-text toggle-button"
                  onClick={() => {
                    if (!_validateRequiredFields(item)) {
                      setEditing({});
                      store.updateAttributeItemLote(item.itemLote.id, 'preenchido', true);
                      setItemError((oldState) => {
                        oldState[item.itemLote.id] = false;
                        return { ...oldState };
                      });
                    }
                  }}
                />
              ) : (
                <FcButton
                  type="button"
                  icon="pi pi-pencil"
                  className="p-button-text toggle-button"
                  onClick={() => {
                    setEditing((oldState) => {
                      oldState[item.itemLote.id] = {};
                      return { ...oldState };
                    });
                    store.updateAttributeItemLote(item.itemLote.id, 'preenchido', false);
                  }}
                />
              )}
              <FcButton
                type="button"
                icon="pi pi-comment"
                className="p-button-text toggle-button"
                onClick={() => {
                  setItemSelected(item);
                  setObsItem(item.observacao);
                  setEditing({});
                  setDialogVisible(true);
                }}
              />
            </>
          ),
        },
    ];

    return (
      <DataTable
        rowHover
        value={store.getItensByLote(lote)}
        emptyMessage="Nenhum item disponível"
        style={{ maxWidth: '100%' }}
        className="p-datatable-sm "
      >
        {columns.map((c, idx) => {
          return <Column key={`field-${idx}`} {...c} />;
        })}
      </DataTable>
    );
  };

  const handleInputValorNegociadoChange = (e, item) => {
    const value = e.value !== undefined && !isNaN(e.value) ? e.value : null;
    store.updateAttributeItem(item.itemLote?.id, item.licitante.id, 'valorUnitario', value);
  };

  const _renderHeader = () => {
    const { lote, labelLicitante, hideFracasso, disableFracasso, store } = props;
    const loteCurrent = store.getLote(lote.id);
    return (
      <>
        <div className="flex-left pointer" onClick={() => toggleLote()}>
          <div className="info-lote">
            <span className="feedback">
              <i className={classNames('p-m-2', { 'pi pi-angle-right': collapsed, 'pi pi-angle-down': !collapsed })} />
            </span>
            <strong className="p-ml-2" onClick={() => toggleLote()}>
              {loteCurrent.nome}
            </strong>
          </div>
        </div>
        <div className="flex-right p-mr-2" style={{ alignItems: 'center' }}>
          <div style={{ marginRight: '15px' }}>
            {!loteCurrent.fracassado && !loteCurrent.deserto && (
              <div>
                <strong className="p-mr-3 p-text-capitalize">{labelLicitante}</strong>
                <Dropdown
                  className="sm:w-26rem lg:w-17rem xl:w-30rem"
                  value={loteCurrent?.licitante?.id}
                  onChange={(e) => store.createOrUpdateVencedorLote(loteCurrent, e?.value)}
                  optionLabel="nome"
                  optionValue="id"
                  options={store?.licitantes}
                  showClear
                  emptyMessage="Não há licitantes disponíveis"
                  placeholder="Selecione um Licitante"
                />
              </div>
            )}
          </div>
          <FcButton
            tooltip="Sinalizar Lote Fracassado"
            type="button"
            icon="pi pi-thumbs-down"
            className={classNames('p-button-raised p-button-danger', {
              hidden: hideFracasso || loteCurrent?.fracassado || loteCurrent?.deserto,
            })}
            onClick={() => {
              setLoteFracassadoDialogVisible(true);
            }}
            disabled={disableFracasso}
          />
          <FcButton
            tooltip="Reverter Lote Fracassado"
            type="button"
            icon="pi pi-replay"
            className={classNames('p-button-raised p-button-success', {
              hidden: hideFracasso || !loteCurrent?.fracassado,
            })}
            onClick={() => {
              setLoteNaoFracassadoDialogVisible(true);
            }}
            disabled={disableFracasso}
          />
          <FcButton
            tooltip="Sinalizar Lote Deserto"
            type="button"
            icon="pi pi-thumbs-down"
            className={classNames('p-button-raised p-button-warning ml-2', {
              hidden: hideFracasso || loteCurrent?.fracassado || loteCurrent?.deserto,
            })}
            onClick={() => {
              setLoteDesertoDialogVisible(true);
            }}
            disabled={disableFracasso}
          />
          <FcButton
            tooltip="Reverter Lote Deserto"
            type="button"
            icon="pi pi-replay"
            className={classNames('p-button-raised p-button-success ml-2', {
              hidden: hideFracasso || !loteCurrent?.deserto,
            })}
            onClick={() => {
              setLoteNaoDesertoDialogVisible(true);
            }}
            disabled={disableFracasso}
          />
          <div className="feedback">
            {store.getStatusLote(lote) === 'check' && (
              <span className="circle check">
                <Tooltip value="Preenchido" sideOffset={10} delayDuration={100}>
                  <i className="pi pi-check" />
                </Tooltip>
              </span>
            )}
            {store.getStatusLote(lote) === 'warning' && (
              <span className="circle warning">
                <Tooltip value="Parcialmente Preenchido" sideOffset={10} delayDuration={100}>
                  <span className="default">!</span>
                </Tooltip>
              </span>
            )}
            {store.getStatusLote(lote) === 'error' && (
              <span className="circle error">
                <Tooltip value="Não Preenchido" sideOffset={10} delayDuration={100}>
                  <i className="pi pi-times" />
                </Tooltip>
              </span>
            )}
          </div>
        </div>
      </>
    );
  };

  const { lote, store } = props;
  let content = <>Erro ao renderizar o lote</>;
  if (lote) {
    content = (
      <div>
        <Panel
          className={`panel-${store.getStatusLote(lote)}`}
          header={_renderHeader()}
          content={_renderDataTable()}
          collapsed={collapsed}
        />
        {_renderDialogObs()}
        {_renderDialogLoteFracassado()}
        {_renderDialogLoteNaoFracassado()}
        {_renderDialogLoteDeserto()}
        {_renderDialogLoteNaoDeserto()}
      </div>
    );
  }
  return content;
});

Lote.propTypes = {
  lote: PropTypes.object,
  collapsed: PropTypes.bool,
  key: PropTypes.any,
  showDesconto: PropTypes.bool,
  showEspecificacao: PropTypes.bool,
  decimalPlaces: PropTypes.number,
  labelLicitante: PropTypes.bool,
  hideFracasso: PropTypes.bool,
  disableFracasso: PropTypes.bool,
  store: PropTypes.objectOf(VencedoresFormStore).isRequired,
};

export default Lote;
