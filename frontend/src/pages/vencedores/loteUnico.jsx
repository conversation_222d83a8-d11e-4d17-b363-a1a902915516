import React, { useState } from 'react';
import './style.scss';
import { observer } from 'mobx-react';
import { InputText } from 'primereact/inputtext';
import FcButton from 'fc/components/FcButton';
import { Dropdown } from 'primereact/dropdown';
import { Dialog } from 'primereact/dialog';
import { InputTextarea } from 'primereact/inputtextarea';
import { PickList } from 'primereact/picklist';
import {
  getNumberFractionDigits,
  getNumberUnitThousands,
  getValueMoney,
  showNotification,
  getValue,
  isValueValid,
  getLightenColor,
} from 'fc/utils/utils';
import Tooltip from 'fc/components/Tooltip';
import classNames from 'classnames';
import InputMonetary from 'fc/components/InputMonetary';
import { Tag } from 'primereact/tag';
import { DataTable } from 'primereact/datatable';
import { Column } from 'react-virtualized';
import Panel from './panel';
import { ConfirmDialog } from 'primereact/confirmdialog';
import PropTypes from 'prop-types';
import VencedoresFormStore from '~/stores/vencedores/formStore';
import { InputNumber } from 'primereact/inputnumber';

const LoteUnico = observer((props) => {
  const [collapsed, setCollapsed] = useState({});
  const [editing, setEditing] = useState({});
  const [editingDesertos, setEditingDesertos] = useState({});
  const [dialogVisible, setDialogVisible] = useState(false);
  const [itemFracassadoPickListVisible, setItemFracassadoPickListVisible] = useState(false);
  const [itemFracassadoDialogVisible, setItemFracassadoDialogVisible] = useState(false);
  const [itemDesertoPickListVisible, setItemDesertoPickListVisible] = useState(false);
  const [itemDesertoDialogVisible, setItemDesertoDialogVisible] = useState(false);
  const [obsItem, setObsItem] = useState('');
  const [itemSelected, setItemSelected] = useState({});
  const [itemError, setItemError] = useState({});
  const [itemErrorDesertos, setItemErrorDesertos] = useState({});

  const [vencedor, setVencedor] = useState({});
  const [sourceFilter, setSourceFilter] = useState('');
  const [targetFilter, setTargetFilter] = useState('');

  const {
    labelLicitante,
    showEspecificacao,
    decimalPlaces,
    showDesconto,
    hideFracasso,
    disableFracasso,
    hideDeserto,
    disableDeserto,
    store,
  } = props;

  const itemTemplateFracassoDeserto = (item) => {
    let quantidadeDisponivel = getNumberUnitThousands(store.getQuantidadeItemDisponivel(item.id));
    let quantidadeTotal = getNumberUnitThousands(item.quantidade);

    return (
      <div className="flex flex-wrap p-2 align-items-center gap-3">
        <div className="flex-1 flex flex-column gap-2">
          <span className="font-bold">
            {item.numero ? item.numero + ' - ' : ''}
            {getValue(item.materialDetalhamento?.pdm?.nome)}
          </span>
          <div className="flex gap-2">
            <Tag
              value={store.getNomeLoteByitem(item)}
              icon="pi pi-box"
              style={{
                backgroundColor: '#fbf7ff',
                color: '#8f48d2',
                border: '1px solid #8f48d2',
              }}
            />
            <Tag
              value={`Quantidade: ${quantidadeDisponivel}/${quantidadeTotal}`}
              className="p-d-flex"
              style={{
                backgroundColor: getLightenColor('#2F83DC', 0.7),
                color: '#2F83DC',
                border: `1px solid #2F83DC`,
              }}
            />
          </div>
        </div>
      </div>
    );
  };

  const sourceItemTemplate = (item) => {
    let quantidadeDisponivel = getNumberUnitThousands(store.getQuantidadeItemDisponivel(item.id));
    let quantidadeTotal = getNumberUnitThousands(item.quantidade);

    return (
      <div className="flex flex-wrap p-2 align-items-center gap-3">
        <div className="flex-1 flex flex-column gap-2">
          <span className="font-bold">
            {item.numero ? item.numero + ' - ' : ''}
            {getValue(item.materialDetalhamento?.pdm?.nome)}
          </span>
          <div className="flex gap-2">
            <Tag
              value={store.getNomeLoteByitem(item)}
              icon="pi pi-box"
              style={{
                backgroundColor: '#fbf7ff',
                color: '#8f48d2',
                border: '1px solid #8f48d2',
              }}
            />
            <Tag
              value={`Quantidade: ${quantidadeDisponivel}/${quantidadeTotal}`}
              className="p-d-flex"
              style={{
                backgroundColor: getLightenColor('#2F83DC', 0.7),
                color: '#2F83DC',
                border: `1px solid #2F83DC`,
              }}
            />
          </div>
        </div>
      </div>
    );
  };

  const targetItemTemplate = (item) => {
    let quantidadeUsada = getNumberUnitThousands(store.getItemAssociadoByItemLoteVencedor(item, vencedor)?.quantidade);
    let quantidadeTotal = getNumberUnitThousands(item.quantidade);

    return (
      <div className="flex flex-wrap p-2 align-items-center gap-3">
        <div className="flex-1 flex flex-column gap-2">
          <span className="font-bold">
            {item.numero ? item.numero + ' - ' : ''}
            {getValue(item.materialDetalhamento?.pdm?.nome)}
          </span>
          <div className="flex gap-2">
            <Tag
              value={store.getNomeLoteByitem(item)}
              icon="pi pi-box"
              style={{
                backgroundColor: '#fbf7ff',
                color: '#8f48d2',
                border: '1px solid #8f48d2',
              }}
            />
            <Tag
              value={`Quantidade: ${quantidadeUsada}/${quantidadeTotal}`}
              className="p-d-flex"
              style={{
                backgroundColor: getLightenColor('#2F83DC', 0.7),
                color: '#2F83DC',
                border: `1px solid #2F83DC`,
              }}
            />
          </div>
        </div>
      </div>
    );
  };

  const targetItemDesertoTemplate = (item) => {
    let quantidadeUsada = getNumberUnitThousands(store.getItemAssociadoByItemLoteDeserto(item)?.quantidade);

    return (
      <div className="flex flex-wrap p-2 align-items-center gap-3">
        <div className="flex-1 flex flex-column gap-2">
          <span className="font-bold">
            {item.numero ? item.numero + ' - ' : ''}
            {getValue(item.materialDetalhamento?.pdm?.nome)}
          </span>
          <div className="flex gap-2">
            <Tag
              value={store.getNomeLoteByitem(item)}
              icon="pi pi-box"
              style={{
                backgroundColor: '#fbf7ff',
                color: '#8f48d2',
                border: '1px solid #8f48d2',
              }}
            />
            <Tag
              value={`Quantidade: ${quantidadeUsada}`}
              className="p-d-flex"
              style={{
                backgroundColor: getLightenColor('#2F83DC', 0.7),
                color: '#2F83DC',
                border: `1px solid #2F83DC`,
              }}
            />
          </div>
        </div>
      </div>
    );
  };

  const _renderVencedoresItens = () => {
    return (
      <div>
        <div className="p-fluid p-formgrid p-grid">
          <div className="p-field p-col-3">
            <strong className=" p-text-capitalize">{labelLicitante}</strong>
            <Dropdown
              value={vencedor}
              onChange={(e) => setVencedor(e.value)}
              optionLabel="nome"
              options={store?.licitantes}
              emptyMessage="Não há licitantes disponíveis"
              placeholder="Selecione um Licitante"
            />
          </div>
          <div className="p-field p-col-3" style={{ paddingRight: '30px' }}>
            <strong className=" p-text-capitalize">Lote</strong>
            <Dropdown
              id="dropdown-lote"
              defaultValue={store.selectedLote}
              value={store.selectedLote}
              onChange={(e) => store.updateSelectedLote(e.value)}
              optionLabel="nome"
              options={store?.lotes}
              emptyMessage="Não há lotes disponíveis"
              placeholder="Selecione um lote"
            />
          </div>
          <div className="flex align-items-center" style={{ paddingLeft: '30px' }}>
            {!hideFracasso && (
              <FcButton
                icon="pi pi-thumbs-down"
                label="Sinalizar Itens Fracassados"
                type="button"
                className="p-button-raised p-button-danger"
                onClick={() => {
                  store.setOldLotesItensFracassados();
                  setItemFracassadoPickListVisible(true);
                }}
                disabled={disableFracasso}
              />
            )}
          </div>
          <div className="flex align-items-center ml-4">
            {!hideDeserto && (
              <FcButton
                icon="pi pi-thumbs-down"
                label="Sinalizar Itens Desertos"
                type="button"
                className="p-button-raised p-button-warning"
                onClick={() => {
                  store.setOldLotesItensDesertos();
                  setItemDesertoPickListVisible(true);
                }}
                disabled={disableDeserto}
              />
            )}
          </div>
          <span className="p-field p-col-6" style={{ paddingRight: '30px' }}>
            <span className="p-input-icon-left">
              <i className="pi pi-search" />
              <InputText value={sourceFilter} onChange={(e) => setSourceFilter(e.target.value?.toLocaleLowerCase())} />
            </span>
          </span>
          <span className="p-field p-col-6" style={{ paddingLeft: '30px' }}>
            <span className="p-input-icon-left">
              <i className="pi pi-search" />
              <InputText value={targetFilter} onChange={(e) => setTargetFilter(e.target.value?.toLocaleLowerCase())} />
            </span>
          </span>
        </div>
        <PickList
          source={store
            .getItensDisponiveisLoteUnico(vencedor)
            .filter(
              (item) =>
                !sourceFilter ||
                item.materialDetalhamento?.pdm?.nome?.toLocaleLowerCase()?.includes(sourceFilter) ||
                sourceFilter == item.numero
            )}
          target={store
            .getItensAssociadosLoteUnico(vencedor)
            .filter(
              (item) =>
                !targetFilter ||
                item.materialDetalhamento?.pdm?.nome?.descricaoDetalhamento?.pdm?.nome
                  ?.toLocaleLowerCase()
                  ?.includes(targetFilter) ||
                targetFilter == item.numero
            )}
          onChange={(e) => {
            if (vencedor?.id && store.licitantes.some((l) => l.id === vencedor.id)) {
              store.createOrUpdateVencedorLoteUnico(e.target, vencedor);
            } else {
              showNotification(
                'warn',
                null,
                <span>
                  Selecione um <span className="p-text-capitalize">{labelLicitante}</span>
                </span>
              );
            }
          }}
          dataKey="id"
          sourceItemTemplate={sourceItemTemplate}
          targetItemTemplate={targetItemTemplate}
          sourceHeader="Itens Disponíveis"
          targetHeader="Itens Adicionados"
          showSourceControls={false}
          showTargetControls={false}
          sourceStyle={{ height: '342px' }}
          targetStyle={{ height: '342px' }}
        />
      </div>
    );
  };

  const toggleLote = (id) => {
    const newCollpsed = { ...collapsed };
    newCollpsed[id] = !collapsed[id];
    setCollapsed(newCollpsed);
  };

  const getId = (item) => `item-${item?.itemLote?.id}-licitante-${item?.licitante?.id}`;

  const enableEdit = (item, field) => {
    if (!item.preenchido) {
      setEditing((oldState) => {
        if (!oldState[getId(item)]) {
          oldState[getId(item)] = {};
        }
        oldState[getId(item)][field] = true;
        return { ...oldState };
      });
    }
  };

  const enableEditDesertos = (item, field) => {
    if (!item.preenchido) {
      setEditingDesertos((oldState) => {
        if (!oldState[getId(item)]) {
          oldState[getId(item)] = {};
        }
        oldState[getId(item)][field] = true;
        return { ...oldState };
      });
    }
  };

  const handleKey = (key, item, field) => {
    if (key === 'Enter') {
      setEditing((oldState) => {
        if (!oldState[getId(item)]) {
          oldState[getId(item)] = {};
        }
        oldState[getId(item)][field] = true;
        return { ...oldState };
      });
    }
  };

  const _validateRequiredFields = (item) => {
    const requiredFields = ['marcaModelo', 'quantidade', 'valorUnitario'];
    const itemId = getId(item);
    const result = { ...itemError };
    let hasError = false;
    requiredFields.forEach((f) => {
      if (f == 'quantidade' || f == 'valorUnitario') {
        if (!isValueValid(item[f]) || item[f] <= 0) {
          result[itemId] = { ...result[itemId] };
          result[itemId][f] = true;
          hasError = true;
        } else {
          result[itemId] = { ...result[itemId] };
          result[itemId][f] = false;
        }
      } else if (!isValueValid(item[f])) {
        result[itemId] = { ...result[itemId] };
        result[itemId][f] = true;
        hasError = true;
      } else {
        result[itemId] = { ...result[itemId] };
        result[itemId][f] = false;
      }
    });
    setItemError(result);

    if (hasError) {
      const fieldsError = result[itemId];
      Object.keys(fieldsError).forEach((k) => {
        if (fieldsError[k]) {
          setEditing((oldState) => {
            if (!oldState[itemId]) {
              oldState[itemId] = {};
            }
            oldState[itemId][k] = true;
            return { ...oldState };
          });
        }
      });
    }
    return hasError;
  };

  const _validateRequiredFieldsDesertos = (item) => {
    const requiredFields = ['quantidade'];
    const itemId = getId(item);
    const result = { ...itemError };
    let hasError = false;
    requiredFields.forEach((f) => {
      if (f == 'quantidade') {
        if (!isValueValid(item[f]) || item[f] <= 0) {
          result[itemId] = { ...result[itemId], quantidade: true };
          hasError = true;
        } else {
          result[itemId] = { ...result[itemId], quantidade: false };
        }
      }
    });
    setItemErrorDesertos(result);

    if (hasError) {
      const fieldsError = result[itemId];
      Object.keys(fieldsError).forEach((k) => {
        if (fieldsError[k]) {
          setEditingDesertos((oldState) => {
            if (!oldState[itemId]) {
              oldState[itemId] = {};
            }
            oldState[itemId][k] = true;
            return { ...oldState };
          });
        }
      });
    }
    return hasError;
  };

  const _renderDialogObs = () => {
    return (
      <Dialog
        header="Observação"
        className="dialog-obs"
        visible={dialogVisible}
        onHide={() => setDialogVisible(false)}
        footer={
          <div>
            <FcButton
              label="Cancelar"
              icon="pi pi-times"
              className="p-button-danger"
              onClick={() => setDialogVisible(false)}
            />
            <FcButton
              label="Salvar"
              icon="pi pi-check"
              onClick={() => {
                store.updateAttributeItem(itemSelected.itemLote?.id, itemSelected.licitante.id, 'observacao', obsItem);
                setDialogVisible(false);
              }}
            />
          </div>
        }
      >
        <div className="p-fluid p-formgrid p-grid">
          <InputTextarea
            onChange={(e) => setObsItem(e.target.value)}
            rows={4}
            value={obsItem}
            placeholder="Descreva a Observação"
          />
        </div>
      </Dialog>
    );
  };

  const _renderDialogItensFracassados = () => {
    return (
      <ConfirmDialog
        header={<strong>Fracassar Itens</strong>}
        message={
          <strong>
            Você está indicando os itens como{' '}
            <span style={{ color: 'rgba(255, 0, 0, 1)', textDecorationLine: 'underline' }}>fracassados</span> ou
            tornando-os{' '}
            <span style={{ color: 'rgba(77, 167, 59, 1)', textDecorationLine: 'underline' }}>disponíveis</span>{' '}
            novamente.Tem certeza?
          </strong>
        }
        visible={itemFracassadoDialogVisible}
        onHide={() => {
          setItemFracassadoDialogVisible(false);
        }}
        accept={() => {
          store.setOldLotesItensFracassados();
          store.updateLotesFracassados();
          setItemFracassadoDialogVisible(false);
          setItemFracassadoPickListVisible(false);
        }}
      />
    );
  };

  const _renderPickListItensFracassados = () => {
    return (
      <Dialog
        header="Selecione os itens que você deseja fracassar"
        visible={itemFracassadoPickListVisible}
        modal
        footer={
          <div className="p-mt-2">
            <span className="p-mr-1 p-d-inline p-d-flex align-items-center">
              <FcButton
                label="Cancelar"
                type="button"
                className="p-ml-auto p-button-secondary p-mr-2"
                onClick={() => {
                  store.resetLotesFracassados();
                  setItemFracassadoPickListVisible(false);
                }}
              />
              <FcButton
                label="Confirmar"
                onClick={() => {
                  setItemFracassadoDialogVisible(true);
                }}
              />
            </span>
          </div>
        }
        style={{ width: '80vw' }}
        onHide={() => {
          setItemFracassadoPickListVisible(false);
          store.resetLotesFracassados();
        }}
        draggable={false}
        resizable={false}
      >
        <div className="p-fluid p-formgrid p-grid">
          <span className="p-field p-col-6" style={{ paddingRight: '30px' }}>
            <span className="p-input-icon-left">
              <i className="pi pi-search" />
              <InputText value={sourceFilter} onChange={(e) => setSourceFilter(e.target.value?.toLocaleLowerCase())} />
            </span>
          </span>
          <span className="p-field p-col-6" style={{ paddingLeft: '30px' }}>
            <span className="p-input-icon-left">
              <i className="pi pi-search" />
              <InputText value={targetFilter} onChange={(e) => setTargetFilter(e.target.value?.toLocaleLowerCase())} />
            </span>
          </span>
        </div>
        <PickList
          source={store.itensDisponiveisParaFracasso?.filter(
            (item) =>
              !sourceFilter ||
              item.materialDetalhamento?.pdm?.nome?.toLocaleLowerCase()?.includes(sourceFilter) ||
              sourceFilter == item.numero
          )}
          target={store.itensFracassados?.filter(
            (item) =>
              !targetFilter ||
              item.materialDetalhamento?.pdm?.nome?.descricaoDetalhamento?.pdm?.nome
                ?.toLocaleLowerCase()
                ?.includes(targetFilter) ||
              targetFilter == item.numero
          )}
          onChange={(e) => {
            store.createOrUpdateLoteUnicoFracassado(e.target);
          }}
          itemTemplate={itemTemplateFracassoDeserto}
          dataKey="id"
          sourceHeader="Itens Disponíveis"
          targetHeader="Itens Fracassados"
          showSourceControls={false}
          showTargetControls={false}
          sourceStyle={{ height: '342px' }}
          targetStyle={{ height: '342px' }}
        />
      </Dialog>
    );
  };

  const _renderDialogItensDesertos = () => {
    return (
      <ConfirmDialog
        header={<strong>Desertar Itens</strong>}
        message={
          <strong>
            Você está indicando os itens como{' '}
            <span style={{ color: 'rgba(255, 0, 0, 1)', textDecorationLine: 'underline' }}>desertos</span> ou
            tornando-os{' '}
            <span style={{ color: 'rgba(77, 167, 59, 1)', textDecorationLine: 'underline' }}>disponíveis</span>{' '}
            novamente.Tem certeza?
          </strong>
        }
        visible={itemDesertoDialogVisible}
        onHide={() => {
          setItemDesertoDialogVisible(false);
        }}
        accept={() => {
          store.setOldLotesItensDesertos();
          store.updateItensDesertos();
          setItemDesertoDialogVisible(false);
          setItemDesertoPickListVisible(false);
        }}
      />
    );
  };

  const _renderPickListItensDesertos = () => {
    return (
      <Dialog
        header="Selecione os itens que você deseja desertar"
        visible={itemDesertoPickListVisible}
        modal
        footer={
          <div className="p-mt-2">
            <span className="p-mr-1 p-d-inline p-d-flex align-items-center">
              <FcButton
                label="Cancelar"
                type="button"
                className="p-ml-auto p-button-secondary p-mr-2"
                onClick={() => {
                  store.resetItensDesertos();
                  setItemDesertoPickListVisible(false);
                }}
              />
              <FcButton
                label="Confirmar"
                onClick={() => {
                  setItemDesertoDialogVisible(true);
                }}
              />
            </span>
          </div>
        }
        style={{ width: '80vw' }}
        onHide={() => {
          setItemDesertoPickListVisible(false);
          store.resetItensDesertos();
        }}
        draggable={false}
        resizable={false}
      >
        <div className="p-fluid p-formgrid p-grid">
          <span className="p-field p-col-6" style={{ paddingRight: '30px' }}>
            <span className="p-input-icon-left">
              <i className="pi pi-search" />
              <InputText value={sourceFilter} onChange={(e) => setSourceFilter(e.target.value?.toLocaleLowerCase())} />
            </span>
          </span>
          <span className="p-field p-col-6" style={{ paddingLeft: '30px' }}>
            <span className="p-input-icon-left">
              <i className="pi pi-search" />
              <InputText value={targetFilter} onChange={(e) => setTargetFilter(e.target.value?.toLocaleLowerCase())} />
            </span>
          </span>
        </div>
        <PickList
          source={store.itensDisponiveisParaDesertar?.filter(
            (item) =>
              !sourceFilter ||
              item.materialDetalhamento?.pdm?.nome?.toLocaleLowerCase()?.includes(sourceFilter) ||
              sourceFilter == item.numero
          )}
          target={store.itensDesertosList?.filter(
            (item) =>
              !targetFilter ||
              item.materialDetalhamento?.pdm?.nome?.descricaoDetalhamento?.pdm?.nome
                ?.toLocaleLowerCase()
                ?.includes(targetFilter) ||
              targetFilter == item.numero
          )}
          onChange={(e) => {
            store.createOrUpdateItensDesertos(e.target);
          }}
          sourceItemTemplate={itemTemplateFracassoDeserto}
          targetItemTemplate={targetItemDesertoTemplate}
          dataKey="id"
          sourceHeader="Itens Disponíveis"
          targetHeader="Itens Desertos"
          showSourceControls={false}
          showTargetControls={false}
          sourceStyle={{ height: '342px' }}
          targetStyle={{ height: '342px' }}
        />
      </Dialog>
    );
  };

  const _renderDataTable = (itens) => {
    const columns = [
      {
        header: 'Lote',
        style: { width: '10%' },
        body: (item) => store.getNomeLoteByitem(item),
      },
      {
        header: 'Código',
        body: (item) => getValue(item.itemLote?.materialDetalhamento?.codigo),
      },
      {
        header: 'Item',
        style: { width: '12%' },
        body: (item) =>
          `${item.itemLote.numero ? item.itemLote.numero + ' - ' : ''} ${getValue(
            item.itemLote.materialDetalhamento?.pdm?.nome
          )}`,
      },
      {
        header: 'Descrição Complementar',
        style: { width: '20%' },
        body: (item) => getValue(item.itemLote?.descricaoComplementar),
      },
      {
        header: 'Unidade de Medida',
        body: (item) => getValue(item.itemLote?.unidadeMedida?.textUnidadeMedida),
      },
      {
        header: (
          <span>
            Marca/Modelo<span className="p-error"> *</span>
          </span>
        ),
        body: (item) =>
          editing[getId(item)]?.marcaModelo || !item.preenchido ? (
            <div className="item-container others">
              <InputText
                className={itemError[getId(item)]?.marcaModelo ? 'p-invalid p-error' : ''}
                onChange={(e) => store.updateAttributeItem(item.itemLote?.id, item.licitante.id, 'marcaModelo', e)}
                placeholder="Informe a Marca/Modelo"
                value={item.marcaModelo}
                onKeyDown={({ key }) => handleKey(key, item, 'marcaModelo')}
              />
            </div>
          ) : (
            <span
              className={classNames({ pointer: !item.preenchido, 'p-pr-5 p-pl-5': !item.marcaModelo })}
              onClick={() => enableEdit(item, 'marcaModelo')}
            >
              {getValue(item.marcaModelo)}
            </span>
          ),
      },
      showEspecificacao && {
        header: 'Especificação',
        body: (item) =>
          !editing[getId(item)]?.especificacao && !item.preenchido ? (
            <div className="item-container others">
              <InputText
                onChange={(e) => store.updateAttributeItem(item.itemLote?.id, item.licitante.id, 'especificacao', e)}
                placeholder="Informe a Especificação"
                value={item.especificacao}
                onKeyDown={({ key }) => handleKey(key, item, 'especificacao')}
              />
            </div>
          ) : (
            <span
              className={classNames({ pointer: !item.preenchido, 'p-pr-5 p-pl-5': !item.especificacao })}
              onClick={() => enableEdit(item, 'especificacao')}
            >
              {getValue(item.especificacao)}
            </span>
          ),
      },
      {
        header: (
          <span>
            Quantidade<span className="p-error"> *</span>
          </span>
        ),
        body: (item) =>
          !editing[getId(item)]?.quantidade && !item.preenchido ? (
            <div className="flex flex-column gap-1 item-container qtd">
              <InputMonetary
                className={itemError[getId(item)]?.quantidade ? 'p-invalid p-error' : ''}
                value={item.quantidade}
                onChange={(e) => {
                  store.updateAttributeItem(item.itemLote.id, item.licitante.id, 'quantidade', e);
                }}
                decimalPlaces={item.itemLote.fracionario ? 2 : 0}
                min={0}
                max={store.getQuantidadeItemDisponivel(item.itemLote.id, item.quantidade)}
                onKeyDown={({ key }) => handleKey(key, item, 'quantidade')}
              />
              <span className="text-sm" style={{ color: '#1d4ed8' }}>
                {`Quantidade máxima: ${getNumberUnitThousands(
                  store.getQuantidadeItemDisponivel(item.itemLote.id, item.quantidade)
                )}`}
              </span>
            </div>
          ) : (
            <span
              className={classNames({
                pointer: !item.preenchido,
                'p-pr-5 p-pl-5': !item.quantidade || !item.quantidade <= 0,
              })}
              onClick={() => enableEdit(item, 'quantidade')}
            >
              {getNumberUnitThousands(item.quantidade)}
            </span>
          ),
      },
      {
        header: 'Valor Negociado',
        body: (item) =>
          !editing[getId(item)]?.valorUnitario && !item.preenchido ? (
            <div className="item-container others">
              <InputNumber
                className={itemError[getId(item)]?.valorUnitario ? 'p-invalid p-error' : ''}
                value={item.valorUnitario}
                onChange={(e) => handleInputValorNegociadoChange(e, item)}
                decimalPlaces={decimalPlaces}
                min={0}
                onKeyDown={({ key }) => handleKey(key, item, 'valorUnitario')}
                mode="currency"
                currency="BRL"
                locale="pt-BR"
              />
            </div>
          ) : (
            <span
              className={classNames({ pointer: !item.preenchido })}
              onClick={() => enableEdit(item, 'valorUnitario')}
            >
              {getValueMoney(item.valorUnitario, decimalPlaces)}
            </span>
          ),
      },
      showDesconto && {
        header: 'Desconto(%)',
        body: (item) =>
          !editing[getId(item)]?.desconto && !item.preenchido ? (
            <div className="item-container others">
              <InputMonetary
                onChange={(e) => store.setDescontoItem(item.itemLote?.id, item.licitante.id, e)}
                placeholder="Desconto"
                value={item.desconto}
                decimalPlaces={2}
                min={0}
                max={100}
                onKeyDown={({ key }) => handleKey(key, item, 'desconto')}
              />
            </div>
          ) : (
            <span className={classNames({ pointer: !item.preenchido })} onClick={() => enableEdit(item, 'desconto')}>
              {getNumberFractionDigits(item.desconto)}
            </span>
          ),
      },
      {
        header: 'Valor Total',
        body: (item) => (
          <div className="item-container others">
            <span className="text-disabled">{getValueMoney(item.valor, decimalPlaces, 0)}</span>
          </div>
        ),
      },
      {
        header: 'Status',
        body: (item) =>
          item.preenchido ? (
            <Tag severity="success" value="Preenchido" rounded />
          ) : (
            <Tag severity="danger" value="Pendente" rounded />
          ),
      },
      {
        header: 'Ações',
        body: (item) => (
          <>
            {!item.preenchido && (
              <FcButton
                type="button"
                icon="pi pi-check"
                className="p-button-text toggle-button"
                onClick={() => {
                  if (!_validateRequiredFields(item)) {
                    setEditing((oldState) => {
                      oldState[getId(item)] = {};
                      return { ...oldState };
                    });
                    store.updateAttributeItem(item.itemLote?.id, item.licitante.id, 'preenchido', true);
                    setItemError((oldState) => {
                      oldState[getId(item)] = false;
                      return { ...oldState };
                    });
                  }
                }}
              />
            )}
            {item.preenchido && (
              <FcButton
                type="button"
                icon="pi pi-pencil"
                className="p-button-text toggle-button"
                onClick={() => {
                  setEditing((oldState) => {
                    oldState[getId(item)] = {};
                    return { ...oldState };
                  });
                  store.updateAttributeItem(item.itemLote?.id, item.licitante.id, 'preenchido', false);
                }}
              />
            )}
            <FcButton
              type="button"
              icon="pi pi-comment"
              className="p-button-text toggle-button"
              onClick={() => {
                setItemSelected(item);
                setObsItem(item.observacao);
                setEditing({});
                setDialogVisible(true);
              }}
            />
            <FcButton
              type="button"
              icon="pi pi-trash"
              className="p-button-text toggle-button"
              onClick={() => {
                store.removeItemVencedor(item);
              }}
            />
          </>
        ),
      },
    ];
    return (
      <div className="card p-fluid">
        <DataTable
          rowHover
          value={itens}
          emptyMessage="Nenhum item disponível"
          style={{ maxWidth: '100%' }}
          className="p-datatable-sm"
        >
          {columns.map((c, idx) => {
            return <Column key={`field-${idx}`} {...c} />;
          })}
        </DataTable>
      </div>
    );
  };

  const handleInputValorNegociadoChange = (e, item) => {
    const value = e.value !== undefined && !isNaN(e.value) ? e.value : null;
    store.updateAttributeItem(item.itemLote?.id, item.licitante.id, 'valorUnitario', value);
  };

  const _renderDataTableFracassadosDesertos = (itens, fracassado = false) => {
    const columns = [
      {
        header: 'Lote',
        style: { width: '20%' },
        body: (item) => store.getNomeLoteByitem(item),
      },
      {
        header: 'Código',
        style: { width: '10%' },
        body: (item) => getValue(item.itemLote?.materialDetalhamento?.codigo),
      },
      {
        header: 'Item',
        style: { width: '30%' },
        body: (item) =>
          `${item.itemLote.numero ? item.itemLote.numero + ' - ' : ''} ${getValue(
            item.itemLote.materialDetalhamento?.pdm?.nome
          )}`,
      },
      {
        header: 'Descrição Complementar',
        body: (item) => getValue(item.itemLote?.descricaoComplementar),
      },
      {
        header: 'Unidade de Medida',
        body: (item) => getValue(item.itemLote?.unidadeMedida?.textUnidadeMedida),
      },
      !fracassado && {
        header: (
          <span>
            Quantidade<span className="p-error"> *</span>
          </span>
        ),
        body: (item) =>
          !editingDesertos[getId(item)]?.quantidade && !item.preenchido ? (
            <div className="flex flex-column gap-1 item-container qtd">
              <InputMonetary
                className={itemErrorDesertos[getId(item)]?.quantidade ? 'p-invalid p-error' : ''}
                value={item.quantidade}
                onChange={(e) => {
                  store.updateAttributeItemDeserto(item.itemLote.id, 'quantidade', e);
                }}
                decimalPlaces={item.itemLote.fracionario ? 2 : 0}
                min={0}
                max={store.getQuantidadeItemDisponivel(item.itemLote.id, item.quantidade)}
                onKeyDown={({ key }) => handleKey(key, item, 'quantidade')}
              />
              <span className="text-sm" style={{ color: '#1d4ed8' }}>
                {`Quantidade máxima: ${getNumberUnitThousands(
                  store.getQuantidadeItemDisponivel(item.itemLote.id, item.quantidade)
                )}`}
              </span>
            </div>
          ) : (
            <span
              className={classNames({
                pointer: !item.preenchido,
                'p-pr-5 p-pl-5': !item.quantidade || !item.quantidade <= 0,
              })}
              onClick={() => enableEditDesertos(item, 'quantidade')}
            >
              {getNumberUnitThousands(item.quantidade)}
            </span>
          ),
      },
      !fracassado && {
        header: 'Ações',
        style: { width: '10%' },
        body: (item) => (
          <>
            {!item.preenchido && (
              <FcButton
                type="button"
                icon="pi pi-check"
                className="p-button-text toggle-button"
                onClick={() => {
                  if (!_validateRequiredFieldsDesertos(item)) {
                    setEditingDesertos((oldState) => {
                      oldState[getId(item)] = {};
                      return { ...oldState };
                    });
                    store.updateAttributeItemDeserto(item.itemLote?.id, 'preenchido', true);
                    setItemErrorDesertos((oldState) => {
                      oldState[getId(item)] = false;
                      return { ...oldState };
                    });
                  }
                }}
              />
            )}
            {item.preenchido && (
              <FcButton
                type="button"
                icon="pi pi-pencil"
                className="p-button-text toggle-button"
                disabled={disableDeserto}
                onClick={() => {
                  setEditing((oldState) => {
                    oldState[getId(item)] = {};
                    return { ...oldState };
                  });
                  store.updateAttributeItemDeserto(item.itemLote?.id, 'preenchido', false);
                }}
              />
            )}
          </>
        ),
      },
    ];

    return (
      <DataTable
        rowHover
        value={itens}
        emptyMessage="Nenhum item disponível"
        style={{ maxWidth: '100%' }}
        className="p-datatable-sm "
      >
        {columns.map((c, idx) => {
          return <Column key={`field-${idx}`} {...c} />;
        })}
      </DataTable>
    );
  };

  const _renderHeader = (vencedor, className) => {
    return (
      <>
        <div className="flex-left pointer" onClick={() => toggleLote(vencedor.licitante.id)}>
          <div className="info-lote">
            <span className="feedback">
              <i
                className={classNames('p-m-2', {
                  'pi pi-angle-right': collapsed[vencedor.licitante?.id],
                  'pi pi-angle-down': !collapsed[vencedor.licitante?.id],
                })}
              />
            </span>
            <strong className="p-ml-2" onClick={() => toggleLote(vencedor.licitante.id)}>
              {vencedor.licitante.nome}
            </strong>
          </div>
        </div>
        <div className="flex-right p-mr-2">
          <div className="feedback">
            {className?.includes('panel-check') && (
              <span className={`circle check`}>
                <Tooltip value="Preenchido" sideOffset={10} delayDuration={100}>
                  <i className="pi pi-check" />
                </Tooltip>
              </span>
            )}
            {className?.includes('panel-warning') && (
              <span className="circle warning">
                <Tooltip value="Parcialmente Preenchido" sideOffset={10} delayDuration={100}>
                  <span className="default">!</span>
                </Tooltip>
              </span>
            )}
            {className?.includes('panel-error') && (
              <span className="circle error">
                <Tooltip value="Não Preenchido" sideOffset={10} delayDuration={100}>
                  <i className="pi pi-times" />
                </Tooltip>
              </span>
            )}
          </div>
        </div>
      </>
    );
  };

  const _renderItensVencedores = () => {
    const vencedores = store.itensGroupByVencedor;
    return (
      <div className="p-mt-2">
        {vencedores?.map((venc) => {
          const itens = venc.itens;
          const className = itens.filter((item) => !item.preenchido)?.length === 0 ? 'panel-check' : 'panel-warning';
          return (
            <Panel
              className={className}
              header={_renderHeader(venc, className)}
              content={_renderDataTable(itens)}
              collapsed={collapsed[venc.licitante?.id]}
            />
          );
        })}
      </div>
    );
  };

  const _renderHeaderDesertosFracassados = (title, warning = false) => {
    return (
      <>
        <div className="flex-left pointer">
          <div className="info-lote">
            <strong className="p-ml-4">{title}</strong>
          </div>
        </div>
        <div className="flex-right p-mr-2">
          <div className="feedback">
            {!warning && (
              <span className="circle check">
                <Tooltip value="Preenchido" sideOffset={10} delayDuration={100}>
                  <i className="pi pi-check" />
                </Tooltip>
              </span>
            )}
            {warning && (
              <span className="circle warning">
                <Tooltip value="Parcialmente Preenchido" sideOffset={10} delayDuration={100}>
                  <span className="default">!</span>
                </Tooltip>
              </span>
            )}
          </div>
        </div>
      </>
    );
  };

  const _renderItensFracassados = () => {
    return (
      <div className="p-mt-2">
        <Panel
          className="panel-error"
          header={_renderHeaderDesertosFracassados('Itens Fracassados')}
          content={_renderDataTableFracassadosDesertos(store.lotesFracassados, true)}
        />
      </div>
    );
  };

  const _renderItensDesertos = () => {
    return (
      <div className="p-mt-2">
        <Panel
          className="panel-desert"
          header={_renderHeaderDesertosFracassados(
            'Itens Desertos',
            store.itensDesertos.some((i) => !i.preenchido)
          )}
          content={_renderDataTableFracassadosDesertos(store.itensDesertos)}
        />
      </div>
    );
  };

  let content = <>Erro ao renderizar o lote</>;
  if (store.lotes?.length > 0) {
    content = (
      <>
        {_renderVencedoresItens()}
        {_renderItensVencedores()}
        {store.lotesFracassados?.length > 0 && _renderItensFracassados()}
        {store.itensDesertos?.length > 0 && _renderItensDesertos()}
        {_renderDialogObs()}
        {_renderPickListItensFracassados()}
        {_renderDialogItensFracassados()}
        {_renderPickListItensDesertos()}
        {_renderDialogItensDesertos()}
      </>
    );
  }
  return content;
});

LoteUnico.propTypes = {
  showDesconto: PropTypes.bool,
  showEspecificacao: PropTypes.bool,
  decimalPlaces: PropTypes.number,
  labelLicitante: PropTypes.bool,
  hideFracasso: PropTypes.bool,
  disableFracasso: PropTypes.bool,
  hideDeserto: PropTypes.bool,
  disableDeserto: PropTypes.bool,
  store: PropTypes.objectOf(VencedoresFormStore).isRequired,
};

export default LoteUnico;
