import React, { useState } from 'react';

import { observer } from 'mobx-react';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { getNumberFractionDigits, getValueMoney, getValue, getNumberUnitThousands } from 'fc/utils/utils';
import Panel from './panel';
import classNames from 'classnames';
import PropTypes from 'prop-types';

const Resumo = observer((props) => {
  const [collapsed, setCollapsed] = useState(false);

  const { showDesconto, showEspecificacao, vencedor, decimalPlaces } = props;

  const toggleTitulo = () => {
    setCollapsed(!collapsed);
  };

  const _renderDataTable = () => {
    const columns = [
      {
        header: 'Código',
        body: (item) => getValue(item.itemLote?.materialDetalhamento?.codigo),
      },
      {
        header: 'Item',
        style: { width: '30%' },
        body: (item) =>
          `${item.numero ? item.numero + ' - ' : ''} ${getValue(item?.itemLote?.materialDetalhamento?.pdm?.nome)}`,
      },
      {
        header: 'Descrição Complementar',
        style: { width: '20%' },
        body: (item) => getValue(item.itemLote?.descricaoComplementar),
      },
      {
        header: 'Marca/Modelo',
        field: 'marcaModelo',
        body: (item) => getValue(item.marcaModelo),
      },
      {
        header: 'Descrição Complementar',
        body: (item) => getValue(item.itemLote?.descricaoComplementar),
      },
      {
        header: 'Unidade de Medida',
        body: (item) => getValue(item.itemLote?.unidadeMedida?.textUnidadeMedida),
      },
      showEspecificacao && {
        header: 'Especificação',
        field: 'especificacao',
        body: (item) => getValue(item.especificacao),
      },
      {
        header: 'Quantidade',
        field: 'quantidade',
        body: (item) =>
          item?.itemLote?.fracionario
            ? getNumberFractionDigits(item.quantidade, 2)
            : getNumberUnitThousands(item.quantidade),
      },
      {
        header: 'Valor Negociado',
        field: 'valorUnitario',
        body: (item) => getValueMoney(item.valorUnitario, decimalPlaces),
      },
      showDesconto && {
        header: 'Desconto(%)',
        field: 'desconto',
        body: (item) => getNumberFractionDigits(item.desconto, 2),
      },
      {
        header: 'Valor Total',
        field: 'valor',
        body: (item) => getValueMoney(item.valor, decimalPlaces, 0),
      },
    ];

    return (
      <DataTable rowHover value={vencedor?.itens} emptyMessage="Nenhum item adicionado" style={{ width: '100%' }}>
        {columns.map((c, idx) => {
          return <Column key={`field-${idx}`} {...c} />;
        })}
      </DataTable>
    );
  };

  const _renderHeader = () => {
    return (
      <>
        <div className="flex-left pointer" onClick={() => toggleTitulo()}>
          <div className="info-lote">
            <span className="feedback">
              <i className={classNames('p-m-2', { 'pi pi-angle-right': collapsed, 'pi pi-angle-down': !collapsed })} />
            </span>
            <strong className="p-ml-2" onClick={() => toggleTitulo()}>
              {vencedor?.licitante?.nome}
            </strong>
          </div>
        </div>
        <div className="flex-right p-mr-5 sm:w-26rem lg:w-17rem xl:w-30rem">
          <div>
            <strong className="p-mr-5">Total</strong>
            <span className="p-ml-2 text-disabled">
              {getValueMoney(
                vencedor?.itens.reduce((accumulator, currentValue) => {
                  return accumulator + currentValue.valor;
                }, 0),
                decimalPlaces
              )}
            </span>
          </div>
        </div>
      </>
    );
  };

  if (vencedor) {
    return (
      <div className="p-mt-2">
        <Panel header={_renderHeader()} content={_renderDataTable()} collapsed={collapsed} />
      </div>
    );
  } else {
    return <></>;
  }
});

Resumo.propTypes = {
  vencedor: PropTypes.object,
  decimalPlaces: PropTypes.number,
  showDesconto: PropTypes.bool,
  showEspecificacao: PropTypes.bool,
  labelLicitante: PropTypes.string,
};

export default Resumo;
