import React, { useState } from 'react';

import { observer } from 'mobx-react';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { getNumberFractionDigits, getNumberUnitThousands, getValue } from 'fc/utils/utils';
import Panel from './panel';
import classNames from 'classnames';
import PropTypes from 'prop-types';

const ResumoFracassados = observer((props) => {
  const [collapsed, setCollapsed] = useState(false);

  const { itensFracassados } = props;

  const toggleTitulo = () => {
    setCollapsed(!collapsed);
  };

  const _renderDataTableFracassado = () => {
    const columnsFracassadas = [
      {
        header: 'Código',
        body: (item) => getValue(item.itemLote?.materialDetalhamento?.codigo),
      },
      {
        header: 'Item',
        style: { width: '30%' },
        body: (item) => `${getValue(item.itemLote.materialDetalhamento?.pdm?.nome)}`,
      },
      {
        header: 'Quantidade',
        field: 'quantidade',
        body: (item) =>
          item?.itemLote?.fracionario
            ? getNumberFractionDigits(item.quantidade, 2)
            : getNumberUnitThousands(item.quantidade),
      },
      {
        header: 'Descrição Complementar',
        style: { width: '20%' },
        body: (item) => getValue(item.itemLote?.descricaoComplementar),
      },
      {
        header: 'Unidade de Medida',
        body: (item) => getValue(item.itemLote?.unidadeMedida?.textUnidadeMedida),
      },
    ];
    return (
      <DataTable rowHover value={itensFracassados} emptyMessage="Nenhum item fracassado" style={{ width: '100%' }}>
        {columnsFracassadas.map((c, idx) => {
          return <Column key={`field-${idx}`} {...c} />;
        })}
      </DataTable>
    );
  };

  const _renderHeader = () => {
    return (
      <div className="flex-left pointer" onClick={() => toggleTitulo()}>
        <div className="info-lote">
          <span className="feedback">
            <i className={classNames('p-m-2', { 'pi pi-angle-right': collapsed, 'pi pi-angle-down': !collapsed })} />
          </span>
          <strong className="p-ml-2" onClick={() => toggleTitulo()}>
            Itens Fracassados
          </strong>
        </div>
      </div>
    );
  };

  if (itensFracassados?.length > 0) {
    return (
      <div className="p-mt-2">
        <Panel header={_renderHeader()} content={_renderDataTableFracassado()} collapsed={collapsed} />
      </div>
    );
  } else {
    return <></>;
  }
});

ResumoFracassados.propTypes = {
  itensFracassados: PropTypes.array,
};

export default ResumoFracassados;
