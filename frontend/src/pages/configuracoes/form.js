import React from 'react';
import { observer } from 'mobx-react';
import PropTypes from 'prop-types';
import UrlRouter from '~/constants/UrlRouter';
import ConfiguracoesFormStore from '~/stores/configuracoes/formStore';
import GenericFormPage from 'fc/pages/GenericFormPage';
import <PERSON><PERSON>ield from 'fc/components/FormField';
import PermissionProxy from 'fc/components/PermissionProxy';
import AccessPermission from '~/constants/AccessPermission';
import { ConfirmDialog } from 'primereact/confirmdialog';
import InputMonetary from 'fc/components/InputMonetary';
import AppBreadCrumb from 'fc/components/AppBreadCrumb';
import FcButton from 'fc/components/FcButton';
import { InputNumber } from 'primereact/inputnumber';
import { showNotification } from 'fc/utils/utils';
import { FilterMatchMode, PrimeIcons } from 'primereact/api';
import { Divider } from 'primereact/divider';
import AsyncDropdown from 'fc/components/AsyncDropdown';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { InputText } from 'primereact/inputtext';
import AppStore from 'fc/stores/AppStore';

@observer
class ConfiguracoesFormPage extends GenericFormPage {
  constructor(props) {
    super(props, UrlRouter.home, AccessPermission.configuracoes);
    this.store = new ConfiguracoesFormStore();

    this.state = {
      submitted: false,
      isConfirmDialogVisible: false,
      idxRemove: null,
      editingRows: {},
      filters: {
        global: { value: null, matchMode: FilterMatchMode.CONTAINS },
      },
      filterValue: null,
    };

    this.onRowEditChange = this.onRowEditChange.bind(this);
    this.toggleDialogVisibility = this.toggleDialogVisibility.bind(this);
    this.onGlobalFilterChange = this.onGlobalFilterChange.bind(this);
    this.entidadeEditor = this.entidadeEditor.bind(this);
    this.riscoEditor = this.riscoEditor.bind(this);
  }

  componentDidMount() {
    this.store.initialize();
  }

  submitFormData(e) {
    e && e.preventDefault();
    const execution = () => {
      if (!this.store.rules.hasError) {
        this.store.save(() => this.forceUpdate());
      } else {
        showNotification('error', null, 'Verifique os campos do formulário!');
      }
    };

    if (this.state.submitted) {
      execution();
    } else {
      this.setState({ submitted: true }, execution);
    }
  }

  toggleDialogVisibility() {
    this.setState({ dialogVisibility: !this.state.dialogVisibility });
  }

  confirmRemove() {
    return (
      <ConfirmDialog
        blockScroll
        visible={this.state.isConfirmDialogVisible}
        message="Você realmente deseja excluir o registro selecionado?"
        header="Excluir registro"
        icon="pi pi-exclamation-triangle"
        acceptClassName="p-button-danger"
        accept={() => {
          this.store.removeRiscoEntidade(this.state.idxRemove);
          this.setState({ isConfirmDialogVisible: false, idxRemove: null });
        }}
        onHide={() => this.setState({ isConfirmDialogVisible: false })}
      />
    );
  }

  renderDialog() {
    return (
      <ConfirmDialog
        blockScroll
        visible={this.state.dialogVisibility}
        message="As configurações serão atualizadas, tem certeza que deseja continuar?"
        header="Atualização de Configurações"
        icon="pi pi-user"
        acceptClassName="p-button-info"
        onHide={() => this.toggleDialogVisibility()}
        accept={(e) => {
          this.submitFormData(e);
        }}
      />
    );
  }

  renderActionButtons() {
    return (
      <div className="p-mt-10 form-actions">
        <div className="p-mt-2">
          <span className="p-mr-1 p-d-inline p-d-flex align-items-center">
            <FcButton
              label="Voltar"
              type="button"
              className="p-ml-auto p-button-secondary p-mr-2"
              onClick={() => this._goBack()}
              loading={this.store.loading}
            />

            <PermissionProxy resourcePermissions={this.getWritePermission()}>
              <FcButton
                label="Salvar"
                type="button"
                loading={this.store.loading}
                onClick={() => this.toggleDialogVisibility()}
              />
            </PermissionProxy>
          </span>
        </div>
      </div>
    );
  }

  onRowEditChange(e) {
    const editingRows = { ...this.state.editingRows, ...{ [`${e.index}`]: false } };
    this.setState({ editingRows });
  }

  onGlobalFilterChange(e) {
    const value = e.target.value;
    let filters = { ...this.state.filters };
    filters['global'].value = value;

    this.setState({ filters, filterValue: value });
  }

  riscoEditor(props) {
    return (
      <InputNumber
        suffix="%"
        inputId="minmax"
        mode="decimal"
        min={0}
        max={100}
        value={this.store.riscosEntidades[props.rowIndex]['riscoEntidade']}
        onChange={(e) => {
          this.store.updateAttributeRisco(e.value, props.rowIndex);
        }}
      />
    );
  }

  entidadeEditor(props) {
    return (
      <div style={{ width: '70%' }}>
        <AsyncDropdown
          value={this.store.riscosEntidades[props.rowIndex]['entidade']?.id}
          placeholder="Selecione a entidade"
          store={this.store.entidadeStore}
          onChange={(_, value) => {
            this.store.updateAttributeEntidade(value, props.rowIndex);
          }}
        />
      </div>
    );
  }

  render() {
    const { submitted } = this.state;
    const { getRule, updateAttribute } = this.store;
    const { submitFormData } = this;

    const header = (
      <>
        <div className="flex justify-content-between">
          <div style={{ width: '8rem' }}>
            <PermissionProxy resourcePermissions={this.getWritePermission()}>
              <FcButton
                type="button"
                className="p-button"
                label="Novo"
                style={{ marginBottom: '5px', marginRight: '5px' }}
                icon={PrimeIcons.PLUS}
                onClick={() => {
                  this.store.addRiscoEntidade();
                  this.forceUpdate();
                }}
              />
            </PermissionProxy>
          </div>
          <span className="p-input-icon-left" style={{ width: '20rem' }}>
            <i className="pi pi-search" />
            <InputText
              value={this.state.filterValue}
              onChange={this.onGlobalFilterChange}
              placeholder="Filtre por algum valor"
            />
          </span>
        </div>
      </>
    );

    const breacrumbItems = [
      { label: 'Configurações', url: UrlRouter.seguranca.configuracoes.editar },
      { label: 'Editar' },
    ];

    let content;
    if (this.store.object) {
      content = (
        <>
          <AppBreadCrumb items={breacrumbItems} />
          <div className="card page form-action-buttons">
            <form onSubmit={submitFormData}>
              <div className="p-fluid p-formgrid p-grid">
                <FormField
                  columns={2}
                  attribute="limiarValorRisco"
                  label="Limiar de Risco"
                  infoTooltip="Limiar utilizado para definir se uma licitação deve ser movida automaticamente para em análise quando o limiar for superado."
                  rule={getRule('limiarValorRisco')}
                  submitted={submitted}
                >
                  <InputMonetary
                    placeholder="Informe o Limiar de Risco"
                    value={this.store.object.limiarValorRisco}
                    onChange={(e) => updateAttribute('limiarValorRisco', e)}
                    disabled={!AppStore.hasPermission(this.getWritePermission())}
                  />
                </FormField>
                <FormField
                  attribute="limiarDiasArquivamento"
                  columns={2}
                  label="Quantidade de Dias"
                  infoTooltip="Quantidade de dias utilizada para arquivar processos cuja análise não foi realizada até este período."
                  rule={getRule('limiarDiasArquivamento')}
                  submitted={submitted}
                >
                  <InputNumber
                    min={1}
                    defaultValue={1}
                    onValueChange={(e) => updateAttribute('limiarDiasArquivamento', e)}
                    placeholder="Informe a quantidade de dias"
                    value={this.store.object.limiarDiasArquivamento}
                    disabled={!AppStore.hasPermission(this.getWritePermission())}
                  />
                </FormField>
                <FormField
                  attribute="limiarDiasMedicaoObra"
                  columns={2}
                  label="Obras Paralisadas (dias)"
                  infoTooltip="Quantidade de dias utilizada para definir que a obra está paralisada, caso uma medição da obra não seja informada até este período de tempo."
                  rule={getRule('limiarDiasMedicaoObra')}
                  submitted={submitted}
                >
                  <InputNumber
                    min={1}
                    onValueChange={(e) => updateAttribute('limiarDiasMedicaoObra', e)}
                    placeholder="Informe a quantidade de dias"
                    value={this.store.object.limiarDiasMedicaoObra}
                    disabled={!AppStore.hasPermission(this.getWritePermission())}
                  />
                </FormField>
                <Divider>
                  <b>Percentual de Risco por Entidade</b>
                </Divider>
                <div className="p-col-12 index-table">
                  <DataTable
                    rowHover
                    filters={this.state.filters}
                    paginator
                    rows={10}
                    globalFilterFields={['riscoEntidade', 'entidade.nome']}
                    editMode="cell"
                    filterDisplay="menu"
                    dataKey="id"
                    responsiveLayout="scroll"
                    stripedRows
                    emptyMessage="Nenhuma entidade adicionada"
                    value={this.store.riscosEntidades}
                    header={header}
                    loading={this.store.loading}
                    showGridlines
                  >
                    <Column
                      field="riscoEntidade"
                      header="Percentual de Risco"
                      style={{ width: '30%' }}
                      editor={(props) => this.riscoEditor(props)}
                      body={({ riscoEntidade }) => `${riscoEntidade}%`}
                    />
                    <Column
                      field="entidade.nome"
                      header="Entidade"
                      style={{ width: '70%' }}
                      editor={(props) => this.entidadeEditor(props)}
                    />
                    <Column
                      body={(_, props) => {
                        return (
                          <div className="actions p-d-flex p-jc-end">
                            <PermissionProxy resourcePermissions={this.getWritePermission()}>
                              <FcButton
                                type="button"
                                icon="pi pi-trash"
                                className="p-button-sm p-button-danger"
                                onClick={() => {
                                  this.setState({ isConfirmDialogVisible: true, idxRemove: props.rowIndex });
                                }}
                              />
                            </PermissionProxy>
                          </div>
                        );
                      }}
                    />
                  </DataTable>
                  {this.state.isConfirmDialogVisible && this.confirmRemove(this.state.idxRemove)}
                </div>
              </div>
              {this.renderActionButtons()}
              {this.state.dialogVisibility && this.renderDialog()}
            </form>
          </div>
        </>
      );
    } else {
      content = (
        <div className="card page">
          <AppBreadCrumb items={breacrumbItems} />
          <i
            className="pi pi-spin pi-spinner"
            style={{
              marginTop: '20px',
              marginLeft: 'calc(50% - 20px)',
              fontSize: '2em',
            }}
          ></i>
        </div>
      );
    }
    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        {content}
      </PermissionProxy>
    );
  }
}

ConfiguracoesFormPage.propTypes = {
  id: PropTypes.any,
  action: PropTypes.any,
  history: PropTypes.any,
};

export default ConfiguracoesFormPage;
