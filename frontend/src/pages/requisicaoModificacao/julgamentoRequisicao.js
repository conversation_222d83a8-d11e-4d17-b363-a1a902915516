import React from 'react';
import { observer, PropTypes } from 'mobx-react';
import AccessPermission from '~/constants/AccessPermission';
import TabAtributos from './julgamentoTabs/TabAtributos';
import TabJustificativa from './julgamentoTabs/TabJustificativa';
import TabJulgamento from './julgamentoTabs/TabJulgamento';
import TabArquivos from './julgamentoTabs/TabArquivos';
import TabPublicacoes from './julgamentoTabs/TabPublicacoes';
import TabVencedores from './julgamentoTabs/TabVencedores';
import TabOcorrencias from './julgamentoTabs/TabOcorrencias';
import TabSecoes from './julgamentoTabs/TabSecoes';
import TabLotes from './julgamentoTabs/TabLotes';
import TabLicitantesLicitacao from './julgamentoTabs/TabLicitantesLicitacao';
import UrlRouter from '~/constants/UrlRouter';
import PermissionProxy from 'fc/components/PermissionProxy';
import AppBreadCrumb from 'fc/components/AppBreadCrumb';
import GenericFormPage from 'fc/pages/GenericFormPage';
import { checkUserGroup, getValueByKey } from 'fc/utils/utils';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import RequisicaoModificacaoFormStore from '~/stores/requisicaoModificacao/formStore';
import FcCloseableTabView from 'fc/components/FcCloseableTabView';
import { Fieldset } from 'primereact/fieldset';

@observer
class JulgamentoRequisicao extends GenericFormPage {
  constructor(props) {
    super(
      props,
      checkUserGroup('Auditor')
        ? UrlRouter.auditoria.requisicaoModificacao.index
        : UrlRouter.administracao.requisicaoModificacao.index,
      AccessPermission.requisicaoModificacao
    );
    this.store = new RequisicaoModificacaoFormStore();
    this.state = {
      activeTabIndex: 0,
    };
    this.resetDialog = this.resetDialog.bind(this);
  }

  componentDidMount() {
    const { id } = this.props.match.params;
    this.store.initialize(id, {}, () => this.store.getObjetoRequisicao(this.store.object, () => this.forceUpdate()));
  }

  resetDialog() {
    this.props.closeDialog();
  }

  render() {
    let data = [];
    const isJurisdicionado = checkUserGroup('Jurisdicionado');
    const isAuditor = checkUserGroup('Auditor');
    const { objetoModificado } = this.store;
    const tabsComuns = [
      {
        id: 0,
        header: 'Dados do Processo',
        closable: false,
        content: (
          <TabAtributos
            history={this.props.history}
            requisicaoModificacao={this.store.object}
            objetoModificado={objetoModificado}
          />
        ),
        badgeCount: this.store.modificacoesAtributos,
      },
      {
        id: 1,
        header: 'Arquivos',
        closable: false,
        content: (
          <TabArquivos
            history={this.props.history}
            requisicaoModificacao={this.store.object}
            arquivosModificados={objetoModificado?.arquivosTemporarios ?? objetoModificado?.files}
            store={this.store}
          />
        ),
        badgeCount: this.store.modificacoesArquivos,
      },
    ];

    const nomeLicitante = getValueByKey(
      this.store.object?.tipoProcesso[0],
      DadosEstaticosService.getTipoProcesso(),
      'value',
      'licitantes'
    );

    const labelLicitante = nomeLicitante.charAt(0).toUpperCase() + nomeLicitante.slice(1);

    const tabsLicitacao = [
      {
        id: 2,
        header: 'Publicações',
        closable: false,
        content: (
          <TabPublicacoes
            history={this.props.history}
            requisicaoModificacao={this.store.object}
            publicacoesModificadas={objetoModificado?.publicacoes}
          />
        ),
        badgeCount: this.store.modificacoesPublicacoes,
      },
      {
        id: 3,
        header: 'Ocorrências',
        closable: false,
        content: (
          <TabOcorrencias
            history={this.props.history}
            requisicaoModificacao={this.store.object}
            ocorrenciasModificadas={objetoModificado?.ocorrencias}
          />
        ),
        badgeCount: this.store.modificacoesOcorrencias,
      },
      {
        id: 4,
        header: 'Licitantes',
        closable: false,
        content: (
          <TabLicitantesLicitacao
            history={this.props.history}
            requisicaoModificacao={this.store.object}
            licitantesModificados={objetoModificado?.licitantes}
          />
        ),
        badgeCount: this.store.modificacoesLicitantes,
      },
    ];

    const srpModificado = objetoModificado && (objetoModificado['newSrp'] ?? objetoModificado['srp']);
    const tresCasasDecimaisModificado =
      objetoModificado && (objetoModificado['tresCasasDecimais'] ?? objetoModificado['tresCasasDecimais']);
    objetoModificado?.secoes?.forEach((secaoTermo) => {
      const secaoCriada = objetoModificado?.secoesCriadas?.find((secao) => secaoTermo.secao.id === secao.id);
      if (secaoCriada?.modificado) {
        secaoTermo.secao.modificado = secaoCriada.modificado;
        secaoTermo.secao.newTitulo = secaoCriada.newTitulo;
      }
    });

    const tabsTermoReferencia = [
      {
        id: 2,
        header: 'Seções',
        closable: false,
        content: (
          <TabSecoes
            history={this.props.history}
            requisicaoModificacao={this.store.object}
            secoesModificadas={objetoModificado?.secoes}
            termoDeArquivo={objetoModificado?.formaPreenchimentoSecao === 'IMPORTACAO_ARQUIVO'}
          />
        ),
        badgeCount: this.store.modificacoesSecoes,
      },
      {
        id: 3,
        header: 'Lotes',
        closable: false,
        content: (
          <TabLotes
            history={this.props.history}
            requisicaoModificacao={this.store.object}
            srp={srpModificado}
            tresCasasDecimais={tresCasasDecimaisModificado}
            lotesModificados={objetoModificado?.lotes}
          />
        ),
        badgeCount: this.store.modificacoesLotes,
      },
    ];

    const showDesconto = objetoModificado?.newTiposLicitacao
      ? objetoModificado?.newTiposLicitacao?.includes('Maior desconto')
      : objetoModificado?.tiposLicitacao?.filter((tipo) => tipo?.nome === 'Maior desconto')?.length;

    const showEspecificacao = objetoModificado?.newNaturezasDoObjeto
      ? objetoModificado?.newNaturezasDoObjeto?.includes('COMPRAS')
      : objetoModificado?.naturezasDoObjeto?.includes('COMPRAS');

    const isEntidadeAntiga = objetoModificado?.lei
      ? objetoModificado?.lei === 'LEI_N_8666'
      : objetoModificado?.processoMigrado;

    const isTresCasasDecimais = !!objetoModificado?.termoReferencia?.tresCasasDecimais;

    const tabVencedores = {
      id: 5,
      header: labelLicitante,
      closable: false,
      content: (
        <TabVencedores
          history={this.props.history}
          requisicaoModificacao={this.store.object}
          nomeLicitante={nomeLicitante}
          vencedorStore={this.store.vencedorStore}
          showDesconto={showDesconto}
          showEspecificacao={showEspecificacao}
          entidadeAntiga={isEntidadeAntiga}
          objetoModificado={objetoModificado}
          tresCasasDecimais={isTresCasasDecimais}
        />
      ),
      badgeCount: this.store.modificacoesVencedores,
    };

    const tabsJustificativa = [
      {
        id: 7,
        header: 'Justificativa',
        closable: false,
        content: <TabJustificativa history={this.props.history} requisicaoModificacao={this.store.object} />,
      },
      !isJurisdicionado || (isJurisdicionado && this.store.object?.status !== 'ENVIADA')
        ? {
            id: 8,
            header: 'Resultado da Análise',
            closable: false,
            content: (
              <TabJulgamento
                history={this.props.history}
                requisicaoModificacao={this.store.object}
                statusRequisicao={this.store.object?.status}
                goBack={this._goBack}
              />
            ),
          }
        : null,
    ].filter((tab) => tab);

    data.push(tabsComuns);
    if (
      this.store.object &&
      ![
        'CONTRATO',
        'ADITIVO',
        'RESCISAO_CONTRATUAL',
        'TERMO_REFERENCIA',
        'OBRA_MEDICAO',
        'CREDENCIAMENTO',
        'ANULACAO_REVOGACAO',
      ].includes(this.store.object.tipoProcesso)
    ) {
      data.push(tabVencedores);
    }
    if (this.store.object && this.store.object.tipoProcesso === 'LICITACAO') {
      data.push(tabsLicitacao);
    }
    if (this.store.object && this.store.object.tipoProcesso === 'TERMO_REFERENCIA') {
      data.push(tabsTermoReferencia);
    }

    data.push(tabsJustificativa);
    const tabs = [].concat.apply([], data);

    const breacrumbItems = [
      {
        label: 'Requisição de Modificação',
        url: isAuditor
          ? UrlRouter.auditoria.requisicaoModificacao.index
          : UrlRouter.administracao.requisicaoModificacao.index,
      },
      {
        label: `Julgamento (${
          DadosEstaticosService.getLabelRequisicaoModificacao()[this.store.object?.tipo]?.labelInf
        } de ${getValueByKey(this.store.object?.tipoProcesso, DadosEstaticosService.getTiposObjetos())})`,
      },
    ];

    let content;
    if (this.store.object) {
      const message =
        this.store.object?.tipoProcesso === 'CREDENCIADO'
          ? 'SOLICITAÇÃO DE EXCLUSÃO DE CREDENCIADO. A APROVAÇÃO DESTA SOLICITAÇÃO RESULTARÁ NA REMOÇÃO DO MESMO DO SISTEMA.'
          : 'SOLICITAÇÃO DE EXCLUSÃO DE PROCESSO. A APROVAÇÃO DESTA SOLICITAÇÃO RESULTARÁ NA REMOÇÃO PERMANENTE DO PROCESSO DO SISTEMA.';
      content = (
        <>
          <AppBreadCrumb items={breacrumbItems} />
          <div className="card page index-table">
            {this.store.object.tipo === 'REMOCAO' && (
              <Fieldset legend="AVISO" className="mb-2">
                <h6 style={{ color: '#dd0303' }}>
                  <b>{message}</b>
                </h6>
              </Fieldset>
            )}
            {this.store.object.tipo === 'REABERTURA' && (
              <Fieldset legend="AVISO" className="mb-2">
                <h6 style={{ color: '#dd0303' }}>
                  <b>
                    SOLICITAÇÃO DE REABERTURA DE PROCESSO. A APROVAÇÃO DESTA SOLICITAÇÃO RESULTARÁ NA REGRESSÃO DE FASE
                    DA LICITAÇÃO E A REMOÇÃO DE ARQUIVOS, LICITANTES E VENCEDORES.
                  </b>
                </h6>
              </Fieldset>
            )}
            <FcCloseableTabView
              tabs={tabs}
              activeTabIndex={this.state.activeTabIndex}
              onChangeTab={(tab) => this.setState({ activeTabIndex: tabs.indexOf(tab) })}
            />
          </div>
        </>
      );
    } else {
      content = (
        <div className="card page">
          <AppBreadCrumb items={breacrumbItems} />
          <i
            className="pi pi-spin pi-spinner"
            style={{
              marginTop: '20px',
              marginLeft: 'calc(50% - 20px)',
              fontSize: '2em',
            }}
          ></i>
        </div>
      );
    }

    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        {content}
      </PermissionProxy>
    );
  }
}

JulgamentoRequisicao.displayName = 'JulgamentoRequisicao';

JulgamentoRequisicao.propTypes = {
  id: PropTypes.any,
  history: PropTypes.any,
  match: PropTypes.any,
};

export default JulgamentoRequisicao;
