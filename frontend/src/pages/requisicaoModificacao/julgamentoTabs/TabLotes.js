import PropTypes from 'prop-types';
import { observer } from 'mobx-react';
import AccessPermission from '~/constants/AccessPermission';
import '../Style.scss';
import { DataTable } from 'primereact/datatable';
import { getValue, getValueElipsis, getValueMoneyIfNotFormatted } from 'fc/utils/utils';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import GenericIndexPage from 'fc/pages/GenericIndexPage';

@observer
class TabLotesRequisicaoModal extends GenericIndexPage {
  constructor(props) {
    super(props, AccessPermission.requisicaoModificacao);
  }

  getValue(oldValue, newValue, valueFunc, inline = false, margin = 1) {
    if (valueFunc) {
      oldValue = valueFunc(oldValue);
      newValue = newValue && valueFunc(newValue);
    }
    return newValue && newValue !== oldValue ? (
      <div className={inline && `p-d-flex p-ml-${margin}`}>
        <div className={inline && `p-mr-${margin}`} style={{ textDecoration: 'line-through' }}>
          {oldValue}
        </div>
        {newValue}
      </div>
    ) : (
      <div className={inline && `p-d-flex p-ml-${margin}`}>{oldValue}</div>
    );
  }

  renderDescricaoMaterial({ pdm }) {
    return pdm?.nome;
  }

  _handleItensSemLote(lotes = []) {
    const itensSemLote = [];
    lotes
      ?.filter((lote) => lote.gerado)
      ?.forEach((lote) => {
        lote.itens[0].modificado = lote.modificado;
        itensSemLote.push(...lote.itens);
      });

    return [...lotes?.filter((lote) => !lote.gerado)?.map((lote) => ({ ...lote, nome: `Lote ${lote.nome}` }))];
  }

  render() {
    const { srp, tresCasasDecimais } = this.props;
    const lotesModificados = this._handleItensSemLote(this.props.lotesModificados);
    const headerQuantidade = srp ? 'Quantidade para Registro' : 'Quantidade';
    const columnsItens = [
      {
        header: 'Descrição Completa',
        body: ({ materialDetalhamento }) => getValue(this.renderDescricaoMaterial(materialDetalhamento)),
        sortable: true,
      },
      {
        header: 'Unidade',
        style: { width: '150px' },
        body: ({ unidadeMedida, newUnidadeMedida }) =>
          this.getValue(unidadeMedida, newUnidadeMedida, (value) =>
            getValue(value?.textUnidadeMedida ?? value?.descricao ?? value)
          ),
        sortable: true,
      },
      {
        field: 'quantidade',
        header: headerQuantidade,
        style: { width: '130px', textAlign: 'center' },
        sortable: true,
        body: ({ quantidade, newQuantidade }) => this.getValue(quantidade, newQuantidade),
      },
      this.props.srp
        ? {
            field: 'quantidadeConsumo',
            header: 'Quantidade para Consumo',
            style: { width: '130px', textAlign: 'center' },
            sortable: true,
            body: ({ quantidadeConsumo, newQuantidadeConsumo }) =>
              this.getValue(quantidadeConsumo, newQuantidadeConsumo),
          }
        : '',
      {
        field: 'descricaoComplementar',
        header: 'Descrição Complementar',
        body: ({ descricaoComplementar, newDescricaoComplementar }) => {
          return (
            <div>
              <div>
                <span
                  className="tooltip-elipsis-original"
                  style={
                    newDescricaoComplementar && newDescricaoComplementar !== descricaoComplementar
                      ? { textDecoration: 'line-through' }
                      : {}
                  }
                >
                  {getValueElipsis(descricaoComplementar, 30)}
                </span>
              </div>
              {newDescricaoComplementar && newDescricaoComplementar !== descricaoComplementar && (
                <span className="tooltip-elipsis-modificado">{getValueElipsis(newDescricaoComplementar, 30)}</span>
              )}
            </div>
          );
        },
        simpleBody: ({ descricaoComplementar, newDescricaoComplementar }) =>
          this.getValue(descricaoComplementar, newDescricaoComplementar, (value) => getValue(value)),
        sortable: true,
      },
      {
        field: 'valorUnitarioEstimado',
        header: 'Valor Unitário',
        body: ({ valorUnitarioEstimado, newValorUnitarioEstimado }) =>
          this.getValue(valorUnitarioEstimado, newValorUnitarioEstimado, (value) =>
            getValueMoneyIfNotFormatted(value, tresCasasDecimais ? 3 : 2)
          ),
        sortable: true,
      },
      {
        style: { width: '10%' },
        field: 'modificado',
        header: 'Modificação',
        body: ({ modificado }) => {
          if (modificado) {
            const { label, color } = DadosEstaticosService.getLabelRequisicaoModificacao()[modificado] ?? {};
            return (
              <span
                className="product-badge p-d-flex p-jc-center"
                style={{
                  background: color,
                  borderRadius: 5,
                  color: 'white',
                  textAlign: 'center',
                  width: '80%',
                  maxWidth: '90px',
                }}
              >
                {label}
              </span>
            );
          }
        },
      },
    ];

    if (lotesModificados) {
      return (
        <div className="lote-div">
          {lotesModificados.map((lote) => {
            const { label, color } = DadosEstaticosService.getLabelRequisicaoModificacao()[lote.modificado] ?? {};
            const header = (
              <div className="p-d-flex">
                {lote.modificado && (
                  <span
                    className="product-badge p-d-flex p-jc-center"
                    style={{
                      background: color,
                      borderRadius: 5,
                      color: 'white',
                      textAlign: 'center',
                      width: '80%',
                      maxWidth: '90px',
                    }}
                  >
                    {label}
                  </span>
                )}
                {this.getValue(lote.nome, lote.newNome, null, true, 2)}
              </div>
            );
            return (
              <>
                <DataTable
                  rowHover
                  header={header}
                  value={lote.itens}
                  emptyMessage="Nenhum item foi adicionado."
                  paginator
                  rows={5}
                >
                  {this.renderColumns(columnsItens)}
                </DataTable>
              </>
            );
          })}
        </div>
      );
    } else {
      return (
        <div className="card page">
          <i className="pi pi-spin pi-spinner" style={{ fontSize: '2em' }}></i>
        </div>
      );
    }
  }
}

TabLotesRequisicaoModal.propTypes = {
  history: PropTypes.any,
  requisicaoModificacao: PropTypes.object,
  src: PropTypes.bool,
  tresCasasDecimais: PropTypes.bool,
  lotesModificados: PropTypes.object,
};

export default TabLotesRequisicaoModal;
