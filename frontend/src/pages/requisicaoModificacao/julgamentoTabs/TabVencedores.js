import PropTypes from 'prop-types';
import { observer } from 'mobx-react';
import AccessPermission from '~/constants/AccessPermission';
import Vencedores from '~/pages/vencedor';
import GenericIndexPage from 'fc/pages/GenericIndexPage';
import { getNumberFractionDigits, getValue, getValueMoney, isValueValid } from 'fc/utils/utils';
import { DataTable } from 'primereact/datatable';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import FcButton from 'fc/components/FcButton';
import { Badge } from 'primereact/badge';
import { Dialog } from 'primereact/dialog';

@observer
class TabVencedoresRequisicaoModal extends GenericIndexPage {
  constructor(props) {
    super(props, AccessPermission.requisicaoModificacao);

    this.state = {
      obsDialogVisible: false,
      obs: null,
      newObs: null,
    };
  }

  getValue(oldValue, newValue, valueFunc, inline = false, valueColumn) {
    if (valueFunc) {
      oldValue = isValueValid(oldValue) && valueFunc(oldValue);
      newValue = newValue && valueFunc(newValue);
    }
    valueColumn && !isValueValid(newValue) ? (oldValue = getValueMoney(oldValue)) : { oldValue };
    return newValue && newValue !== oldValue ? (
      <div className={inline && 'p-d-flex p-ml-1'}>
        <div className={inline && 'p-mr-1'} style={{ textDecoration: 'line-through' }}>
          {getValue(oldValue)}
        </div>
        {newValue}
      </div>
    ) : (
      <>{getValue(oldValue)}</>
    );
  }

  _renderDialogObs() {
    return (
      <Dialog
        header="Observação"
        className="dialog-obs"
        visible={this.state.obsDialogVisible}
        onHide={() => this.setState({ obsDialogVisible: false })}
      >
        {this.getValue(this.state.obs, this.state.newObs, (value) => (
          <p className="text-justify">{value}</p>
        ))}
      </Dialog>
    );
  }

  render() {
    const {
      objetoModificado,
      requisicaoModificacao,
      vencedorStore,
      entidadeAntiga,
      showEspecificacao,
      showDesconto,
      nomeLicitante,
      tresCasasDecimais,
    } = this.props;

    if (entidadeAntiga) {
      return (
        <Vencedores
          reqModificacao
          readOnly
          valueFuncReqModificacao={this.getValue}
          store={vencedorStore}
          labelLicitante={nomeLicitante?.replace('es', '')}
          showDesconto={showDesconto}
          showEspecificacao={showEspecificacao}
          entidadeAntiga={entidadeAntiga}
        />
      );
    } else if (objetoModificado) {
      const { licitantes, singularLicitante, licitante } = DadosEstaticosService.getTipoProcesso().find(
        (process) => process.value === requisicaoModificacao.tipoProcesso[0]
      );

      const columnsVencedores = [
        {
          style: { width: '25%' },
          header: 'Item',
          body: ({ itemLote }) => itemLote?.materialDetalhamento?.pdm?.nome,
        },
        {
          style: { width: '20%' },
          field: 'licitante',
          header: licitante,
          body: ({ licitante, newLicitante }) =>
            this.getValue(licitante, newLicitante, (value) => value?.nome ?? value),
        },
        {
          style: { width: '15%' },
          field: 'marcaModelo',
          header: 'Marca/Modelo',
          body: ({ marcaModelo, newMarcaModelo }) => this.getValue(marcaModelo, newMarcaModelo),
        },
        {
          style: { width: '10%' },
          field: 'quantidade',
          header: 'Quantidade',
          body: ({ quantidade, newQuantidade, itemLote }) =>
            this.getValue(quantidade, newQuantidade, (value) =>
              typeof value === 'string'
                ? itemLote?.fracionario
                  ? value.replace('R$ ', '')
                  : value.replace('R$ ', '').replace(',00', '')
                : itemLote?.fracionario
                ? getNumberFractionDigits(value)
                : value
            ),
        },
        {
          style: { width: '10%' },
          field: 'valorUnitario',
          header: 'Valor Negociado',
          body: ({ valorUnitario, newValorUnitario }) =>
            this.getValue(valorUnitario, newValorUnitario, (value) =>
              typeof value === 'string'
                ? tresCasasDecimais
                  ? value + '0'
                  : value
                : getValueMoney(value, tresCasasDecimais ? 3 : 2)
            ),
        },
        ...(showDesconto
          ? [
              {
                style: { width: '10%' },
                field: 'desconto',
                header: 'Desconto (%)',
                body: ({ desconto, newDesconto }) =>
                  this.getValue(desconto, newDesconto, (value) =>
                    typeof value === 'string' ? value.replace('R$ ', '') : getNumberFractionDigits(value)
                  ),
              },
            ]
          : []),

        {
          style: { width: '10%' },
          field: 'observacao',
          header: 'Observação',
          body: ({ observacao, newObservacao }) => (
            <FcButton
              type="button"
              icon="pi pi-comment"
              className="p-button-text toggle-button"
              onClick={() => this.setState({ obsDialogVisible: true, obs: observacao, newObs: newObservacao })}
              style={{ fontSize: '2rem' }}
              disabled={!observacao && !newObservacao}
            >
              {newObservacao && (
                <Badge
                  value="!"
                  severity="danger"
                  style={{ position: 'absolute', margin: '0px', top: '3px', right: '20px' }}
                />
              )}
            </FcButton>
          ),
        },
        {
          style: { width: '10%' },
          field: 'modificado',
          header: 'Modificação',
          body: ({ modificado }) => {
            if (modificado) {
              const { label, color } = DadosEstaticosService.getLabelRequisicaoModificacao()[modificado];
              return (
                <span
                  className="product-badge p-d-flex p-jc-center"
                  style={{
                    background: color,
                    borderRadius: 5,
                    color: 'white',
                    textAlign: 'center',
                    width: '80%',
                    maxWidth: '90px',
                  }}
                >
                  {label}
                </span>
              );
            }
          },
        },
      ];

      return (
        <>
          <DataTable
            rowHover
            value={objetoModificado[licitantes]}
            emptyMessage={`Nenhum ${singularLicitante} adicionado.`}
            paginator
            rows={10}
          >
            {this.renderColumns(columnsVencedores)}
          </DataTable>
          {this.state.obsDialogVisible && this._renderDialogObs()}
        </>
      );
    } else {
      return (
        <div className="card page">
          <i className="pi pi-spin pi-spinner" style={{ fontSize: '2em' }}></i>
        </div>
      );
    }
  }
}

TabVencedoresRequisicaoModal.propTypes = {
  history: PropTypes.any,
  requisicaoModificacao: PropTypes.object,
  nomeLicitante: PropTypes.string,
  vencedorStore: PropTypes.object,
  entidadeAntiga: PropTypes.bool,
  showDesconto: PropTypes.bool,
  showEspecificacao: PropTypes.bool,
  objetoModificado: PropTypes.object,
  tresCasasDecimais: PropTypes.bool,
};

export default TabVencedoresRequisicaoModal;
