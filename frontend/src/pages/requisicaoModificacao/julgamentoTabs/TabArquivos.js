import PropTypes from 'prop-types';
import { observer } from 'mobx-react';
import AccessPermission from '~/constants/AccessPermission';
import { DataTable } from 'primereact/datatable';
import { getValueDate, getValueByKey, isValueValid, getValue } from 'fc/utils/utils';
import { DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS } from 'fc/utils/date';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import FcButton from 'fc/components/FcButton';
import GenericIndexPage from 'fc/pages/GenericIndexPage';

@observer
class TabArquivosRequisicaoModal extends GenericIndexPage {
  constructor(props) {
    super(props, AccessPermission.requisicaoModificacao);
    this.store = props.store;
  }

  getValue(oldValue, newValue, valueFunc) {
    if (valueFunc) {
      oldValue = valueFunc(oldValue);
      newValue = newValue && valueFunc(newValue);
    }
    return newValue && newValue !== oldValue ? (
      <>
        <div style={{ textDecoration: 'line-through' }}>{getValue(oldValue)}</div>
        {newValue}
      </>
    ) : (
      oldValue
    );
  }

  render() {
    const columnsArquivo = [
      {
        style: { width: '25%' },
        field: 'arquivo',
        header: 'Arquivo',
        body: ({ arquivo }) => arquivo?.nomeOriginal,
      },
      {
        style: { width: '15%' },
        field: 'tipo',
        header: 'Tipo',
        body: ({ tipo, newTipo }) =>
          this.getValue(tipo, newTipo, (value) => {
            const newValue = getValueByKey(value, DadosEstaticosService.getTodosTiposArquivos());
            return isValueValid(newValue) && newValue !== '-' ? newValue : value;
          }),
      },
      {
        style: { width: '15%' },
        field: 'descricao',
        header: 'Descrição',
        body: ({ descricao, newDescricao }) => this.getValue(descricao, newDescricao),
      },
      {
        style: { width: '15%' },
        field: 'dataEnvio',
        header: 'Data de Envio',
        body: ({ dataEnvio }) => getValueDate(dataEnvio, DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS),
      },
      {
        style: { width: '15%' },
        field: 'modificado',
        header: 'Modificação',
        body: ({ modificado }) => {
          if (modificado) {
            const { label, color } = DadosEstaticosService.getLabelRequisicaoModificacao()[modificado];
            return (
              <span
                className="product-badge p-d-flex p-jc-center"
                style={{
                  background: color,
                  borderRadius: 5,
                  color: 'white',
                  textAlign: 'center',
                  width: '80%',
                  maxWidth: '90px',
                }}
              >
                {label}
              </span>
            );
          }
        },
      },
      {
        style: { width: '5%', textAlign: 'center' },
        header: 'Download',
        body: (rowData) => (
          <FcButton
            disabled={this.props.requisicaoModificacao.status === 'ACEITA' && rowData.modificado === 'REMOCAO'}
            icon="pi pi-download"
            type="button"
            onClick={() => this.store.downloadArquivoProcesso(rowData.arquivo)}
          />
        ),
      },
    ];

    const { arquivosModificados } = this.props;
    if (arquivosModificados) {
      return (
        <DataTable rowHover value={arquivosModificados} emptyMessage="Nenhum arquivo adicionado." paginator rows={10}>
          {this.renderColumns(columnsArquivo)}
        </DataTable>
      );
    } else {
      return 'Não existem arquivos adicionados.';
    }
  }
}

TabArquivosRequisicaoModal.propTypes = {
  history: PropTypes.any,
  requisicaoModificacao: PropTypes.object,
  arquivosModificados: PropTypes.array,
  store: PropTypes.object,
};

export default TabArquivosRequisicaoModal;
