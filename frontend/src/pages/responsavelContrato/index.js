import React from 'react';
import { observer } from 'mobx-react';
import IndexDataTable from 'fc/components/IndexDataTable';
import FcButton from 'fc/components/FcButton';
import { PrimeIcons } from 'primereact/api';
import AppBreadCrumb from 'fc/components/AppBreadCrumb';
import UrlRouter from '~/constants/UrlRouter';
import GenericIndexPage from 'fc/pages/GenericIndexPage';
import AdvancedSearch from 'fc/components/AdvancedSearch';
import AccessPermission from '~/constants/AccessPermission';
import PermissionProxy from 'fc/components/PermissionProxy';
import ResponsavelContratoIndexStore from '~/stores/responsavelContrato/indexStore';

@observer
class ResponsavelContratoIndexPage extends GenericIndexPage {
  constructor(props) {
    super(props, AccessPermission.responsavelContrato);
    this.store = new ResponsavelContratoIndexStore();
    this.state = {
      idRemove: null,
    };
  }

  render() {
    const columns = [
      {
        field: 'nome',
        header: 'Nome',
        sortable: true,
      },
      {
        field: 'telefone',
        header: 'Telefone',
      },
      {
        field: 'email',
        header: 'E-mail',
      },
      {
        field: 'cpf',
        header: 'CPF',
      },
      {
        field: 'efetivo',
        header: 'Efetivo',
        body: ({ efetivo }) => (efetivo ? 'Sim' : 'Não'),
      },
      {
        style: { width: '165px' },
        body: (rowData) => {
          return (
            <div className="actions p-d-flex p-jc-end">
              <PermissionProxy resourcePermissions={this.getWritePermission()}>
                <FcButton
                  icon="pi pi-pencil"
                  className="p-button-sm p-button-success p-mr-2"
                  onClick={() =>
                    this.pushUrlToHistory(UrlRouter.administracao.responsavelContrato.editar.replace(':id', rowData.id))
                  }
                />
              </PermissionProxy>
              <PermissionProxy resourcePermissions={this.getWritePermission()}>
                <FcButton
                  icon="pi pi-trash"
                  className="p-button-sm p-button-danger"
                  onClick={() => {
                    this.setState({ idRemove: rowData.id });
                    this.store.toggleShowConfirmDialog();
                  }}
                />
              </PermissionProxy>
            </div>
          );
        },
      },
    ];

    const header = (
      <div className="table-header p-grid p-dir-col">
        <PermissionProxy resourcePermissions={this.getWritePermission()}>
          <FcButton
            className="p-button"
            label="Novo"
            style={{ marginBottom: '5px', marginRight: '5px' }}
            icon={PrimeIcons.PLUS}
            onClick={() => this.pushUrlToHistory(UrlRouter.administracao.responsavelContrato.novo)}
          />
        </PermissionProxy>
      </div>
    );

    const { listKey, loading } = this.store;
    const { getDefaultTableProps } = this;

    const breacrumbItems = [{ label: 'Gestor e Fiscal' }];

    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        <AppBreadCrumb items={breacrumbItems} />
        <div className="card page index-table">
          <AdvancedSearch
            searchParams={this.store.getAdvancedSearchParams()}
            store={this.store}
            searchFields={['nome', 'telefone', 'email', 'cpf']}
          />
          <IndexDataTable
            columns={columns}
            value={listKey}
            header={header}
            loading={loading}
            {...getDefaultTableProps()}
          />
          {this.store.isConfirmDialogVisible && this.confirmRemove(this.state.idRemove)}
        </div>
      </PermissionProxy>
    );
  }
}

ResponsavelContratoIndexPage.displayName = 'ResponsavelContratoIndexPage';

export default ResponsavelContratoIndexPage;
