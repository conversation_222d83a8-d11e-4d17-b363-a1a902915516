import React from 'react';
import { observer } from 'mobx-react';
import PropTypes from 'prop-types';
import UrlRouter from '~/constants/UrlRouter';
import GenericFormPage from 'fc/pages/GenericFormPage';
import AppBreadCrumb from 'fc/components/AppBreadCrumb';
import AccessPermission from '~/constants/AccessPermission';
import PermissionProxy from 'fc/components/PermissionProxy';
import ResponsavelContratoFormStore from '~/stores/responsavelContrato/formStore';
import ResponsavelContratoForm from './formResponsavelContrato';

@observer
class ResponsavelContratoFormPage extends GenericFormPage {
  constructor(props) {
    super(props, UrlRouter.administracao.responsavelContrato.index, AccessPermission.responsavelContrato);
    this.store = new ResponsavelContratoFormStore();
  }

  componentDidMount() {
    const { id } = this.props;
    this.store.initialize(id, { efetivo: false });
  }

  render() {
    const breacrumbItems = [
      { label: 'Gestor e Fiscal', url: UrlRouter.administracao.responsavelContrato.index },
      { label: this.props.action === 'new' ? 'Novo' : 'Editar' },
    ];

    const { submitted } = this.state;

    let content = '';
    if (this.store.object) {
      content = (
        <>
          <AppBreadCrumb items={breacrumbItems} />
          <div className="card page">
            <ResponsavelContratoForm
              object={this.store.object}
              updateAttribute={this.store.updateAttribute}
              getRule={this.store.getRule}
              onSubmit={this.submitFormData}
              renderActionButtons={this.renderActionButtons}
              submitted={submitted}
            />
          </div>
        </>
      );
    } else {
      content = (
        <div className="card page">
          <AppBreadCrumb items={breacrumbItems} />
          <i className="pi pi-spin pi-spinner" style={{ fontSize: '2em' }}></i>
        </div>
      );
    }

    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        {content}
      </PermissionProxy>
    );
  }
}

ResponsavelContratoFormPage.propTypes = {
  id: PropTypes.any,
  action: PropTypes.any,
  history: PropTypes.any,
};

export default ResponsavelContratoFormPage;
