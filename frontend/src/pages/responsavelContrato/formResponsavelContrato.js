import React from 'react';
import PropTypes from 'prop-types';
import { InputText } from 'primereact/inputtext';
import <PERSON><PERSON>ield from 'fc/components/FormField';
import { InputMask } from 'primereact/inputmask';
import { Checkbox } from 'primereact/checkbox';
import { observer } from 'mobx-react';
@observer
class ResponsavelContratoForm extends React.Component {
  constructor(props) {
    super(props);
  }

  render() {
    const { onSubmit, renderActionButtons, getRule, updateAttribute, object, submitted } = this.props;

    return (
      <form onSubmit={onSubmit}>
        <div className="p-fluid p-formgrid p-grid">
          <FormField columns={6} attribute="nome" label="Nome" rule={getRule('nome')} submitted={submitted}>
            <InputText onChange={(e) => updateAttribute('nome', e)} placeholder="Informe o nome" value={object.nome} />
          </FormField>
          <FormField columns={6} attribute="email" label="E-mail" rule={getRule('email')} submitted={submitted}>
            <InputText
              onChange={(e) => updateAttribute('email', e)}
              value={object.email}
              placeholder="Informe o e-mail"
              id="email"
            />
          </FormField>
          <FormField columns={6} attribute="telefone" label="Telefone" rule={getRule('telefone')} submitted={submitted}>
            <InputMask
              mask="(99) 99999-9999"
              onChange={(e) => updateAttribute('telefone', e)}
              value={object.telefone}
              placeholder="Informe o telefone"
              id="telefone"
            />
          </FormField>
          <FormField columns={6} attribute="cpf" label="CPF" rule={getRule('cpf')} submitted={submitted}>
            <InputMask
              mask="999.999.999-99"
              onChange={(e) => updateAttribute('cpf', e)}
              value={object.cpf}
              placeholder="Informe o CPF"
              id="cpf"
            />
          </FormField>
          <FormField columns={6} attribute="efetivo" label="Servidor Efetivo" checkbox submitted={submitted}>
            <Checkbox
              inputId="efetivo"
              checked={object.efetivo}
              onChange={(e) => {
                updateAttribute('efetivo', e.checked);
              }}
              id="efetivo"
            />
          </FormField>
        </div>
        {renderActionButtons()}
      </form>
    );
  }
}

ResponsavelContratoForm.propTypes = {
  getRule: PropTypes.any,
  updateAttribute: PropTypes.any,
  object: PropTypes.any,
  onSubmit: PropTypes.any,
  renderActionButtons: PropTypes.any,
  submitted: PropTypes.any,
};

export default ResponsavelContratoForm;
