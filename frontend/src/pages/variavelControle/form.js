import './Form.scss';

import React from 'react';
import { observer } from 'mobx-react';
import PropTypes from 'prop-types';
import VariavelControleFormStore from '../../stores/variavelControle/formStore';
import { InputText } from 'primereact/inputtext';
import UrlRouter from '../../constants/UrlRouter';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import GenericFormPage from 'fc/pages/GenericFormPage';
import FormField from 'fc/components/FormField';
import InputNumber from 'fc/components/InputNumber';
import AppBreadCrumb from 'fc/components/AppBreadCrumb';
import { SelectButton } from 'primereact/selectbutton';
import PermissionProxy from 'fc/components/PermissionProxy';
import AccessPermission from '~/constants/AccessPermission';
import FcDropdown from 'fc/components/FcDropdown';

@observer
class VariavelControleFormPage extends GenericFormPage {
  constructor(props) {
    super(props, UrlRouter.administracao.variavelControle.index, AccessPermission.variavelControle);
    this.store = new VariavelControleFormStore();
  }

  renderTextComponent() {
    const { submitted } = this.state;
    const { updateAttribute, getRule } = this.store;
    return (
      <FormField columns={12} attribute="valor" label="Valor" rule={getRule('valor')} submitted={submitted}>
        <InputText
          onChange={(e) => updateAttribute('valor', e)}
          placeholder="Informe o valor"
          value={this.store.object.valor}
        />
      </FormField>
    );
  }
  renderNumberComponent() {
    const { submitted } = this.state;
    const { updateAttribute, getRule } = this.store;
    return (
      <FormField columns={12} attribute="valor" label="Valor" rule={getRule('valor')} submitted={submitted}>
        <InputNumber
          inputId="withoutgrouping"
          onChange={(e) => updateAttribute('valor', e)}
          placeholder="Informe o valor"
          value={this.store.object.valor}
          leadingZeros
        />
      </FormField>
    );
  }
  renderBooleanComponent() {
    const { submitted } = this.state;
    const { updateAttribute, getRule } = this.store;
    return (
      <FormField columns={4} attribute="valor" label="Valor" rule={getRule('valor')} submitted={submitted}>
        <SelectButton
          id="valor"
          optionLabel="text"
          optionValue="value"
          value={this.store.object.valor}
          options={DadosEstaticosService.getVerdadeiroFalsoVariavelControle()}
          onChange={(e) => updateAttribute('valor', e)}
        />
      </FormField>
    );
  }

  render() {
    const { submitted } = this.state;
    const { updateAttribute, getRule } = this.store;
    const { submitFormData } = this;

    const breacrumbItems = [
      { label: 'Variáveis de Controle', url: UrlRouter.administracao.variavelControle.index },
      { label: this.props.action === 'new' ? 'Novo' : 'Editar' },
    ];

    let content;
    if (this.store.object) {
      content = (
        <>
          <AppBreadCrumb items={breacrumbItems} />
          <div className="card page form-action-buttons">
            <form onSubmit={submitFormData}>
              <div className="p-fluid p-formgrid p-grid">
                <FormField columns={6} attribute="nome" label="Nome" rule={getRule('nome')} submitted={submitted}>
                  <InputText
                    onChange={(e) => updateAttribute('nome', e)}
                    placeholder="Informe o nome"
                    value={this.store.object.nome}
                  />
                </FormField>

                <FormField columns={6} attribute="tipo" label="Tipo" rule={getRule('tipo')} submitted={submitted}>
                  <FcDropdown
                    inOrder
                    onChange={(e) => updateAttribute('tipo', e)}
                    value={this.store.object.tipo}
                    placeholder="Selecione o tipo"
                    options={DadosEstaticosService.getTipoVariavelAmbiente()}
                    optionLabel="text"
                  />
                </FormField>
                {this.store.object.tipo === 'TEXTO' && this.renderTextComponent()}
                {this.store.object.tipo === 'NUMERICO' && this.renderNumberComponent()}
                {this.store.object.tipo === 'BOOLEAN' && this.renderBooleanComponent()}
              </div>
              {this.renderActionButtons()}
            </form>
          </div>
        </>
      );
    } else {
      content = (
        <div className="card page">
          <AppBreadCrumb items={breacrumbItems} />
          <i
            className="pi pi-spin pi-spinner"
            style={{
              marginTop: '20px',
              marginLeft: 'calc(50% - 20px)',
              fontSize: '2em',
            }}
          ></i>
        </div>
      );
    }
    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        {content}
      </PermissionProxy>
    );
  }
}

VariavelControleFormPage.propTypes = {
  id: PropTypes.any,
  action: PropTypes.any,
  history: PropTypes.any,
};

export default VariavelControleFormPage;
