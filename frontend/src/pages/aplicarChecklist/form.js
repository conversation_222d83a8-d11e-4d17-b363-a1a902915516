import React from 'react';
import { observer } from 'mobx-react';
import PropTypes from 'prop-types';
import UrlRouter from '~/constants/UrlRouter';
import GenericFormPage from 'fc/pages/GenericFormPage';
import PermissionProxy from 'fc/components/PermissionProxy';
import AccessPermission from '~/constants/AccessPermission';
import AplicarChecklistFormStore from '~/stores/aplicarChecklist/formStore';
import { InputText } from 'primereact/inputtext';
import FormField from 'fc/components/FormField';
import { InputTextarea } from 'primereact/inputtextarea';
import InputMonetary from 'fc/components/InputMonetary';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import { RadioButton } from 'primereact/radiobutton';
import './style.scss';
import moment from 'moment';
import { Divider } from 'primereact/divider';
import { ConfirmDialog } from 'primereact/confirmdialog';
import AppStore from 'fc/stores/AppStore';
import FcButton from 'fc/components/FcButton';
import MultipleFileUploader from 'fc/components/MultipleFileUploader';
import { showNotification, somaValoresLotes } from 'fc/utils/utils';
import { Fieldset } from 'primereact/fieldset';
import { CKEditor } from '@ckeditor/ckeditor5-react';
import TermoEditor from 'fc/editors/termoReferencia';
import Tooltip from 'fc/components/Tooltip';
import FcCalendar from 'fc/components/FcCalendar';
import { Message } from 'primereact/message';

@observer
class AplicarChecklistFormPage extends GenericFormPage {
  intervalSectionTable;
  EMPTY_TABLE_MESSAGE = 'Nenhum Ato Administrativo e Documentos a serem verificados';

  constructor(props) {
    super(props, UrlRouter.auditoria.analiseProcesso.index, AccessPermission.secaoChecklist);
    this.store = new AplicarChecklistFormStore(this.props.idProcesso, this.props.tipoProcesso);
    this._renderButtonChecklist = this._renderButtonChecklist.bind(this);
    this._renderTitleAto = this._renderTitleAto.bind(this);
    this._checaItensChecklist = this._checaItensChecklist.bind(this);
    this._renderConfirmDialogConcluir = this._renderConfirmDialogConcluir.bind(this);
    this.hasWritePermission = this.hasWritePermission.bind(this);
    this.verifySectionTableRendering = this.verifySectionTableRendering.bind(this);

    this.state = {
      submitted: false,
      expandedRows: [],
      lastSave: undefined,
      isConfirmDialogVisible: false,
      isConfirmDialogVisibleReport: false,
    };
  }

  componentDidMount() {
    this.intervalSectionTable = setInterval(this.verifySectionTableRendering, 500);
    const id = this.props.idProcesso;
    this.store.initialize(id, {}, () => {
      this.store.initializeArquivoChecklistFormStore(() => this.forceUpdate());
      this.store.getSecoesChecklistByProcesso(() => this.forceUpdate());
      this.store.initializeTda(() => {
        this.forceUpdate();
        this.hasWritePermission() && this.startAutomaticSave();
      });
    });
  }

  verifySectionTableRendering() {
    const tableRef = document.getElementById('section-table');
    if (tableRef) {
      if (tableRef.textContent.includes(this.EMPTY_TABLE_MESSAGE)) {
        clearInterval(this.intervalSectionTable);
      } else if (tableRef.getElementsByClassName('p-row-toggler')?.length > 1) {
        [...document.getElementsByClassName('p-row-toggler')].forEach((item) => item.click());
        clearInterval(this.intervalSectionTable);
      }
    }
  }

  hasWritePermission() {
    return !this.getWritePermission() || AppStore.hasPermission(this.getWritePermission());
  }

  startAutomaticSave() {
    this.store.changeFlagModifier(false);
    if (this.store.checkUserIsResponsavel()) {
      this.interval = setInterval(() => {
        if (this.store.houveModificacao) {
          this.store.secoesLoading = true;
          this.store.automaticSave(() => {
            this.store.secoesLoading = false;
            this.setState({ lastSave: moment() });
          });
        }
      }, 20000);
    }
  }

  renderActionButtons() {
    return (
      <div className="form-actions">
        <div className="p-mt-2">
          <span className="p-mr-1 p-d-inline p-d-flex align-items-center">
            <b className="p-ml-auto">
              {this.state.lastSave
                ? `Informações salvas pela última vez às ${this.state.lastSave.format('LTS')}`
                : 'Nenhuma alteração foi salva'}
            </b>
            <FcButton
              label="Voltar"
              type="button"
              className="p-ml-2 p-button-secondary p-mr-2"
              onClick={() => {
                clearInterval(this.interval);
                this._goBack();
              }}
              loading={this.store.loading}
            />
            {this.hasWritePermission() && (
              <FcButton
                label="Salvar"
                disabled={this.store.disabledButton()}
                type="submit"
                onClick={() => clearInterval(this.interval)}
                loading={this.store.loading}
              />
            )}
            <FcButton
              disabled={this.store.disabledButton()}
              label="Concluir"
              className="p-ml-2"
              onClick={(e) => this._checaItensChecklist(e)}
              loading={this.store.loading}
            />
          </span>
        </div>
      </div>
    );
  }

  _goBack() {
    if (this.props.editMode) {
      this.props.disableEditMode();
      this.props.reload && this.props.reload(this.props.idProcesso, this.props.tipoProcesso, this.props.idTab);
    } else {
      this.props.history.push(this.goBackUrl);
    }
  }

  _checaItensChecklist(event) {
    event.preventDefault();
    if (!this.store.tda?.relatorioChecklist || this.store.tda?.relatorioChecklist.length < 200) {
      showNotification('error', null, 'O relatório deve possuir ao menos 200 caracteres!');
    } else {
      const existeItemSemResposta = this.store.secoes
        .map((s) => s.itemChecklistProcesso.resposta)
        .includes('SELECIONE');
      if (existeItemSemResposta) {
        this.setState({ isConfirmDialogVisible: true });
      } else {
        this.store.updateStatusChecklist(this._goBack);
      }
    }
  }

  _renderConfirmDialogConcluir() {
    return (
      <ConfirmDialog
        visible={this.state.isConfirmDialogVisible}
        message="O preenchimento do checklist está incompleto. Deseja continuar?"
        header="Concluir"
        icon="pi pi-exclamation-triangle"
        accept={() => {
          clearInterval(this.interval);
          this.setState({ isConfirmDialogVisible: false });
          this.store.updateStatusChecklist(this._goBack);
        }}
        onHide={() => this.setState({ isConfirmDialogVisible: false })}
      />
    );
  }

  _renderConfirmDialogReport() {
    return (
      <ConfirmDialog
        visible={this.state.isConfirmDialogVisibleReport}
        message="O relatório será sobrescrito. Deseja continuar?"
        header="Gerar Relatório"
        icon="pi pi-exclamation-triangle"
        accept={() => {
          this.store.generateReport(() => this.forceUpdate());
        }}
        onHide={() => this.setState({ isConfirmDialogVisibleReport: false })}
      />
    );
  }

  _renderColumns(columns) {
    return columns.map((col) => <Column key={`col-${col.field}`} {...col} />);
  }

  headerTemplate(data) {
    return <span className={`customer-badge status`}>{data.secaoChecklist.titulo}</span>;
  }

  _renderIconByResposta(resposta) {
    if (resposta === 'SIM')
      return <i className="pi pi-check" style={{ marginLeft: '10%', fontSize: '2.5em', color: 'green' }} />;
    else if (resposta === 'NAO')
      return <i className="pi pi-times" style={{ marginLeft: '10%', fontSize: '2.5em', color: 'red' }} />;
    else if (resposta === 'JUSTIFICADO')
      return (
        <i className="pi pi-verified" style={{ marginLeft: '10%', fontSize: '2.5em', color: 'rgb(228, 226, 103)' }} />
      );
    else if (resposta === 'NAO_APLICA')
      return <i className="pi pi-exclamation-circle" style={{ marginLeft: '10%', fontSize: '2.5em', color: 'gray' }} />;
    else {
      return (
        <i
          className="pi pi-exclamation-circle"
          style={{ marginLeft: '10%', fontSize: '2.5em', color: 'rgba(0,0,0,0)' }}
        />
      );
    }
  }

  _renderButtonChecklist(rowData) {
    return (
      <div className="p-field-radiobutton p-row p-d-flex p-jc-center p-ai-center">
        {DadosEstaticosService.getSituacaoItemChecklist().map((situacao, index) => {
          return (
            <>
              <RadioButton
                key={index}
                value={situacao.value}
                checked={rowData.itemChecklistProcesso.resposta === situacao.value}
                style={{ marginLeft: '5%' }}
                disabled={this.store.disabledButton()}
                onChange={(event) => {
                  this.store.updateAtrributeRespostaChecklist(event, rowData);
                  this.forceUpdate();
                }}
              />
              <label>{situacao.text}</label>
            </>
          );
        })}
        {this._renderIconByResposta(rowData.itemChecklistProcesso.resposta)}
      </div>
    );
  }

  _renderTitleAto(rowData) {
    return (
      <p>
        `{rowData.itemChecklistProcesso.tituloItem} - {rowData.itemChecklistProcesso.legislacaoItem}`
      </p>
    );
  }

  getValuePregoeiroByTipoProcesso(tipoProcesso) {
    if (this.store.object) {
      if (tipoProcesso === 'carona' || tipoProcesso === 'dispensa') {
        return this.store.object[DadosEstaticosService.getColumnPregoeiroResponsavelByTipoProcesso(tipoProcesso)];
      } else if (tipoProcesso === 'inexigibilidade') {
        const responsavelHomologacao =
          this.store.object[
            DadosEstaticosService.getColumnPregoeiroResponsavelByTipoProcesso(tipoProcesso).responsavelHomologacao
          ];
        return responsavelHomologacao != ''
          ? responsavelHomologacao
          : this.store.object[
              DadosEstaticosService.getColumnPregoeiroResponsavelByTipoProcesso(tipoProcesso).responsavelRatificacao
            ];
      } else {
        return this.store.object[DadosEstaticosService.getColumnPregoeiroResponsavelByTipoProcesso(tipoProcesso)]?.nome;
      }
    }
  }

  _getHomeUrl() {
    const splited = document.location.href.split('#');
    return splited.length >= 1 ? splited[0] : '/';
  }

  _renderOrigemProcesso() {
    const processo = this.store.object;

    const links = {
      L: {
        link: UrlRouter.cadastrosConsulta.licitacao.detalhe.replace(':id', processo.id),
        text: `${`${processo?.entidade?.nome} - ${processo.numeroLicitacao}` ?? ''}`,
        permission: AccessPermission.licitacao.readPermission,
      },
      D: {
        link: UrlRouter.cadastrosConsulta.dispensa.detalhe.replace(':id', processo.id),
        text: `${processo?.entidade?.nome} - Dispensa ${processo.numeroProcesso ?? ''}`,
        permission: AccessPermission.dispensa.readPermission,
      },
      I: {
        link: UrlRouter.cadastrosConsulta.inexigibilidade.detalhe.replace(':id', processo.id),
        text: `${processo?.entidade?.nome} - Inexigibilidade ${processo.numeroProcesso ?? ''}`,
        permission: AccessPermission.inexigibilidade.readPermission,
      },
      C: {
        link: UrlRouter.cadastrosConsulta.carona.detalhe.replace(':id', processo.id),
        text: `${processo?.entidade?.nome} - Carona ${processo.numeroProcessoAdministrativo ?? ''}`,
        permission: AccessPermission.carona.readPermission,
      },
      CR: {
        link: UrlRouter.cadastrosConsulta.credenciamento.detalhe.replace(':id', processo.id),
        text: `${processo?.entidade?.nome} - Credenciamento ${processo.numeroProcessoAdministrativo ?? ''}`,
        permission: AccessPermission.credenciamento.readPermission,
      },
    };
    const typeProcess = DadosEstaticosService.getTipoProcesso().find(
      (tipoProcesso) => tipoProcesso.urlLabel === this.props.tipoProcesso
    )?.value;

    const linkProcesso = links[typeProcess];

    const element = AppStore.hasPermission(linkProcesso.permission) ? (
      <a
        href={`${this._getHomeUrl()}#${linkProcesso.link.replace(':id', processo.id)}`}
        style={{ color: '#3F51B5' }}
        target="_blank"
        rel="noreferrer"
      >
        <u>{linkProcesso.text}</u>
      </a>
    ) : (
      <p>{linkProcesso.text}</p>
    );
    return element;
  }

  render() {
    const { submitted } = this.state;
    const columnsChecklist = [
      {
        style: { width: '60%' },
        field: 'itemChecklistProcesso.tituloItem',
        header: 'Atos administrativos e documentos a serem verificados',
        body: this._renderTitleAto,
      },
      {
        style: { width: '40%' },
        field: 'itemChecklistProcesso.resposta',
        header: 'Sim/Não/Não se aplica',
        body: this._renderButtonChecklist,
      },
    ];

    const { submitFormData } = this;
    const { tipoProcesso } = this.props;

    let content;
    if (this.store.object && this.store.arquivoStore && this.store.arquivoStore.loadedFiles) {
      content = (
        <>
          <div className="card form-action-buttons">
            <form onSubmit={submitFormData}>
              <div className="p-fluid p-formgrid p-grid">
                <FormField columns={12} label="Identificador do Processo">
                  <div>{this._renderOrigemProcesso()}</div>
                </FormField>
                <FormField columns={4} label="Órgão">
                  <InputText placeholder="Órgão" value={this.store.object.entidade.nome} disabled />
                </FormField>
                <FormField columns={8} label="Objeto">
                  <InputTextarea placeholder="Objeto" value={this.store.object.objeto} disabled autoResize />
                </FormField>
                <FormField columns={3} label="Data da Abertura">
                  <FcCalendar
                    placeholder="Data da abertura"
                    value={this.getDateAttributeValue(
                      this.store.object[DadosEstaticosService.getColumnDataAberturaByTipoProcesso(tipoProcesso)]
                    )}
                    disabled
                  />
                </FormField>
                <FormField columns={3} label="Pregoeiro/Responsável">
                  <InputText
                    placeholder="Pregoeiro/Responsável"
                    value={this.store.object && (this.getValuePregoeiroByTipoProcesso(tipoProcesso) ?? '')}
                    disabled
                  />
                </FormField>
                <FormField columns={3} label="Valor Total Estimado">
                  <InputMonetary
                    placeholder="R$"
                    value={
                      tipoProcesso === 'credenciamento'
                        ? somaValoresLotes(this.store.object.termoReferencia.lotes)
                        : this.store.object[DadosEstaticosService.getColumnValorEstimadoByTipoProcesso(tipoProcesso)]
                    }
                    disabled
                  />
                </FormField>
                <FormField columns={3} label="Número do Processo">
                  <InputText
                    placeholder="Número do processo"
                    value={this.store.object[DadosEstaticosService.getColumnNumeroProcessoByTipoProcesso(tipoProcesso)]}
                    disabled
                  />
                </FormField>
                <FormField columns={12} label="Observação do Inspetor (TDA)">
                  <InputTextarea
                    placeholder="Observação do Inspetor (TDA)"
                    value={this.store.object.tda.observacao}
                    disabled
                    autoResize
                  />
                </FormField>
                {this.store.object.tda.checklistRejeitado && (
                  <Fieldset className="p-col-12" legend={<Message severity="error" text="Checklist Rejeitado" />}>
                    <FormField
                      columns={12}
                      attribute="motivoChecklistRejeitado"
                      submitted={submitted}
                      label="Motivo(s) de Rejeição pelo Inspetor"
                    >
                      <InputTextarea
                        value={this.store.object.tda.motivoChecklistRejeitado}
                        rows={5}
                        cols={30}
                        disabled
                      />
                    </FormField>
                  </Fieldset>
                )}
                <DataTable
                  rowHover
                  id="section-table"
                  emptyMessage={this.EMPTY_TABLE_MESSAGE}
                  style={{ width: '100%' }}
                  value={this.store.secoes}
                  rowGroupMode="subheader"
                  groupRowsBy="secaoChecklist.titulo"
                  responsiveLayout="scroll"
                  expandableRowGroups
                  expandedRows={this.state.expandedRows}
                  onRowToggle={(e) => this.setState({ expandedRows: e.data })}
                  rowGroupHeaderTemplate={this.headerTemplate}
                  loading={this.store.secoesLoading}
                >
                  {this._renderColumns(columnsChecklist)}
                </DataTable>
                <Divider />
                <Fieldset
                  className="p-col-12"
                  legend={
                    <span
                      style={{ color: 'rgba(0, 0, 0, 0.87)' }}
                      className="flex align-content-start align-items-center gap-2"
                    >
                      <Tooltip value="Gera o Relatório de Forma Automática" delayDuration={0}>
                        <div>
                          <FcButton
                            icon="pi pi-print"
                            className="p-button-sm gap-2"
                            type="button"
                            disabled={this.store.disabledButton()}
                            onClick={() =>
                              this.store.tda?.relatorioChecklist
                                ? this.setState({ isConfirmDialogVisibleReport: true })
                                : this.store.generateReport(() => this.forceUpdate())
                            }
                          >
                            Gerar Relatório
                          </FcButton>
                        </div>
                      </Tooltip>
                    </span>
                  }
                >
                  <FormField columns={12} attribute="relatorioChecklist" label="Relatório do Checklist">
                    <CKEditor
                      editor={TermoEditor}
                      data={this.store.tda?.relatorioChecklist}
                      onChange={(_, editor) => {
                        this.store.updateRelatorio(editor);
                      }}
                      disabled={this.store.disabledButton()}
                    />
                  </FormField>
                </Fieldset>
                <FormField columns={12} attribute="arquivos" label="Arquivos">
                  <MultipleFileUploader
                    store={this.store.arquivoStore.fileStore}
                    showFileType={false}
                    onChangeFiles={(files) => this.store.arquivoStore.setFileList(files)}
                    downloadOnly={this.store.disabledButton()}
                  />
                </FormField>
              </div>
              {}
              {this.renderActionButtons()}
              {this.state.isConfirmDialogVisible && this._renderConfirmDialogConcluir()}
              {this.state.isConfirmDialogVisibleReport && this._renderConfirmDialogReport()}
            </form>
          </div>
        </>
      );
    } else {
      content = (
        <div className="card page">
          <i className="pi pi-spin pi-spinner" style={{ fontSize: '2em' }}></i>
        </div>
      );
    }
    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        {content}
      </PermissionProxy>
    );
  }
}

AplicarChecklistFormPage.propTypes = {
  id: PropTypes.any,
  action: PropTypes.any,
  history: PropTypes.any,
  index: PropTypes.any,
  disableEditMode: PropTypes.func,
};

export default AplicarChecklistFormPage;
