import React from 'react';
import { observer } from 'mobx-react';
import PropTypes from 'prop-types';
import PublicacaoFormStore from '~/stores/publicacao/formStore';
import UrlRouter from '~/constants/UrlRouter';
import GenericFormPage from 'fc/pages/GenericFormPage';
import <PERSON><PERSON>ield from 'fc/components/FormField';
import { InputTextarea } from 'primereact/inputtextarea';
import AsyncDropdown from 'fc/components/AsyncDropdown';
import { Dialog } from 'primereact/dialog';
import FcButton from 'fc/components/FcButton';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { PrimeIcons } from 'primereact/api';
import { ConfirmDialog } from 'primereact/confirmdialog';
import { showNotification } from 'fc/utils/utils';
import FcCalendar from 'fc/components/FcCalendar';

@observer
class PublicacaoDialogFormPage extends GenericFormPage {
  constructor(props) {
    super(props, UrlRouter.cadastrosConsulta.licitacao.index);
    this.store = new PublicacaoFormStore();
    this.state = {
      showPublicacaoDialog: false,
      rowDataRemove: null,
      isConfirmDialogRemovePublicacaoVisible: false,
      isEdit: false,
    };
    this.setPublicacaoList = props.setPublicacaoList;

    this.switchPublicacaoDialog = this.switchPublicacaoDialog.bind(this);
  }

  componentDidMount() {
    const { id } = this.props;
    this.store.initialize(id, {}, () => this.setPublicacaoList(this.store.publicacaoList));
  }

  switchPublicacaoDialog() {
    this.setState({
      showPublicacaoDialog: !this.state.showPublicacaoDialog,
    });
  }

  renderColumns(columns) {
    return columns.map((col) => <Column key={`col-${col.field}`} {...col} />);
  }

  getPublicacaoDescLabel(tpFormaPublicacao) {
    let label = '';

    if (
      tpFormaPublicacao === 'Diário Oficial da União (DOU)' ||
      tpFormaPublicacao === 'Diário Oficial de Contas' ||
      tpFormaPublicacao === 'Diário Oficial do Estado (DOE)'
    ) {
      label = 'Número';
    } else {
      if (tpFormaPublicacao === 'Internet') label = 'Site';
      else if (tpFormaPublicacao === 'Jornal de grande circulação') label = 'Nome';
      else if (tpFormaPublicacao === 'Mural') label = 'Local';
      else label = 'Descrição';
    }

    return label;
  }

  submitFormData() {
    const execution = () => {
      if (!this.store.rules.hasError) {
        this.switchPublicacaoDialog();
        this.store.addPublicacao();
        this.setPublicacaoList(this.store.publicacaoList);
        this.setState({ submitted: false });
      } else {
        showNotification('error', null, 'Verifique os campos do formulário!');
      }
    };

    if (this.state.submitted) {
      execution();
    } else {
      this.setState({ submitted: true }, execution);
    }
  }

  renderActionLabel(state) {
    return state ? 'Editar' : 'Adicionar';
  }

  renderFooter(state) {
    return (
      <div>
        <FcButton
          label="Cancelar"
          icon="pi pi-times"
          onClick={() => {
            this.store.resetPublicacaoOldObject();
            this.switchPublicacaoDialog();
          }}
          className="p-button-text"
        />
        <FcButton label={this.renderActionLabel(state)} icon="pi pi-check" onClick={() => this.submitFormData()} />
      </div>
    );
  }

  renderDialogPublicacao(state) {
    const { submitted } = this.state;
    const { updateAttribute, getRule } = this.store;
    return (
      <Dialog
        header={`${this.renderActionLabel(state)} Publicação`}
        visible={this.state.showPublicacaoDialog}
        style={{ width: '50vw' }}
        footer={this.renderFooter(state)}
        onHide={() => this.switchPublicacaoDialog()}
      >
        <form>
          <div className="p-fluid p-formgrid p-grid">
            <FormField
              columns={6}
              attribute="tpFormaPublicacao"
              label="Formas de Publicação"
              rule={getRule('tpFormaPublicacao')}
              submitted={submitted}
            >
              <AsyncDropdown
                onChange={updateAttribute}
                value={this.store.object.tpFormaPublicacao?.id ?? undefined}
                placeholder="Selecione a forma de publicação"
                store={this.store.formaPublicacaoStore}
              />
            </FormField>

            <FormField
              columns={6}
              attribute="dataPublicacao"
              label="Data da Publicação"
              rule={getRule('dataPublicacao')}
              submitted={submitted}
            >
              <FcCalendar
                value={this.getDateAttributeValue(this.store.object.dataPublicacao)}
                onChange={(e) => this.store.updateAttributeDate('dataPublicacao', e)}
                hourFormat="24"
                showIcon
                mask="99/99/9999"
              />
            </FormField>

            <FormField
              columns={12}
              attribute="descricao"
              label={`${this.getPublicacaoDescLabel(this.store.object.tpFormaPublicacao?.nome ?? undefined)}`}
              rule={getRule('descricao')}
              submitted={submitted}
            >
              <InputTextarea
                onChange={(e) => {
                  let formatValue = e?.target?.value;
                  if (this.getPublicacaoDescLabel(this.store.object.tpFormaPublicacao?.nome ?? undefined) === 'Número')
                    formatValue = formatValue.replace(/[^0-9.,]/g, '');
                  this.store.updateAttribute('descricao', formatValue);
                }}
                placeholder="Informe a descrição"
                rows={1}
                autoResize
                value={this.store.object.descricao}
              />
            </FormField>

            {this.store.hasPagField && (
              <FormField columns={12} attribute="pagina" label="Página" rule={getRule('pagina')} submitted={submitted}>
                <InputTextarea
                  onChange={(e) => {
                    let formatValue = e?.target?.value?.replace(/\D/g, '');
                    this.store.updateAttribute('pagina', formatValue);
                  }}
                  placeholder="Informe a página"
                  rows={1}
                  autoResize
                  value={this.store.object.pagina}
                />
              </FormField>
            )}
          </div>
        </form>
      </Dialog>
    );
  }

  removePublicacao(rowDataKey) {
    return (
      <ConfirmDialog
        blockScroll
        visible={this.state.isConfirmDialogRemovePublicacaoVisible}
        message="Você reamente deseja remover esta publicação?"
        header="Excluir Publicação"
        icon="pi pi-info-circle"
        acceptClassName="p-button-danger"
        accept={() => {
          this.store.removePublicacao(rowDataKey);
          this.setPublicacaoList(this.store.publicacaoList);
          this.setState({ rowDataRemove: null, isConfirmDialogRemovePublicacaoVisible: false });
        }}
        onHide={() => this.setState({ isConfirmDialogRemovePublicacaoVisible: false })}
      />
    );
  }

  renderDescricao(rowData) {
    if (rowData.pagina !== '' && rowData.pagina !== undefined) {
      return `Nº ${rowData?.descricao}, Página ${rowData?.pagina}`;
    } else if (rowData.tpFormaPublicacao?.nome && rowData.tpFormaPublicacao?.nome.includes('Diário Oficial')) {
      return `Nº ${rowData?.descricao}`;
    }
    return rowData?.descricao ?? '';
  }

  render() {
    const columnsPublicacao = [
      {
        style: { width: '40%' },
        field: 'tpFormaPublicacao',
        header: 'Tipo',
        body: ({ tpFormaPublicacao }) => tpFormaPublicacao?.nome ?? '',
      },
      {
        style: { width: '45%' },
        field: 'descricao',
        header: 'Descrição',
        body: (rowData) => {
          return this.renderDescricao(rowData);
        },
      },
      !this.props.readOnly && {
        style: { width: '15%' },
        body: (rowData) => {
          return (
            <div className="actions p-d-flex p-jc-end" style={{ textAlign: 'center' }}>
              <FcButton
                icon="pi pi-pencil"
                className="p-button-success p-mr-2"
                type="button"
                onClick={() => {
                  this.state.isEdit = true;
                  this.store.setPublicacaoObject({ ...rowData });
                  this.switchPublicacaoDialog();
                }}
              />
              <FcButton
                icon="pi pi-trash"
                className="p-button p-button-danger"
                type="button"
                onClick={() => {
                  this.setState({ rowDataRemove: rowData.key, isConfirmDialogRemovePublicacaoVisible: true });
                }}
              />
            </div>
          );
        },
      },
    ];

    if (this.store.object) {
      return (
        <>
          <div className="p-field p-col-12">
            <div className="p-grid p-dir-col">
              <span className="p-mt-5 p-mb-5 p-ml-4">
                <b>Publicações</b>
              </span>
            </div>
            {!this.props.readOnly && (
              <div className="p-col-3">
                <FcButton
                  className="p-button p-mt-2 p-mb-2"
                  label="Adicionar Publicação"
                  icon={PrimeIcons.PLUS}
                  type="button"
                  onClick={() => {
                    this.state.isEdit = false;
                    this.store.resetPublicacaoObject();
                    this.switchPublicacaoDialog();
                  }}
                />
              </div>
            )}
            <DataTable
              rowHover
              value={this.store.publicacaoListKeyed}
              emptyMessage="Nenhuma publicação foi adicionada."
              paginator
              rows={5}
            >
              {this.renderColumns(columnsPublicacao)}
            </DataTable>
            {this.state.isConfirmDialogRemovePublicacaoVisible && this.removePublicacao(this.state.rowDataRemove)}
          </div>
          {this.renderDialogPublicacao(this.state.isEdit)}
        </>
      );
    } else {
      return (
        <i
          className="pi pi-spin pi-spinner"
          style={{
            marginTop: '20px',
            marginLeft: 'calc(50% - 20px)',
            fontSize: '2em',
          }}
        ></i>
      );
    }
  }
}

PublicacaoDialogFormPage.propTypes = {
  id: PropTypes.any,
  action: PropTypes.any,
  history: PropTypes.any,
  readOnly: PropTypes.bool,
};

export default PublicacaoDialogFormPage;
