import React from 'react';
import { observer } from 'mobx-react';
import PropTypes from 'prop-types';
import FundamentacaoLegalFormStore from '../../stores/fundamentacaoLegal/formStore';
import UrlRouter from '../../constants/UrlRouter';
import GenericFormPage from 'fc/pages/GenericFormPage';
import FormField from 'fc/components/FormField';
import AppBreadCrumb from 'fc/components/AppBreadCrumb';
import { Checkbox } from 'primereact/checkbox';
import PermissionProxy from 'fc/components/PermissionProxy';
import AccessPermission from '~/constants/AccessPermission';
import { InputText } from 'primereact/inputtext';
import { RadioButton } from 'primereact/radiobutton';
import { SelectButton } from 'primereact/selectbutton';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import FcInputTextarea from 'fc/components/FcInputTextarea';

@observer
class FundamentacaoLegalFormPage extends GenericFormPage {
  constructor(props) {
    super(props, UrlRouter.administracao.fundamentacaoLegal.index, AccessPermission.fundamentacaoLegal);

    this.store = new FundamentacaoLegalFormStore();
  }

  componentDidMount() {
    const { id } = this.props;
    this.store.initialize(id, { ativo: true, tipoDispensa: true, legislacao: 'LEI_N_14133' });
  }

  render() {
    const { submitted } = this.state;
    const { submitFormData } = this;
    const { updateAttribute, getRule } = this.store;

    const breacrumbItems = [
      { label: 'Fundamentação Legal', url: UrlRouter.administracao.fundamentacaoLegal.index },
      { label: this.props.action === 'new' ? 'Novo' : 'Editar' },
    ];

    let content;
    if (this.store.object) {
      content = (
        <>
          <AppBreadCrumb items={breacrumbItems} />
          <div className="card page form-action-buttons">
            <form onSubmit={submitFormData}>
              <div className="p-fluid p-formgrid p-grid">
                <FormField
                  columns={12}
                  attribute="fundamentacao"
                  label="Fundamentação"
                  rule={getRule('fundamentacao')}
                  submitted={submitted}
                >
                  <InputText
                    onChange={(e) => updateAttribute('fundamentacao', e)}
                    placeholder="Informe a fundamentação"
                    value={this.store.object.fundamentacao}
                  />
                </FormField>
                <FormField
                  columns={6}
                  attribute="legislacao"
                  label="A Fundamentação Legal será cadastrada conforme qual legislação?"
                  rule={getRule('legislacao')}
                  submitted={submitted}
                >
                  <SelectButton
                    optionLabel="text"
                    optionValue="value"
                    value={this.store.object.legislacao ?? 'LEI_N_8666'}
                    options={DadosEstaticosService.getTipoLicitacaoLegislacao()}
                    onChange={(e) => {
                      this.store.updateAttribute('legislacao', e);
                      this.forceUpdate();
                    }}
                  />
                </FormField>
                <FormField
                  columns={6}
                  attribute="tipoDispensa"
                  label="Se aplica a"
                  rule={getRule('tipoDispensa')}
                  submitted={submitted}
                >
                  <div className="p-field-radiobutton p-dir-row p-col-1">
                    <div className="p-field-radiobutton p-col">
                      <RadioButton
                        inputId="dispensa"
                        name="Dispensa"
                        value={true}
                        onChange={(e) => {
                          this.store.updateAttribute('tipoDispensa', e.value);
                          this.forceUpdate();
                        }}
                        checked={this.store.object.tipoDispensa}
                      />
                      <label htmlFor="dispensa">Dispensa</label>
                    </div>

                    <div className="p-field-radiobutton p-col">
                      <RadioButton
                        inputId="inexigibilidade"
                        name="Inexigibilidade"
                        value={false}
                        onChange={(e) => {
                          this.store.updateAttribute('tipoDispensa', e.value);
                          this.forceUpdate();
                        }}
                        checked={!this.store.object.tipoDispensa}
                      />
                      <label htmlFor="inexigibilidade">Inexigibilidade</label>
                    </div>
                  </div>
                </FormField>

                <FormField
                  columns={12}
                  attribute="descricaoComplementar"
                  label="Descrição complementar"
                  rule={getRule('descricaoComplementar')}
                  submitted={submitted}
                >
                  <FcInputTextarea
                    rows={6}
                    cols={30}
                    onChange={(e) => updateAttribute('descricaoComplementar', e)}
                    placeholder="Informe a descrição complementar"
                    value={this.store.object.descricaoComplementar}
                  />
                </FormField>

                <FormField columns={6} attribute="ativo" label="Ativo" checkbox submitted={submitted}>
                  <Checkbox
                    inputId="ativo"
                    checked={this.store.object.ativo}
                    onChange={(e) => this.store.updateAttributeCheckbox('ativo', e)}
                    id="ativo"
                  />
                </FormField>
              </div>
              {this.renderActionButtons()}
            </form>
          </div>
        </>
      );
    } else {
      content = (
        <div className="card page">
          <AppBreadCrumb items={breacrumbItems} />
          <i
            className="pi pi-spin pi-spinner"
            style={{
              marginTop: '20px',
              marginLeft: 'calc(50% - 20px)',
              fontSize: '2em',
            }}
          ></i>
        </div>
      );
    }
    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        {content}
      </PermissionProxy>
    );
  }
}

FundamentacaoLegalFormPage.propTypes = {
  id: PropTypes.any,
  action: PropTypes.any,
  history: PropTypes.any,
};

export default FundamentacaoLegalFormPage;
