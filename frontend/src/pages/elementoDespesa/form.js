import React from 'react';
import { observer } from 'mobx-react';
import PropTypes from 'prop-types';
import ElementoDespesaFormStore from '../../stores/elementoDespesa/formStore';
import { InputText } from 'primereact/inputtext';
import UrlRouter from '../../constants/UrlRouter';
import GenericFormPage from 'fc/pages/GenericFormPage';
import FormField from 'fc/components/FormField';
import AppBreadCrumb from 'fc/components/AppBreadCrumb';
import AccessPermission from '~/constants/AccessPermission';
import PermissionProxy from 'fc/components/PermissionProxy';

@observer
class ElementoDespesaFormPage extends GenericFormPage {
  constructor(props) {
    super(props, UrlRouter.administracao.elementoDespesa.index, AccessPermission.elementoDespesa);

    this.store = new ElementoDespesaFormStore();
  }

  render() {
    const { submitted } = this.state;
    const { updateAttribute, getRule } = this.store;
    const { submitFormData } = this;

    const breacrumbItems = [
      { label: 'Elementos de Despesa', url: UrlRouter.administracao.elementoDespesa.index },
      { label: this.props.action === 'new' ? 'Novo' : 'Editar' },
    ];

    let content;

    if (this.store.object) {
      content = (
        <>
          <AppBreadCrumb items={breacrumbItems} />
          <div className="card page form-action-buttons">
            <form onSubmit={submitFormData}>
              <div className="p-fluid p-formgrid p-grid">
                <FormField columns={6} attribute="numero" label="Número" rule={getRule('numero')} submitted={submitted}>
                  <InputText
                    onChange={(e) => updateAttribute('numero', e)}
                    placeholder="Informe o número"
                    value={this.store.object.numero}
                  />
                </FormField>

                <FormField
                  columns={6}
                  attribute="descricao"
                  label="Descricão"
                  rule={getRule('descricao')}
                  submitted={submitted}
                >
                  <InputText
                    onChange={(e) => updateAttribute('descricao', e)}
                    placeholder="Informe a descrição"
                    value={this.store.object.descricao}
                  />
                </FormField>
              </div>
              {this.renderActionButtons()}
            </form>
          </div>
        </>
      );
    } else {
      content = (
        <div className="card page">
          <AppBreadCrumb items={breacrumbItems} />
          <i
            className="pi pi-spin pi-spinner"
            style={{
              marginTop: '20px',
              marginLeft: 'calc(50% - 20px)',
              fontSize: '2em',
            }}
          ></i>
        </div>
      );
    }
    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        {content}
      </PermissionProxy>
    );
  }
}

ElementoDespesaFormPage.propTypes = {
  id: PropTypes.any,
  action: PropTypes.any,
  history: PropTypes.any,
};

export default ElementoDespesaFormPage;
