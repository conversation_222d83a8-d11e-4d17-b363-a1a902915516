import { observer } from 'mobx-react';
import moment from 'moment';
import PropTypes from 'prop-types';
import AnulacaoRevogacaoFormStore from '~/stores/anulacaoRevogacao/formStore';
import RequisicaoModificacaoFormStore from '~/stores/requisicaoModificacao/formStore';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import { Dialog } from 'primereact/dialog';
import { Fieldset } from 'primereact/fieldset';
import { InputTextarea } from 'primereact/inputtextarea';
import GenericFormPage from 'fc/pages/GenericFormPage';
import FormField from 'fc/components/FormField';
import FcDropdown from 'fc/components/FcDropdown';
import FcButton from 'fc/components/FcButton';
import PermissionProxy from 'fc/components/PermissionProxy';
import MultipleFileUploader from 'fc/components/MultipleFileUploader';
import FcCalendar from 'fc/components/FcCalendar';
import { isValueValid, showNotification } from 'fc/utils/utils';

@observer
class AnulacaoRevogacaoModal extends GenericFormPage {
  constructor(props) {
    super(props);
    this.store = new AnulacaoRevogacaoFormStore();
    this.reqModificacaoStore = new RequisicaoModificacaoFormStore();

    this.state = {
      showDialogReqMod: false,
    };
  }

  componentDidMount() {
    const { selectedRow, tipoProcessoAssociado } = this.props;

    if (selectedRow) {
      const idAnulacaoRevogacao = this.props.selectedRow.anulacaoRevogacao?.id;
      this.store.initialize(tipoProcessoAssociado, idAnulacaoRevogacao, {}, () => {
        this.store.initializeArquivos(idAnulacaoRevogacao, () => this.forceUpdate());
        if (idAnulacaoRevogacao) {
          this.isReqMod = true;
        }
      });
    }
  }

  validadeDialogAndSubmit(selectedRow) {
    const { closeDialog, updateTable } = this.props;
    const { rules, arquivosList } = this.store;
    const execution = () => {
      if (rules.hasError) {
        showNotification('error', null, 'Verifique os campos do formulário!');
      } else if (this.store.validateSubmittedFiles(arquivosList)) {
        this.store.validaArquivos(() =>
          this.store.anularRevogarProcesso(
            selectedRow?.id,
            selectedRow?.numeroProcesso ? selectedRow.numeroProcesso : selectedRow.numeroProcessoAdministrativo,
            () => {
              updateTable();
              closeDialog();
              this.setState({ submitted: false });
            }
          )
        );
      }
    };
    if (this.state.submitted) {
      execution();
    } else {
      this.setState({ submitted: true }, execution);
    }
  }

  submitReqMod() {
    const { closeDialog, updateTable } = this.props;

    const anulacaoRevogacaoDTO = {
      anulacaoRevogacao: this.store.object,
      arquivos: this.store.arquivosList,
    };

    this.isReqMod &&
      this.reqModificacaoStore.justificativaJurisdicionado &&
      this.reqModificacaoStore.enviarRequisicaoAnulacaoRevogacao(anulacaoRevogacaoDTO, () => {
        this._toggleDialogReqMod();
        closeDialog();
        updateTable();
      });
  }

  _toggleDialogReqMod() {
    if (!this.store.rules.hasError) {
      this.setState((oldState) => ({ showDialogReqMod: !oldState.showDialogReqMod }));
    } else {
      showNotification('error', null, 'Verifique os campos do formulário!');
    }
  }

  renderDialogReqModFooter() {
    return (
      <div>
        <FcButton
          label="Cancelar"
          icon="pi pi-times"
          onClick={() => this._toggleDialogReqMod()}
          className="p-button-text"
        />
        <FcButton
          label="Enviar Requisição"
          icon="pi pi-check"
          loading={this.reqModificacaoStore.loading}
          onClick={() => {
            if (this.reqModificacaoStore.justificativaJurisdicionado) {
              this.submitReqMod();
              this._toggleDialogReqMod();
            }
          }}
        />
      </div>
    );
  }

  renderDialogRequisicaoModificacao() {
    return (
      <Dialog
        header="Justificativa Requisição"
        visible={this.state.showDialogReqMod}
        style={{ width: '50vw' }}
        footer={this.renderDialogReqModFooter()}
        onHide={() => this._toggleDialogReqMod()}
      >
        <div className="p-fluid p-formgrid p-grid">
          <FormField columns={12} attribute="justificativa" label="Justificativa">
            <InputTextarea
              value={this.reqModificacaoStore.justificativaJurisdicionado}
              onChange={(e) => {
                this.reqModificacaoStore.updateAttribute('jurisdicionado', e);
                isValueValid(this.reqModificacaoStore.justificativaJurisdicionado?.length);
              }}
              placeholder="Descreva a justificativa"
            />
          </FormField>
        </div>
      </Dialog>
    );
  }

  _renderFooter() {
    const { selectedRow, closeDialog, getWritePermission, updateTable } = this.props;
    return (
      <div>
        <FcButton
          label="Cancelar"
          icon="pi pi-times"
          onClick={() => {
            updateTable();
            closeDialog();
            this.isReqMod = false;
          }}
          className="p-button-text"
        />
        <PermissionProxy resourcePermissions={getWritePermission()} blockOnFail>
          {!this.isReqMod ? (
            <FcButton
              label="Salvar"
              icon="pi pi-check"
              type="button"
              onClick={() => this.validadeDialogAndSubmit(selectedRow)}
              loading={this.store.loading}
            />
          ) : (
            <FcButton
              label="Enviar Requisição"
              type="button"
              className="p-ml-auto p-mr-2"
              onClick={() =>
                this.store.validateSubmittedFiles(this.store.arquivosList) &&
                this.store.validaArquivos(() => {
                  this._toggleDialogReqMod();
                })
              }
              loading={this.reqModificacaoStore.loading}
            />
          )}
        </PermissionProxy>
      </div>
    );
  }

  render() {
    const { showAnularRevogar, closeDialog, updateTable, tipoProcessoAssociado } = this.props;
    const { object, getRule, updateAttribute, fileStore } = this.store;
    const { submitted } = this.state;

    if (this.store.object) {
      return (
        <div>
          <Dialog
            blockScroll
            header="Anular/Revogar Processo"
            visible={showAnularRevogar}
            style={{ width: '55vw' }}
            footer={this._renderFooter()}
            onHide={() => {
              closeDialog();
              updateTable();
            }}
          >
            <form>
              {this.isReqMod && (
                <Fieldset legend="AVISO">
                  <h6 style={{ color: '#dd0303' }}>
                    A EDIÇÃO DESTA ANULAÇÃO/REVOGAÇÃO ESTARÁ SUJEITA A RATIFICAÇÃO PELA EQUIPE DE AUDITORIA DO TCE
                  </h6>
                </Fieldset>
              )}
              <div className="p-fluid p-formgrid p-grid">
                <FormField
                  columns={6}
                  attribute="tipoOcorrencia"
                  label="Tipo da Ocorrência"
                  rule={getRule('tipoOcorrencia')}
                  submitted={submitted}
                >
                  <FcDropdown
                    {...this.validateField('tipoOcorrencia')}
                    inOrder
                    onChange={(e) => {
                      updateAttribute('tipoOcorrencia', e);
                      tipoProcessoAssociado === 'CREDENCIAMENTO' && this.store.loadTipos(tipoProcessoAssociado);
                    }}
                    placeholder="Informe o motivo da ocorrência"
                    value={object?.tipoOcorrencia}
                    id="tipoOcorrencia"
                    optionLabel="text"
                    optionValue="value"
                    options={DadosEstaticosService.getValueAnulacaoRevogacao()}
                  />
                </FormField>
                {tipoProcessoAssociado === 'CREDENCIAMENTO' && (
                  <FormField
                    columns={6}
                    attribute="dataAviso"
                    label="Data da publicação do aviso"
                    rule={getRule('dataAviso')}
                    submitted={submitted}
                  >
                    <FcCalendar
                      value={object?.dataAviso ? moment(this.store.object.dataAviso)._d : null}
                      onChange={(e) => updateAttribute('dataAviso', e)}
                      id="datAviso"
                      showIcon
                      mask="99/99/9999"
                    />
                  </FormField>
                )}
                <FormField
                  columns={12}
                  attribute="descricao"
                  label="Descrição da Ocorrência"
                  rule={getRule('descricao')}
                  submitted={submitted}
                >
                  <InputTextarea
                    {...this.validateField('descricao')}
                    onChange={(e) => {
                      updateAttribute('descricao', e);
                    }}
                    placeholder="Informe a descrição da ocorrência"
                    rows={3}
                    autoResize
                    value={object?.descricao}
                  />
                </FormField>
              </div>
              {this.props.tipoProcessoAssociado === 'CREDENCIAMENTO' ? (
                this.store.object?.tipoOcorrencia && (
                  <Fieldset legend="Arquivos">
                    <div className="p-col-12">
                      <MultipleFileUploader
                        store={fileStore}
                        onChangeFiles={(files) => this.store.setArquivosList(files)}
                        fileTypes={DadosEstaticosService.getTipoArquivoAnulacaoRevogacao()}
                      />
                    </div>
                  </Fieldset>
                )
              ) : (
                <Fieldset legend="Arquivos">
                  <div className="p-col-12">
                    <MultipleFileUploader
                      store={fileStore}
                      onChangeFiles={(files) => this.store.setArquivosList(files)}
                      fileTypes={DadosEstaticosService.getTipoArquivoAnulacaoRevogacao()}
                    />
                  </div>
                </Fieldset>
              )}
            </form>
          </Dialog>
          {this.renderDialogRequisicaoModificacao()}
        </div>
      );
    } else {
      return (
        <i
          className="pi pi-spin pi-spinner"
          style={{
            marginTop: '20px',
            marginLeft: 'calc(50% - 20px)',
            fontSize: '2em',
          }}
        ></i>
      );
    }
  }
}

AnulacaoRevogacaoModal.propTypes = {
  showAnularRevogar: PropTypes.bool.isRequired,
  closeDialog: PropTypes.func.isRequired,
  selectedRow: PropTypes.any,
  getWritePermission: PropTypes.func.isRequired,
  tipoProcessoAssociado: PropTypes.any.isRequired,
  updateTable: PropTypes.func.isRequired,
};

export default AnulacaoRevogacaoModal;
