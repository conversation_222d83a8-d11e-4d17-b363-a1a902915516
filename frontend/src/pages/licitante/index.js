import React from 'react';
import { observer } from 'mobx-react';
import LicitanteIndexStore from '~/stores/licitante/indexStore';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import IndexDataTable from 'fc/components/IndexDataTable';
import FcButton from 'fc/components/FcButton';
import { PrimeIcons } from 'primereact/api';
import AppBreadCrumb from 'fc/components/AppBreadCrumb';
import UrlRouter from '~/constants/UrlRouter';
import GenericIndexPage from 'fc/pages/GenericIndexPage';
import AdvancedSearch from 'fc/components/AdvancedSearch';
import PermissionProxy from 'fc/components/PermissionProxy';
import AccessPermission from '~/constants/AccessPermission';
import { Dialog } from 'primereact/dialog';
import { Divider } from 'primereact/divider';
import { getValueByKey } from 'fc/utils/utils';

@observer
class LicitanteIndexPage extends GenericIndexPage {
  constructor(props) {
    super(props, AccessPermission.licitante);
    this.store = new LicitanteIndexStore();

    this.state = {
      detalhesVisibility: false,
      selectedRow: null,
      idRemove: null,
    };
  }

  _renderValue(label, value, col = 12) {
    return (
      <div className={`p-mt-3 p-col-12 p-md-${col} p-sm-${col >= 6 ? col : 6}`}>
        <div className="p-col-12 drawer-content-label">{label}</div>
        <div className={`p-col-12`}>{value ? value : '-'}</div>
      </div>
    );
  }

  renderDetalhesDialog() {
    const header = (
      <div style={{ width: '100%', textAlign: 'left' }}>
        <h4 style={{ margin: 0 }}>Detalhes</h4>
      </div>
    );

    return (
      <Dialog
        header={header}
        visible={this.state.detalhesVisibility}
        style={{ width: '40%' }}
        breakpoints={{ '960px': '75vw', '640px': '75vw' }}
        onHide={() => this.setState({ detalhesVisibility: false })}
        draggable={false}
        resizable={false}
        dismissableMask
      >
        <div>
          <Divider />
          <div className="p-grid">
            {this._renderValue('Nome', this.state.selectedRow.nome, 6)}
            {this._renderValue(
              'Tipo de Pessoa',
              getValueByKey(this.state.selectedRow.pessoaFisica, DadosEstaticosService.getTipoPessoa()),
              6
            )}
            {this._renderValue(
              'Pessoa/Empresa Internacional',
              getValueByKey(this.state.selectedRow.internacional, DadosEstaticosService.getSimNao()),
              6
            )}
            {this._renderValue('CPF/CNPJ', this.state.selectedRow.cpfCnpj, 6)}
            {this._renderValue('Endereço', this.state.selectedRow.endereco, 6)}
          </div>
        </div>
      </Dialog>
    );
  }

  render() {
    const columns = [
      {
        field: 'nome',
        header: 'Nome',
        sortable: true,
      },
      {
        field: 'pessoaFisica',
        header: 'Tipo de Pessoa',
        body: ({ pessoaFisica }) => (pessoaFisica ? 'Física' : 'Jurídica'),
        sortable: true,
        style: { width: '10%' },
      },
      {
        field: 'internacional',
        header: 'Pessoa/Empresa Internacional',
        body: ({ internacional }) => (internacional ? 'Sim' : 'Não'),
        sortable: true,
        style: { width: '10%' },
      },
      {
        field: 'cpfCnpj',
        header: 'CPF/CNPJ',
        sortable: true,
        style: { width: '20%' },
      },
      {
        style: { width: '165px' },
        body: (rowData) => {
          return (
            <div className="actions p-d-flex p-jc-end">
              <PermissionProxy resourcePermissions={this.getReadPermission()}>
                <FcButton
                  icon="pi pi-list"
                  className="p-button-sm p-button-info p-mr-2"
                  onClick={() => this.setState({ detalhesVisibility: true, selectedRow: rowData })}
                />
              </PermissionProxy>
              <PermissionProxy resourcePermissions={['admin']}>
                <FcButton
                  icon="pi pi-pencil"
                  className="p-button-sm p-button-success p-mr-2"
                  onClick={() =>
                    this.pushUrlToHistory(UrlRouter.administracao.licitante.editar.replace(':id', rowData.id))
                  }
                />
              </PermissionProxy>
              <PermissionProxy resourcePermissions={this.getWritePermission()}>
                <FcButton
                  icon="pi pi-trash"
                  className="p-button-sm p-button-danger"
                  onClick={() => {
                    this.setState({ idRemove: rowData.id });
                    this.store.toggleShowConfirmDialog();
                  }}
                />
              </PermissionProxy>
            </div>
          );
        },
      },
    ];

    const header = (
      <div className="table-header">
        <PermissionProxy resourcePermissions={this.getWritePermission()}>
          <FcButton
            className="p-button"
            label="Novo"
            style={{ marginBottom: '5px', marginRight: '5px' }}
            icon={PrimeIcons.PLUS}
            onClick={() => this.pushUrlToHistory(UrlRouter.administracao.licitante.novo)}
          />
        </PermissionProxy>
        {this.renderTableDataExport(columns, 'licitantes')}
      </div>
    );

    const { listKey, loading } = this.store;
    const { getDefaultTableProps } = this;

    const breacrumbItems = [{ label: 'Licitantes' }];

    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        <AppBreadCrumb items={breacrumbItems} />
        <div className="card page index-table">
          <AdvancedSearch
            searchParams={this.store.getAdvancedSearchParams()}
            store={this.store}
            searchFields={['nome']}
          />
          <IndexDataTable
            setColumnsToExport={this.setColumnsToExport}
            columns={columns}
            value={listKey}
            header={header}
            loading={loading}
            {...getDefaultTableProps()}
          />
          {this.store.isConfirmDialogVisible && this.confirmRemove(this.state.idRemove)}
          {this.state.selectedRow && this.renderDetalhesDialog()}
        </div>
      </PermissionProxy>
    );
  }
}

LicitanteIndexPage.displayName = 'LicitanteIndexPage';

export default LicitanteIndexPage;
