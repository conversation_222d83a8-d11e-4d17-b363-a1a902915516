import React from 'react';
import { observer } from 'mobx-react';
import PropTypes from 'prop-types';
import LicitanteFormStore from '~/stores/licitante/formStore';
import UrlRouter from '~/constants/UrlRouter';
import GenericFormPage from 'fc/pages/GenericFormPage';
import AppBreadCrumb from 'fc/components/AppBreadCrumb';
import Form<PERSON>ield from 'fc/components/FormField';
import { RadioButton } from 'primereact/radiobutton';
import { InputMask } from 'primereact/inputmask';
import { InputTextarea } from 'primereact/inputtextarea';
import PermissionProxy from 'fc/components/PermissionProxy';
import AccessPermission from '~/constants/AccessPermission';
import FcButton from 'fc/components/FcButton';
import { Divider } from 'primereact/divider';
import { ConfirmDialog } from 'primereact/confirmdialog';
import { InputText } from 'primereact/inputtext';

@observer
class LicitanteFormPage extends GenericFormPage {
  constructor(props) {
    super(props, UrlRouter.administracao.licitante.index, AccessPermission.licitante);
    this.store = new LicitanteFormStore();
  }

  _goBack(licitante) {
    this.props.index === 'licitante' ? this.props.history.push(this.goBackUrl) : this.props.closeMethod(licitante);
  }

  componentDidMount() {
    const { id } = this.props;
    this.store.initialize(id, { pessoaFisica: true, status: 'ATIVO' });
  }

  renderDialog(callback) {
    return (
      <ConfirmDialog
        blockScroll
        visible={this.store.reativarLicitanteDialogVisible}
        message="Registro já existe e foi desativado, deseja reativá-lo?"
        header="Reativar registro"
        icon="pi pi-user"
        acceptClassName="p-button-info"
        onHide={() => this.store.toggleDialogVisibility()}
        accept={() => {
          this.store.reativarLicitante(callback);
          this.store.toggleDialogVisibility();
        }}
      />
    );
  }

  renderFormDialogActionButtons() {
    return (
      <div className={this.props.index === 'licitante' ? 'p-mt-10 form-actions' : 'p-mt-10 form-actions-dialog'}>
        <Divider />
        <span className="p-d-flex">
          <FcButton
            label="Voltar"
            type="button"
            className="p-ml-auto p-button-secondary p-mr-2"
            onClick={() => this._goBack()}
            loading={this.store.loading}
          />
          <PermissionProxy resourcePermissions={this.getWritePermission()} blockOnFail>
            <FcButton label="Salvar" type="button" onClick={this.submitFormData} loading={this.store.loading} />
          </PermissionProxy>
        </span>
      </div>
    );
  }
  render() {
    const { submitted } = this.state;
    const { getRule } = this.store;
    const { submitFormData } = this;

    const breacrumbItems = [
      { label: 'Licitantes', url: UrlRouter.administracao.licitante.index },
      { label: this.props.action === 'new' ? 'Novo' : 'Editar' },
    ];

    let content;
    if (this.store.object) {
      content = (
        <>
          {this.props.index === 'licitante' ? <AppBreadCrumb items={breacrumbItems} /> : ''}
          <div className="card page form-action-buttons">
            <form onSubmit={submitFormData}>
              <div className="p-fluid p-formgrid p-grid">
                <FormField
                  columns={this.props.index === 'licitante' ? 6 : 12}
                  attribute="pessoaFisica"
                  label="Pessoa"
                  rule={getRule('pessoaFisica')}
                  submitted={submitted}
                >
                  <div className="p-field-radiobutton p-dir-row">
                    <div className="p-field-radiobutton p-col">
                      <RadioButton
                        inputId="pessoaFisica"
                        name="fisica"
                        value={true}
                        onChange={(e) => this.store.updateAttribute('pessoaFisica', e.value)}
                        checked={this.store.object.pessoaFisica === true}
                      />
                      <label htmlFor="pessoaFisica">Física</label>
                    </div>

                    <div className="p-field-radiobutton p-col">
                      <RadioButton
                        inputId="pessoaJuridica"
                        name="juridica"
                        value={false}
                        onChange={(e) => this.store.updateAttribute('pessoaFisica', e.value)}
                        checked={this.store.object.pessoaFisica === false}
                      />
                      <label htmlFor="pessoaJuridica">Jurídica</label>
                    </div>
                  </div>
                </FormField>

                <FormField
                  columns={this.props.index === 'licitante' ? 6 : 12}
                  attribute="internacional"
                  label="Pessoa/Empresa Internacional"
                  rule={getRule('internacional')}
                  submitted={submitted}
                >
                  <div className="p-field-radiobutton p-dir-row">
                    <div className="p-field-radiobutton p-col">
                      <RadioButton
                        inputId="internacional"
                        name="internacional"
                        value={true}
                        onChange={(e) => {
                          if (this.store.object.internacional !== e.value) {
                            this.store.updateAttribute('cpfCnpj', '');
                          }
                          this.store.updateAttribute('internacional', e.value);
                        }}
                        checked={this.store.object.internacional}
                      />
                      <label htmlFor="internacional">Sim</label>
                    </div>

                    <div className="p-field-radiobutton p-col">
                      <RadioButton
                        inputId="naoInternacional"
                        name="naoInternacional"
                        value={false}
                        onChange={(e) => this.store.updateAttribute('internacional', e.value)}
                        checked={!this.store.object.internacional}
                      />
                      <label htmlFor="naoInternacional">Não</label>
                    </div>
                  </div>
                </FormField>

                {this.store.object.internacional ? (
                  <FormField
                    columns={this.props.index === 'licitante' ? 4 : 12}
                    attribute="cpfCnpj"
                    label="CPF/CNPJ"
                    rule={getRule('cpfCnpj')}
                    submitted={submitted}
                  >
                    <InputText
                      onChange={(e) => this.store.updateAttribute('cpfCnpj', e)}
                      placeholder="Informe o CPF/CNPJ"
                      value={this.store.object.cpfCnpj}
                      id="cpfCnpj"
                    />
                  </FormField>
                ) : (
                  <FormField
                    columns={this.props.index === 'licitante' ? 4 : 12}
                    attribute="cpfCnpj"
                    label="CPF/CNPJ"
                    rule={getRule('cpfCnpj')}
                    submitted={submitted}
                  >
                    <InputMask
                      mask={this.store.object.pessoaFisica ? '999.999.999-99' : '99.999.999/9999-99'}
                      onChange={(e) => this.store.updateAttribute('cpfCnpj', e)}
                      placeholder="Informe o CPF/CNPJ"
                      value={this.store.object.cpfCnpj}
                      id="cpfCnpj"
                    />
                  </FormField>
                )}

                <FormField
                  columns={this.props.index === 'licitante' ? 4 : 12}
                  attribute="nome"
                  label="Nome"
                  rule={getRule('nome')}
                  submitted={submitted}
                >
                  <InputTextarea
                    rows={1}
                    autoResize={true}
                    onChange={(e) => this.store.updateAttribute('nome', e)}
                    placeholder="Informe o nome"
                    value={this.store.object.nome}
                    id="nomeLicitante"
                  />
                </FormField>

                <FormField
                  columns={this.props.index === 'licitante' ? 4 : 12}
                  attribute="endereco"
                  label="Endereço"
                  rule={getRule('endereco')}
                  submitted={submitted}
                >
                  <InputTextarea
                    rows={1}
                    autoResize={true}
                    onChange={(e) => this.store.updateAttribute('endereco', e)}
                    placeholder="Informe o endereço"
                    value={this.store.object.endereco}
                    id="enderecoLicitante"
                  />
                </FormField>
              </div>
              {this.props.index === 'licitante' ? this.renderActionButtons() : this.renderFormDialogActionButtons()}
              {this.store.reativarLicitanteDialogVisible && this.renderDialog(this._goBack)}
            </form>
          </div>
        </>
      );
    } else {
      content = (
        <div className="card page">
          <AppBreadCrumb items={breacrumbItems} />
          <i
            className="pi pi-spin pi-spinner"
            style={{
              marginTop: '20px',
              marginLeft: 'calc(50% - 20px)',
              fontSize: '2em',
            }}
          ></i>
        </div>
      );
    }
    return (
      <PermissionProxy
        resourcePermissions={this.props.action === 'edit' ? ['admin'] : this.getWritePermission()}
        blockOnFail
      >
        {content}
      </PermissionProxy>
    );
  }
}

LicitanteFormPage.propTypes = {
  id: PropTypes.any,
  action: PropTypes.any,
  history: PropTypes.any,
  index: PropTypes.any,
};

export default LicitanteFormPage;
