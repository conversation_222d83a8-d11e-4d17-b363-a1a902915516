import React from 'react';
import { observer } from 'mobx-react';
import GenericFormPage from 'fc/pages/GenericFormPage';
import UrlRouter from '~/constants/UrlRouter';
import PropTypes from 'prop-types';
import OcorrenciaLicitacaoFormStore from '~/stores/ocorrenciaLicitacao/formStore';
import FormField from 'fc/components/FormField';
import { InputTextarea } from 'primereact/inputtextarea';
import { Dialog } from 'primereact/dialog';
import FcButton from 'fc/components/FcButton';
import { showNotification } from 'fc/utils/utils';
import AccessPermission from '~/constants/AccessPermission';
import PermissionProxy from 'fc/components/PermissionProxy';
import MultipleFileUploader from 'fc/components/MultipleFileUploader';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import FcDropdown from 'fc/components/FcDropdown';
import { confirmDialog, ConfirmDialog } from 'primereact/confirmdialog';
import FcCalendar from 'fc/components/FcCalendar';
import RequisicaoReaberturaModal from '../requisicaoReabertura/requisicaoReaberturaModal';

@observer
class OcorrenciaReabrirFormPage extends GenericFormPage {
  constructor(props) {
    super(props, UrlRouter.cadastrosConsulta.licitacao.index, AccessPermission.ocorrenciaLicitacao);
    this.store = new OcorrenciaLicitacaoFormStore();

    this.state = {
      showRequisicaoReabertura: false,
    };
    this._closeTabs = this._closeTabs.bind(this);
    this._toggleDialogReqMod = this._toggleDialogReqMod.bind(this);
    this._closeRequisicaoReabertura = this._closeRequisicaoReabertura.bind(this);
  }

  componentDidMount() {
    this.store.initialize(this.props.idLicitacao, 'REABRIR');
    this.store.initializeFormValues('REABRIR');
  }

  _closeTabs() {
    this.props.switchReabrirDialog();
    this.store.resetObject();
    this.setState({ submitted: false });
    this.props.reload(this.props.idLicitacao);
  }

  _renderFooter() {
    return (
      <div>
        <FcButton
          label="Cancelar"
          icon="pi pi-times"
          onClick={() => this.props.switchReabrirDialog()}
          className="p-button-text"
        />
        <PermissionProxy resourcePermissions={this.getWritePermission()} blockOnFail>
          <FcButton
            label="Enviar Requisição"
            icon="pi pi-check"
            loading={this.store.loading}
            type="button"
            onClick={() => {
              if (this.store.pendencias?.length) {
                confirmDialog({
                  message: (
                    <div>
                      <p style={{ color: 'red' }}>
                        A licitação possui adesões/caronas. Ao reabrir a licitação, essas caronas serão invalidadas e
                        não estarão disponíveis para edição. Deseja prosseguir?
                      </p>
                      <br />
                      <ul>
                        {this.store.pendencias.map((item) => {
                          return <li>{item}</li>;
                        })}
                      </ul>
                    </div>
                  ),
                  header: '"Enviar Requisição"',
                  accept: () => {
                    this.submitFormData();
                    this.forceUpdate();
                  },
                });
              } else {
                this.submitFormData();
                this.forceUpdate();
              }
            }}
          />
        </PermissionProxy>
      </div>
    );
  }

  _renderDialogRequisicaoReabertura() {
    return (
      <RequisicaoReaberturaModal
        idProcesso={this.props.idLicitacao}
        visibleDialog={this.state.showRequisicaoReabertura}
        closeDialog={this._closeRequisicaoReabertura}
        updateTable={this.props.reload}
        ocorrenciaDTO={this.store.object}
      />
    );
  }

  _toggleDialogReqMod() {
    this.setState((oldState) => ({ showRequisicaoReabertura: !oldState.showRequisicaoReabertura }));
  }

  _closeRequisicaoReabertura() {
    this._toggleDialogReqMod();
    this._closeTabs();
  }

  salvarOcorrenciaReabrir() {
    const ocorrenciaLicitacaoDTO = this.store.object;
    const idLicitacao = this.props.idLicitacao;
    this.store.reabrirLicitacao(ocorrenciaLicitacaoDTO, idLicitacao, this._closeTabs);
  }

  submitFormData() {
    const execution = () => {
      if (this.store.rules.hasError) {
        showNotification('error', null, 'Verifique os campos do formulário!');
      } else if (this.store.getArquivoOcorrenciaList().length === 0) {
        showNotification('error', null, 'É obrigatório anexar ao menos 1 arquivo!');
      } else {
        this._toggleDialogReqMod();
      }
    };

    if (this.state.submitted) {
      execution();
    } else {
      this.setState({ submitted: true }, execution);
    }
  }

  validateField(field) {
    return this.store.rules[field] && this.store.rules[field].error && this.state.submitted
      ? { className: 'p-invalid p-error' }
      : {};
  }

  render() {
    const { submitted } = this.state;
    const { getRule } = this.store;

    if (this.store.object && !this.store.loading) {
      return (
        <Dialog
          blockScroll
          header="Reabrir Licitação"
          visible={this.props.showReabrirDialog}
          style={{ width: '50vw' }}
          footer={this._renderFooter()}
          onHide={() => this.props.switchReabrirDialog()}
        >
          <form>
            <div className="p-fluid p-formgrid p-grid">
              <FormField
                columns={6}
                attribute="tipoOcorrencia"
                label="Motivo da Ocorrência"
                rule={getRule('tipoOcorrencia')}
                submitted={submitted}
              >
                <FcDropdown
                  inOrder
                  {...this.validateField('tipoOcorrencia')}
                  onChange={(e) => {
                    this.store.updateAttribute('tipoOcorrencia', e);
                    this.store.carregarArquivosObrigatorios(e?.value);
                    this.store.setArquivoOcorrenciaList([]);
                  }}
                  placeholder="Informe o motivo da ocorrência"
                  value={this.store.object.tipoOcorrencia}
                  id="tipoOcorrencia"
                  optionLabel="text"
                  optionValue="value"
                  options={this.store.getStatusOcorrenciaLicitacaoReabrir(this.props.faseLicitacao)}
                />
              </FormField>
              <FormField
                columns={6}
                attribute="dataProrrogada"
                label="Data da nova abertura"
                rule={getRule('dataProrrogada')}
                submitted={submitted}
              >
                <FcCalendar
                  value={this.getDateAttributeValue(this.store.object.dataProrrogada)}
                  onChange={(e) => this.store.updateAttributeDateWithHours('dataProrrogada', e)}
                  showTime
                  hourFormat="24"
                  showIcon
                  disabledDays={[0, 6]}
                  mask="99/99/9999 99:99"
                />
              </FormField>

              <FormField
                columns={6}
                attribute="dataAviso"
                label="Data da publicação do aviso"
                rule={getRule('dataAviso')}
                submitted={submitted}
              >
                <FcCalendar
                  value={this.getDateAttributeValue(this.store.object.dataAviso)}
                  onChange={(e) => this.store.updateAttributeDateWithHours('dataAviso', e)}
                  showIcon
                  disabledDays={[0, 6]}
                  mask="99/99/9999"
                />
              </FormField>

              <FormField
                columns={12}
                attribute="motivoOcorrencia"
                label="Descrição da Ocorrência"
                rule={getRule('motivoOcorrencia')}
                submitted={submitted}
              >
                <InputTextarea
                  onChange={(e) => {
                    this.store.updateAttribute('motivoOcorrencia', e);
                  }}
                  placeholder="Informe a descrição da ocorrência"
                  rows={3}
                  autoResize
                  value={this.store.object.motivoOcorrencia}
                />
              </FormField>

              {this.store.object.tipoOcorrencia !== 'REABRIR' && (
                <div className="p-col-12">
                  <MultipleFileUploader
                    store={this.store.fileStore}
                    onChangeFiles={(files) => {
                      this.store.setArquivoOcorrenciaList(files);
                    }}
                    fileTypes={DadosEstaticosService.getTipoArquivoFinalizar()}
                  />
                </div>
              )}
            </div>
          </form>
          {this._renderDialogRequisicaoReabertura()}
          <ConfirmDialog />
        </Dialog>
      );
    } else {
      return (
        <i
          className="pi pi-spin pi-spinner"
          style={{
            marginTop: '20px',
            marginLeft: 'calc(50% - 20px)',
            fontSize: '2em',
          }}
        ></i>
      );
    }
  }
}

OcorrenciaReabrirFormPage.propTypes = {
  showReabrirDialog: PropTypes.bool,
  switchReabrirDialog: PropTypes.func,
  reload: PropTypes.func,
  idLicitacao: PropTypes.number,
  faseLicitacao: PropTypes.string,
};

export default OcorrenciaReabrirFormPage;
