import React from 'react';
import PropTypes from 'prop-types';
import { observer } from 'mobx-react';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import FcButton from 'fc/components/FcButton';
import {
  getNumberFractionDigits,
  getValue,
  getValue<PERSON>y<PERSON><PERSON>,
  getValueDate,
  getValueMoney,
  getValueTime,
} from 'fc/utils/utils';
import { DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS, DATE_FORMAT, DATE_PARSE_FORMAT } from 'fc/utils/date';
import OcorrenciaLicitacaoFormStore from '~/stores/ocorrenciaLicitacao/formStore';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import { Divider } from 'primereact/divider';
import { Dialog } from 'primereact/dialog';
import { Link } from 'react-router-dom/cjs/react-router-dom.min';
import AppStore from 'fc/stores/AppStore';
import { TIME_FORMAT } from 'fc/utils/hours';
import FcCloseableTabView from 'fc/components/FcCloseableTabView';
import { Message } from 'primereact/message';
import './style.scss';
import OcorrenciaLicitacaoDetailStore from '~/stores/ocorrenciaLicitacao/detailStore';
import MultipleFileUploader from 'fc/components/MultipleFileUploader';

@observer
class OcorrenciaLicitacaoDetailPage extends React.Component {
  constructor(props) {
    super(props);

    this.store = new OcorrenciaLicitacaoFormStore();
    this.indexStore = new OcorrenciaLicitacaoDetailStore();
    this.showDetails = this.showDetails.bind(this);

    this.state = {
      isDetailDialogVisible: false,
      ocorrencia: null,
      activeTabIndex: 0,
      activeTabPreparatoria: 0,
      activeTabFinalizacao: 0,
    };
  }

  _renderColumns(columns) {
    return columns.map((col) => <Column key={`col-${col.field}`} {...col} />);
  }

  getValueStyle() {
    return { fontSize: AppStore.layout === 'extenso' ? '1.26rem' : '1.1rem' };
  }

  _renderValue(label, value, col = 12, type = 'value', url = '#') {
    return (
      <div
        style={this.getValueStyle()}
        className={`details-set details-display p-col-12 p-md-${col} p-sm-${col >= 6 ? col : 6}`}
      >
        <div
          style={{ color: 'rgb(0 0 0 / 50%)', paddingLeft: 0 }}
          className="xl:col-3 lg:col-4 md:col-4 sm:col-5 details-attributte drawer-content-label"
        >
          {label}
        </div>

        {type == 'value' && (
          <div style={{ paddingLeft: 0 }} className="xl:col-9 lg:col-8 md:col-8 sm:col-7 details-value p-text-justify">
            {value ?? '-'}
          </div>
        )}

        {type == 'list' && (
          <div style={{ display: 'block', paddingLeft: 0 }}>
            {value?.length ? value.map((value) => <div className={`details-value p-text-justify`}>{value}</div>) : '-'}
          </div>
        )}

        {type == 'link' && (
          <div style={{ paddingLeft: 0 }} className="xl:col-9 lg:col-8 md:col-8 sm:col-7 details-value p-text-justify">
            {
              <Link target="_blank" rel="noreferrer" style={{ color: '#3F51B5' }} to={url}>
                <u>{value}</u>
              </Link>
            }
          </div>
        )}
      </div>
    );
  }

  _renderDivider(label) {
    return (
      <div style={{ flexBasis: 'auto', display: 'flex' }}>
        <h5
          style={{
            padding: '7px',
            whiteSpace: 'nowrap',
            width: 'auto',
            color: '#333333',
            alignContent: 'left',
            marginRight: '5px',
            marginTop: '11px',
          }}
        >
          {label}
        </h5>
        <Divider />
      </div>
    );
  }

  renderResumo(ocorrencia) {
    const natureza = getValueByKey(ocorrencia.natureza, DadosEstaticosService.getTipoOcorrencia());
    const responsavel = ocorrencia.responsavel?.nome ?? '-';
    const dataAviso = getValueDate(ocorrencia.dataAviso, DATE_FORMAT, DATE_PARSE_FORMAT);
    const dataCadastro = getValueDate(ocorrencia.dataCadastro, DATE_FORMAT, DATE_PARSE_FORMAT);
    const dataCadastroH = getValueDate(ocorrencia.dataCadastro, DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS);
    const horaDataCadastro = getValueTime(dataCadastroH, TIME_FORMAT, DATE_FORMAT_WITH_HOURS);
    const dataProrrogadaH = getValueDate(
      ocorrencia?.dataProrrogada,
      DATE_FORMAT_WITH_HOURS,
      DATE_PARSE_FORMAT_WITH_HOURS
    );
    const horaDataProrrogada = getValueTime(dataProrrogadaH, TIME_FORMAT, DATE_FORMAT_WITH_HOURS);
    const dataInicialAberturaLicitacao = getValueDate(
      ocorrencia.dataInicialAberturaLicitacao,
      DATE_FORMAT_WITH_HOURS,
      DATE_PARSE_FORMAT_WITH_HOURS
    );
    const dataAberturaInicial = getValueDate(ocorrencia.dataInicialAberturaLicitacao, DATE_FORMAT, DATE_PARSE_FORMAT);
    const horaAberturaInicial = getValueTime(dataInicialAberturaLicitacao, TIME_FORMAT, DATE_FORMAT_WITH_HOURS);
    const dataProrrogada = getValueDate(ocorrencia?.dataProrrogada, DATE_FORMAT, DATE_PARSE_FORMAT);
    const horaProrrogada = getValueTime(dataProrrogadaH, TIME_FORMAT, DATE_FORMAT_WITH_HOURS);

    let content = `Licitação ${natureza.toUpperCase()} por ${responsavel} em ${dataCadastro} às ${horaDataCadastro}`;

    if (ocorrencia.natureza !== 'CONTINUAR') {
      content += `, com data da publicação do aviso em ${dataAviso}`;
    }

    if (ocorrencia.natureza === 'REABRIR') {
      content += ` e nova data de abertura em ${dataProrrogada} às ${horaDataProrrogada}`;
    }

    if (ocorrencia.natureza === 'PRORROGAR') {
      dataAberturaInicial === '-'
        ? (content += `. Nova data de abertura: ${dataProrrogada} às ${horaProrrogada}`)
        : (content += `. Data de abertura anterior: ${dataAberturaInicial} às ${horaAberturaInicial}. Nova data de abertura: ${dataProrrogada} às ${horaProrrogada}`);
    }

    return content;
  }

  showDetails() {
    const ocorrencia = this.state.ocorrencia;
    return (
      <Dialog
        blockScroll
        visible={this.state.isDetailDialogVisible}
        header={<strong>Detalhes</strong>}
        style={{ width: '50%' }}
        onHide={() => this.setState({ isDetailDialogVisible: !this.state.isDetailDialogVisible, ocorrencia: null })}
      >
        {this.detailsMessage(ocorrencia)}
      </Dialog>
    );
  }

  _renderTabApresentacaoPropostasItensFrac(ocorrencia) {
    const columnsItensLotes = [
      { field: 'nome', header: 'Lote', sortable: true },
      {
        field: 'itemLote',
        header: 'Item',
        sortable: true,
        body: ({ itemLote }) => itemLote.materialDetalhamento.pdm.nome,
      },
    ];
    return (
      <DataTable
        rowHover
        value={ocorrencia.itensLotesFracassados}
        className="p-datatable"
        paginator
        emptyMessage="Nenhum lote/item fracassado"
        responsiveLayout="scroll"
        currentPageReportTemplate="{first} - {last} de {totalRecords} registros"
        rows={5}
      >
        {this._renderColumns(columnsItensLotes)}
      </DataTable>
    );
  }

  _renderTabApresentacaoPropostasVencedores(ocorrencia) {
    let columns = [
      {
        field: 'licitante',
        header: 'Licitante',
        body: ({ licitante }) => licitante.nome,
      },
      { field: 'nomeLote', header: 'Lote' },
      {
        field: 'lote',
        header: 'item',
        body: ({ lote }) => lote.materialDetalhamento?.pdm?.nome,
      },
      { field: 'marcaModelo', header: 'Marca/Modelo' },
      { filed: 'quantidade', header: 'Quantidade', body: ({ quantidade }) => getValue(quantidade) },
      { field: 'valorUnitario', header: 'Valor Unitário', body: ({ valorUnitario }) => getValueMoney(valorUnitario) },
      { field: 'desconto', header: 'Desconto(%)', body: ({ desconto }) => getNumberFractionDigits(desconto) },
      { field: 'valor', header: 'Valor', body: ({ valor }) => getValueMoney(valor) },
      { field: 'espeficacao', header: 'Espeficação', body: ({ espeficacao }) => getValue(espeficacao) },
      { field: 'observacao', header: 'Observação', body: ({ observacao }) => getValue(observacao) },
    ];

    if (this.props.legislacao === 'LEI_N_8666') {
      columns = [
        {
          field: 'licitante',
          header: 'Licitante',
          body: ({ licitante }) => licitante.nome,
        },
        { field: 'valor', header: 'Valor', body: ({ valor }) => getValueMoney(valor) },
      ];
    }

    return (
      <DataTable
        rowHover
        responsiveLayout="scroll"
        paginator
        value={ocorrencia.vencedores}
        emptyMessage="Nenhum vencedor adicionado."
        rows={5}
      >
        {this._renderColumns(columns)}
      </DataTable>
    );
  }

  _renderTabApresentacaoPropostasLicitantes(ocorrencia) {
    const columnsLicitantes = [
      {
        field: 'nome',
        header: 'Nome',
        sortable: true,
      },
      {
        field: 'cpfCnpj',
        header: 'CPF/CNPJ',
        sortable: true,
      },
    ];
    return (
      <DataTable
        rowHover
        value={ocorrencia.licitantesLicitacao}
        paginator
        responsiveLayout="scroll"
        emptyMessage="Nenhum licitante adicionado"
        rows={5}
      >
        {this._renderColumns(columnsLicitantes)}
      </DataTable>
    );
  }

  _renderTabApresentacaoPropostasItensDesertos(ocorrencia) {
    const columnsItensLotesDesertos = [
      { field: 'nome', header: 'Lote', sortable: true },
      {
        field: 'itemLote',
        header: 'Item',
        sortable: true,
        body: ({ itemLote }) => itemLote.materialDetalhamento.pdm.nome,
      },
      {
        field: 'quantidade',
        header: 'Quantidade',
        sortable: true,
        body: ({ itemLote }) => itemLote.quantidade,
      },
    ];

    return (
      <div className="p-col-12 p-mt-1">
        <DataTable
          rowHover
          responsiveLayout="scroll"
          value={ocorrencia.itensLotesDesertos}
          className="p-datatable"
          paginator
          emptyMessage="Nenhum lote/item deserto"
          rows={5}
        >
          {this._renderColumns(columnsItensLotesDesertos)}
        </DataTable>
      </div>
    );
  }

  _renderTabsFaseFinalizacao() {
    const tabs = [];

    tabs.push({
      id: tabs.length,
      header: 'Arquivos',
      content: (
        <MultipleFileUploader
          fileTypes={DadosEstaticosService.getTipoArquivoLicitacao()}
          store={this.indexStore.fileStoreFinalizacao}
          downloadOnly
        />
      ),
    });

    return (
      <div className="overflow-hidden p-col-12 p-mt-1">
        <Message icon="pi pi-check-square" text="Cadastros feitos na fase de Finalização" />
        <div className="overflow-hidden index-table">
          <FcCloseableTabView
            tabs={tabs}
            activeTabIndex={this.state.activeTabFinalizacao}
            onChangeTab={(tab) => this.setState({ activeTabFinalizacao: tab.id })}
          />
        </div>
      </div>
    );
  }

  _renderTabsFaseApresentacaoPropostas(ocorrencia) {
    const tabs = [];

    ocorrencia?.licitantesLicitacao?.length &&
      tabs.push({
        id: tabs.length,
        header: 'Licitantes selecionados',
        content: this._renderTabApresentacaoPropostasLicitantes(ocorrencia),
      });

    ocorrencia?.vencedores?.length &&
      tabs.push({
        id: tabs.length,
        header: 'Vencedores',
        content: this._renderTabApresentacaoPropostasVencedores(ocorrencia),
      });

    ocorrencia?.itensLotesFracassados?.length &&
      tabs.push({
        id: tabs.length,
        header: 'Itens Fracassados',
        content: this._renderTabApresentacaoPropostasItensFrac(ocorrencia),
      });

    ocorrencia?.itensLotesDesertos?.length &&
      tabs.push({
        id: tabs.length,
        header: 'Itens Desertos',
        content: this._renderTabApresentacaoPropostasItensDesertos(ocorrencia),
      });

    ocorrencia?.licitantesLicitacao?.length &&
      tabs.push({
        id: tabs.length,
        header: 'Arquivos',
        content: (
          <MultipleFileUploader
            store={this.indexStore.fileStoreApresentacao}
            fileTypes={DadosEstaticosService.getTipoArquivoLicitacao()}
            downloadOnly
          />
        ),
      });

    return (
      <div className="overflow-hidden p-col-12 p-mt-1">
        <Message icon="pi pi-shield" text="Cadastros feitos na fase de Apresentacao de Propostas e Lances" />
        <div className="overflow-hidden index-table">
          <FcCloseableTabView
            tabs={tabs}
            activeTabIndex={this.state.activeTabPreparatoria}
            onChangeTab={(tab) => this.setState({ activeTabPreparatoria: tab.id })}
          />
        </div>
      </div>
    );
  }

  detailsMessage() {
    const ocorrencia = this.state.ocorrencia;
    const arquivosFinalizacao = this.indexStore?.fileStoreFinalizacao?.uploadedFiles;

    const columnsLicitantes = [
      {
        field: 'nome',
        header: 'Nome',
        sortable: true,
      },
      {
        field: 'cpfCnpj',
        header: 'CPF/CNPJ',
        sortable: true,
      },
    ];

    return (
      <div className="p-grid details p-fluid p-form ">
        {ocorrencia.natureza === 'PRORROGAR' &&
          this._renderValue(
            'Data de abertura inicial',
            getValueDate(ocorrencia.dataInicialAberturaLicitacao, DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS),
            12
          )}
        {(ocorrencia.natureza === 'PRORROGAR' || ocorrencia.natureza === 'REABRIR') &&
          this._renderValue(
            'Nova data de abertura',
            getValueDate(ocorrencia.dataProrrogada, DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS),
            12
          )}
        {ocorrencia.natureza !== 'CONTINUAR' &&
          this._renderValue(
            'Data da publicação do aviso',
            getValueDate(ocorrencia.dataAviso, DATE_FORMAT, DATE_PARSE_FORMAT),
            12
          )}
        {ocorrencia.natureza !== 'REABRIR' &&
          this._renderValue(
            'Motivo',
            getValueByKey(ocorrencia.tipoOcorrencia, DadosEstaticosService.getStatusOcorrenciaLicitacao()),
            12
          )}
        {this._renderValue('Descrição', ocorrencia.motivoOcorrencia ?? '-', 12)}
        {ocorrencia.tipoOcorrencia === 'FRACASSADA' && (
          <span className="p-col-12 p-mt-1">
            {this._renderDivider('Licitantes')}
            <DataTable
              rowHover
              value={ocorrencia.licitantes}
              className="p-datatable"
              sortOrder={-1}
              paginator
              emptyMessage="Nenhum licitante adicionado"
              responsiveLayout="scroll"
              currentPageReportTemplate="{first} - {last} de {totalRecords} registros"
              rows={5}
            >
              {this._renderColumns(columnsLicitantes)}
            </DataTable>
          </span>
        )}
        <span className="p-col-12 p-mt-1">{this._renderDivider('Arquivos da Ocorrência')}</span>
        <div className="p-col-12 p-mt-1">
          <MultipleFileUploader
            store={this.indexStore.fileStoreOcorrencia}
            fileTypes={DadosEstaticosService.getTipoArquivoFinalizar()}
            downloadOnly
          />
        </div>
        {ocorrencia?.natureza === 'REABRIR' &&
          ocorrencia?.licitantesLicitacao.length > 0 &&
          this._renderTabsFaseApresentacaoPropostas(ocorrencia)}
        {arquivosFinalizacao.length > 0 && ocorrencia?.natureza === 'REABRIR' && this._renderTabsFaseFinalizacao()}
      </div>
    );
  }

  render() {
    const ocorrenciaList = this.props.ocorrenciaList;
    const columns = [
      {
        field: 'tipoOcorrencia',
        header: 'Aviso Mais Recente',
        body: ({ natureza }) => getValueByKey(natureza, DadosEstaticosService.getTipoOcorrencia()),
        style: { width: '15%' },
      },
      {
        header: 'Resumo',
        body: (rowdata) => this.renderResumo(rowdata),
        style: { width: '70%' },
      },
      {
        style: { width: '15%' },
        body: (rowData) => {
          return (
            <div className="actions p-d-flex p-jc-end">
              <FcButton
                icon="pi pi-list"
                className="p-button-sm p-button-info p-mr-2"
                onClick={() => {
                  this.indexStore.initializeArquivos(rowData.id, rowData.tipoOcorrencia);
                  this.setState({ isDetailDialogVisible: !this.state.isDetailDialogVisible, ocorrencia: rowData });
                }}
              />
            </div>
          );
        },
      },
    ];

    return (
      <>
        <DataTable
          rowHover
          value={ocorrenciaList}
          className="p-datatable"
          sortField="dataCadastro"
          sortOrder={-1}
          paginator
          emptyMessage="Nenhuma ocorrência cadastrada."
          responsiveLayout="scroll"
          paginatorTemplate="CurrentPageReport FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink RowsPerPageDropdown"
          currentPageReportTemplate="{first} - {last} de {totalRecords} registros"
          rows={5}
        >
          {this._renderColumns(columns)}
        </DataTable>

        {this.state.isDetailDialogVisible && this.showDetails()}
      </>
    );
  }
}

OcorrenciaLicitacaoDetailPage.propTypes = {
  ocorrenciaList: PropTypes.array,
  legislacao: PropTypes.string,
};

export default OcorrenciaLicitacaoDetailPage;
