import React from 'react';
import { observer } from 'mobx-react';
import GenericFormPage from 'fc/pages/GenericFormPage';
import UrlRouter from '~/constants/UrlRouter';
import PropTypes from 'prop-types';
import OcorrenciaLicitacaoFormStore from '~/stores/ocorrenciaLicitacao/formStore';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import FormField from 'fc/components/FormField';
import { InputTextarea } from 'primereact/inputtextarea';
import { Dialog } from 'primereact/dialog';
import FcButton from 'fc/components/FcButton';
import { showNotification } from 'fc/utils/utils';
import AccessPermission from '~/constants/AccessPermission';
import PermissionProxy from 'fc/components/PermissionProxy';
import MultipleFileUploader from 'fc/components/MultipleFileUploader';
import FcDropdown from 'fc/components/FcDropdown';
import FcCalendar from 'fc/components/FcCalendar';

@observer
class OcorrenciaProrrogarFormPage extends GenericFormPage {
  constructor(props) {
    super(props, UrlRouter.cadastrosConsulta.licitacao.index, AccessPermission.ocorrenciaLicitacao);
    this.store = new OcorrenciaLicitacaoFormStore(props.openDate);

    this._closeTabs = this._closeTabs.bind(this);
  }

  componentDidMount() {
    this.store.initialize();
    this.store.initializeFormValues('PRORROGAR');
  }

  _closeTabs() {
    this.props.switchProrrogarDialog();
    this.store.resetObject();
    this.setState({ submitted: false });
    this.props.reload(this.props.idLicitacao);
  }

  _renderFooter() {
    return (
      <div>
        <FcButton
          label="Cancelar"
          icon="pi pi-times"
          onClick={() => this.props.switchProrrogarDialog()}
          className="p-button-text"
        />
        <PermissionProxy resourcePermissions={this.getWritePermission()} blockOnFail>
          <FcButton
            label="Salvar"
            icon="pi pi-check"
            loading={this.store.loading}
            type="button"
            onClick={() => {
              this.submitFormData();
            }}
          />
        </PermissionProxy>
      </div>
    );
  }

  salvarOcorrenciaProrrogar() {
    const ocorrenciaLicitacaoDTO = this.store.object;
    const idLicitacao = this.props.idLicitacao;
    this.store.prorrogarLicitacao(ocorrenciaLicitacaoDTO, idLicitacao, this._closeTabs);
  }

  submitFormData() {
    const execution = () => {
      if (this.store.rules.hasError) {
        showNotification('error', null, 'Verifique os campos do formulário!');
      } else if (this.store.getArquivoOcorrenciaList().length === 0) {
        showNotification('error', null, 'É obrigatório anexar ao menos 1 arquivo!');
      } else {
        this.salvarOcorrenciaProrrogar();
      }
    };

    if (this.state.submitted) {
      execution();
    } else {
      this.setState({ submitted: true }, execution);
    }
  }

  render() {
    const { submitted } = this.state;
    const { getRule } = this.store;

    if (this.store.object) {
      return (
        <Dialog
          blockScroll
          header="Prorrogar Licitação"
          visible={this.props.showProrrogarDialog}
          style={{ width: '50vw' }}
          footer={this._renderFooter()}
          onHide={() => this.props.switchProrrogarDialog()}
        >
          <form>
            <div className="p-fluid p-formgrid p-grid">
              <FormField
                columns={6}
                attribute="tipoOcorrencia"
                label="Motivo da Ocorrência"
                rule={getRule('tipoOcorrencia')}
                submitted={submitted}
              >
                <FcDropdown
                  inOrder
                  {...this.validateField('tipoOcorrencia')}
                  onChange={(e) => {
                    this.store.updateAttribute('tipoOcorrencia', e);
                    this.store.carregarArquivosObrigatorios(e?.value);
                    this.store.setArquivoOcorrenciaList([]);
                  }}
                  placeholder="Informe o motivo da ocorrência"
                  value={this.store.object.tipoOcorrencia}
                  id="tipoOcorrencia"
                  optionLabel="text"
                  optionValue="value"
                  options={DadosEstaticosService.getStatusOcorrenciaLicitacao().filter(
                    this.store.filtraStatusOcorrenciaLicitacaoProrrogar
                  )}
                />
              </FormField>

              <FormField
                columns={6}
                attribute="dataProrrogada"
                label="Data da nova abertura"
                rule={getRule('dataProrrogada')}
                submitted={submitted}
              >
                <FcCalendar
                  value={this.getDateAttributeValue(this.store.object.dataProrrogada)}
                  onChange={(e) => this.store.updateAttributeDateWithHours('dataProrrogada', e)}
                  showTime
                  hourFormat="24"
                  showIcon
                  mask="99/99/9999 99:99"
                  disabledDays={[0, 6]}
                />
              </FormField>

              <FormField
                columns={6}
                attribute="dataAviso"
                label="Data da publicação do aviso"
                rule={getRule('dataAviso')}
                submitted={submitted}
              >
                <FcCalendar
                  value={this.getDateAttributeValue(this.store.object.dataAviso)}
                  onChange={(e) => this.store.updateAttributeDateWithHours('dataAviso', e)}
                  showIcon
                  mask="99/99/9999"
                  disabledDays={[0, 6]}
                />
              </FormField>

              <FormField
                columns={12}
                attribute="motivoOcorrencia"
                label="Descrição da Ocorrência"
                rule={getRule('motivoOcorrencia')}
                submitted={submitted}
              >
                <InputTextarea
                  onChange={(e) => this.store.updateAttribute('motivoOcorrencia', e)}
                  placeholder="Informe a descrição da ocorrência"
                  rows={3}
                  autoResize
                  value={this.store.object.motivoOcorrencia}
                />
              </FormField>

              {this.store.object.tipoOcorrencia && (
                <div className="p-col-12">
                  <MultipleFileUploader
                    store={this.store.fileStore}
                    onChangeFiles={(files) => this.store.setArquivoOcorrenciaList(files)}
                    fileTypes={DadosEstaticosService.getTipoArquivoFinalizar()}
                  />
                </div>
              )}
            </div>
          </form>
        </Dialog>
      );
    } else {
      return (
        <i
          className="pi pi-spin pi-spinner"
          style={{
            marginTop: '20px',
            marginLeft: 'calc(50% - 20px)',
            fontSize: '2em',
          }}
        ></i>
      );
    }
  }
}

OcorrenciaProrrogarFormPage.propTypes = {
  showProrrogarDialog: PropTypes.bool,
  switchProrrogarDialog: PropTypes.func,
  reload: PropTypes.func,
  openDate: PropTypes.any,
  idLicitacao: PropTypes.number,
};

export default OcorrenciaProrrogarFormPage;
