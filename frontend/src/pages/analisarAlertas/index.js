import React from 'react';
import { observer } from 'mobx-react';
import IndexDataTable from 'fc/components/IndexDataTable';
import FcButton from 'fc/components/FcButton';
import AppBreadCrumb from 'fc/components/AppBreadCrumb';
import GenericIndexPage from 'fc/pages/GenericIndexPage';
import AdvancedSearch from 'fc/components/AdvancedSearch';
import AccessPermission from '~/constants/AccessPermission';
import PermissionProxy from 'fc/components/PermissionProxy';
import AlertasIndexStore from '~/stores/alertas/AlertasIndexStore';
import { DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS } from 'fc/utils/date';
import { getValue, getValueByKey, getValueDate, somaValoresLotes } from 'fc/utils/utils';
import { Dialog } from 'primereact/dialog';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import { Fieldset } from 'primereact/fieldset';
import FormField from 'fc/components/FormField';
import { InputText } from 'primereact/inputtext';
import { InputTextarea } from 'primereact/inputtextarea';
import { CKEditor } from '@ckeditor/ckeditor5-react';
import TermoEditor from 'fc/editors/termoReferencia';
import moment from 'moment';
import { Column } from 'primereact/column';
import FcCalendar from 'fc/components/FcCalendar';
import InputMonetary from 'fc/components/InputMonetary';

@observer
class AlertasIndexPage extends GenericIndexPage {
  constructor(props) {
    super(props, AccessPermission.analisarAlertaDafo);
    this.store = new AlertasIndexStore();
    this.state = {
      visibleDialogAlerta: false,
      visibleDialogRejeitarAlerta: false,
      submitted: false,
      loadingAlerta: false,
    };

    this._toggleDialogAlerta = this._toggleDialogAlerta.bind(this);
    this._toggleDialogRejeitarAlerta = this._toggleDialogRejeitarAlerta.bind(this);
  }

  _toggleDialogAlerta() {
    this.setState((oldState) => ({ visibleDialogAlerta: !oldState.visibleDialogAlerta }));
  }

  renderDialogAlertaFooter() {
    return (
      <div>
        <FcButton
          label="Cancelar"
          icon="pi pi-times"
          onClick={() => this._toggleDialogAlerta()}
          className="p-button-text"
        />
        <FcButton
          label="Rejeitar Alerta"
          icon="pi pi-times"
          className="p-button-danger"
          loading={this.state.loadingAlerta}
          onClick={() => this._toggleDialogRejeitarAlerta()}
        />
        <FcButton
          label="Enviar para Jurisdicionado"
          icon="pi pi-check"
          loading={this.state.loadingAlerta}
          onClick={() => {
            this.setState({ loadingAlerta: true }, () =>
              this.store.enviarAlertaJurisdicionado(this.store.object, () => {
                this.setState({ loadingAlerta: false, visibleDialogAlerta: false });
                this.store.load();
              })
            );
          }}
        />
      </div>
    );
  }

  _toggleDialogRejeitarAlerta() {
    this.setState((oldState) => ({ visibleDialogRejeitarAlerta: !oldState.visibleDialogRejeitarAlerta }));
  }

  renderDialogRejeitarAlertaFooter() {
    return (
      <div>
        <FcButton
          label="Cancelar"
          icon="pi pi-times"
          loading={this.state.loadingAlerta}
          onClick={() => this._toggleDialogRejeitarAlerta()}
          className="p-button-text"
        />
        <FcButton
          label="Rejeitar Alerta"
          icon="pi pi-times"
          className="p-button-danger"
          loading={this.state.loadingAlerta}
          onClick={() => {
            this.setState({ loadingAlerta: true }, () => {
              this.store.rejeitarAlerta(
                this.store.object,
                () => {
                  this.setState({
                    loadingAlerta: false,
                    visibleDialogAlerta: false,
                    visibleDialogRejeitarAlerta: false,
                  });
                  this.store.load();
                },
                () => this.setState({ loadingAlerta: false })
              );
            });
          }}
        />
      </div>
    );
  }

  getDateAttributeValue(value) {
    return value && moment(value).toDate();
  }
  getValuePregoeiroByTipoProcesso(tipoProcesso) {
    if (this.store.processo) {
      const columnPregoeiroResponsavel =
        DadosEstaticosService.getColumnPregoeiroResponsavelByTipoProcesso(tipoProcesso);
      if (tipoProcesso === 'carona' || tipoProcesso === 'dispensa') {
        return this.store.processo[columnPregoeiroResponsavel];
      } else if (tipoProcesso === 'inexigibilidade') {
        const responsavelHomologacao = this.store.processo[columnPregoeiroResponsavel.responsavelHomologacao];
        return responsavelHomologacao !== ''
          ? responsavelHomologacao
          : this.store.processo[columnPregoeiroResponsavel.responsavelRatificacao];
      } else {
        return this.store.processo[columnPregoeiroResponsavel]?.nome;
      }
    }
  }

  _renderColumnsDestinatarios(columns) {
    return columns.map((col) => <Column key={`col-${col.field}`} {...col} />);
  }

  renderDialogAlerta() {
    const { submitted } = this.state;

    return (
      <Dialog
        header="Detalhes do Alerta"
        visible={this.state.visibleDialogAlerta}
        style={{ width: '60%' }}
        footer={this.renderDialogAlertaFooter()}
        onHide={() => this._toggleDialogAlerta()}
      >
        <div className="p-fluid p-formgrid p-grid">
          <Fieldset
            className="p-col-12"
            legend={<span style={{ color: 'rgba(0, 0, 0, 0.87)' }}>Dados do Processo</span>}
          >
            <div className="p-fluid p-formgrid p-grid">
              <FormField columns={6} label="Órgão">
                <InputText placeholder="Órgão" value={this.store.processo.entidade.nome} disabled />
              </FormField>
              <FormField columns={6} label="Número do Processo">
                <InputText
                  placeholder="Número do processo"
                  value={
                    this.store.processo &&
                    this.store.processo[
                      DadosEstaticosService.getColumnNumeroProcessoByTipoProcesso(this.store.tipoProcesso)
                    ]
                  }
                  disabled
                />
              </FormField>
              <FormField columns={12} label="Objeto">
                <InputTextarea placeholder="Objeto" value={this.store.processo.objeto} disabled autoResize />
              </FormField>
              <FormField columns={4} label="Data da Abertura">
                <FcCalendar
                  placeholder="Data da abertura"
                  value={this.getDateAttributeValue(
                    this.store.processo[
                      DadosEstaticosService.getColumnDataAberturaByTipoProcesso(this.store.tipoProcesso)
                    ]
                  )}
                  disabled
                />
              </FormField>

              <FormField columns={4} label="Valor Total Estimado">
                <InputMonetary
                  value={
                    this.store.tipoProcesso === 'credenciamento'
                      ? somaValoresLotes(this.store.processo.termoReferencia.lotes)
                      : this.store.processo[
                          DadosEstaticosService.getColumnValorEstimadoByTipoProcesso(this.store.tipoProcesso)
                        ]
                  }
                  prefix="R$ "
                  disabled
                />
              </FormField>
              <FormField columns={4} label="Pregoeiro/Responsável">
                <InputText
                  placeholder="Pregoeiro/Responsável"
                  value={this.store.processo && this.getValuePregoeiroByTipoProcesso(this.store.tipoProcesso)}
                  disabled
                />
              </FormField>
            </div>
          </Fieldset>
          <Fieldset className="p-col-12" legend={<span style={{ color: 'rgba(0, 0, 0, 0.87)' }}>Mensagem</span>}>
            <FormField columns={12} attribute="mensagem" label="Texto" submitted={submitted}>
              <CKEditor
                disabled
                editor={TermoEditor}
                data={this.store.object.mensagem}
                onChange={(_, editor) => this.store.updateMensagem(editor)}
              />
            </FormField>
          </Fieldset>
        </div>
      </Dialog>
    );
  }

  renderDialogRejeitarAlerta() {
    const { submitted } = this.state;

    return (
      <Dialog
        header="Rejeitar Alerta"
        visible={this.state.visibleDialogRejeitarAlerta}
        style={{ width: '40%' }}
        footer={this.renderDialogRejeitarAlertaFooter()}
        onHide={() => this._toggleDialogRejeitarAlerta()}
      >
        <div className="p-fluid p-formgrid p-grid">
          <FormField attribute="respostaRejeicao" submitted={submitted} columns={12} label="Motivo Rejeição">
            <InputTextarea
              placeholder="Motivo rejeição"
              value={this.store.object.respostaRejeicao}
              onChange={(e) => {
                this.store.updateRespostaRejeicao(e);
              }}
              autoResize
            />
          </FormField>
        </div>
      </Dialog>
    );
  }

  render() {
    const columns = [
      {
        field: 'data',
        header: 'Data de Emissão',
        body: ({ data }) => getValueDate(data, DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS),
        sortable: true,
      },
      {
        field: 'usuarioResponsavel',
        header: 'Auditor Responsável',
        body: ({ usuarioResponsavel }) => getValue(usuarioResponsavel?.nome),
        sortable: true,
      },
      {
        field: 'diretorDafo',
        header: 'Diretor DAFO Responsável',
        body: ({ diretorDafo }) => getValue(diretorDafo?.nome),
        sortable: true,
      },
      {
        field: 'entidade',
        header: 'Entidade',
        body: ({ entidade }) => getValue(entidade?.nome),
        sortable: true,
      },
      {
        field: 'tipoProcesso',
        header: 'Processo',
        body: ({ tipoProcesso }) => getValue(tipoProcesso),
        sortable: true,
      },
      {
        field: 'dataAberturaProcesso',
        header: 'Data de Abertura',
        body: ({ dataAberturaProcesso }) =>
          getValueDate(dataAberturaProcesso, DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS),
        sortable: true,
      },
      {
        field: 'status',
        header: 'Status',
        body: ({ status }) => getValueByKey(status, DadosEstaticosService.getStatusAlertaAnalise()),
        sortable: true,
      },
      {
        style: { width: '80px' },
        body: (rowData) => {
          return (
            <div className="actions p-d-flex p-jc-end">
              <FcButton
                icon="pi pi-eye"
                className="p-button-sm p-button-success p-mr-2"
                onClick={() => this.store.loadProcesso(rowData, this._toggleDialogAlerta)}
              />
            </div>
          );
        },
      },
    ];

    const { listKey, loading } = this.store;
    const { getDefaultTableProps } = this;

    const breacrumbItems = [{ label: 'Analisar Alertas' }];

    return (
      <PermissionProxy resourcePermissions={this.getWritePermission()} blockOnFail>
        <AppBreadCrumb items={breacrumbItems} />
        <div className="card page index-table">
          <AdvancedSearch
            searchParams={this.store.getAdvancedSearchParams()}
            store={this.store}
            searchFields={['entidade', 'tipoProcesso', 'usuarioResponsavel', 'diretorDafo']}
            filterSuggest={this.store.getFilterSuggest()}
            useOr
          />
          <IndexDataTable columns={columns} value={listKey} loading={loading} {...getDefaultTableProps()} />
          {this.store.isConfirmDialogVisible && this.confirmRemove(this.state.idRemove)}
        </div>
        {!this.store.loadingProcesso && this.state.visibleDialogAlerta && this.renderDialogAlerta()}
        {this.state.visibleDialogRejeitarAlerta && this.renderDialogRejeitarAlerta()}
      </PermissionProxy>
    );
  }
}

AlertasIndexPage.displayName = 'AlertasIndexPage';

export default AlertasIndexPage;
