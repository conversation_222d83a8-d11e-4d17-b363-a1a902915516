import React from 'react';
import { observer } from 'mobx-react';
import PropTypes from 'prop-types';
import GenericFormPage from 'fc/pages/GenericFormPage';
import AppBreadCrumb from 'fc/components/AppBreadCrumb';
import PermissionProxy from 'fc/components/PermissionProxy';
import Form<PERSON>ield from 'fc/components/FormField';
import { InputText } from 'primereact/inputtext';
import { InputNumber } from 'primereact/inputnumber';
import UrlRouter from '~/constants/UrlRouter';
import AccessPermission from '~/constants/AccessPermission';
import { Checkbox } from 'primereact/checkbox';
import FuncaoRiscoFormStore from '~/stores/funcaoRisco/formStore';
import { Divider } from 'primereact/divider';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import FcButton from 'fc/components/FcButton';
import { PrimeIcons } from 'primereact/api';
import { ConfirmDialog } from 'primereact/confirmdialog';
import { getValueByKey, isValueValid, showNotification } from 'fc/utils/utils';
import InputMonetary from 'fc/components/InputMonetary';
import { SplitButton } from 'primereact/splitbutton';
import FcMultiSelect from 'fc/components/FcMultiSelect';
import Tooltip from 'fc/components/Tooltip';
import { InputTextarea } from 'primereact/inputtextarea';
import AsyncMultiselect from 'fc/components/AsyncMultiselect';
import FcDropdown from 'fc/components/FcDropdown';
import { Dropdown } from 'primereact/dropdown';

@observer
class FuncaoRiscoFormPage extends GenericFormPage {
  constructor(props) {
    super(props, UrlRouter.seguranca.funcaoRisco.index, AccessPermission.funcaoRisco);
    this.store = new FuncaoRiscoFormStore();
    this.state = {
      removeDialogVisible: false,
      removeIdx: undefined,
      resetConditionsDialogVisible: false,
      resetConditionsDialogTiposProcessoVisible: false,
      newColumn: '',
      newTiposProcesso: [],
      editingRows: {},
    };

    this._renderTableButtons = this._renderTableButtons.bind(this);
    this.onRowEditValidator = this.onRowEditValidator.bind(this);
    this.onRowEditComplete = this.onRowEditComplete.bind(this);
    this.onRowEditCancel = this.onRowEditCancel.bind(this);
    this.onRowEditChange = this.onRowEditChange.bind(this);
  }

  componentDidMount() {
    const { id } = this.props;
    this.store.initialize(id, { ativa: true, peso: 1, condicoes: [] });
  }

  _riscoEditor(props) {
    const editing = this.state.editingRows[props.rowIndex];
    const conditions = editing ? this.store.conditionsTemp : this.store.conditions;
    const riscoValue = conditions[props.rowIndex]['risco'];

    return editing ? (
      <InputNumber
        inputId="minmax"
        mode="decimal"
        min={0}
        max={100}
        maxLength={3}
        value={riscoValue}
        onChange={(e) => this.store.handleRiscoChange(e.value, props.rowIndex)}
      />
    ) : (
      riscoValue
    );
  }

  _tipoComparacaoEditor(props) {
    const values = DadosEstaticosService.getTipoComparacao().filter((comparator) =>
      comparator.compatible.includes(
        getValueByKey(
          this.store.object.nomeColuna,
          DadosEstaticosService.getColumnsMatrizRisco(this.store.object.tipoProcesso),
          'value',
          'type'
        )
      )
    );
    return this.state.editingRows[props.rowIndex] ? (
      <Dropdown
        onChange={(e) =>
          this.store.updateAttributeTipoComparacao(e.target.value, props.rowIndex, () => this.forceUpdate())
        }
        value={this.store.conditionsTemp[props.rowIndex]['tipoComparacao']}
        options={values}
        optionLabel="text"
        optionValue="value"
        placeholder="Selecione o tipo de comparação"
      />
    ) : (
      getValueByKey(this.store.conditions[props.rowIndex]['tipoComparacao'], DadosEstaticosService.getTipoComparacao())
    );
  }

  _multiSelectEditor(productKey, columnName, props) {
    return (
      <FcMultiSelect
        maxSelectedLabels={3}
        {...this.store.getColumnsMatrizRisco()[columnName].optionsComponent}
        onChange={(e) =>
          this.store.updateAttributeValorComparavel(e.target.value, props.rowIndex, undefined, () => this.forceUpdate())
        }
        value={this.store.conditionsTemp[props.rowIndex][productKey]}
        options={this.store.response}
        filter
        selectedItemsLabel="{} itens selecionados"
        showClear
        showOverlay
      />
    );
  }

  _dropdownEditor(productKey, columnName, props) {
    return (
      <Dropdown
        inOrder
        {...this.store.getColumnsMatrizRisco()[columnName].optionsComponent}
        options={this.store.response}
        value={this.store.conditionsTemp[props.rowIndex][productKey]}
        onChange={(e) =>
          this.store.updateAttributeValorComparavel(e.target.value, props.rowIndex, undefined, () => this.forceUpdate())
        }
        showClear
        showFilterClear
        filter
        key="dropdown"
      />
    );
  }

  _intervalEditor(productKey, columnName, props) {
    const propsIntervalOne = { placeholder: 'Digite o intervalo inicial', style: { width: '47.5%' } };
    const propsIntervalTwo = { placeholder: 'Digite o intervalo final', style: { width: '47.5%', marginLeft: '5%' } };

    return (
      <>
        {this._getSimpleEditorByType(productKey, columnName, props, 0, propsIntervalOne)}
        {this._getSimpleEditorByType(productKey, columnName, props, 1, propsIntervalTwo)}
      </>
    );
  }

  _getFormattedValue(value, formatter) {
    return formatter ? formatter(value) : value;
  }

  _getBodyComparableValue(props) {
    const comparator = this.store.conditions[props.rowIndex ?? props.key].tipoComparacao;
    const currentCondition = this.store.conditions[props.rowIndex ?? props.key];
    const columnName = this.store.object.nomeColuna;
    const bodyFormatter = this.store.getColumnsMatrizRisco()[columnName]?.bodyFormatter;

    if (comparator === 'INTERVALO') {
      return `(${this._getFormattedValue(
        currentCondition.valorComparavel[0],
        bodyFormatter
      )} ~ ${this._getFormattedValue(currentCondition.valorComparavel[1], bodyFormatter)})`;
    } else {
      return this._getFormattedValue(currentCondition.valorComparavel, bodyFormatter);
    }
  }

  _getSimpleEditorByType(productKey, columnName, props, idxInterval, options) {
    let { type, optionsComponent } = this.store.getColumnsMatrizRisco()[columnName];
    optionsComponent = Object.assign(optionsComponent, options);
    if (type === 'numeric') {
      optionsComponent.decimalPlaces = optionsComponent.decimalPlaces ?? 0;
      return (
        <InputMonetary
          {...optionsComponent}
          onChange={(e) => this.store.updateAttributeValorComparavel(e, props.rowIndex, idxInterval)}
          value={
            isValueValid(idxInterval)
              ? this.store.conditionsTemp[props.rowIndex][productKey][idxInterval]
              : this.store.conditionsTemp[props.rowIndex][productKey]
          }
        />
      );
    } else if (type === 'boolean') {
      return (
        <FcDropdown
          inOrder
          {...this.store.getColumnsMatrizRisco()[columnName].optionsComponent}
          options={this.store.response}
          value={this.store.conditionsTemp[props.rowIndex][productKey]}
          onChange={(e) =>
            this.store.updateAttributeValorComparavel(e.target.value, props.rowIndex, undefined, () =>
              this.forceUpdate()
            )
          }
          showClear
          showFilterClear
          filter
          key="dropdown"
        />
      );
    }
  }

  _getEditorByComparableValue(productKey, props) {
    const comparator =
      this.store.conditionsTemp[props.rowIndex]?.tipoComparacao ?? this.store.conditions[props.rowIndex].tipoComparacao;
    const columnName = this.store.object.nomeColuna;
    if (!this.state.editingRows[props.rowIndex]) {
      return this._getBodyComparableValue(props);
    } else {
      if (this.store.getColumnsMatrizRisco()[columnName].type == 'list') {
        return this._multiSelectEditor(productKey, columnName, props);
      } else if (this.store.getColumnsMatrizRisco()[columnName].type == 'select') {
        return this._dropdownEditor(productKey, columnName, props);
      } else if (comparator === 'INTERVALO') {
        return this._intervalEditor(productKey, columnName, props);
      } else {
        return this._getSimpleEditorByType(productKey, columnName, props);
      }
    }
  }

  toggleShowConfirmRemoveDialog() {
    this.setState((oldState) => ({ removeDialogVisible: !oldState.removeDialogVisible, removeIdx: undefined }));
  }

  _confirmRemove() {
    return (
      <ConfirmDialog
        blockScroll
        visible={this.state.removeDialogVisible}
        message="Você realmente deseja excluir a condição selecionada?"
        header="Excluir registro"
        icon="pi pi-exclamation-triangle"
        acceptClassName="p-button-danger"
        accept={() => {
          this.store.deleteCondicao(this.state.removeIdx);
        }}
        onHide={() => this.toggleShowConfirmRemoveDialog()}
      />
    );
  }

  _toggleShowConfirmResetDialog() {
    this.setState((oldState) => ({
      resetConditionsDialogVisible: !oldState.resetConditionsDialogVisible,
    }));
  }

  _toggleShowConfirmResetDialogTiposProcesso() {
    this.setState((oldState) => ({
      resetConditionsDialogTiposProcessoVisible: !oldState.resetConditionsDialogTiposProcessoVisible,
    }));
  }

  _confirmReset() {
    return (
      <ConfirmDialog
        blockScroll
        visible={this.state.resetConditionsDialogVisible}
        message="Ao selecionar um novo atributo, todas as condições serão excluidas. Deseja prosseguir?"
        header="Excluir condições"
        icon="pi pi-exclamation-triangle"
        acceptClassName="p-button-danger"
        accept={() => {
          this.store.updateAttributeNomeColuna(this.state.newColumn);
          this.store.resetConditions();
        }}
        onHide={() => this._toggleShowConfirmResetDialog()}
      />
    );
  }

  _confirmResetTiposProcesso() {
    return (
      <ConfirmDialog
        blockScroll
        visible={this.state.resetConditionsDialogTiposProcessoVisible}
        message="Ao modificar os tipos de processo, todas as condições serão excluidas. Deseja prosseguir?"
        header="Excluir condições"
        icon="pi pi-exclamation-triangle"
        acceptClassName="p-button-danger"
        accept={() => {
          this.store.updateAttribute('tiposProcessos', this.state.newTiposProcesso);
          this.store.resetConditionsTiposProcesso();
        }}
        onHide={() => this._toggleShowConfirmResetDialogTiposProcesso()}
      />
    );
  }

  _renderTableButtons(rowData, props) {
    const rowEditor = props.rowEditor;

    rowEditor.editing = rowData && this.state.editingRows[rowData.key];
    if (rowEditor.editing) {
      return rowEditor.element;
    } else {
      return (
        <SplitButton
          icon="pi pi-pencil"
          onClick={() => {
            const editingRows = { ...this.state.editingRows, ...{ [`${rowData.key}`]: true } };
            this.setState({ editingRows }, () => {
              this.forceUpdate();
            });
            this.store.setToEditMode(rowData.key);
          }}
          tooltip="Editar"
          tooltipOptions={{ position: 'top' }}
          model={[
            {
              label: 'Excluir',
              icon: 'pi pi-trash',
              command: () => {
                this.setState({ removeDialogVisible: true, removeIdx: rowData.key });
              },
            },
          ]}
        />
      );
    }
  }

  _renderColumns(columns) {
    return columns.map((col) => <Column key={`col-${col.field}`} {...col} />);
  }

  onRowEditCancel(e) {
    const editingRows = { ...this.state.editingRows, ...{ [`${e.index}`]: false } };
    if (!this.store.conditions[e.index].persistent) {
      this.store.deleteCondicao(e.index);
      this.setState({ editingRows }, () => {
        this.forceUpdate();
      });
      this.store.removeEditMode(e.index);
    } else {
      this.setState({ editingRows }, () => {
        this.forceUpdate();
      });
      this.store.removeEditMode(e.index);
    }
  }

  onRowEditComplete(e) {
    this.store.setToPersistentMode(e.index);
    this.store.updatePersistFlag(e.index);
    this.onRowEditChange(e);
  }

  onRowEditValidator(e) {
    const currentValue = this.store.conditionsTemp[e.key ?? e.index] ?? this.store.conditions[e.key ?? e.index];
    let errorMessage = undefined;
    if (!currentValue.risco) {
      errorMessage = 'É necessário informar um valor de risco';
    } else if (!currentValue.tipoComparacao) {
      errorMessage = 'É necessário selecionar o tipo de comparação';
    } else if (!isValueValid(currentValue.valorComparavel)) {
      errorMessage = 'É necessário informar um valor para comparação';
    } else if (
      (Array.isArray(currentValue.valorComparavel) && currentValue.valorComparavel.length == 0) ||
      currentValue.valorComparavel === ''
    ) {
      errorMessage = 'É necessário informar ao menos um valor para comparação';
    }
    return !errorMessage ? true : showNotification('error', null, errorMessage);
  }

  onRowEditChange(e) {
    const editingRows = { ...this.state.editingRows, ...{ [`${e.index}`]: false } };
    this.setState({ editingRows }, () => this.forceUpdate());
  }

  _renderTable() {
    const header = (
      <FcButton
        type="button"
        label="Novo"
        style={{ marginBottom: '5px', marginRight: '5px', width: '8%' }}
        onClick={(event) => {
          this.store.newCondicao(() => {
            const editingRows = Object.assign({}, this.state.editingRows);
            editingRows[this.store.conditions.length - 1] = true;
            this.setState({ editingRows }, () => {
              this.forceUpdate();
            });
          });
          event.options?.clear();
        }}
        icon={PrimeIcons.PLUS}
        disabled={!this.store.object?.nomeColuna}
      />
    );

    const columns = [
      {
        style: { width: '30%' },
        field: 'risco',
        header: (
          <Tooltip value={'Risco gerado caso a condição seja avaliada com sucesso.'}>
            <label htmlFor={'questionIcon'}>
              Risco
              <i id="questionIcon" className={`pi pi-question-circle p-ml-1 tooltip-risco`} />
            </label>
          </Tooltip>
        ),
        editor: (props) => this._riscoEditor(props),
      },
      {
        style: { width: '30%' },
        field: 'tipoComparacao',
        header: (
          <Tooltip value={'Comparador utilizado para avaliar a condição.'}>
            <label htmlFor={'questionIcon'}>
              Operador
              <i id="questionIcon" className={`pi pi-question-circle p-ml-1 tooltip-tipoComparacao`} />
            </label>
          </Tooltip>
        ),
        editor: (props) => this._tipoComparacaoEditor(props),
        body: (options) => getValueByKey(options.tipoComparacao, DadosEstaticosService.getTipoComparacao()),
      },
      {
        style: { width: '30%' },
        field: 'valorComparavel',
        header: (
          <Tooltip value={'Valor utilizado para realizar a comparação para avaliar a expressão.'}>
            <label htmlFor={'questionIcon'}>
              Valor de Comparação
              <i id="questionIcon" className={`pi pi-question-circle p-ml-1 tooltip-valorComparavel`} />
            </label>
          </Tooltip>
        ),
        body: (options) => this._getBodyComparableValue(options),
        editor: (props) => this._getEditorByComparableValue('valorComparavel', props),
      },
      {
        rowEditor: true,
        body: this._renderTableButtons,
        style: { width: '10%' },
      },
    ];

    return (
      <DataTable
        rowHover
        style={{ width: '100%' }}
        header={header}
        value={this.store.conditions}
        editMode="row"
        dataKey="key"
        stripedRows
        emptyMessage={'Nenhuma condição adicionada'}
        editingRows={this.state.editingRows}
        onRowEditChange={this.onRowEditChange}
        onRowEditComplete={this.onRowEditComplete}
        onRowEditCancel={this.onRowEditCancel}
        rowEditValidator={this.onRowEditValidator}
      >
        {this._renderColumns(columns)}
      </DataTable>
    );
  }

  _validateRows() {
    return !Object.keys(this.state.editingRows).some((k) => this.state.editingRows[k]);
  }

  submitFormData(e) {
    e && e.preventDefault();
    const execution = () => {
      if (!this.store.rules.hasError && this._validateRows()) {
        this.store.save(this._goBack, this.props.action);
      } else {
        showNotification('error', null, 'Verifique os campos do formulário!');
      }
    };

    if (this.state.submitted) {
      execution();
    } else {
      this.setState({ submitted: true }, execution);
    }
  }

  render() {
    const { submitted } = this.state;
    const { updateAttribute, getRule } = this.store;
    const { submitFormData } = this;

    const breacrumbItems = [
      { label: 'Função de Risco', url: UrlRouter.seguranca.funcaoRisco.index },
      { label: this.props.action === 'new' ? 'Novo' : 'Editar' },
    ];

    let content = '';
    if (this.store.object) {
      content = (
        <>
          <AppBreadCrumb items={breacrumbItems} />
          <div className="card page form-action-buttons">
            <form onSubmit={submitFormData}>
              <div className="p-fluid p-formgrid p-grid">
                <FormField columns={6} attribute="nome" label="Nome" rule={getRule('nome')} submitted={submitted}>
                  <InputText
                    onChange={(e) => updateAttribute('nome', e)}
                    placeholder="Informe o nome"
                    value={this.store.object.nome}
                  />
                </FormField>
                <FormField
                  columns={6}
                  attribute="descricao"
                  label="Descrição"
                  rule={getRule('descricao')}
                  submitted={submitted}
                >
                  <InputTextarea
                    onChange={(e) => updateAttribute('descricao', e)}
                    placeholder="Informe a descrição"
                    value={this.store.object.descricao}
                  />
                </FormField>
                <FormField
                  columns={6}
                  attribute="peso"
                  label="Peso"
                  rule={getRule('peso')}
                  submitted={submitted}
                  infoTooltip="Deve ser informado um peso para o cálculo de risco, esse valor irá influenciar o risco gerado pela condição cujo o processo a ser analisado adentre."
                >
                  <InputNumber
                    value={this.store.object.peso}
                    onValueChange={(e) => updateAttribute('peso', e)}
                    min={1}
                    max={10}
                    mode="decimal"
                    minFractionDigits={1}
                  />
                </FormField>
                <FormField
                  columns={4}
                  attribute="tiposProcessos"
                  label="Tipos de Processo"
                  rule={getRule('tiposProcessos')}
                  submitted={submitted}
                >
                  <AsyncMultiselect
                    placeholder="Selecione os Tipos de Processo"
                    value={this.store.object.tiposProcessos}
                    onChange={(e) => {
                      this.store.conditions?.length > 0
                        ? this.setState({ newTiposProcesso: e, resetConditionsDialogTiposProcessoVisible: true })
                        : updateAttribute('tiposProcessos', e);
                    }}
                    store={this.store.tiposProcessosStore}
                    showOverlay
                    label="nome"
                  />
                </FormField>
                <FormField
                  columns={6}
                  attribute="nomeColuna"
                  label="Atributo para Comparação"
                  infoTooltip="Característica do processo que deseja-se avaliar."
                  rule={getRule('nomeColuna')}
                  submitted={submitted}
                >
                  <FcDropdown
                    inOrder
                    {...this.validateField('nomeColuna')}
                    onChange={(e) => {
                      this.store.conditions?.length > 0
                        ? this.setState({ newColumn: e.target.value, resetConditionsDialogVisible: true })
                        : this.store.updateAttributeNomeColuna(e.target.value);
                    }}
                    placeholder="Selecione o atributo para ser comparado"
                    value={this.store.object.nomeColuna}
                    id="nomeColuna"
                    optionLabel="text"
                    optionValue="value"
                    options={DadosEstaticosService.getColumnsMatrizRisco(this.store.object.tiposProcessos).sort(
                      (a, b) => a.text.localeCompare(b.text)
                    )}
                    disabled={!this.store.object?.tiposProcessos?.length > 0}
                    emptyMessage="Nenhuma opção disponível para os tipos de processo selecionados"
                  />
                </FormField>
                {this.state.resetConditionsDialogVisible && this._confirmReset()}
                {this.state.resetConditionsDialogTiposProcessoVisible && this._confirmResetTiposProcesso()}
                <FormField
                  columns={6}
                  attribute="ativa"
                  label="Ativa"
                  rule={getRule('ativa')}
                  submitted={submitted}
                  checkbox
                >
                  <Checkbox onChange={(e) => updateAttribute('ativa', e.checked)} checked={this.store.object.ativa} />
                </FormField>
                <Divider />
                {this._renderTable()}
                {this.state.removeDialogVisible && this._confirmRemove()}
              </div>
              {this.renderActionButtons()}
            </form>
          </div>
        </>
      );
    } else {
      content = (
        <div className="card page">
          <AppBreadCrumb items={breacrumbItems} />
          <i className="pi pi-spin pi-spinner" style={{ fontSize: '2em' }}></i>
        </div>
      );
    }

    return (
      <PermissionProxy resourcePermissions={this.getWritePermission()} blockOnFail>
        {content}
      </PermissionProxy>
    );
  }
}

FuncaoRiscoFormPage.propTypes = {
  id: PropTypes.any,
  action: PropTypes.any,
  history: PropTypes.any,
};

export default FuncaoRiscoFormPage;
