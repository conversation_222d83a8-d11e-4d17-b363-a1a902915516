import React from 'react';
import { Link } from 'react-router-dom';
import { observer } from 'mobx-react';
import IndexDataTable from 'fc/components/IndexDataTable';
import FcButton from 'fc/components/FcButton';
import { PrimeIcons } from 'primereact/api';
import AppBreadCrumb from 'fc/components/AppBreadCrumb';
import UrlRouter from '~/constants/UrlRouter';
import GenericIndexPage from 'fc/pages/GenericIndexPage';
import AdvancedSearch from 'fc/components/AdvancedSearch';
import AccessPermission from '~/constants/AccessPermission';
import PermissionProxy from 'fc/components/PermissionProxy';
import FuncaoRiscoIndexStore from '~/stores/funcaoRisco/indexStore';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import { getNumberFractionDigits, getValueByKey } from 'fc/utils/utils';
import { Dialog } from 'primereact/dialog';
import { Divider } from 'primereact/divider';
import Tooltip from 'fc/components/Tooltip';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';

@observer
class FuncaoRiscoIndexPage extends GenericIndexPage {
  constructor(props) {
    super(props, AccessPermission.funcaoRisco);
    this.store = new FuncaoRiscoIndexStore();
    this.state = {
      idRemove: null,
      detalhesVisibility: false,
      selectedRow: null,
    };
  }

  _getBodyTiposProcessos(tiposProcessos) {
    const values = [];
    tiposProcessos.forEach((tipo) => {
      values.push(tipo.nome);
    });
    return values.join(', ');
  }

  filterLegislacao(data) {
    return data.filter((item) => item.legislacao.includes('LEI_N_14133'));
  }

  getColumnsMatrizRisco() {
    return {
      VALOR_ESTIMADO: {
        value: 'VALOR_ESTIMADO',
        text: 'Valor estimado',
        type: 'numeric',
        bodyFormatter: (value) => getNumberFractionDigits(value, 3),
        optionsComponent: {
          decimalPlaces: 3,
        },
      },
      TIPOS_LICITACAO: {
        text: 'Tipo da licitação',
        type: 'list',
        loadData: () => TipoLicitacaoService.getAll(),
        handleResponse: (data) => this.filterLegislacao(data),
        bodyFormatter: (value) => value.join(', '),
        optionsComponent: {
          optionLabel: 'nome',
          optionValue: 'nome',
          filterBy: 'text',
        },
      },
      NATUREZAS_OBJETO: {
        text: 'Naturezas de objeto',
        type: 'list',
        loadData: () => DadosEstaticosService.getNaturezaObjetoLicitacao(),
        bodyFormatter: (value) =>
          value
            .map((v) => {
              const result = getValueByKey(v, DadosEstaticosService.getNaturezaObjetoLicitacao());
              return result === '-' ? v : result;
            })
            ?.join(', ') ?? '-',
        optionsComponent: {
          optionLabel: 'text',
          optionValue: 'value',
          filterBy: 'text',
        },
      },
      PERMITE_CONSORCIO: {
        text: 'Permite consórcio',
        type: 'boolean',
        loadData: () => DadosEstaticosService.getSimNao(),
        bodyFormatter: (value) =>
          value.map((v) => {
            const result = getValueByKey(v, DadosEstaticosService.getSimNao(), 'bit');
            return result === '-' ? v : result;
          }),
        optionsComponent: {
          optionLabel: 'text',
          optionValue: 'bit',
          filterBy: 'text',
        },
      },
    };
  }

  _renderValue(label, value, col = 12) {
    return (
      <div className={`p-mt-3 p-col-12 p-md-${col} p-sm-${col >= 6 ? col : 6}`}>
        <div className="p-col-12 drawer-content-label font-bold">{label}</div>
        <div className={`p-col-12`}>{value ? value : '-'}</div>
      </div>
    );
  }

  _getFormattedValue(value, formatter) {
    return formatter ? formatter(value) : value;
  }

  _getBodyComparableValue(condicao) {
    const comparator = condicao.tipoComparacao;
    const columnName = this.state.selectedRow.nomeColuna;
    const bodyFormatter = this.getColumnsMatrizRisco()[columnName]?.bodyFormatter;

    if (comparator === 'INTERVALO') {
      const values = condicao.valorComparavel.split(';');
      return `(${this._getFormattedValue(values[0], bodyFormatter)} ~ ${this._getFormattedValue(
        values[1],
        bodyFormatter
      )})`;
    } else {
      return this._getFormattedValue(condicao.valorComparavel.split(';'), bodyFormatter);
    }
  }

  _renderColumns(columns) {
    return columns.map((col) => <Column key={`col-${col.field}`} {...col} />);
  }

  renderDetalhesDialog() {
    const data = this.state.selectedRow;
    const columns = [
      {
        style: { width: '15%' },
        field: 'risco',
        header: (
          <Tooltip value={'Risco gerado caso a condição seja avaliada com sucesso.'}>
            <label htmlFor={'questionIcon'}>
              Risco
              <i id="questionIcon" className={`pi pi-question-circle p-ml-1 tooltip-risco`} />
            </label>
          </Tooltip>
        ),
      },
      {
        style: { width: '20%' },
        field: 'tipoComparacao',
        header: (
          <Tooltip value={'Comparador utilizado para avaliar a condição.'}>
            <label htmlFor={'questionIcon'}>
              Operador
              <i id="questionIcon" className={`pi pi-question-circle p-ml-1 tooltip-tipoComparacao`} />
            </label>
          </Tooltip>
        ),
        body: (rowData) => getValueByKey(rowData.tipoComparacao, DadosEstaticosService.getTipoComparacao()),
      },
      {
        style: { width: '60%' },
        field: 'valorComparavel',
        header: (
          <Tooltip value={'Valor utilizado para realizar a comparação para avaliar a expressão.'}>
            <label htmlFor={'questionIcon'}>
              Valor de Comparação
              <i id="questionIcon" className={`pi pi-question-circle p-ml-1 tooltip-valorComparavel`} />
            </label>
          </Tooltip>
        ),
        body: (rowData) => this._getBodyComparableValue(rowData),
      },
    ];

    return (
      <Dialog
        header="Detalhes da Função de Risco"
        visible={this.state.detalhesVisibility}
        style={{ width: '40%' }}
        breakpoints={{ '960px': '75vw', '640px': '75vw' }}
        onHide={() => this.setState({ detalhesVisibility: false })}
        draggable={false}
        resizable={false}
        dismissableMask
      >
        <div>
          <div className="grid">
            {this._renderValue('Nome', data.nome, 6)}
            {this._renderValue('Descrição', data.descricao, 6)}
            {this._renderValue('Peso', getNumberFractionDigits(data.peso), 6)}
            {this._renderValue(
              'Atributo',
              getValueByKey(data.nomeColuna, DadosEstaticosService.getColumnsMatrizRisco(data.tiposProcessos)),
              6
            )}
            {this._renderValue('Tipos dos Processos', this._getBodyTiposProcessos(data.tiposProcessos), 6)}
            {this._renderValue('Ativa', getValueByKey(data.ativa, DadosEstaticosService.getSimNao()), 6)}
          </div>
          <Divider />
          <DataTable
            rowHover
            style={{ width: '100%' }}
            header=""
            value={data.condicoes}
            editMode="row"
            dataKey="key"
            stripedRows
            emptyMessage={'Nenhuma condição adicionada'}
          >
            {this._renderColumns(columns)}
          </DataTable>
        </div>
      </Dialog>
    );
  }

  render() {
    const columns = [
      {
        field: 'nome',
        header: 'Nome',
        sortable: true,
        style: { width: '30%' },
      },
      {
        field: 'descricao',
        header: 'Descrição',
        sortable: true,
        style: { width: '30%' },
        body: ({ descricao }) => <div style={{ wordBreak: 'break-word' }}>{descricao}</div>,
        className: 'text-justify',
      },
      {
        field: 'ativa',
        header: 'Ativa',
        sortable: true,
        body: ({ ativa }) => getValueByKey(ativa, DadosEstaticosService.getSimNao()),
        style: { width: '10%' },
      },
      {
        field: 'peso',
        header: 'Peso',
        sortable: true,
        body: ({ peso }) => getNumberFractionDigits(peso),
        style: { width: '10%' },
      },
      {
        field: 'nomeColuna',
        header: 'Atributo',
        sortable: true,
        style: { width: '10%' },
        body: ({ nomeColuna, tiposProcessos }) =>
          getValueByKey(nomeColuna, DadosEstaticosService.getColumnsMatrizRisco(tiposProcessos)),
      },
      {
        field: 'tiposProcessos',
        header: 'Tipos Processos',
        sortable: true,
        style: { width: '10%' },
        body: ({ tiposProcessos }) => this._getBodyTiposProcessos(tiposProcessos),
      },
      {
        style: { width: '165px' },
        body: (rowData) => {
          return (
            <div className="actions p-d-flex p-jc-end">
              <PermissionProxy resourcePermissions={this.getReadPermission()}>
                <FcButton
                  icon="pi pi-eye"
                  className="p-button-sm p-button p-mr-2"
                  onClick={() => this.setState({ detalhesVisibility: true, selectedRow: rowData })}
                />
              </PermissionProxy>
              <PermissionProxy resourcePermissions={this.getWritePermission()}>
                <FcButton
                  icon="pi pi-pencil"
                  className="p-button-sm p-button-success p-mr-2"
                  onClick={() =>
                    this.pushUrlToHistory(UrlRouter.seguranca.funcaoRisco.editar.replace(':id', rowData.id))
                  }
                />
              </PermissionProxy>
              <PermissionProxy resourcePermissions={this.getWritePermission()}>
                <FcButton
                  icon="pi pi-trash"
                  className="p-button-sm p-button-danger"
                  onClick={() => {
                    this.setState({ idRemove: rowData.id });
                    this.store.toggleShowConfirmDialog();
                  }}
                />
              </PermissionProxy>
            </div>
          );
        },
      },
    ];

    const header = (
      <div className="table-header p-grid p-dir-col">
        <PermissionProxy resourcePermissions={this.getWritePermission()}>
          <Link to={UrlRouter.seguranca.funcaoRisco.novo}>
            <FcButton
              className="p-button"
              label="Novo"
              style={{ marginBottom: '5px', marginRight: '5px' }}
              icon={PrimeIcons.PLUS}
            />
          </Link>
        </PermissionProxy>
      </div>
    );

    const { listKey, loading } = this.store;
    const { getDefaultTableProps } = this;

    const breacrumbItems = [{ label: 'Funções de Risco' }];

    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        <AppBreadCrumb items={breacrumbItems} />
        <div className="card page index-table">
          <AdvancedSearch
            searchParams={this.store.getAdvancedSearchParams()}
            store={this.store}
            searchFields={['nome']}
          />
          <IndexDataTable
            columns={columns}
            value={listKey}
            header={header}
            loading={loading}
            {...getDefaultTableProps()}
          />
          {this.store.isConfirmDialogVisible && this.confirmRemove(this.state.idRemove)}
          {this.state.selectedRow && this.renderDetalhesDialog()}
        </div>
      </PermissionProxy>
    );
  }
}

FuncaoRiscoIndexPage.displayName = 'FuncaoRiscoIndexPage';

export default FuncaoRiscoIndexPage;
