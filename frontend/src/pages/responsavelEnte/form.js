import React from 'react';
import { observer } from 'mobx-react';
import PropTypes from 'prop-types';
import GenericFormPage from 'fc/pages/GenericFormPage';
import AppBreadCrumb from 'fc/components/AppBreadCrumb';
import AsyncDropdown from 'fc/components/AsyncDropdown';
import PermissionProxy from 'fc/components/PermissionProxy';
import Form<PERSON>ield from 'fc/components/FormField';
import { InputText } from 'primereact/inputtext';
import UrlRouter from '~/constants/UrlRouter';
import AccessPermission from '~/constants/AccessPermission';
import ResponsavelEnteFormStore from '~/stores/responsavelEnte/formStore';

@observer
class ResponsavelEnteFormPage extends GenericFormPage {
  constructor(props) {
    super(props, UrlRouter.administracao.responsavelEnte.index, AccessPermission.responsavelEnte);
    this.store = new ResponsavelEnteFormStore();
  }

  render() {
    const { submitted } = this.state;
    const { updateAttribute, getRule } = this.store;
    const { submitFormData } = this;

    const breacrumbItems = [
      { label: 'Responsável Ente', url: UrlRouter.administracao.responsavelEnte.index },
      { label: this.props.action === 'new' ? 'Novo' : 'Editar' },
    ];

    let content = '';
    if (this.store.object) {
      content = (
        <>
          <AppBreadCrumb items={breacrumbItems} />
          <div className="card page form-action-buttons">
            <form onSubmit={submitFormData}>
              <div className="p-fluid p-formgrid p-grid">
                <FormField columns={6} attribute="nome" label="Nome" rule={getRule('nome')} submitted={submitted}>
                  <InputText
                    onChange={(e) => updateAttribute('nome', e)}
                    placeholder="Informe o nome"
                    value={this.store.object.nome}
                  />
                </FormField>
                <FormField columns={6} attribute="email" label="E-mail" rule={getRule('email')} submitted={submitted}>
                  <InputText
                    onChange={(e) => updateAttribute('email', e)}
                    placeholder="Informe o e-mail"
                    value={this.store.object.email}
                  />
                </FormField>
                <FormField
                  columns={12}
                  attribute="ente"
                  label="Selecione a Ente"
                  rule={getRule('ente')}
                  submitted={submitted}
                >
                  <AsyncDropdown
                    onChange={updateAttribute}
                    value={this.store.object.ente?.id ?? undefined}
                    placeholder="Selecione a ente"
                    store={this.store.enteSelectStore}
                  />
                </FormField>
              </div>
              {this.renderActionButtons()}
            </form>
          </div>
        </>
      );
    } else {
      content = (
        <div className="card page">
          <AppBreadCrumb items={breacrumbItems} />
          <i className="pi pi-spin pi-spinner" style={{ fontSize: '2em' }}></i>
        </div>
      );
    }

    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        {content}
      </PermissionProxy>
    );
  }
}

ResponsavelEnteFormPage.propTypes = {
  id: PropTypes.any,
  action: PropTypes.any,
  history: PropTypes.any,
};

export default ResponsavelEnteFormPage;
