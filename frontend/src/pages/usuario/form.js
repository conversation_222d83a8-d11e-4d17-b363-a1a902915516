import React from 'react';
import { observer } from 'mobx-react';
import PropTypes from 'prop-types';
import UsuarioFormStore from '~/stores/usuario/formStore';
import { InputText } from 'primereact/inputtext';
import UrlRouter from '~/constants/UrlRouter';
import GenericFormPage from 'fc/pages/GenericFormPage';
import AppBreadCrumb from 'fc/components/AppBreadCrumb';
import FormField from 'fc/components/FormField';
import AsyncPickList from 'fc/components/AsyncPicklist';
import { InputMask } from 'primereact/inputmask';
import { ProgressSpinner } from 'primereact/progressspinner';
import PermissionProxy from 'fc/components/PermissionProxy';
import AccessPermission from '~/constants/AccessPermission';

@observer
class UsuarioFormPage extends GenericFormPage {
  constructor(props) {
    super(props, UrlRouter.seguranca.usuario.index, AccessPermission.usuario);
    this.store = new UsuarioFormStore();
  }

  componentDidMount() {
    const { id, action } = this.props;
    this.store.initialize(id, { grupos: [] }, () => this.store.carregarGrupos(id, action, () => this.forceUpdate()));
  }

  render() {
    const { submitted } = this.state;
    const { getRule } = this.store;
    const { submitFormData } = this;

    const breacrumbItems = [
      { label: 'Usuários', url: UrlRouter.seguranca.usuario.index },
      { label: this.props.action === 'new' ? 'Novo' : 'Editar' },
    ];

    let content;
    if (this.store.object) {
      content = (
        <>
          <AppBreadCrumb items={breacrumbItems} />
          <div className="card page form-action-buttons">
            <form onSubmit={submitFormData}>
              <div className="p-fluid p-formgrid p-grid">
                <FormField columns={6} attribute="nome" label="Nome" submitted={submitted}>
                  <InputText
                    onChange={(e) => this.store.updateAttribute('nome', e)}
                    value={this.store.object.nome}
                    placeholder="Informe o nome"
                    disabled
                    id="nome"
                  />
                </FormField>
                <FormField columns={6} attribute="cpf" label="CPF" submitted={submitted}>
                  <InputMask
                    mask="999.999.999-99"
                    onChange={(e) => this.store.updateAttribute('cpf', e)}
                    value={this.store.object.cpf}
                    placeholder="Informe o CPF"
                    disabled
                    id="cpf"
                  />
                </FormField>
                <FormField columns={6} attribute="orgao" label="Órgão" submitted={submitted}>
                  <InputText
                    onChange={(e) => this.store.updateAttribute('orgao', e)}
                    value={this.store.object.orgao}
                    placeholder="Informe o órgão"
                    disabled
                    id="orgao"
                  />
                </FormField>

                <FormField
                  columns={6}
                  attribute="matricula"
                  label="Matrícula"
                  rule={getRule('matricula')}
                  submitted={submitted}
                >
                  <InputText
                    onChange={(e) => this.store.updateAttribute('matricula', e)}
                    value={this.store.object.matricula}
                    placeholder="Informe a matrícula"
                    disabled
                    id="matricula"
                  />
                </FormField>

                <FormField columns={6} attribute="funcao" label="Função" submitted={submitted}>
                  <InputText
                    onChange={(e) => this.store.updateAttribute('funcao', e)}
                    value={this.store.object.funcao}
                    placeholder="Informe a função"
                    disabled
                    id="funcao"
                  />
                </FormField>

                <FormField columns={6} attribute="telefone" label="Telefone" submitted={submitted}>
                  <InputMask
                    mask="(99) 99999-9999"
                    onChange={(e) => this.store.updateAttribute('telefone', e)}
                    value={this.store.object.telefone}
                    placeholder="Informe o telefone"
                    disabled
                    id="telefone"
                  />
                </FormField>

                <FormField columns={6} attribute="login" label="Login" submitted={submitted}>
                  <InputText
                    onChange={(e) => this.store.updateAttribute('login', e)}
                    value={this.store.object.login}
                    placeholder="Informe o login"
                    disabled
                    id="login"
                  />
                </FormField>

                <FormField columns={6} attribute="email" label="E-mail" submitted={submitted}>
                  <InputText
                    onChange={(e) => this.store.updateAttribute('email', e)}
                    value={this.store.object.email}
                    placeholder="Informe o e-mail"
                    disabled
                    id="email"
                  />
                </FormField>

                {!this.store.loadingGrupos ? (
                  <FormField
                    columns={12}
                    attribute="grupos"
                    label="Grupos"
                    rule={getRule('grupos')}
                    submitted={submitted}
                  >
                    <AsyncPickList
                      value={this.store.object.grupos ?? []}
                      sourceHeader="Lista de Grupos"
                      targetHeader="Grupos que o Usuário Participa"
                      onChange={(e) => this.store.updateAttribute('grupos', e)}
                      store={this.store.grupoStore}
                    />
                  </FormField>
                ) : (
                  <ProgressSpinner />
                )}
              </div>
              {this.renderActionButtons()}
            </form>
          </div>
        </>
      );
    } else {
      content = (
        <div className="card page">
          <AppBreadCrumb items={breacrumbItems} />
          <i
            className="pi pi-spin pi-spinner"
            style={{
              marginTop: '20px',
              marginLeft: 'calc(50% - 20px)',
              fontSize: '2em',
            }}
          ></i>
        </div>
      );
    }
    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        {content}
      </PermissionProxy>
    );
  }
}

UsuarioFormPage.propTypes = {
  id: PropTypes.any,
  action: PropTypes.any,
  history: PropTypes.any,
};

export default UsuarioFormPage;
