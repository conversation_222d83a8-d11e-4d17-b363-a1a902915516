import React from 'react';
import PropTypes from 'prop-types';
import { observer } from 'mobx-react';
import { getValueDate, getValue<PERSON><PERSON>, getValue<PERSON><PERSON><PERSON><PERSON>, generateFullURL } from 'fc/utils/utils';
import { DATE_FORMAT, DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS } from 'fc/utils/date';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import AccessPermission from '~/constants/AccessPermission';
import { Link } from 'react-router-dom/cjs/react-router-dom.min';
import UrlRouter from '~/constants/UrlRouter';
import Vencedores from '~/pages/vencedores';
import { Divider } from 'primereact/divider';
import AppStore from 'fc/stores/AppStore';
import './style.scss';
import FcCloseableTabView from 'fc/components/FcCloseableTabView';
import ScrollMenu from 'fc/components/ScrollMenu';
import classNames from 'classnames';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import MultipleFileUploader from 'fc/components/MultipleFileUploader';
import GerenciamentoTermoFormStore from '~/stores/gerenciamentoTermo/formStore';
import { FeatureGroup, MapContainer, Marker, Polygon, Polyline, TileLayer } from 'react-leaflet';
import { Dialog } from 'primereact/dialog';
import FcButton from 'fc/components/FcButton';

@observer
class InexigibilidadeDetailPage extends React.Component {
  termoReferenciaStore;
  constructor(props) {
    super(props);
    this.state = {
      activeTabIndex: 0,
      visibleDialogObras: false,
      showDetalhesDialog: false,
    };
    this.termoReferenciaStore = new GerenciamentoTermoFormStore();
  }

  componentDidMount() {
    const { idTermo, inexigibilidadeStore } = this.props;
    this.termoReferenciaStore.initialize(idTermo, {}, () => {
      this.termoReferenciaStore.recuperarArquivos(idTermo);
    });
    inexigibilidadeStore.hasAlert(inexigibilidadeStore.object.id);
  }

  getValueStyle() {
    return { fontSize: AppStore.layout === 'extenso' ? '1.26rem' : '1.1rem' };
  }

  getValueLayoutClassNames() {
    const tipoLayout = AppStore.layout;
    return { 'py-0': tipoLayout === 'compacto', 'py-2': tipoLayout === 'default' || tipoLayout === 'extenso' };
  }

  _renderValue(label, value, col = 12, type = 'value', url = '#') {
    return (
      <div
        style={this.getValueStyle()}
        className={classNames(
          `details-set details-display flex align-items-center p-col-12 p-md-${col} p-sm-${col >= 6 ? col : 6}`,
          this.getValueLayoutClassNames()
        )}
      >
        <div
          style={{ color: 'rgb(0 0 0 / 50%)', paddingLeft: 0 }}
          className="xl:col-2 lg:col-3 md:col-4 sm:col-4 details-attributte drawer-content-label"
        >
          {label}
        </div>

        {type == 'value' && (
          <div
            style={{ paddingLeft: 0 }}
            className={`xl:col-10 lg:col-9 md:col-8 sm:col-8 details-value p-text-justify`}
          >
            {value ?? '-'}
          </div>
        )}

        {type == 'list' && (
          <div style={{ display: 'block', paddingLeft: 0 }}>
            {value?.length > 0
              ? value.map((value) => <div className={`details-value p-text-justify`}>{value}</div>)
              : '-'}
          </div>
        )}

        {type == 'link' && (
          <div
            style={{ paddingLeft: 0 }}
            className={`xl:col-10 lg:col-9 md:col-8 sm:col-8 details-value p-text-justify`}
          >
            {
              <Link target="_blank" rel="noreferrer" style={{ color: '#3F51B5' }} to={url}>
                <u>{value}</u>
              </Link>
            }
          </div>
        )}

        {type == 'button' && <div className="details-value p-text-justify p-0">{value ?? '-'}</div>}
      </div>
    );
  }

  _renderDivider(label) {
    return (
      <div style={{ flexBasis: 'auto', display: 'flex' }}>
        <h5
          style={{
            padding: '7px',
            whiteSpace: 'nowrap',
            width: 'auto',
            color: '#333333',
            alignContent: 'left',
            marginRight: '5px',
            marginTop: '11px',
          }}
        >
          {label}
        </h5>
        <Divider />
      </div>
    );
  }

  _renderTabs() {
    const { inexigibilidadeStore } = this.props;

    const tabs = [];
    tabs.push({ id: 0, header: 'Fornecedor Contratado', content: this._renderTabFornecedores() });
    tabs.push({
      id: 1,
      header: 'Arquivos',
      content: (
        <MultipleFileUploader
          fileTypes={DadosEstaticosService.getTipoArquivoInexigibilidade()}
          downloadOnly
          store={inexigibilidadeStore.fileStore}
        />
      ),
    });
    if (this.props.idTermo) {
      tabs.push({
        id: 2,
        header: 'Arquivos do Termo de Referência',
        content: (
          <MultipleFileUploader
            downloadOnly
            store={this.termoReferenciaStore?.fileStore}
            fileTypes={DadosEstaticosService.getTipoArquivoTermoReferencia()}
            accept=".pdf, .xls, .xlsx"
          />
        ),
      });
    }

    return (
      <FcCloseableTabView
        tabs={tabs}
        activeTabIndex={this.state.activeTabIndex}
        onChangeTab={(tab) => this.setState({ activeTabIndex: tabs.indexOf(tab) })}
      />
    );
  }

  _renderAlert() {
    const { inexigibilidadeStore } = this.props;
    if (inexigibilidadeStore.hasPermissionAlerta() && inexigibilidadeStore.idAlerta) {
      return (
        <FcButton
          icon="pi pi-bell"
          label="Histórico de Alertas"
          className="p-button-danger my-3"
          onClick={() => {
            window.open(
              generateFullURL(UrlRouter.alerta.editar.replace(':id', inexigibilidadeStore.idAlerta)),
              '_blank'
            );
          }}
        />
      );
    }
  }

  _renderTabFornecedores() {
    const { inexigibilidadeStore } = this.props;
    const vencedores = inexigibilidadeStore.vencedorStore?.vencedores;
    const isEntidadeAntiga = inexigibilidadeStore.isLegislacaoAntiga();

    const columns = isEntidadeAntiga
      ? [
          {
            field: 'licitante',
            header: 'Fornecedor',
            body: ({ licitante }) => licitante.nome,
          },
        ]
      : [
          {
            field: 'licitante',
            header: 'Fornecedor',
            body: ({ licitante }) => licitante.nome,
          },
          {
            field: 'valor',
            header: 'Valor (R$)',
            body: ({ valor }) => getValueMoney(valor),
          },
        ];

    return inexigibilidadeStore.isLegislacaoAntiga() && vencedores ? (
      <DataTable rowHover value={vencedores} emptyMessage="Nenhum vencedor adicionado.">
        {this._renderColumns(columns)}
      </DataTable>
    ) : (
      inexigibilidadeStore.vencedoresStore && (
        <Vencedores
          readOnly
          store={inexigibilidadeStore.vencedoresStore}
          labelLicitante="detentor"
          entidadeAntiga={isEntidadeAntiga}
        />
      )
    );
  }

  _renderColumns(columns) {
    return columns.map((col, idx) => <Column className={`p-p-3`} key={`col-${idx}`} {...col} />);
  }

  renderDadosBasicos() {
    const { inexigibilidadeStore, showGerenciamento } = this.props;
    const inexigibilidade = inexigibilidadeStore.object;
    const { idInexigibilidade } = inexigibilidadeStore;

    return (
      <div className="p-fluid p-formgrid">
        <div id={`detalhes-${idInexigibilidade}`}>{this._renderDivider('Detalhes')}</div>
        {this._renderValue('Número do Processo Administrativo', inexigibilidade.numeroSei)}
        {this._renderValue('Número da Inexigibilidade', inexigibilidade.numeroProcesso)}
        {this._renderValue('Ano', inexigibilidade.anoInexigibilidade)}
        {this._renderValue(
          'Valor',
          getValueMoney(inexigibilidade.valor, inexigibilidade.termoReferencia?.tresCasasDecimais ? 3 : 2)
        )}
        {this._renderValue('Objeto', inexigibilidade.objeto)}
        {this._renderValue(
          'Data da Autorização',
          getValueDate(inexigibilidade.dataPedido, DATE_FORMAT, DATE_PARSE_FORMAT_WITH_HOURS)
        )}
        {inexigibilidade.observacoes && this._renderValue('Observações', inexigibilidade.observacoes)}
        {inexigibilidade.termoReferencia && (
          <>
            {AppStore.hasPermission([
              AccessPermission.gerenciamentoTermo.readPermission,
              AccessPermission.gerenciamentoTermo.writePermission,
            ])
              ? this._renderValue(
                  'Termo de Referência',
                  inexigibilidade.termoReferencia.identificadorProcesso,
                  12,
                  'link',
                  UrlRouter.termoReferencia.gerenciamentoTermos.detalhe.replace(
                    ':idTermo',
                    inexigibilidade.termoReferencia?.id
                  )
                )
              : this._renderValue('Termo de Referência', inexigibilidade.termoReferencia?.identificadorProcesso, 12)}
          </>
        )}
        {this._renderValue(
          'Naturezas do Objeto',
          inexigibilidade?.naturezasDoObjeto?.map((n) =>
            getValueByKey(n, DadosEstaticosService.getCategoriasNaturezaObjeto())
          ),
          12,
          'list'
        )}
        <div id={`informacoesLegais-${idInexigibilidade}`}>{this._renderDivider('Informações Legais')}</div>
        {inexigibilidade.fundamentacaoLegalEntidade
          ? this._renderValue('Fundamentação Legal', inexigibilidade.fundamentacaoLegalEntidade.fundamentacao)
          : inexigibilidade.fundamentacao && this._renderValue('Regência Legal', inexigibilidade.fundamentacao)}
        {this._renderValue(
          'Responsável pela Inexigibilidade',
          inexigibilidade.processoMigrado
            ? inexigibilidade.responsavelRatificacao
            : inexigibilidade.responsavelInexigibilidade?.nome
        )}
        {inexigibilidade?.orgaosParticipantes?.length > 0 &&
          this._renderValue(
            'Órgãos Participantes',
            inexigibilidade?.orgaosParticipantes.map((op) => op.nome),
            12,
            'list'
          )}
        <div id={`recursos-${idInexigibilidade}`}>{this._renderDivider('Recursos')}</div>
        {this._renderValue(
          'Fontes de Recurso',
          inexigibilidade?.fontesDeRecurso?.map((f) => f.nome),
          12,
          'list'
        )}
        {showGerenciamento && (
          <>
            <div id={`gerenciamento-${idInexigibilidade}`}>{this._renderDivider('Gerenciamento')}</div>
            {this._renderValue('Cadastrado por', inexigibilidade?.usuario?.nome, 12)}
            {this._renderValue(
              'Data/Hora de Cadastro',
              getValueDate(inexigibilidade.dataCadastro, DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS),
              12
            )}
            {this._renderValue('Alterado por', inexigibilidadeStore?.ultimaAlteracao?.nome, 12)}
            {this._renderValue(
              'Data/Hora de Alteração',
              getValueDate(
                inexigibilidadeStore?.ultimaAlteracao?.data,
                DATE_FORMAT_WITH_HOURS,
                DATE_PARSE_FORMAT_WITH_HOURS
              ),
              12
            )}
          </>
        )}
      </div>
    );
  }

  _toggleDialogObras() {
    this.setState((oldState) => ({ visibleDialogObras: !oldState.visibleDialogObras }));
  }

  renderEdificacao(inexigibilidade) {
    const obra = inexigibilidade?.obra?.edificacao?.localizacao;
    const polyline = [];
    const polygon = [];
    const marker = [];
    if (inexigibilidade.coordenadas) {
      const coordObra = inexigibilidade?.coordenadas.split(',').map(Number);
      if (inexigibilidade.tipoSelecao === 'POLIGONO') {
        const coordObraPol = [];
        coordObra.forEach((coord, i) => {
          if (i % 2 === 0) {
            coordObraPol.push([coord, coordObra[i + 1]]);
          }
        });
        const finalCoord = [coordObraPol];
        polygon.push(finalCoord[0]);
      } else if (inexigibilidade.tipoSelecao === 'LINHA') {
        const coordObraLin = [];
        coordObra.forEach((coord, i) => {
          if (i % 2 === 0) {
            coordObraLin.push([coord, coordObra[i + 1]]);
          }
        });
        const finalCoord = [coordObraLin];
        polyline.push(finalCoord);
      } else {
        marker.push.apply(marker, coordObra);
      }
    } else {
      switch (obra?.type) {
        case 'Polygon':
          polygon.push(obra.coordinates[0]);
          break;
        case 'LineString':
          polyline.push(obra.coordinates);
          break;
        default:
          marker.push.apply(marker, obra.coordinates);
      }
    }
    return (
      <>
        {marker && marker.length > 0 && <Marker position={marker} radius={20} />}
        {polyline && polyline.length > 0 && <Polyline positions={polyline} />}
        {polygon && polygon.length > 0 && <Polygon positions={polygon} />}
      </>
    );
  }

  _renderDialogObras() {
    let defaultCoordinates = [-8.921198844909668, -70.98129272460939];
    const { inexigibilidadeStore } = this.props;
    const inexigibilidade = inexigibilidadeStore.object;

    const obra = inexigibilidade?.obra?.edificacao?.localizacao;

    if (inexigibilidade?.coordenadas) {
      const coordObra = inexigibilidade?.coordenadas.split(',').map(Number);
      switch (inexigibilidade?.tipoSelecao) {
        case 'POLIGONO':
          defaultCoordinates = [coordObra[0], coordObra[1]];
          break;
        case 'LINHA':
          defaultCoordinates = [coordObra[0], coordObra[1]];
          break;
        default:
          defaultCoordinates = coordObra;
      }
    } else {
      switch (obra?.type) {
        case 'Polygon':
          defaultCoordinates = obra.coordinates[0][0];
          break;
        case 'LineString':
          defaultCoordinates = obra.coordinates[0];
          break;
        default:
          defaultCoordinates = obra.coordinates;
      }
    }

    return (
      <Dialog
        header="Localização de obra"
        visible={this.state.visibleDialogObras}
        style={{ width: '80%' }}
        footer={[]}
        onHide={() => this._toggleDialogObras()}
      >
        <div>
          <MapContainer center={defaultCoordinates} zoom={16} style={{ height: '70vh', width: '100wh' }}>
            <TileLayer
              attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
              url="https://tile.openstreetmap.org/{z}/{x}/{y}.png"
            />
            <FeatureGroup />

            {this.renderEdificacao(inexigibilidade)}
          </MapContainer>
        </div>
      </Dialog>
    );
  }

  _renderButton() {
    return (
      <FcButton
        label="Visualizar no mapa"
        type="button"
        className="p-button-secondary"
        icon="pi pi-map"
        onClick={() => this._toggleDialogObras()}
      />
    );
  }

  render() {
    const { idInexigibilidade } = this.props.inexigibilidadeStore;
    const { inexigibilidadeStore } = this.props;
    const inexigibilidade = inexigibilidadeStore.object;

    return (
      <div>
        <div className="relative">
          <div className="scroll-menu">
            <ScrollMenu
              title="sumário"
              layoutPosition="left"
              offsetTopOnScroll={50}
              links={[
                { id: `detalhes-${idInexigibilidade}`, label: 'Detalhes' },
                { id: `informacoesLegais-${idInexigibilidade}`, label: 'Informações Legais' },
                { id: `recursos-${idInexigibilidade}`, label: 'Recursos' },
                { id: `gerenciamento-${idInexigibilidade}`, label: 'Gerenciamento' },
              ]}
            />
          </div>
        </div>

        {this.renderDadosBasicos()}
        {inexigibilidade?.obra && (
          <>
            {this._renderDialogObras()}
            {this._renderDivider('Obra')}
            {this._renderValue('Tipo da Obra', inexigibilidade.obra?.tipo?.nome)}
            {this._renderValue('Categoria da Obra', inexigibilidade.obra?.categoria?.nome)}
            {this._renderValue(
              'Obra',
              inexigibilidade?.coordenadas || inexigibilidade?.obra?.edificacao ? this._renderButton(3) : '-',
              12,
              'button'
            )}
          </>
        )}
        <Divider style={{ marginBottom: `1px` }} />
        {this._renderAlert()}
        {this._renderTabs()}
      </div>
    );
  }
}

InexigibilidadeDetailPage.defaultProps = {
  showGerenciamento: true,
};

InexigibilidadeDetailPage.propTypes = {
  inexigibilidadeStore: PropTypes.any,
  showGerenciamento: PropTypes.bool,
  idTermo: PropTypes.number,
};

export default InexigibilidadeDetailPage;
