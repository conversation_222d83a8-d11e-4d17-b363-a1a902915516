import React from 'react';
import PropTypes from 'prop-types';
import { observer } from 'mobx-react';
import InexigibilidadeFormStore from '~/stores/inexigibilidade/formStore';
import { ProgressSpinner } from 'primereact/progressspinner';
import InexigibilidadeDetailPage from './detail';
@observer
class InexigibilidadeIndexDetailPage extends React.Component {
  constructor(props) {
    super(props);
    this.inexigibilidadeStore = new InexigibilidadeFormStore();
  }

  componentDidMount() {
    const { id } = this.props;
    if (id) {
      this.inexigibilidadeStore.initialize(id, {}, () => {
        this.inexigibilidadeStore.initializeVencedorStore();
        this.inexigibilidadeStore.initializeArquivos(id);
        this.inexigibilidadeStore.carregaUltimaAlteracao(id);
        this.inexigibilidadeStore.initializeTdaInexigibilidade(id);
      });
    }
  }

  render() {
    const inexigibilidade = this.inexigibilidadeStore?.object;
    let content = <></>;
    if (this.inexigibilidadeStore.loading) {
      content = (
        <div className="p-d-inline p-d-flex align-items-center">
          <ProgressSpinner />
        </div>
      );
    } else if (inexigibilidade) {
      content = (
        <InexigibilidadeDetailPage
          inexigibilidadeStore={this.inexigibilidadeStore}
          idTermo={inexigibilidade.termoReferencia?.id}
        />
      );
    } else {
      content = <div>Erro ao exibir detalhes da Inexigibilidade.</div>;
    }
    return content;
  }
}

InexigibilidadeIndexDetailPage.propTypes = {
  id: PropTypes.number,
};

export default InexigibilidadeIndexDetailPage;
