import React from 'react';
import { observer } from 'mobx-react';
import InexigibilidadeIndexStore from '~/stores/inexigibilidade/indexStore';
import UrlRouter from '~/constants/UrlRouter';
import GenericIndexPage from 'fc/pages/GenericIndexPage';
import AdvancedSearch from 'fc/components/AdvancedSearch';
import {
  getValueDate,
  getValueMoney,
  checkUserGroup,
  hasPermissionProxy,
  generateFullURL,
  getValueByKey,
} from 'fc/utils/utils';
import { DATE_FORMAT, DATE_PARSE_FORMAT } from 'fc/utils/date';
import AccessPermission from '~/constants/AccessPermission';
import RequisicaoRemocao from '~/pages/requisicaoRemocao/requisicaoRemocaoModal';
import CardList from 'fc/components/CardList';
import PermissionProxy from 'fc/components/PermissionProxy';
import FcButton from 'fc/components/FcButton';
import { PrimeIcons } from 'primereact/api';
import { ConfirmDialog } from 'primereact/confirmdialog';
import AppStore from 'fc/stores/AppStore';
import AnulacaoRevogacaoModal from '../AnulacaoRevogacao/AnulacaoRevogacaoModal';
import DadosEstaticosService from '~/services/DadosEstaticosService';

@observer
class InexigibilidadeListagemPage extends GenericIndexPage {
  constructor(props) {
    super(props, AccessPermission.inexigibilidade);
    this.store = new InexigibilidadeIndexStore();

    this.state = {
      detalhesVisibility: false,
      showEntidadeDialog: false,
      selectedRow: null,
      idRemove: null,
      showRequisicaoRemocao: false,
      showAnularRevogar: false,
    };

    this._closeRequisicaoRemocaoModal = this._closeRequisicaoRemocaoModal.bind(this);
    this._closeAnularRevogarModal = this._closeAnularRevogarModal.bind(this);
    this._updateDatatable = this._updateDatatable.bind(this);
  }

  componentDidMount() {
    const { id } = this.props.match?.params || {};
    if (id) this.store.getById(id, (inexigibilidade) => this.props.onDetail(inexigibilidade));
  }

  _handleRemocaoRequisicaoModal(inexigibilidade) {
    this.setState({ showRequisicaoRemocao: true, selectedRow: inexigibilidade });
  }

  _handleAnularRevogarModal(dispensa) {
    this.setState({ showAnularRevogar: true, selectedRow: dispensa });
  }

  _closeRequisicaoRemocaoModal() {
    this.setState({ showRequisicaoRemocao: false, selectedRow: null });
  }

  _closeAnularRevogarModal() {
    this.setState({ showAnularRevogar: false, selectedRow: null });
  }

  _updateDatatable() {
    this.store.reloadTableData(() => this.forceUpdate());
  }

  _renderDialogRequisicaoRemocao() {
    return (
      <RequisicaoRemocao
        history={this.props.history}
        processo={this.state.selectedRow}
        visibleDialog={this.state.showRequisicaoRemocao}
        closeDialog={this._closeRequisicaoRemocaoModal}
        updateTable={this._updateDatatable}
        tipoProcesso="inexigibilidade"
      />
    );
  }

  _renderDialogAnularRevogar() {
    return (
      <AnulacaoRevogacaoModal
        showAnularRevogar={this.state.showAnularRevogar}
        closeDialog={this._closeAnularRevogarModal}
        selectedRow={this.state.selectedRow}
        getWritePermission={this.getWritePermission}
        tipoProcessoAssociado="INEXIGIBILIDADE"
        updateTable={this._updateDatatable}
      />
    );
  }

  openDialogEntidadeVisibility() {
    this.setState({
      showEntidadeDialog: !this.state.showEntidadeDialog,
    });
  }

  closeDialogEntidadeVisibility() {
    this.setState({
      showEntidadeDialog: !this.state.showEntidadeDialog,
    });
  }

  _renderDialogConfirmacaoEntidade() {
    const message = `O novo registro criado será associado à entidade selecionada: ${
      AppStore.getContextEntity().nome
    }. Deseja continuar?`;
    return (
      <ConfirmDialog
        blockScroll
        visible={this.state.showEntidadeDialog}
        message={message}
        header="Confirmação"
        icon="pi pi-user preview-item"
        acceptClassName="p-button-info"
        draggable={false}
        onHide={() => this.closeDialogEntidadeVisibility()}
        accept={() => {
          this.pushUrlToHistory(UrlRouter.cadastrosConsulta.inexigibilidade.novo);
        }}
      />
    );
  }

  getCardEllipsisOptions(cardData) {
    const isAuditor = checkUserGroup('Auditor');
    const items = [];

    if (
      (cardData.idRequisicaoModificacao || cardData.anulacaoRevogacao?.idRequisicaoModificacao) &&
      hasPermissionProxy(AccessPermission.requisicaoModificacao.writePermission)
    ) {
      const itemToAdd = {
        label: 'Requisição de modificação pendente',
        icon: 'pi pi-exclamation-triangle',
      };

      const idRequisicaoInexigibilidade =
        cardData.idRequisicaoModificacao ?? cardData.anulacaoRevogacao?.idRequisicaoModificacao;

      if (isAuditor) {
        itemToAdd.url = generateFullURL(
          UrlRouter.auditoria.requisicaoModificacao.indexDetail.replace(':id', idRequisicaoInexigibilidade)
        );
      } else {
        itemToAdd.url = generateFullURL(
          UrlRouter.administracao.requisicaoModificacao.indexDetail.replace(':id', idRequisicaoInexigibilidade)
        );
      }

      items.push(itemToAdd);
    } else {
      if (hasPermissionProxy(this.getReadPermission())) {
        items.push({
          label: 'Detalhes',
          icon: 'pi pi-eye',
          command: () => this.props.onDetail(cardData),
        });
      }

      if (hasPermissionProxy(this.getWritePermission())) {
        items.push({
          label: this.store.isRequisicaoModificacao(cardData.dataCadastro)
            ? 'Criar Requisição de Modificação'
            : 'Editar',
          icon: 'pi pi-pencil',
          url: this.store.isRequisicaoModificacao(cardData.dataCadastro)
            ? generateFullURL(
                UrlRouter.administracao.requisicaoModificacao.inexigibilidade.requisitar.replace(':id', cardData.id)
              )
            : generateFullURL(UrlRouter.cadastrosConsulta.inexigibilidade.editar.replace(':id', cardData.id)),
        });
        items.push({
          label: 'Remover',
          icon: 'pi pi-trash',
          command: () => this._handleRemocaoRequisicaoModal(cardData),
        });
      }
      if (hasPermissionProxy(this.getWritePermission())) {
        items.push({
          label: 'Anular/Revogar',
          icon: 'pi pi-times',
          command: () => this._handleAnularRevogarModal(cardData),
        });
      }
    }

    return items;
  }

  renderCardTitle(cardData) {
    const isJurisdicionado = checkUserGroup('Jurisdicionado');
    if (isJurisdicionado) {
      const numeroProcesso = cardData?.numeroProcesso;
      const ano = cardData?.anoInexigibilidade;
      if (numeroProcesso && numeroProcesso.includes('/')) {
        return `Inexigibilidade: ${numeroProcesso}`;
      } else {
        return `Inexigibilidade: ${numeroProcesso}/${ano}`;
      }
    } else {
      return cardData?.entidade?.nome ?? 'Não foi possível identificar nome da entidade';
    }
  }

  render() {
    const isJurisdicionado = checkUserGroup('Jurisdicionado');

    const fields = [
      {
        label: 'Número do Processo',
        field: 'numeroProcesso',
        value: 'title',
        sortable: true,
        body: this.renderCardTitle,
      },
      {
        label: 'Responsável pela Ratificacao',
        value: 'subtitle',
        field: 'responsavelRatificacao',
        sortable: true,
        body: (cardData) => {
          if (isJurisdicionado) {
            const fornecedores = cardData?.fornecedores ?? [];

            if (fornecedores.length >= 1) {
              return (
                `${fornecedores[0].licitante?.nome} - ${fornecedores[0].licitante?.cpfCnpj ?? ''}` ??
                'Não foi possível identificar o fornecedor'
              );
            }

            return 'Não foi possível identificar o fornecedor';
          }

          if (cardData?.numeroProcesso) {
            const numeroProcesso = cardData?.numeroProcesso;
            const ano = cardData?.anoInexigibilidade;
            if (numeroProcesso && numeroProcesso.includes('/')) {
              return `Inexigibilidade: ${numeroProcesso}`;
            } else {
              return `Inexigibilidade: ${numeroProcesso}/${ano}`;
            }
          } else {
            return 'Não foi possível identificar o número do processo';
          }
        },
      },
      {
        field: 'objeto',
        label: 'Objeto',
        value: 'mainContent',
        sortable: true,
      },
      {
        label: 'Data da Autorização',
        field: 'dataPedido',
        value: 'iconLabel',
        color: '#4da73b',
        icon: 'pi pi-calendar',
        sortable: true,
        body: (cardData) => {
          return getValueDate(cardData.dataPedido, DATE_FORMAT, DATE_PARSE_FORMAT);
        },
      },
      {
        field: 'valor',
        label: 'Valor',
        value: 'iconLabel',
        color: '#2F83DC',
        icon: 'pi pi-money-bill',
        body: (cardData) => getValueMoney(cardData.valor, cardData?.termoReferencia?.tresCasasDecimais ? 3 : 2),
        sortable: true,
      },
      {
        value: 'iconLabel',
        body: (cardData) => {
          if (isJurisdicionado) {
            return null;
          }

          if (cardData?.fornecedores) {
            const fornecedoresLabels = cardData.fornecedores.map((fornecedor) => ({
              value: fornecedor.licitante?.nome,
              toolTip: 'Fornecedor Contratado',
              icon: 'pi pi-user-plus',
              color: '#38AAAD',
            }));

            return fornecedoresLabels;
          }
        },
      },
      {
        field: 'anulacaoRevogacao',
        label: 'Anulado/Revogado',
        value: 'iconLabel',
        showIfExists: true,
        color: '#b83b20',
        icon: 'pi pi-money-bill',
        body: ({ anulacaoRevogacao }) =>
          getValueByKey(anulacaoRevogacao?.tipoOcorrencia, DadosEstaticosService.getValueAnuladoRevogado()),
        sortable: true,
      },
      {
        label: 'Ações',
        value: 'ellipsisItems',
        body: (cardData) => this.getCardEllipsisOptions(cardData),
      },
    ];

    const exportFields = [
      {
        field: isJurisdicionado ? 'numeroProcesso' : 'entidade.nome',
        header: isJurisdicionado ? 'Número do Processo' : 'Entidade',
      },
      {
        field: !isJurisdicionado && 'numeroProcesso',
        header: !isJurisdicionado && 'Número do Processo',
      },
      {
        field: 'objeto',
        header: 'Objeto',
      },
      {
        field: 'dataPedido',
        header: 'Data da Autorização',
        body: (cardData) => getValueDate(cardData.dataPedido, DATE_FORMAT, DATE_PARSE_FORMAT),
      },
      {
        field: 'valor',
        header: 'Valor',
      },
    ];

    const header = () => (
      <div className="table-header flex justify-content-start">
        <PermissionProxy resourcePermissions={this.getWritePermission()}>
          <FcButton
            className="p-button"
            label="Novo"
            style={{ marginBottom: '5px', marginRight: '5px' }}
            icon={PrimeIcons.PLUS}
            onClick={() => this.openDialogEntidadeVisibility()}
          />
        </PermissionProxy>

        {this.renderTableDataExport(exportFields, 'inexigibilidades', true)}
      </div>
    );

    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        <>
          <AdvancedSearch
            searchParams={this.store.getAdvancedSearchParams()}
            store={this.store}
            searchFields={['numeroProcesso', 'responsavelRatificacao', 'objeto']}
            filterSuggest={this.store.getFilterSuggest()}
            useOr
          />

          <CardList
            fields={fields}
            store={this.store}
            labelsLimit={4}
            header={header()}
            onTitleClick={(cardData) => {
              if (hasPermissionProxy(this.getReadPermission())) {
                return {
                  command: () => this.props.onDetail(cardData),
                };
              }
            }}
          />

          {this.store.isConfirmDialogVisible && this.confirmRemove(this.state.idRemove)}
          {this._renderDialogRequisicaoRemocao()}
          {this.state.showEntidadeDialog && this._renderDialogConfirmacaoEntidade()}
          {this.state.showAnularRevogar && this._renderDialogAnularRevogar()}
        </>
      </PermissionProxy>
    );
  }
}

export default InexigibilidadeListagemPage;
