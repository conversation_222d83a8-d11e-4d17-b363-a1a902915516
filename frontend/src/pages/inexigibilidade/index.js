import React from 'react';
import { observer } from 'mobx-react';
import AppBreadCrumb from 'fc/components/AppBreadCrumb';
import GenericIndexPage from 'fc/pages/GenericIndexPage';
import AccessPermission from '~/constants/AccessPermission';
import PermissionProxy from 'fc/components/PermissionProxy';
import InexigibilidadeListagemPage from './listagem';
import InexigibilidadeIndexDetailPage from './detalhes/indexDetail';
import FcCloseableTabView from 'fc/components/FcCloseableTabView';

@observer
class InexigibilidadeIndexPage extends GenericIndexPage {
  constructor(props) {
    super(props, AccessPermission.inexigibilidade);
    this.state = {
      data: [
        {
          id: 1,
          header: 'Listagem',
          closeable: false,
          content: (
            <InexigibilidadeListagemPage {...props} onDetail={(inexigibilidade) => this.onDetail(inexigibilidade)} />
          ),
        },
      ],
      count: 2,
      activeIndex: 0,
    };

    this.setActiveTabIndex = this.setActiveTabIndex.bind(this);
    this.handleCloseTab = this.handleCloseTab.bind(this);
    this.onDetail = this.onDetail.bind(this);
  }

  onDetail(inexigibilidade) {
    const existingTab = this.state.data.find((tab) => tab.idInexigibilidade === inexigibilidade.id);
    if (existingTab) {
      this.setActiveTabIndex(existingTab);
    } else {
      const newInexigibilidade = {
        id: inexigibilidade.id,
        idInexigibilidade: inexigibilidade.id,
        header: inexigibilidade?.numeroProcesso.includes('/')
          ? inexigibilidade?.numeroProcesso
          : `${inexigibilidade?.numeroProcesso}/${inexigibilidade?.anoInexigibilidade}`,
        closeable: true,
        content: <InexigibilidadeIndexDetailPage id={inexigibilidade.id} />,
      };
      this.setState({ data: [...this.state.data, newInexigibilidade], count: this.state.count + 1 });
    }
  }

  setActiveTabIndex(tab) {
    this.setState({ activeTabIndex: this.state.data.indexOf(tab) });
  }

  handleCloseTab(tab) {
    this.setState((prevState) => {
      return { data: prevState.data.filter((t) => t.id !== tab.id) };
    });
  }

  render() {
    const breacrumbItems = [{ label: 'Inexigibilidades' }];
    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        <AppBreadCrumb items={breacrumbItems} />
        <div className="card page index-table">
          <FcCloseableTabView
            tabs={this.state.data}
            activeTabIndex={this.state.activeTabIndex}
            onChangeTab={this.setActiveTabIndex}
            onClose={this.handleCloseTab}
            fixedFirstTab
          />
        </div>
      </PermissionProxy>
    );
  }
}

InexigibilidadeIndexPage.displayName = 'InexigibilidadeIndexPage';

export default InexigibilidadeIndexPage;
