import React from 'react';
import { observer } from 'mobx-react';
import PropTypes from 'prop-types';
import AppStore from 'fc/stores/AppStore';
import TdaFormStore from '~/stores/tda/formStore';
import UrlRouter from '~/constants/UrlRouter';
import GenericFormPage from 'fc/pages/GenericFormPage';
import FormField from 'fc/components/FormField';
import PermissionProxy from 'fc/components/PermissionProxy';
import AccessPermission from '~/constants/AccessPermission';
import { InputText } from 'primereact/inputtext';
import moment from 'moment';
import FcInputTextarea from 'fc/components/FcInputTextarea';
import AsyncDropdown from 'fc/components/AsyncDropdown';
import { DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS } from 'fc/utils/date';
import { getValueDate } from 'fc/utils/utils';
import { DATE_FORMAT, DATE_PARSE_FORMAT_INVERSE } from 'fc/utils/date';
import FcButton from 'fc/components/FcButton';
import FcCalendar from 'fc/components/FcCalendar';

@observer
class TdaFormPage extends GenericFormPage {
  constructor(props) {
    super(props, UrlRouter.auditoria.analiseProcesso.index, AccessPermission.cadastrarTda);
    this.store = new TdaFormStore(this.props.tipoProcesso);
  }

  componentDidMount() {
    const { id, idProcesso, tipoProcesso } = this.props;
    const userDetails = AppStore.getData('userDetails');

    this.store.initialize(
      id,
      { dataTda: moment(), setor: 'Grupo de Trabalho LICON' },
      () => {
        this.store.setIdProcesso(idProcesso, tipoProcesso);
        this.props.action == 'new' && this.store.setTda();
      },
      userDetails
    );
  }

  render() {
    const { submitted } = this.state;
    const { getRule, updateAttribute, updateAttributeDate } = this.store;
    const { submitFormData, getDateAttributeValue } = this;

    let content;
    if (this.store.object) {
      content = (
        <>
          <div className="card form-action-buttons">
            <form onSubmit={submitFormData}>
              <div className="p-fluid p-formgrid p-grid">
                <FormField columns={6} attribute="tda" label="TDA" rule={getRule('tda')} submitted={submitted}>
                  <InputText
                    disabled
                    placeholder={this.store.loadingTda ? 'TDA sendo carregado' : 'Falha ao obter TDA'}
                    value={
                      String(this.store.object.tda).split('/')[0] +
                      '/' +
                      getValueDate(
                        getDateAttributeValue(this.store.object.dataTda),
                        DATE_PARSE_FORMAT_INVERSE,
                        DATE_FORMAT
                      )
                    }
                  />
                </FormField>
                <FormField
                  columns={6}
                  attribute="analista"
                  label="Atribuidor"
                  rule={getRule('analista')}
                  submitted={submitted}
                >
                  <AsyncDropdown
                    onChange={updateAttribute}
                    value={this.store.object.analista?.id ?? undefined}
                    placeholder="Selecione o atribuidor"
                    store={this.store.atribuidorStore}
                    defaultValue={this.store.object.analista}
                  />
                </FormField>
                <FormField
                  columns={6}
                  attribute="responsavel"
                  label="Responsável"
                  rule={getRule('responsavel')}
                  submitted={submitted}
                >
                  <AsyncDropdown
                    onChange={updateAttribute}
                    value={this.store.object.responsavel?.id ?? undefined}
                    placeholder="Selecione o responsável"
                    store={this.store.auditorStore}
                    defaultValue={this.store.object.responsavel}
                  />
                </FormField>
                <FormField
                  columns={3}
                  attribute="dataInicio"
                  label="Data Inicial"
                  rule={getRule('dataInicio')}
                  submitted={submitted}
                >
                  <FcCalendar
                    value={getDateAttributeValue(this.store.object.dataInicio)}
                    onChange={(e) => updateAttributeDate('dataInicio', e)}
                    id="dataInicio"
                    showIcon
                    mask="99/99/9999"
                  />
                </FormField>
                <FormField
                  columns={3}
                  attribute="dataFinal"
                  label="Data Final"
                  rule={getRule('dataFinal')}
                  submitted={submitted}
                >
                  <FcCalendar
                    value={getDateAttributeValue(this.store.object.dataFinal)}
                    onChange={(e) => updateAttributeDate('dataFinal', e)}
                    id="dataFinal"
                    showIcon
                    mask="99/99/9999"
                  />
                </FormField>
                <FormField
                  columns={3}
                  attribute="Suspensão"
                  rule={getRule('dataSuspensao')}
                  label="Suspensão"
                  submitted={submitted}
                >
                  <FcCalendar
                    value={getDateAttributeValue(this.store.object.dataSuspensao)}
                    onChange={(e) => updateAttributeDate('dataSuspensao', e)}
                    id="dataSuspensao"
                    showIcon
                    mask="99/99/9999"
                  />
                </FormField>
                <FormField
                  columns={3}
                  attribute="dataProrrogacao"
                  label="Prorrogação"
                  rule={getRule('dataProrrogacao')}
                  submitted={submitted}
                >
                  <FcCalendar
                    value={getDateAttributeValue(this.store.object.dataProrrogacao)}
                    onChange={(e) => updateAttributeDate('dataProrrogacao', e)}
                    id="dataProrrogacao"
                    showIcon
                    mask="99/99/9999"
                  />
                </FormField>
                <FormField
                  columns={12}
                  attribute="observacao"
                  label="Observação"
                  rule={getRule('observacao')}
                  submitted={submitted}
                >
                  <FcInputTextarea
                    rows={5}
                    cols={30}
                    onChange={(e) => this.store.updateAttribute('observacao', e)}
                    placeholder="Informe as observações"
                    value={this.store.object.observacao}
                    id="observacao"
                  />
                </FormField>
                <div className="p-col-12 p-d-inline-flex">
                  <b style={{ paddingRight: '5px' }}>Alterado / Editado em:</b>
                  <div>
                    {this.store.object.usuarioAlteracao
                      ? `${this.store.object.usuarioAlteracao} no dia ${getValueDate(
                          this.store.object.dataAlteracao,
                          DATE_FORMAT_WITH_HOURS,
                          DATE_PARSE_FORMAT_WITH_HOURS
                        )}`
                      : '-'}
                  </div>
                </div>
              </div>
              {this.renderActionButtons()}
            </form>
          </div>
        </>
      );
    } else {
      content = (
        <div className="card page">
          <i
            className="pi pi-spin pi-spinner"
            style={{
              marginTop: '20px',
              marginLeft: 'calc(50% - 20px)',
              fontSize: '2em',
            }}
          ></i>
        </div>
      );
    }
    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        {content}
      </PermissionProxy>
    );
  }

  renderActionButtons() {
    const hasWritePermission = !this.getWritePermission() || AppStore.hasPermission(this.getWritePermission());
    return (
      <div className="p-mt-10 form-actions">
        <div className="p-mt-2">
          <span className="p-mr-1 p-d-inline p-d-flex align-items-center">
            <FcButton
              label="Voltar"
              type="button"
              className="p-ml-auto p-button-secondary p-mr-2"
              onClick={() => this._goBack()}
              loading={this.store.loading}
            />
            {hasWritePermission && <FcButton label="Salvar" type="submit" loading={this.store.loading} />}
          </span>
        </div>
      </div>
    );
  }

  _goBack() {
    if (this.props.editMode) {
      this.props.disableEditMode();
      this.props.reload && this.props.reload(this.props.idProcesso, this.props.tipoProcesso, this.props.idTab);
    } else {
      this.props.history.push(this.goBackUrl);
    }
  }
}

TdaFormPage.propTypes = {
  id: PropTypes.any,
  action: PropTypes.any,
  history: PropTypes.any,
  index: PropTypes.any,
  disableEditMode: PropTypes.func,
  reload: PropTypes.func,
};

export default TdaFormPage;
