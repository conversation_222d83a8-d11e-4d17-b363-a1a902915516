import React from 'react';
import PropTypes from 'prop-types';
import { observer } from 'mobx-react';
import DispensaFormStore from '~/stores/dispensa/formStore';
import DispensaDetailPage from './detail';
import { ProgressSpinner } from 'primereact/progressspinner';

@observer
class DispensaIndexDetailPage extends React.Component {
  constructor(props) {
    super(props);
    this.dispensaStore = new DispensaFormStore();
  }

  componentDidMount() {
    const { id } = this.props;

    if (id) {
      this.dispensaStore.initialize(id, {}, () => {
        this.dispensaStore.initializeVencedorStore();
        this.dispensaStore.initializeArquivos(id);
        this.dispensaStore.carregaUltimaAlteracao(id);
        this.dispensaStore.initializeTdaDispensa(id);
      });
    }
  }

  render() {
    const dispensa = this.dispensaStore?.object;
    let content = <></>;
    if (this.dispensaStore.loading) {
      content = (
        <div className="p-d-inline p-d-flex align-items-center">
          <ProgressSpinner />
        </div>
      );
    } else if (dispensa) {
      content = <DispensaDetailPage dispensaStore={this.dispensaStore} idTermo={dispensa.termoReferencia?.id} />;
    } else {
      content = <div>Erro ao exibir detalhes da Dispensa.</div>;
    }

    return content;
  }
}

DispensaIndexDetailPage.propTypes = {
  id: PropTypes.number,
};

export default DispensaIndexDetailPage;
