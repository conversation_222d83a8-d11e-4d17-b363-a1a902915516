import React from 'react';
import { observer } from 'mobx-react';
import AppBreadCrumb from 'fc/components/AppBreadCrumb';
import GenericIndexPage from 'fc/pages/GenericIndexPage';
import AccessPermission from '~/constants/AccessPermission';
import PermissionProxy from 'fc/components/PermissionProxy';
import DispensaListagemPage from './listagem';
import DispensaIndexDetailPage from './detalhes/indexDetail';
import FcCloseableTabView from 'fc/components/FcCloseableTabView';

@observer
class DispensaIndexPage extends GenericIndexPage {
  constructor(props) {
    super(props, AccessPermission.dispensa);

    this.state = {
      data: [
        {
          id: 1,
          header: 'Listagem',
          closeable: false,
          content: <DispensaListagemPage {...props} onDetail={(dispensa) => this.onDetail(dispensa)} />,
        },
      ],
      count: 2,
      activeTabIndex: 0,
    };

    this.setActiveTabIndex = this.setActiveTabIndex.bind(this);
    this.handleCloseTab = this.handleCloseTab.bind(this);
    this.onDetail = this.onDetail.bind(this);
  }

  onDetail(dispensa) {
    const existingTab = this.state.data.find((tab) => tab.idDispensa === dispensa.id);
    if (existingTab) {
      this.setActiveTabIndex(existingTab);
    } else {
      const newDispensa = {
        id: dispensa.id,
        idDispensa: dispensa.id,
        header: dispensa?.numeroProcesso.includes('/')
          ? dispensa?.numeroProcesso
          : `${dispensa?.numeroProcesso}/${dispensa?.anoDispensa}`,
        closeable: true,
        content: <DispensaIndexDetailPage id={dispensa.id} />,
      };
      this.setState({ data: [...this.state.data, newDispensa], count: this.state.count + 1 });
    }
  }

  setActiveTabIndex(tab) {
    this.setState({ activeTabIndex: this.state.data.indexOf(tab) });
  }

  handleCloseTab(tab) {
    this.setState((prevState) => {
      return { data: prevState.data.filter((t) => t.id !== tab.id) };
    });
  }

  render() {
    const breacrumbItems = [{ label: 'Dispensa' }];
    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        <AppBreadCrumb items={breacrumbItems} />
        <div className="card page index-table">
          <FcCloseableTabView
            tabs={this.state.data}
            activeTabIndex={this.state.activeTabIndex}
            onChangeTab={this.setActiveTabIndex}
            onClose={this.handleCloseTab}
            fixedFirstTab
          />
        </div>
      </PermissionProxy>
    );
  }
}

DispensaIndexPage.displayName = 'DispensaIndexPage';

export default DispensaIndexPage;
