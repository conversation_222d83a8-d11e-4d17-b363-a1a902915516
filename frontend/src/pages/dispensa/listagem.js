import React from 'react';
import { observer } from 'mobx-react';
import DispensaIndexStore from '~/stores/dispensa/indexStore';
import UrlRouter from '~/constants/UrlRouter';
import GenericIndexPage from 'fc/pages/GenericIndexPage';
import AdvancedSearch from 'fc/components/AdvancedSearch';
import {
  checkUserGroup,
  generateFullURL,
  getValueByKey,
  getValueDate,
  getValueMoney,
  hasPermissionProxy,
} from 'fc/utils/utils';
import { DATE_FORMAT, DATE_PARSE_FORMAT } from 'fc/utils/date';
import AccessPermission from '~/constants/AccessPermission';
import PermissionProxy from 'fc/components/PermissionProxy';
import RequisicaoRemocao from '~/pages/requisicaoRemocao/requisicaoRemocaoModal';
import CardList from 'fc/components/CardList';
import FcButton from 'fc/components/FcButton';
import { PrimeIcons } from 'primereact/api';
import { ConfirmDialog } from 'primereact/confirmdialog';
import AppStore from 'fc/stores/AppStore';
import AnulacaoRevogacaoModal from '../AnulacaoRevogacao/AnulacaoRevogacaoModal';
import DadosEstaticosService from '~/services/DadosEstaticosService';

@observer
class DispensaListagemPage extends GenericIndexPage {
  constructor(props) {
    super(props, AccessPermission.dispensa);
    this.store = new DispensaIndexStore();

    this.state = {
      idRemove: null,
      selectedRow: null,
      showRequisicaoRemocao: false,
      showEntidadeDialog: false,
      showAnularRevogar: false,
    };

    this._closeRequisicaoRemocaoModal = this._closeRequisicaoRemocaoModal.bind(this);
    this._closeAnularRevogarModal = this._closeAnularRevogarModal.bind(this);
    this._updateDatatable = this._updateDatatable.bind(this);
    this.renderCardTitle = this.renderCardTitle.bind(this);
  }

  componentDidMount() {
    const { id } = this.props.match?.params || {};
    if (id) this.store.getById(id, (dispensa) => this.props.onDetail(dispensa));
  }

  _handleRemocaoRequisicaoModal(dispensa) {
    this.setState({ showRequisicaoRemocao: true, selectedRow: dispensa });
  }

  _handleAnularRevogarModal(dispensa) {
    this.setState({ showAnularRevogar: true, selectedRow: dispensa });
  }

  _closeRequisicaoRemocaoModal() {
    this.setState({ showRequisicaoRemocao: false, selectedRow: null });
  }

  _closeAnularRevogarModal() {
    this.setState({ showAnularRevogar: false, selectedRow: null });
  }

  _updateDatatable() {
    this.store.reloadTableData(() => this.forceUpdate());
  }

  _renderDialogRequisicaoRemocao() {
    return (
      <RequisicaoRemocao
        history={this.props.history}
        processo={this.state.selectedRow}
        visibleDialog={this.state.showRequisicaoRemocao}
        closeDialog={this._closeRequisicaoRemocaoModal}
        updateTable={this._updateDatatable}
        tipoProcesso="dispensa"
      />
    );
  }

  _renderDialogAnularRevogar() {
    return (
      <AnulacaoRevogacaoModal
        showAnularRevogar={this.state.showAnularRevogar}
        closeDialog={this._closeAnularRevogarModal}
        selectedRow={this.state.selectedRow}
        getWritePermission={this.getWritePermission}
        tipoProcessoAssociado="DISPENSA"
        updateTable={this._updateDatatable}
      />
    );
  }

  openDialogEntidadeVisibility() {
    this.setState({
      showEntidadeDialog: !this.state.showEntidadeDialog,
    });
  }

  closeDialogEntidadeVisibility() {
    this.setState({
      showEntidadeDialog: !this.state.showEntidadeDialog,
    });
  }

  _renderDialogConfirmacaoEntidade() {
    const message = `O novo registro criado será associado à entidade selecionada: ${
      AppStore.getContextEntity().nome
    }. Deseja continuar?`;
    return (
      <ConfirmDialog
        blockScroll
        visible={this.state.showEntidadeDialog}
        message={message}
        header="Confirmação"
        icon="pi pi-user preview-item"
        acceptClassName="p-button-info"
        draggable={false}
        onHide={() => this.closeDialogEntidadeVisibility()}
        accept={() => {
          this.pushUrlToHistory(UrlRouter.cadastrosConsulta.dispensa.novo);
        }}
      />
    );
  }

  getCardEllipsisOptions(cardData) {
    const isAuditor = checkUserGroup('Auditor');
    const items = [];

    if (
      (cardData.idRequisicaoModificacao || cardData.anulacaoRevogacao?.idRequisicaoModificacao) &&
      hasPermissionProxy(AccessPermission.requisicaoModificacao.writePermission)
    ) {
      const itemToAdd = {
        label: 'Requisição de modificação pendente',
        icon: 'pi pi-exclamation-triangle',
      };

      const idRequisicaoDispensa =
        cardData.idRequisicaoModificacao ?? cardData.anulacaoRevogacao?.idRequisicaoModificacao;

      if (isAuditor) {
        itemToAdd.url = generateFullURL(
          UrlRouter.auditoria.requisicaoModificacao.indexDetail.replace(':id', idRequisicaoDispensa)
        );
      } else {
        itemToAdd.url = generateFullURL(
          UrlRouter.administracao.requisicaoModificacao.indexDetail.replace(':id', idRequisicaoDispensa)
        );
      }

      items.push(itemToAdd);
    } else {
      if (hasPermissionProxy(this.getReadPermission())) {
        items.push({
          label: 'Detalhes',
          icon: 'pi pi-eye',
          command: () => this.props.onDetail(cardData),
        });
      }

      if (hasPermissionProxy(this.getWritePermission())) {
        items.push({
          label: this.store.isRequisicaoModificacao(cardData.dataCadastro)
            ? 'Criar Requisição de Modificação'
            : 'Editar',
          icon: 'pi pi-pencil',
          url: this.store.isRequisicaoModificacao(cardData.dataCadastro)
            ? generateFullURL(
                UrlRouter.administracao.requisicaoModificacao.dispensa.requisitar.replace(':id', cardData.id)
              )
            : generateFullURL(UrlRouter.cadastrosConsulta.dispensa.editar.replace(':id', cardData.id)),
        });
      }

      if (hasPermissionProxy(this.getWritePermission())) {
        items.push({
          label: 'Remover',
          icon: 'pi pi-trash',
          command: () => this._handleRemocaoRequisicaoModal(cardData),
        });
      }

      if (hasPermissionProxy(this.getWritePermission())) {
        items.push({
          label: 'Anular/Revogar',
          icon: 'pi pi-times',
          command: () => this._handleAnularRevogarModal(cardData),
        });
      }
    }

    return items;
  }

  _getDispensaTitle(cardData) {
    const numeroProcesso = cardData?.numeroProcesso;
    const ano = cardData?.anoDispensa;
    return numeroProcesso && numeroProcesso.includes('/') ? numeroProcesso : `${numeroProcesso}/${ano}`;
  }

  renderCardTitle(cardData) {
    const isJurisdicionado = checkUserGroup('Jurisdicionado');
    return isJurisdicionado
      ? `Dispensa: ${this._getDispensaTitle(cardData)}`
      : cardData?.entidade?.nome ?? 'Não foi possível identificar o nome da entidade';
  }

  render() {
    const isJurisdicionado = checkUserGroup('Jurisdicionado');

    const fields = [
      {
        label: 'Número do Processo',
        field: 'numeroProcesso',
        value: 'title',
        sortable: true,
        body: this.renderCardTitle,
      },
      {
        label: 'Responsável',
        value: 'subtitle',
        field: 'gestor',
        sortable: true,
        body: (cardData) => {
          if (isJurisdicionado) {
            const fornecedores = cardData?.fornecedores ?? [];

            if (fornecedores.length >= 1) {
              return (
                `${fornecedores[0].licitante?.nome} - ${fornecedores[0].licitante?.cpfCnpj ?? ''}` ??
                'Não foi possível identificar fornecedor'
              );
            }

            return 'Não foi possível identificar fornecedor';
          }

          return `Dispensa: ${this._getDispensaTitle(cardData)}`;
        },
      },
      {
        field: 'objeto',
        label: 'Objeto',
        value: 'mainContent',
        sortable: true,
      },
      {
        field: 'dataPedido',
        label: 'Data da Autorização',
        value: 'iconLabel',
        color: '#4da73b',
        icon: 'pi pi-calendar',
        sortable: true,
        body: (cardData) => {
          return getValueDate(cardData.dataPedido, DATE_FORMAT, DATE_PARSE_FORMAT);
        },
      },
      {
        field: 'numeroProcesso',
        label: 'Número do Processo Administrativo',
        value: 'iconLabel',
        color: '#38AAAD',
        icon: 'pi pi-tag',
        sortable: false,
        body: (cardData) => isJurisdicionado && (cardData?.numeroProcessoSEI ?? null),
      },
      {
        field: 'valor',
        label: 'Valor',
        value: 'iconLabel',
        color: '#2F83DC',
        icon: 'pi pi-money-bill',
        body: (cardData) => getValueMoney(cardData.valor, cardData?.termoReferencia?.tresCasasDecimais ? 3 : 2),
        sortable: true,
      },
      {
        value: 'iconLabel',
        body: (cardData) => {
          if (isJurisdicionado) {
            return null;
          }

          if (cardData?.fornecedores) {
            const fornecedoresLabels = cardData.fornecedores.map((fornecedor) => ({
              value: fornecedor.licitante?.nome,
              toolTip: 'Fornecedor Contratado',
              icon: 'pi pi-user-plus',
              color: '#38AAAD',
            }));

            return fornecedoresLabels;
          }
        },
      },
      {
        field: 'anulacaoRevogacao',
        label: 'Anulado/Revogado',
        value: 'iconLabel',
        showIfExists: true,
        color: '#b83b20',
        icon: 'pi pi-money-bill',
        body: ({ anulacaoRevogacao }) =>
          getValueByKey(anulacaoRevogacao?.tipoOcorrencia, DadosEstaticosService.getValueAnuladoRevogado()),
        sortable: true,
      },
      {
        label: 'Ações',
        value: 'ellipsisItems',
        body: (cardData) => this.getCardEllipsisOptions(cardData),
      },
    ];

    const exportFields = [
      {
        field: isJurisdicionado ? 'numeroProcesso' : 'entidade.nome',
        header: isJurisdicionado ? 'Número do Processo' : 'Entidade',
      },
      {
        field: !isJurisdicionado && 'numeroProcesso',
        header: !isJurisdicionado && 'Número do Processo',
      },
      {
        field: 'objeto',
        header: 'Objeto',
      },
      {
        field: 'dataPedido',
        header: 'Data da Autorização',
        body: (cardData) => getValueDate(cardData.dataPedido, DATE_FORMAT, DATE_PARSE_FORMAT),
      },
      {
        field: 'valor',
        header: 'Valor',
      },
    ];

    const header = () => (
      <div className="table-header flex justify-content-start">
        <PermissionProxy resourcePermissions={this.getWritePermission()}>
          <FcButton
            className="p-button"
            label="Novo"
            style={{ marginBottom: '5px', marginRight: '5px' }}
            icon={PrimeIcons.PLUS}
            onClick={() => this.openDialogEntidadeVisibility()}
          />
        </PermissionProxy>

        {this.renderTableDataExport(exportFields, 'dispensas', true)}
      </div>
    );

    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        <>
          <AdvancedSearch
            searchParams={this.store.getAdvancedSearchParams()}
            store={this.store}
            searchFields={['numeroProcesso', 'gestor', 'objeto']}
            filterSuggest={this.store.getFilterSuggest()}
            useOr
          />

          <CardList
            fields={fields}
            store={this.store}
            header={header()}
            labelsLimit={4}
            onTitleClick={(cardData) => {
              if (hasPermissionProxy(this.getReadPermission())) {
                return {
                  command: () => this.props.onDetail(cardData),
                };
              }
            }}
          />

          {this.store.isConfirmDialogVisible && this.confirmRemove(this.state.idRemove)}
          {this._renderDialogRequisicaoRemocao()}
          {this.state.showEntidadeDialog && this._renderDialogConfirmacaoEntidade()}
          {this.state.showAnularRevogar && this._renderDialogAnularRevogar()}
        </>
      </PermissionProxy>
    );
  }
}

export default DispensaListagemPage;
