import React from 'react';
import { observer } from 'mobx-react';
import PropTypes from 'prop-types';
import DispensaFormStore from '~/stores/dispensa/formStore';
import { InputText } from 'primereact/inputtext';
import UrlRouter from '~/constants/UrlRouter';
import GenericFormPage from 'fc/pages/GenericFormPage';
import FormField from 'fc/components/FormField';
import AppBreadCrumb from 'fc/components/AppBreadCrumb';
import AsyncDropdown from 'fc/components/AsyncDropdown';
import { isValueValid, showNotification } from 'fc/utils/utils';
import AccessPermission from '~/constants/AccessPermission';
import PermissionProxy from 'fc/components/PermissionProxy';
import InputMonetary from 'fc/components/InputMonetary';
import FormDadosBasicos from './tabs/FormDadosBasicos';
import { Steps } from 'primereact/steps';
import FcButton from 'fc/components/FcButton';
import FormFornecedoresItens from './tabs/FormFornecedoresItens';
import AppStore from 'fc/stores/AppStore';
import DispensaSummaryTab from './tabs/FormResumo';
import { Message } from 'primereact/message';
import classNames from 'classnames';
import FcInputTextarea from 'fc/components/FcInputTextarea';
import { Dialog } from 'primereact/dialog';
import RequisicaoModificacaoDispensaFormStore from '~/stores/dispensa/requisicaoModificacao/formStore';

@observer
class DispensaFormPage extends GenericFormPage {
  constructor(props) {
    super(props, UrlRouter.cadastrosConsulta.dispensa.index, AccessPermission.dispensa);

    this.store = new DispensaFormStore();
    this.reqModificacaoStore = new RequisicaoModificacaoDispensaFormStore();
    this.state = { activeIndexForm: 0, showDialogReqMod: false, errorDialogValue: false, visibleDialogObras: false };
    this.renderDialogRequisicaoModificacao = this.renderDialogRequisicaoModificacao.bind(this);
    this._toggleDialogReqMod = this._toggleDialogReqMod.bind(this);
  }

  componentDidMount() {
    const { id } = this.props;
    const initialize = () => {
      this.store.initialize(
        id,
        { fornecedores: [], entidade: AppStore.getContextEntity(), lei: 'LEI_N_14133', tipoAdjudicacao: 'ITEM' },
        () => {
          if (id) {
            this.store.checkDataCadastro();
          }
          this.store.object?.obra && this.store.carregarEdificacaoObra();
          this.forceUpdate();
        }
      );
    };
    if (id) {
      this.store.initializeArquivos(id, initialize);
    } else {
      initialize();
    }
  }

  _toggleDialogReqMod() {
    this.setState((oldState) => ({ showDialogReqMod: !oldState.showDialogReqMod }));
  }

  renderDialogReqModFooter() {
    return (
      <div>
        <FcButton
          label="Cancelar"
          icon="pi pi-times"
          onClick={() => this._toggleDialogReqMod()}
          className="p-button-text"
        />
        <FcButton
          label="Enviar Requisição"
          icon="pi pi-check"
          onClick={() => {
            if (this.reqModificacaoStore.justificativaJurisdicionado) {
              this.submitFormData();
              this._toggleDialogReqMod();
            } else {
              !this.state.errorDialogValue && this.setState({ errorDialogValue: true });
            }
          }}
        />
      </div>
    );
  }

  _getFieldErrorMessage(rendered, message) {
    if (rendered) {
      return <small className="p-error">{message}</small>;
    }
    return null;
  }

  renderDialogRequisicaoModificacao() {
    const { getRule } = this.store;
    return (
      <Dialog
        header="Justificativa Requisição"
        visible={this.state.showDialogReqMod}
        style={{ width: '50vw' }}
        footer={this.renderDialogReqModFooter()}
        onHide={() => this._toggleDialogReqMod()}
      >
        <div className="p-fluid p-formgrid p-grid">
          <FormField columns={12} attribute="justificativa" rule={getRule('justificativa')} label="Justificativa">
            <FcInputTextarea
              className={this.state.errorDialogValue ? 'p-invalid p-error' : ''}
              value={this.reqModificacaoStore.justificativaJurisdicionado}
              onChange={(e) => {
                this.reqModificacaoStore.updateAttribute('jurisdicionado', e);
                isValueValid(this.reqModificacaoStore.justificativaJurisdicionado?.length) &&
                  this.setState({ errorDialogValue: false });
              }}
              placeholder="Descreva a justificativa"
            />
          </FormField>
          <div className="p-col-6">
            {this._getFieldErrorMessage(this.state.errorDialogValue, 'Por favor, preencha o campo')}
          </div>
        </div>
      </Dialog>
    );
  }

  submitFormData(e) {
    e && e.preventDefault();
    const execution = () => {
      if (!this.store.rules.hasError) {
        const dispensa = this.store.object;
        const dispensaDTO = {
          id: dispensa.id,
          dispensa: { ...dispensa },
          obra: this.store.obraObject && Object.keys(this.store.obraObject).length > 0 ? this.store.obraObject : null,
          edificacao: this.store.edificacao,
          arquivosDispensa: this.store.arquivoDispensaList,
        };
        if (!this.store.enableReqMod) {
          this.store.criarDispensa(dispensaDTO, this._goBack);
        } else {
          this.store.enableReqMod &&
            this.reqModificacaoStore.justificativaJurisdicionado &&
            this.reqModificacaoStore.enviarRequisicaoDispensa(dispensaDTO, this._goBack);
        }
      } else {
        showNotification('error', null, 'Verifique os campos do formulário!');
      }
    };

    if (this.state.submitted) {
      execution();
    } else {
      this.setState({ submitted: true }, execution);
    }
  }

  isDisabledAvancar() {
    const { activeIndexForm } = this.state;

    const errorsMessages = {
      message: '',
      disabled: true,
    };

    if (activeIndexForm == 0 && !this.store.validaDadosBasicos()) {
      errorsMessages.message =
        'É necessário preencher todos os campos obrigatórios antes de avançar para a próxima etapa.';
    } else if (activeIndexForm > 0 && !this.store.object?.fornecedores?.length) {
      errorsMessages.message = 'É necessário adicionar ao menos um fornecedor antes de avançar para a próxima etapa.';
    } else if (
      activeIndexForm > 0 &&
      this.store.isLegislacaoAntiga() &&
      this.store.vencedoresStore.hasLicitantesNotAssign
    ) {
      errorsMessages.message = 'É necessário adicionar todos os fornecedores.';
    } else if (
      activeIndexForm > 0 &&
      !this.store.isLegislacaoAntiga() &&
      this.store.vencedoresStore.hasItensNotFilleds
    ) {
      errorsMessages.message = 'Itens vencidos precisam ser preenchidos.';
    } else if (this.store.object?.fornecedores?.some((i) => i.itemLote?.valorUnitarioEstimado < i.valorUnitario)) {
      errorsMessages.message = 'O valor unitário dos itens não deve ser superior ao valor adjudicado.';
    } else if (
      activeIndexForm > 0 &&
      !this.store.isLegislacaoAntiga() &&
      this.store.vencedoresStore.hasQuantityNotAssign
    ) {
      errorsMessages.message = 'Alguns itens possuem quantidades remanescentes não vencidas.';
    } else if (
      activeIndexForm > 0 &&
      !this.store.isLegislacaoAntiga() &&
      this.store.vencedoresStore.hasLicitantesNotAssign
    ) {
      errorsMessages.message = 'Há fornecedores que não possuem nenhum item/lote.';
    } else {
      errorsMessages.disabled = false;
    }
    return errorsMessages;
  }

  onChangeStep(step, action = 'avancar') {
    if (step < 0) {
      this.store.toggleShowConfirmDialog();
    } else {
      if (step < this.state.activeIndexForm) {
        this.setState({ activeIndexForm: step });
      } else {
        if (
          action !== 'avancar' ||
          (this.store.arquivoDispensaList.length > 0 &&
            this.store.validateSubmittedFiles(this.store.arquivoDispensaList) &&
            !this.isDisabledAvancar().disabled)
        ) {
          this.setState({ activeIndexForm: step });
        }
      }
    }
  }

  validarForm(callback) {
    if (this.store.validateSubmittedFiles(this.store.arquivoDispensaList)) {
      if (this.state.activeIndexForm === 0) {
        this.store.validaArquivos(callback);
      } else {
        callback && callback();
      }
    }
  }

  renderStepButtons() {
    const hasWritePermission = !this.getWritePermission() || AppStore.hasPermission(this.getWritePermission());
    const disabledAvancar = this.isDisabledAvancar();
    return (
      <div className="p-mt-10 form-actions">
        <div className="p-mt-2">
          <span className="p-mr-1 p-d-inline p-d-flex align-items-center justify-content-end">
            {disabledAvancar.disabled && (
              <Message className="p-ml-auto p-mr-2" severity="warn" text={disabledAvancar.message} />
            )}
            <FcButton
              label="Voltar"
              type="button"
              className={classNames('p-button-secondary p-mr-2', { 'p-ml-auto': !disabledAvancar.disabled })}
              onClick={() => this.onChangeStep(this.state.activeIndexForm - 1, 'voltar')}
              loading={this.store.loading}
            />
            {this.state.activeIndexForm < 2 && (
              <FcButton
                label="Avançar"
                type="button"
                disabled={disabledAvancar.disabled}
                onClick={() => this.validarForm(() => this.onChangeStep(this.state.activeIndexForm + 1))}
              />
            )}
            {hasWritePermission && this.state.activeIndexForm === 2 && !this.store.enableReqMod && (
              <FcButton label="Salvar" type="submit" loading={this.store.loading} />
            )}
            {hasWritePermission && this.state.activeIndexForm === 2 && this.store.enableReqMod && (
              <FcButton
                label="Enviar Requisição"
                onClick={() => this._toggleDialogReqMod()}
                loading={this.store.loading}
                type="button"
              />
            )}
          </span>
        </div>
      </div>
    );
  }

  renderValorFornecedor() {
    const { submitted } = this.state;
    const { updateAttribute, getRule } = this.store;

    return (
      <FormField columns={4} attribute="valor" label="Valor Total" rule={getRule('valor')} submitted={submitted}>
        <InputMonetary
          onChange={(e) => updateAttribute('valor', e)}
          placeholder="R$"
          value={this.store.object.valor}
          decimalPlaces={this.store?.object?.termoReferencia?.tresCasasDecimais ? 3 : 2}
        />
      </FormField>
    );
  }

  renderCamposFundamentacao() {
    const { submitted } = this.state;
    const { updateAttribute, getRule } = this.store;

    const fundDropdown = (
      <FormField
        columns={6}
        attribute="fundamentacaoLegalEntidade"
        label="Fundamentação Legal"
        rule={getRule('fundamentacaoLegalEntidade')}
        submitted={submitted}
      >
        <AsyncDropdown
          onChange={updateAttribute}
          value={this.store.object.fundamentacaoLegalEntidade?.id ?? undefined}
          placeholder="Selecione a fundamentação legal"
          store={this.store.fundamentacaoLegalEntidadeSelectStore}
        />
      </FormField>
    );
    const fundInputText = (
      <FormField columns={6} attribute="fundamentacao" label="Fundamentação Legal (valor antigo)" submitted={submitted}>
        <InputText value={this.store.object.fundamentacao} disabled />
      </FormField>
    );

    return (
      <>
        {!this.store.object.id ? (
          fundDropdown
        ) : (
          <>
            {fundDropdown}
            {this.store.object.fundamentacao && fundInputText}
          </>
        )}
      </>
    );
  }

  render() {
    const { submitted } = this.state;
    const { submitFormData } = this;

    const breacrumbItems = [
      { label: 'Dispensa', url: UrlRouter.cadastrosConsulta.dispensa.index },
      { label: this.props.action === 'new' ? 'Novo' : 'Editar' },
    ];

    const forms = [
      {
        label: 'Dados Básicos',
        step: 0,
        body: (
          <FormDadosBasicos key="dadosBasicos" submitted={submitted} store={this.store} action={this.props.action} />
        ),
      },
      {
        label: 'Fornecedores e Itens',
        step: 1,
        body: (
          <FormFornecedoresItens
            key="fornecedoresItens"
            store={this.store}
            disabled={this.getWritePermission() && !AppStore.hasPermission(this.getWritePermission())}
            submitted={submitted}
          />
        ),
      },
      { label: 'Resumo', step: 2, body: <DispensaSummaryTab store={this.store} /> },
    ];

    let content;

    if (this.store.object) {
      content = (
        <>
          <AppBreadCrumb items={breacrumbItems} />
          <div className="card page form-action-buttons">
            <form onSubmit={submitFormData}>
              <Steps
                model={forms}
                activeIndex={this.state.activeIndexForm}
                onSelect={(e) => {
                  if (this.store.validaDadosBasicos()) {
                    this.validarForm(() => this.onChangeStep(e.index));
                  }
                }}
                readOnly={false}
              />
              {forms.find((item) => item.step === this.state.activeIndexForm).body}
              {this.renderStepButtons()}
              {this.renderDialogRequisicaoModificacao()}
              {this.store.isConfirmDialogVisible && this.confirmDiscardChanges()}
            </form>
          </div>
        </>
      );
    } else {
      content = (
        <div className="card page">
          <AppBreadCrumb items={breacrumbItems} />
          <i
            className="pi pi-spin pi-spinner"
            style={{
              marginTop: '20px',
              marginLeft: 'calc(50% - 20px)',
              fontSize: '2em',
            }}
          ></i>
        </div>
      );
    }
    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        {content}
      </PermissionProxy>
    );
  }
}

DispensaFormPage.propTypes = {
  id: PropTypes.any,
  action: PropTypes.any,
  history: PropTypes.any,
};

export default DispensaFormPage;
