import React from 'react';
import { observer } from 'mobx-react';
import UrlRouter from '~/constants/UrlRouter';
import GenericIndexPage from 'fc/pages/GenericIndexPage';
import AdvancedSearch from 'fc/components/AdvancedSearch';
import {
  getValueDate,
  getValueMoney,
  checkUserGroup,
  hasPermissionProxy,
  generateFullURL,
  somaValoresLotes,
  getValueByKey,
} from 'fc/utils/utils';
import { DATE_FORMAT, DATE_PARSE_FORMAT } from 'fc/utils/date';
import AccessPermission from '~/constants/AccessPermission';
import RequisicaoRemocao from '~/pages/requisicaoRemocao/requisicaoRemocaoModal';
import CardList from 'fc/components/CardList';
import PermissionProxy from 'fc/components/PermissionProxy';
import FcButton from 'fc/components/FcButton';
import { PrimeIcons } from 'primereact/api';
import CredenciamentoIndexStore from '~/stores/credenciamento/indexStore';
import { ConfirmDialog } from 'primereact/confirmdialog';
import AppStore from 'fc/stores/AppStore';
import AnulacaoRevogacaoModal from '../AnulacaoRevogacao/AnulacaoRevogacaoModal';
import DadosEstaticosService from '~/services/DadosEstaticosService';

@observer
class CredenciamentoListagemPage extends GenericIndexPage {
  constructor(props) {
    super(props, AccessPermission.credenciamento);
    this.store = new CredenciamentoIndexStore();

    this.state = {
      idRemove: null,
      selectedRow: null,
      showEntidadeDialog: false,
      showRequisicaoRemocao: false,
      showAnularRevogar: false,
    };

    this._closeRequisicaoRemocaoModal = this._closeRequisicaoRemocaoModal.bind(this);
    this._closeAnularRevogarModal = this._closeAnularRevogarModal.bind(this);
    this._updateDatatable = this._updateDatatable.bind(this);
    this.renderCardTitle = this.renderCardTitle.bind(this);
  }

  componentDidMount() {
    const { id } = this.props.match?.params || {};
    if (id) this.store.getById(id, (credenciamento) => this.props.onDetail(credenciamento));
  }

  _handleRemocaoRequisicaoModal(credenciamento) {
    this.setState({ showRequisicaoRemocao: true, selectedRow: credenciamento });
  }

  _handleAnularRevogarModal(dispensa) {
    this.setState({ showAnularRevogar: true, selectedRow: dispensa });
  }

  _closeRequisicaoRemocaoModal() {
    this.setState({ showRequisicaoRemocao: false, selectedRow: null });
  }

  _closeAnularRevogarModal() {
    this.setState({ showAnularRevogar: false, selectedRow: null });
  }

  _updateDatatable() {
    this.store.reloadTableData(() => this.forceUpdate());
  }

  _renderDialogAnularRevogar() {
    return (
      <AnulacaoRevogacaoModal
        showAnularRevogar={this.state.showAnularRevogar}
        closeDialog={this._closeAnularRevogarModal}
        selectedRow={this.state.selectedRow}
        getWritePermission={this.getWritePermission}
        tipoProcessoAssociado="CREDENCIAMENTO"
        updateTable={this._updateDatatable}
      />
    );
  }

  _renderDialogRequisicaoRemocao() {
    return (
      <RequisicaoRemocao
        history={this.props.history}
        processo={this.state.selectedRow}
        visibleDialog={this.state.showRequisicaoRemocao}
        closeDialog={this._closeRequisicaoRemocaoModal}
        updateTable={this._updateDatatable}
        tipoProcesso="credenciamento"
      />
    );
  }

  getCardEllipsisOptions(cardData) {
    const isAuditor = checkUserGroup('Auditor');
    const items = [];

    if (
      (cardData.idRequisicaoModificacao || cardData.anulacaoRevogacao?.idRequisicaoModificacao) &&
      hasPermissionProxy(AccessPermission.requisicaoModificacao.writePermission)
    ) {
      const itemToAdd = {
        label: 'Requisição de modificação pendente',
        icon: 'pi pi-exclamation-triangle',
      };

      const idRequisicaoCredenciamento =
        cardData.idRequisicaoModificacao ?? cardData.anulacaoRevogacao?.idRequisicaoModificacao;

      if (isAuditor) {
        itemToAdd.url = generateFullURL(
          UrlRouter.auditoria.requisicaoModificacao.indexDetail.replace(':id', idRequisicaoCredenciamento)
        );
      } else {
        itemToAdd.url = generateFullURL(
          UrlRouter.administracao.requisicaoModificacao.indexDetail.replace(':id', idRequisicaoCredenciamento)
        );
      }

      items.push(itemToAdd);
    } else {
      if (hasPermissionProxy(this.getReadPermission())) {
        items.push({
          label: 'Detalhes',
          icon: 'pi pi-eye',
          command: () => this.props.onDetail(cardData),
        });
      }

      if (hasPermissionProxy(this.getWritePermission())) {
        items.push({
          label: this.store.isRequisicaoModificacao(cardData.dataCadastro)
            ? 'Criar Requisição de Modificação'
            : 'Editar',
          icon: 'pi pi-pencil',
          url: this.store.isRequisicaoModificacao(cardData.dataCadastro)
            ? generateFullURL(
                UrlRouter.administracao.requisicaoModificacao.credenciamento.requisitar.replace(':id', cardData.id)
              )
            : generateFullURL(UrlRouter.cadastrosConsulta.credenciamento.editar.replace(':id', cardData.id)),
        });
        items.push({
          label: 'Novo/Gerenciar Credenciados',
          icon: 'pi pi-user-edit',
          url: generateFullURL(
            UrlRouter.cadastrosConsulta.credenciamento.credenciado.index.replace(':idCredenciamento', cardData.id)
          ),
        });
        items.push({
          label: 'Remover',
          icon: 'pi pi-trash',
          command: () => this._handleRemocaoRequisicaoModal(cardData),
        });
        items.push({
          label: 'Anular/Revogar',
          icon: 'pi pi-times',
          command: () => this._handleAnularRevogarModal(cardData),
        });
      }
    }

    return items;
  }

  _getCredenciamentoTitle(cardData) {
    const numeroProcesso = cardData?.numero;
    const ano = cardData?.ano;
    return numeroProcesso && numeroProcesso.includes('/') ? numeroProcesso : `${numeroProcesso}/${ano}`;
  }

  renderCardTitle(cardData) {
    const isJurisdicionado = checkUserGroup('Jurisdicionado');
    return isJurisdicionado
      ? `Credenciamento: ${this._getCredenciamentoTitle(cardData)}`
      : cardData?.entidade?.nome ?? 'Não foi possível identificar nome da entidade';
  }

  openDialogEntidadeVisibility() {
    this.setState({
      showEntidadeDialog: !this.state.showEntidadeDialog,
    });
  }

  closeDialogEntidadeVisibility() {
    this.setState({
      showEntidadeDialog: !this.state.showEntidadeDialog,
    });
  }

  _renderDialogConfirmacaoEntidade() {
    const message = `O novo registro criado será associado à entidade selecionada: ${
      AppStore.getContextEntity().nome
    }. Deseja continuar?`;
    return (
      <ConfirmDialog
        blockScroll
        visible={this.state.showEntidadeDialog}
        message={message}
        header="Confirmação"
        icon="pi pi-user preview-item"
        acceptClassName="p-button-info"
        draggable={false}
        onHide={() => this.closeDialogEntidadeVisibility()}
        accept={() => {
          this.pushUrlToHistory(UrlRouter.cadastrosConsulta.credenciamento.novo);
        }}
      />
    );
  }

  render() {
    const isJurisdicionado = checkUserGroup('Jurisdicionado');

    const fields = [
      {
        label: 'Número do Processo',
        field: 'numero',
        value: 'title',
        sortable: true,
        body: this.renderCardTitle,
      },
      {
        label: 'Responsável',
        value: 'subtitle',
        field: 'responsavel',
        sortable: true,
        body: (cardData) => {
          if (isJurisdicionado) {
            return `Responsável: ${cardData.responsavel?.nome ?? 'Não foi possível identificá-lo'}`;
          }
          if (cardData?.numero) {
            return `Credenciamento: ${this._getCredenciamentoTitle(cardData)}`;
          }
        },
      },
      {
        field: 'objeto',
        label: 'Objeto',
        value: 'mainContent',
        sortable: true,
      },
      {
        label: 'Vigência',
        field: ['inicioVigencia', 'fimVigencia'],
        value: 'iconLabel',
        color: '#4c4c4b',
        icon: 'pi pi-calendar',
        sortable: true,
        body: ({ inicioVigencia, fimVigencia }) => {
          return `${getValueDate(inicioVigencia, DATE_FORMAT, DATE_PARSE_FORMAT)} a ${getValueDate(
            fimVigencia,
            DATE_FORMAT,
            DATE_PARSE_FORMAT
          )} `;
        },
      },
      {
        field: 'valor',
        label: 'Valor',
        value: 'iconLabel',
        color: '#2F83DC',
        icon: 'pi pi-money-bill',
        body: ({ termoReferencia }) =>
          getValueMoney(somaValoresLotes(termoReferencia.lotes), termoReferencia?.tresCasasDecimais ? 3 : 2),
        sortable: false,
      },
      {
        field: 'anulacaoRevogacao',
        label: 'Anulado/Revogado',
        value: 'iconLabel',
        showIfExists: true,
        color: '#b83b20',
        icon: 'pi pi-money-bill',
        body: ({ anulacaoRevogacao }) =>
          getValueByKey(anulacaoRevogacao?.tipoOcorrencia, DadosEstaticosService.getValueAnuladoRevogado()),
        sortable: true,
      },
      {
        label: 'Ações',
        value: 'ellipsisItems',
        body: (cardData) => this.getCardEllipsisOptions(cardData),
      },
    ];

    const exportFields = [
      {
        field: isJurisdicionado ? 'numeroProcesso' : 'entidade.nome',
        header: isJurisdicionado ? 'Número do Processo' : 'Entidade',
      },
      {
        field: !isJurisdicionado && 'numeroProcesso',
        header: !isJurisdicionado && 'Número do Processo',
      },
      {
        field: 'objeto',
        header: 'Objeto',
      },
      {
        field: 'valor',
        header: 'Valor',
        body: ({ termoReferencia }) =>
          getValueMoney(somaValoresLotes(termoReferencia.lotes), termoReferencia?.tresCasasDecimais ? 3 : 2),
      },
    ];

    const header = () => (
      <div className="table-header flex justify-content-start">
        <PermissionProxy resourcePermissions={this.getWritePermission()}>
          <FcButton
            className="p-button"
            label="Novo"
            style={{ marginBottom: '5px', marginRight: '5px' }}
            icon={PrimeIcons.PLUS}
            onClick={() => this.openDialogEntidadeVisibility()}
          />
        </PermissionProxy>

        {this.renderTableDataExport(exportFields, 'credenciamento', true)}
      </div>
    );

    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        <>
          <AdvancedSearch
            searchParams={this.store.getAdvancedSearchParams()}
            store={this.store}
            searchFields={['numeroProcesso', 'responsavel', 'objeto']}
            filterSuggest={this.store.getFilterSuggest()}
            useOr
          />

          <CardList
            fields={fields}
            store={this.store}
            header={header()}
            onTitleClick={(cardData) => {
              if (hasPermissionProxy(this.getReadPermission())) {
                return {
                  command: () => this.props.onDetail(cardData),
                };
              }
            }}
          />

          {this.store.isConfirmDialogVisible && this.confirmRemove(this.state.idRemove)}
          {this._renderDialogRequisicaoRemocao()}
          {this.state.showEntidadeDialog && this._renderDialogConfirmacaoEntidade()}
          {this.state.showAnularRevogar && this._renderDialogAnularRevogar()}
        </>
      </PermissionProxy>
    );
  }
}

export default CredenciamentoListagemPage;
