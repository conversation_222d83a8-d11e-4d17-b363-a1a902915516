import React from 'react';
import { observer } from 'mobx-react';
import PropTypes from 'prop-types';
import UrlRouter from '~/constants/UrlRouter';
import GenericFormPage from 'fc/pages/GenericFormPage';
import AppBreadCrumb from 'fc/components/AppBreadCrumb';
import AccessPermission from '~/constants/AccessPermission';
import PermissionProxy from 'fc/components/PermissionProxy';
import { Steps } from 'primereact/steps';
import { Message } from 'primereact/message';
import FcButton from 'fc/components/FcButton';
import AppStore from 'fc/stores/AppStore';
import { classNames } from 'primereact/utils';
import { ProgressSpinner } from 'primereact/progressspinner';
import { isValueValid, showNotification } from 'fc/utils/utils';
import CredenciamentoFormStore from '~/stores/credenciamento/formStore';
import TabDadosBasicos from './tabs/tabDadosBasicos';
import TabResumo from './tabs/tabResumo';
import RequisicaoModificacaoFormStore from '~/stores/requisicaoModificacao/formStore';
import { Dialog } from 'primereact/dialog';
import FormField from 'fc/components/FormField';
import FcInputTextarea from 'fc/components/FcInputTextarea';

@observer
class CredenciamentoFormPage extends GenericFormPage {
  constructor(props) {
    super(props, UrlRouter.cadastrosConsulta.credenciamento.index, AccessPermission.credenciamento);

    this.store = new CredenciamentoFormStore();
    this.reqModificacaoStore = new RequisicaoModificacaoFormStore();

    this.formIndexes = Object.freeze({
      dadosBasicos: 0,
      resumo: 1,
    });

    this.state = {
      activeIndexForm: this.formIndexes.dadosBasicos,
      visibleDialogReqMod: false,
      errorDialogValue: false,
    };

    this._toggleDialogReqMod = this._toggleDialogReqMod.bind(this);
    this.renderDialogRequisicaoModificacao = this.renderDialogRequisicaoModificacao.bind(this);
  }

  componentDidMount() {
    const { id } = this.props;

    const initialize = () =>
      this.store.initialize(id, { lei: 'LEI_N_14133' }, () => {
        this.store.checkDataCadastro();
        this.store.object?.obra && this.store.carregarEdificacaoObra();
      });

    if (id) {
      this.store.initializeArquivos(id, initialize);
    } else {
      initialize();
    }
  }

  renderDialogReqModFooter() {
    return (
      <div>
        <FcButton
          label="Cancelar"
          icon="pi pi-times"
          onClick={() => this._toggleDialogReqMod()}
          className="p-button-text"
        />
        <FcButton
          label="Enviar Requisição"
          icon="pi pi-check"
          onClick={() => {
            if (this.reqModificacaoStore.justificativaJurisdicionado) {
              this.submitFormData();
              this._toggleDialogReqMod();
            } else {
              !this.state.errorDialogValue && this.setState({ errorDialogValue: true });
            }
          }}
        />
      </div>
    );
  }

  _getFieldErrorMessage(rendered, message) {
    if (rendered) {
      return <small className="p-error">{message}</small>;
    }
    return null;
  }

  renderDialogRequisicaoModificacao() {
    const { getRule } = this.store;
    return (
      <Dialog
        header="Justificativa Requisição"
        visible={this.state.showDialogReqMod}
        style={{ width: '50vw' }}
        footer={this.renderDialogReqModFooter()}
        onHide={() => this._toggleDialogReqMod()}
      >
        <div className="p-fluid p-formgrid p-grid">
          <FormField columns={12} attribute="justificativa" rule={getRule('justificativa')} label="Justificativa">
            <FcInputTextarea
              className={this.state.errorDialogValue ? 'p-invalid p-error' : ''}
              value={this.reqModificacaoStore.justificativaJurisdicionado}
              onChange={(e) => {
                this.reqModificacaoStore.updateAttribute('jurisdicionado', e);
                isValueValid(this.reqModificacaoStore.justificativaJurisdicionado?.length) &&
                  this.setState({ errorDialogValue: false });
              }}
              placeholder="Descreva a justificativa"
            />
          </FormField>
          <div className="p-col-6">
            {this._getFieldErrorMessage(this.state.errorDialogValue, 'Por favor, preencha o campo')}
          </div>
        </div>
      </Dialog>
    );
  }

  onBack = () => {
    this.setState({ activeIndexForm: this.state.activeIndexForm - 1 });
  };

  onNext = () => {
    this.setState({ activeIndexForm: this.state.activeIndexForm + 1 });
  };

  _toggleDialogReqMod() {
    this.setState((oldState) => ({ showDialogReqMod: !oldState.showDialogReqMod }));
  }

  isDisabledAvancar() {
    const { activeIndexForm } = this.state;

    const errorsMessages = {
      message: '',
      disabled: true,
    };

    if (activeIndexForm == 0 && !this.store.validaDadosBasicos()) {
      errorsMessages.message =
        'É necessário preencher todos os campos obrigatórios antes de avançar para a próxima etapa.';
    } else {
      errorsMessages.disabled = false;
    }

    return errorsMessages;
  }

  onChangeStep(step, action = 'avancar') {
    if (step < this.formIndexes.dadosBasicos) {
      this.store.toggleShowConfirmDialog();
    } else {
      (action !== 'avancar' || !this.isDisabledAvancar().disabled) && this.setState({ activeIndexForm: step });
    }
  }

  validarForm(callback) {
    if (this.store.validateSubmittedFiles(this.store.arquivos)) {
      if (this.state.activeIndexForm === this.formIndexes.arquivos) {
        this.store.validaArquivos(callback);
      } else {
        callback && callback();
      }
    }
  }

  renderActionButtons() {
    const { activeIndexForm } = this.state;
    const hasWritePermission = !this.getWritePermission() || AppStore.hasPermission(this.getWritePermission());

    const disabledAvancar = this.isDisabledAvancar();
    return (
      <div className="p-mt-10 form-actions">
        <div className="p-mt-2">
          <span className="p-mr-1 p-d-inline p-d-flex align-items-center justify-content-end">
            {disabledAvancar.disabled && (
              <Message className="p-ml-auto p-mr-2" severity="warn" text={disabledAvancar.message} />
            )}
            <FcButton
              label="Voltar"
              type="button"
              className={classNames('p-button-secondary p-mr-2', { 'p-ml-auto': !disabledAvancar.disabled })}
              onClick={() => this.onChangeStep(activeIndexForm - 1, 'voltar', 'button')}
              loading={this.store.loading}
            />
            {activeIndexForm < this.formIndexes.resumo && (
              <FcButton
                label="Avançar"
                type="button"
                onClick={() => this.onChangeStep(activeIndexForm + 1, 'avancar', 'button')}
                disabled={disabledAvancar.disabled}
              />
            )}
            {hasWritePermission &&
              activeIndexForm === this.formIndexes.resumo &&
              (this.store.enableReqMod ? (
                <FcButton
                  label="Enviar Requisição"
                  onClick={() => this._toggleDialogReqMod()}
                  loading={this.store.loading}
                />
              ) : (
                <FcButton
                  label="Salvar"
                  onClick={(e) => this.submitFormData(e)}
                  loading={this.store.loading}
                  disabled={disabledAvancar.disabled}
                />
              ))}
          </span>
        </div>
      </div>
    );
  }

  submitFormData(e) {
    e && e.preventDefault();
    const execution = () => {
      if (!this.store.rules.hasError && this.store.validateSubmittedFiles(this.store.arquivos)) {
        if (this.store.enableReqMod) {
          const saveObject = this.store.getObjectToSave(this.props.action);

          if (Object.keys(saveObject).length === 0) {
            showNotification('warn', null, 'Nenhuma alteração realizada.');
          } else {
            const credenciamentoDTO = {
              credenciamento: {
                ...this.store.object,
              },
              obra:
                this.store.obraObject && Object.keys(this.store.obraObject).length > 0 ? this.store.obraObject : null,
              edificacao: this.store.edificacao,
              arquivos: this.store.arquivos,
            };

            this.reqModificacaoStore.justificativaJurisdicionado &&
              this.reqModificacaoStore.enviarRequisicaoCredenciamento(credenciamentoDTO, this._goBack);
          }
        } else {
          this.store.validaArquivos(() => this.store.save(this._goBack, this.props.action));
        }
      } else {
        showNotification('error', null, 'Verifique os campos dos formulários!');
      }
    };

    if (this.state.submitted) {
      execution();
    } else {
      this.setState({ submitted: true }, execution);
    }
  }

  render() {
    const { submitted } = this.state;

    const breacrumbItems = [
      { label: 'Credenciamento', url: UrlRouter.cadastrosConsulta.credenciamento.index },
      { label: this.props.action === 'new' ? 'Novo' : 'Editar' },
    ];

    if (this.store.object) {
      let forms = [
        {
          label: 'Dados Básicos',
          step: this.formIndexes.dadosBasicos,
          body: (
            <TabDadosBasicos key="dadosBasicos" store={this.store} submitted={submitted} action={this.props.action} />
          ),
        },
        {
          label: 'Resumo',
          step: this.formIndexes.resumo,
          body: <TabResumo key="resumo" store={this.store} submitted={submitted} />,
        },
      ];

      return (
        <PermissionProxy resourcePermissions={this.getWritePermission()} blockOnFail>
          <AppBreadCrumb items={breacrumbItems} />
          <div className="card page form-action-buttons">
            <Steps
              model={forms}
              activeIndex={this.state.activeIndexForm}
              onSelect={(e) => {
                e.index == 2
                  ? this.validarForm(() =>
                      this.onChangeStep(e.index, e.index < this.state.activeIndexForm ? 'voltar' : 'avancar')
                    )
                  : this.onChangeStep(e.index, e.index < this.state.activeIndexForm ? 'voltar' : 'avancar');
              }}
              readOnly={false}
            />
            {forms.find((item) => item.step === this.state.activeIndexForm).body}
            {this.renderActionButtons()}
            {this.renderDialogRequisicaoModificacao()}
            {this.store.isConfirmDialogVisible && this.confirmDiscardChanges()}
          </div>
        </PermissionProxy>
      );
    } else {
      return (
        <div className="card page">
          <AppBreadCrumb items={breacrumbItems} />
          <div className="p-d-inline p-d-flex align-items-center">
            <ProgressSpinner />
          </div>
        </div>
      );
    }
  }
}

CredenciamentoFormPage.propTypes = {
  id: PropTypes.any,
  action: PropTypes.any,
  history: PropTypes.any,
};

export default CredenciamentoFormPage;
