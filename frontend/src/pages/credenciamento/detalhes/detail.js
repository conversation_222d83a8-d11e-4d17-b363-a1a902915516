import React from 'react';
import PropTypes from 'prop-types';
import { observer } from 'mobx-react';
import {
  getValueDate,
  getValue<PERSON><PERSON><PERSON><PERSON>,
  getValueM<PERSON>,
  getValue,
  generateFullURL,
  somaValoresLotes,
} from 'fc/utils/utils';
import { DATE_FORMAT, DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS } from 'fc/utils/date';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import AccessPermission from '~/constants/AccessPermission';
import { Link } from 'react-router-dom/cjs/react-router-dom.min';
import UrlRouter from '~/constants/UrlRouter';
import { Divider } from 'primereact/divider';
import AppStore from 'fc/stores/AppStore';
import './style.scss';
import ScrollMenu from 'fc/components/ScrollMenu';
import classNames from 'classnames';
import { Column } from 'primereact/column';
import MultipleFileUploader from 'fc/components/MultipleFileUploader';
import FcCloseableTabView from 'fc/components/FcCloseableTabView';
import { FeatureGroup, MapContainer, Marker, Polygon, Polyline, TileLayer } from 'react-leaflet';
import { Dialog } from 'primereact/dialog';
import FcButton from 'fc/components/FcButton';
import GerenciamentoTermoFormStore from '~/stores/gerenciamentoTermo/formStore';
import { DataTable } from 'primereact/datatable';
import moment from 'moment';
import PermissionProxy from 'fc/components/PermissionProxy';

@observer
class CredenciamentoDetailPage extends React.Component {
  termoReferenciaStore;
  constructor(props) {
    super(props);
    this.state = {
      activeTabIndex: 0,
      visibleDialogObras: false,
    };

    this.termoReferenciaStore = new GerenciamentoTermoFormStore();
  }

  componentDidMount() {
    const { idTermo, credenciamentoStore } = this.props;
    this.termoReferenciaStore.initialize(idTermo, {}, () => {
      this.termoReferenciaStore.recuperarArquivos(idTermo);
    });

    credenciamentoStore.object?.id && credenciamentoStore.hasAlert(credenciamentoStore.object.id);
  }

  getValueStyle() {
    return { fontSize: AppStore.layout === 'extenso' ? '1.26rem' : '1.1rem' };
  }

  getValueLayoutClassNames() {
    const tipoLayout = AppStore.layout;
    return { 'py-0': tipoLayout === 'compacto', 'py-2': tipoLayout === 'default' || tipoLayout === 'extenso' };
  }

  _renderTabCredenciados(credenciados) {
    const valorTotalLote = (credenciadoItems) => {
      const somaTotal = credenciadoItems.reduce((acc, item) => acc + item.valor, 0);
      return getValueMoney(somaTotal);
    };
    const columns = [
      {
        field: 'nomeCredenciado',
        header: 'Nome',
        body: ({ licitante }) => getValue(licitante?.nome),
      },
      {
        field: 'cnpj',
        header: 'CNPJ',
        body: ({ licitante }) => getValue(licitante?.cpfCnpj),
      },
      {
        field: 'valorCredenciamento',
        header: 'Valor',
        body: ({ credenciadoItems }) => valorTotalLote(credenciadoItems),
      },
      {
        field: 'dataInicioVigencia',
        header: 'Início da Vigência',
        body: ({ vigenciaInicial }) => getValueDate(vigenciaInicial),
      },
      {
        field: 'dataFimVigenica',
        header: 'Fim da Vigência',
        body: ({ vigenciaFinal }) => getValueDate(vigenciaFinal),
      },
    ];

    return (
      <DataTable
        rowHover
        value={credenciados}
        paginator
        rows={5}
        className="p-datatable-sm"
        emptyMessage="Nenhum credenciado adicionado."
      >
        {this._renderColumns(columns)}
      </DataTable>
    );
  }

  _renderValue(label, value, col = 12, type = 'value', url = '#') {
    return (
      <div
        style={this.getValueStyle()}
        className={classNames(
          `details-set details-display flex align-items-center p-col-12 p-md-${col} p-sm-${col >= 6 ? col : 6}`,
          this.getValueLayoutClassNames()
        )}
      >
        <div
          style={{ color: 'rgb(0 0 0 / 50%)', paddingLeft: 0 }}
          className="xl:col-2 lg:col-3 md:col-4 sm:col-4 details-attributte drawer-content-label"
        >
          {label}
        </div>

        {type == 'value' && (
          <div
            style={{ paddingLeft: 0 }}
            className={`xl:col-10 lg:col-9 md:col-8 sm:col-8 details-value p-text-justify`}
          >
            {value ?? '-'}
          </div>
        )}

        {type == 'list' && (
          <div style={{ display: 'block', paddingLeft: 0 }}>
            {value?.map((value) => (
              <div className={`details-value p-text-justify`}>{value ?? '-'}</div>
            ))}
          </div>
        )}

        {type == 'link' && (
          <div
            style={{ paddingLeft: 0 }}
            className={`xl:col-10 lg:col-9 md:col-8 sm:col-8 details-value p-text-justify`}
          >
            {
              <Link target="_blank" rel="noreferrer" style={{ color: '#3F51B5' }} to={url}>
                <u>{value}</u>
              </Link>
            }
          </div>
        )}

        {type == 'button' && <div className="details-value p-text-justify p-0">{value ?? '-'}</div>}
      </div>
    );
  }
  _renderDivider(label, id) {
    return (
      <div style={{ display: 'flex', alignItems: 'center', width: '100%' }}>
        <h5
          id={id}
          style={{
            padding: '7px',
            whiteSpace: 'nowrap',
            marginRight: '10px',
            color: '#333333',
            marginBottom: 0,
          }}
        >
          {label}
        </h5>
        <Divider style={{ flexGrow: 1, height: '1px' }} />
      </div>
    );
  }

  _renderTabs() {
    const { credenciamentoStore } = this.props;
    const tabs = [];
    tabs.push({
      id: 0,
      header: 'Arquivos',
      content: (
        <MultipleFileUploader
          fileTypes={DadosEstaticosService.getTipoArquivoCredenciamento()}
          downloadOnly
          store={credenciamentoStore.fileStore}
        />
      ),
    });
    if (this.props.idTermo) {
      tabs.push({
        id: 1,
        header: 'Arquivos do Termo de Referência',
        content: (
          <MultipleFileUploader
            downloadOnly
            store={this.termoReferenciaStore?.fileStore}
            fileTypes={DadosEstaticosService.getTipoArquivoTermoReferencia()}
            accept=".pdf, .xls, .xlsx"
          />
        ),
      });
    }
    tabs.push({
      id: tabs?.length,
      header: 'Credenciados',
      content: this._renderTabCredenciados(credenciamentoStore.credenciados),
    });
    return (
      <FcCloseableTabView
        tabs={tabs}
        activeTabIndex={this.state.activeTabIndex}
        onChangeTab={(tab) => this.setState({ activeTabIndex: tabs.indexOf(tab) })}
      />
    );
  }

  _renderColumns(columns) {
    return columns.map((col, idx) => <Column className={`p-p-3`} key={`col-${idx}`} {...col} />);
  }

  _toggleDialogObras() {
    this.setState((oldState) => ({ visibleDialogObras: !oldState.visibleDialogObras }));
  }

  renderEdificacao(credenciamento) {
    const obra = credenciamento?.obra?.edificacao?.localizacao;
    const polyline = [];
    const polygon = [];
    const marker = [];

    if (credenciamento?.coordenadas) {
      const coordObra = credenciamento.coordenadas.split(',').map(Number);
      if (credenciamento?.tipoSelecao === 'POLIGONO') {
        const coordObraPol = [];
        for (let i = 0; i < coordObra.length; i += 2) {
          coordObra.forEach((coord, i) => {
            if (i % 2 === 0) {
              coordObraPol.push([coord, coordObra[i + 1]]);
            }
          });
        }
        const finalCoord = [coordObraPol];
        polygon.push(finalCoord[0]);
      } else if (credenciamento?.tipoSelecao === 'LINHA') {
        const coordObraLin = [];
        for (let i = 0; i < coordObra.length; i += 2) {
          coordObra.forEach((coord, i) => {
            if (i % 2 === 0) {
              coordObraLin.push([coord, coordObra[i + 1]]);
            }
          });
        }
        const finalCoord = [coordObraLin];
        polyline.push(finalCoord);
      } else {
        marker.push.apply(marker, coordObra);
      }
    } else {
      if (obra.type === 'Polygon') {
        polygon.push(obra.coordinates[0]);
      } else if (obra.type === 'LineString') {
        polyline.push(obra.coordinates);
      } else {
        marker.push.apply(marker, obra.coordinates);
      }
    }
    return (
      <>
        {marker && marker.length > 0 && <Marker position={marker} radius={20} />}
        {polyline && polyline.length > 0 && <Polyline positions={polyline} />}
        {polygon && polygon.length > 0 && <Polygon positions={polygon} />}
      </>
    );
  }

  _renderDialogObras() {
    let defaultCoordinates = [-8.921198844909668, -70.98129272460939];
    const { credenciamentoStore } = this.props;
    const credenciamento = credenciamentoStore.object;
    const obra = credenciamento?.obra?.edificacao?.localizacao;

    if (credenciamento?.coordenadas) {
      const coordObra = credenciamento.coordenadas.split(',').map(Number);
      switch (credenciamento?.tipoSelecao) {
        case 'POLIGONO':
          defaultCoordinates = [coordObra[0], coordObra[1]];
          break;
        case 'LINHA':
          defaultCoordinates = [coordObra[0], coordObra[1]];
          break;
        default:
          defaultCoordinates = coordObra;
      }
    } else {
      switch (obra?.type) {
        case 'Polygon':
          defaultCoordinates = obra.coordinates[0][0];
          break;
        case 'LineString':
          defaultCoordinates = obra.coordinates[0];
          break;
        default:
          defaultCoordinates = obra.coordinates;
      }
    }

    return (
      <Dialog
        header="Localização de obra"
        visible={this.state.visibleDialogObras}
        style={{ width: '80%' }}
        onHide={() => this._toggleDialogObras()}
      >
        <div>
          <MapContainer center={defaultCoordinates} zoom={16} style={{ height: '70vh', width: '100wh' }}>
            <TileLayer
              attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
              url="https://tile.openstreetmap.org/{z}/{x}/{y}.png"
            />
            <FeatureGroup />

            {this.renderEdificacao(credenciamento)}
          </MapContainer>
        </div>
      </Dialog>
    );
  }

  _renderButton() {
    return (
      <FcButton
        label="Visualizar no mapa"
        type="button"
        className="p-button-secondary"
        icon="pi pi-map"
        onClick={() => this._toggleDialogObras()}
      />
    );
  }

  pushUrlToHistory(url) {
    url && this.props.history.push(url);
  }

  renderDadosBasicos() {
    const { credenciamentoStore, showGerenciamento } = this.props;
    const { idCredenciamento } = credenciamentoStore;
    const credenciamento = credenciamentoStore.object;

    const fimVigenciaPermiteAdicionar = () => {
      return moment().isAfter(moment(credenciamento?.fimVigencia)) ? false : true;
    };

    return (
      <div className="p-fluid p-formgrid">
        <div className="p-d-flex p-jc-between p-ai-center p-mb-3">
          {this._renderDivider('Detalhes', `detalhes-${idCredenciamento}`)}
          {credenciamento && (
            <PermissionProxy resourcePermissions={AccessPermission.credenciado.writePermission}>
              <FcButton
                icon="pi pi-plus"
                tooltip={
                  fimVigenciaPermiteAdicionar()
                    ? 'Adicionar credenciado'
                    : 'Vigência do credenciamento não permite novos credenciados'
                }
                style={{ backgroundColor: '#3f51b5' }}
                disabled={!fimVigenciaPermiteAdicionar()}
                onClick={() =>
                  this.pushUrlToHistory(
                    UrlRouter.cadastrosConsulta.credenciamento.credenciado.novo.replace(
                      ':idCredenciamento',
                      idCredenciamento
                    )
                  )
                }
              />
            </PermissionProxy>
          )}
        </div>
        {this._renderValue('Número do Processo Administrativo', credenciamento.numeroProcesso)}
        {this._renderValue('Número do Credenciamento', credenciamento.numero)}
        {this._renderValue('Ano', credenciamento.ano)}
        {this._renderValue(
          'Início da Vigência',
          getValueDate(credenciamento.inicioVigencia, DATE_FORMAT, DATE_PARSE_FORMAT_WITH_HOURS)
        )}
        {this._renderValue(
          'Fim da Vigência',
          getValueDate(credenciamento.fimVigencia, DATE_FORMAT, DATE_PARSE_FORMAT_WITH_HOURS)
        )}
        {credenciamento.termoReferencia && (
          <>
            {AppStore.hasPermission([
              AccessPermission.gerenciamentoTermo.readPermission,
              AccessPermission.gerenciamentoTermo.writePermission,
            ])
              ? this._renderValue(
                  'Termo de Referência',
                  credenciamento.termoReferencia.identificadorProcesso,
                  12,
                  'link',
                  UrlRouter.termoReferencia.gerenciamentoTermos.detalhe.replace(
                    ':idTermo',
                    credenciamento.termoReferencia?.id
                  )
                )
              : this._renderValue('Termo de Referência', credenciamento.termoReferencia?.identificadorProcesso, 12)}
          </>
        )}
        {this._renderValue(
          'Valor',
          getValueMoney(
            somaValoresLotes(credenciamento.termoReferencia.lotes),
            credenciamento.termoReferencia?.tresCasasDecimais ? 3 : 2
          )
        )}
        {this._renderValue('Objeto', credenciamento.objeto)}
        {this._renderValue('Sítio de Divulgação do Edital de Chamamento Público', credenciamento.sitioDivulgacao)}
        {this._renderValue(
          'Naturezas do Objeto',
          credenciamento?.naturezasDoObjeto?.map((n) =>
            getValueByKey(n, DadosEstaticosService.getCategoriasNaturezaObjeto())
          ),
          12,
          'list'
        )}

        <div id={`informacoesLegais-${idCredenciamento}`}>{this._renderDivider('Informações Legais')}</div>
        {this._renderValue(
          'Comissão de Contratação',
          credenciamento.comissaoContratacao &&
            credenciamento.comissaoContratacao.numero +
              ' - ' +
              getValueByKey(credenciamento.comissaoContratacao.tipo, DadosEstaticosService.getTipoComissao()) +
              ' - ' +
              getValueByKey(
                credenciamento.comissaoContratacao.tipoConjunto,
                DadosEstaticosService.getTipoConjuntoComissao()
              )
        )}
        {this._renderValue('Presidente da Comissão', credenciamento.presidenteComissao?.nome)}
        {this._renderValue(
          'Tipo de Contratação',
          getValueByKey(credenciamento.tipoContratacao, DadosEstaticosService.getTipoContratacao())
        )}
        {this._renderValue('Responsável pelo Credenciamento', credenciamento.responsavel?.nome)}
        {this._renderValue(
          'Órgãos Participantes',
          credenciamento?.orgaosParticipantes?.map((op) => op.nome),
          12,
          'list'
        )}

        <div id={`recursos-${idCredenciamento}`}>{this._renderDivider('Recursos')}</div>
        {this._renderValue(
          'Fontes de Recurso',
          credenciamento?.fontesDeRecurso?.map((f) => f.nome),
          12,
          'list'
        )}

        {showGerenciamento && (
          <>
            <div id={`gerenciamento-${idCredenciamento}`}>{this._renderDivider('Gerenciamento')}</div>
            {this._renderValue('Cadastrado por', credenciamento?.usuario?.nome, 12)}
            {this._renderValue(
              'Data/Hora de Cadastro',
              getValueDate(credenciamento.dataCadastro, DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS),
              12
            )}
            {this._renderValue('Alterado por', credenciamentoStore?.ultimaAlteracao?.nome, 12)}
            {this._renderValue(
              'Data/Hora de Alteração',
              getValueDate(
                credenciamentoStore?.ultimaAlteracao?.data,
                DATE_FORMAT_WITH_HOURS,
                DATE_PARSE_FORMAT_WITH_HOURS
              ),
              12
            )}
          </>
        )}
        {credenciamento?.obra && (
          <>
            {this._renderDialogObras()}
            {this._renderDivider('Obra')}
            {this._renderValue('Tipo da Obra', credenciamento.obra?.tipo?.nome)}
            {this._renderValue('Categoria da Obra', credenciamento.obra?.categoria?.nome)}
            {this._renderValue(
              'Obra',
              credenciamento.obra?.edificacao || credenciamento.coordenadas ? this._renderButton(3) : '-',
              12,
              'button'
            )}
          </>
        )}
      </div>
    );
  }

  _renderAlert() {
    const { credenciamentoStore } = this.props;
    if (credenciamentoStore.hasPermissionAlerta() && credenciamentoStore.idAlerta) {
      return (
        <FcButton
          icon="pi pi-bell"
          label="Histórico de Alertas"
          className="p-button-danger my-3"
          onClick={() => {
            window.open(
              generateFullURL(UrlRouter.alerta.editar.replace(':id', credenciamentoStore.idAlerta)),
              '_blank'
            );
          }}
        />
      );
    }
  }

  render() {
    const { idCredenciamento } = this.props.credenciamentoStore;

    return (
      <div>
        <div className="relative">
          <div className="scroll-menu">
            <ScrollMenu
              title="sumário"
              layoutPosition="left"
              offsetTopOnScroll={50}
              links={[{ id: `detalhes-${idCredenciamento}`, label: 'Detalhes' }]}
            />
          </div>
        </div>
        {this.renderDadosBasicos()}
        <Divider style={{ marginBottom: `1px` }} />
        {this._renderAlert()}
        {this._renderTabs()}
      </div>
    );
  }
}

CredenciamentoDetailPage.defaultProps = {
  showGerenciamento: true,
};

CredenciamentoDetailPage.propTypes = {
  credenciamentoStore: PropTypes.any,
  showGerenciamento: PropTypes.bool,
  idTermo: PropTypes.number,
  history: PropTypes.any,
};

export default CredenciamentoDetailPage;
