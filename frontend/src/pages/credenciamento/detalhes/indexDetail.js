import React from 'react';
import PropTypes from 'prop-types';
import { observer } from 'mobx-react';
import { ProgressSpinner } from 'primereact/progressspinner';
import CredenciamentoFormStore from '~/stores/credenciamento/formStore';
import CredenciamentoDetailPage from './detail';

@observer
class CredenciamentoIndexDetailPage extends React.Component {
  constructor(props) {
    super(props);
    this.credenciamentoStore = new CredenciamentoFormStore();
  }

  componentDidMount() {
    const { id } = this.props;
    if (id) {
      this.credenciamentoStore.initialize(id, {}, () => {
        this.credenciamentoStore.initializeArquivos(id);
        this.credenciamentoStore.carregaUltimaAlteracao(id);
        this.credenciamentoStore.getCredenciados();
        this.credenciamentoStore.initializeTdaCredenciamento(id);
      });
    }
  }

  render() {
    const credenciamento = this.credenciamentoStore?.object;
    let content = <></>;
    if (this.credenciamentoStore.loading) {
      content = (
        <div className="p-d-inline p-d-flex align-items-center">
          <ProgressSpinner />
        </div>
      );
    } else if (credenciamento) {
      content = (
        <CredenciamentoDetailPage
          credenciamentoStore={this.credenciamentoStore}
          idTermo={credenciamento.termoReferencia?.id}
          history={this.props.history}
        />
      );
    } else {
      content = <div>Erro ao exibir detalhes do Credenciamento.</div>;
    }
    return content;
  }
}

CredenciamentoIndexDetailPage.propTypes = {
  id: PropTypes.number,
  history: PropTypes.any,
};

export default CredenciamentoIndexDetailPage;
