import React from 'react';
import { observer } from 'mobx-react';
import AppBreadCrumb from 'fc/components/AppBreadCrumb';
import GenericIndexPage from 'fc/pages/GenericIndexPage';
import AccessPermission from '~/constants/AccessPermission';
import PermissionProxy from 'fc/components/PermissionProxy';
import FcCloseableTabView from 'fc/components/FcCloseableTabView';
import CredenciamentoListagemPage from './listagem';
import CredenciamentoIndexDetailPage from './detalhes/indexDetail';

@observer
class CredenciamentoIndexPage extends GenericIndexPage {
  constructor(props) {
    super(props, AccessPermission.credenciamento);

    this.state = {
      data: [
        {
          id: 1,
          header: 'Listagem',
          closeable: false,
          content: (
            <CredenciamentoListagemPage {...props} onDetail={(credenciamento) => this.onDetail(credenciamento)} />
          ),
        },
      ],
      count: 2,
      activeTabIndex: 0,
    };

    this.setActiveTabIndex = this.setActiveTabIndex.bind(this);
    this.handleCloseTab = this.handleCloseTab.bind(this);
    this.onDetail = this.onDetail.bind(this);
  }

  onDetail(credenciamento) {
    const existingTab = this.state.data.find((tab) => tab.idCredenciamento === credenciamento.id);

    if (existingTab) {
      this.setActiveTabIndex(existingTab);
    } else {
      const newCredenciamento = {
        id: this.state.count,
        idCredenciamento: credenciamento.id,
        header: credenciamento?.numero.includes('/')
          ? credenciamento?.numero
          : `${credenciamento?.numero}/${credenciamento?.ano}`,
        closeable: true,
        content: <CredenciamentoIndexDetailPage id={credenciamento.id} history={this.props.history} />,
      };
      this.setState({ data: [...this.state.data, newCredenciamento], count: this.state.count + 1 });
    }
  }

  setActiveTabIndex(tab) {
    this.setState({ activeTabIndex: this.state.data.indexOf(tab) });
  }

  handleCloseTab(tab) {
    this.setState((prevState) => {
      return { data: prevState.data.filter((t) => t.id !== tab.id) };
    });
  }

  render() {
    const breacrumbItems = [{ label: 'Credenciamento' }];
    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        <AppBreadCrumb items={breacrumbItems} />
        <div className="card page index-table">
          <FcCloseableTabView
            tabs={this.state.data}
            activeTabIndex={this.state.activeTabIndex}
            onChangeTab={this.setActiveTabIndex}
            onClose={this.handleCloseTab}
            fixedFirstTab
          />
        </div>
      </PermissionProxy>
    );
  }
}

CredenciamentoIndexPage.displayName = 'CredenciamentoIndexPage';

export default CredenciamentoIndexPage;
