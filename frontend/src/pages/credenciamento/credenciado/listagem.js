import React from 'react';
import { observer } from 'mobx-react';
import CredenciadoIndexStore from '~/stores/credenciamento/credenciado/indexStore';
import IndexDataTable from 'fc/components/IndexDataTable';
import FcButton from 'fc/components/FcButton';
import { PrimeIcons } from 'primereact/api';
import UrlRouter from '~/constants/UrlRouter';
import GenericIndexPage from 'fc/pages/GenericIndexPage';
import AdvancedSearch from 'fc/components/AdvancedSearch';
import PermissionProxy from 'fc/components/PermissionProxy';
import AccessPermission from '~/constants/AccessPermission';
import SearchTypes from 'fc/components/AdvancedSearch/SearchTypes';
import { getValueDate, getValueMoney, getValue, getValueByKey, checkUserGroup } from 'fc/utils/utils';
import RequisicaoRemocao from '~/pages/requisicaoRemocao/requisicaoRemocaoModal';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import { ProgressSpinner } from 'primereact/progressspinner';
import { ConfirmDialog } from 'primereact/confirmdialog';
import { DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS } from 'fc/utils/date';

@observer
class CredenciadoListagemPage extends GenericIndexPage {
  constructor(props) {
    super(props, AccessPermission.credenciado);
    this.store = new CredenciadoIndexStore();
    this.store.setIdCredenciamento(props.match.params.idCredenciamento);
    this.state = {
      rowSelected: null,
      idRemove: undefined,
      showSuspenderDialog: false,
      showRequisicaoRemocao: false,
    };
    this._renderDialogRequisicaoRemocao = this._renderDialogRequisicaoRemocao.bind(this);
    this._handleRemocaoRequisicaoModal = this._handleRemocaoRequisicaoModal.bind(this);
    this._closeRequisicaoRemocaoModal = this._closeRequisicaoRemocaoModal.bind(this);
    this._updateDatatable = this._updateDatatable.bind(this);
  }

  componentDidMount() {
    this.store.carregaDadosBasicos(() => this.forceUpdate());
  }

  _updateDatatable() {
    this.store.reloadTableData(() => this.forceUpdate());
  }

  _renderDialogRequisicaoRemocao() {
    return (
      <RequisicaoRemocao
        history={this.props.history}
        processo={this.state.rowSelected}
        visibleDialog={this.state.showRequisicaoRemocao}
        closeDialog={this._closeRequisicaoRemocaoModal}
        updateTable={this._updateDatatable}
        tipoProcesso="credenciado"
      />
    );
  }

  _handleRemocao(credenciado) {
    if (credenciado?.hasContratoAssociado) {
      this.setState({ showSuspenderDialog: true, idRemove: credenciado?.id });
    } else {
      this._handleRemocaoRequisicaoModal(credenciado);
    }
  }

  confirmSuspender() {
    return (
      <ConfirmDialog
        blockScroll
        visible={this.state.showSuspenderDialog}
        message="O credenciado já possui contratos associados. Você realmente deseja suspender o credenciado selecionado?"
        header="Suspender credenciado"
        icon="pi pi-exclamation-triangle"
        acceptClassName="p-button-danger"
        accept={() => {
          this.store.suspender(this.state.idRemove, () => {
            this._updateDatatable();
          });
        }}
        onHide={() => this._closeSuspenderDialog()}
      />
    );
  }

  _closeSuspenderDialog() {
    this.setState({ showSuspenderDialog: false });
  }

  _handleRemocaoRequisicaoModal(credenciado) {
    this.setState({ showRequisicaoRemocao: true, rowSelected: credenciado });
  }

  _closeRequisicaoRemocaoModal() {
    this.setState({ showRequisicaoRemocao: false, rowSelected: undefined });
  }

  render() {
    const isAuditor = checkUserGroup('Auditor');

    const valorTotalLote = (credenciadoItems) => {
      let somaTotal = 0;
      credenciadoItems.forEach((item) => {
        somaTotal += item.quantidade * item.valorUnitario;
      });
      return getValueMoney(somaTotal);
    };

    const columns = [
      {
        field: 'numero',
        header: 'Número',
        body: ({ id }) => this.store.getNumeroById(id),
        sortable: true,
      },
      {
        field: 'dataCadastro',
        header: 'Data de Cadastro',
        body: ({ dataCadastro }) => getValueDate(dataCadastro, DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS),
        sortable: true,
      },
      {
        field: 'licitante.nome',
        header: 'Credenciado',
        body: ({ licitante }) => getValue(licitante?.nome),
        sortable: true,
      },
      {
        field: 'valor',
        header: 'Valor',
        body: ({ credenciadoItems }) => valorTotalLote(credenciadoItems),
        sortable: true,
      },
      {
        field: 'vigenciaInicial',
        header: 'Início do Credenciado',
        body: ({ vigenciaInicial }) => getValueDate(vigenciaInicial),
        sortable: true,
      },
      {
        field: 'vigenciaFinal',
        header: 'Final do Credenciado',
        body: ({ vigenciaFinal }) => getValueDate(vigenciaFinal),
        sortable: true,
      },
      {
        field: 'situacao',
        header: 'Situação',
        body: ({ situacao }) => getValueByKey(situacao, DadosEstaticosService.getSituacaoCredenciado()),
        sortable: true,
      },
      {
        style: { width: '180px' },
        body: (rowData) => {
          return (
            <div className="actions p-d-flex p-jc-end">
              {rowData.idRequisicaoModificacao ? (
                <PermissionProxy resourcePermissions={this.getReadPermission()}>
                  <FcButton
                    icon="pi pi-exclamation-triangle"
                    tooltip="Requisição de modificação pendente"
                    className="p-button-sm p-button-info p-mr-1"
                    onClick={() =>
                      this.pushUrlToHistory(
                        isAuditor
                          ? UrlRouter.auditoria.requisicaoModificacao.indexDetail.replace(
                              ':id',
                              rowData.idRequisicaoModificacao
                            )
                          : UrlRouter.administracao.requisicaoModificacao.indexDetail.replace(
                              ':id',
                              rowData.idRequisicaoModificacao
                            )
                      )
                    }
                  />
                </PermissionProxy>
              ) : (
                <div>
                  <PermissionProxy resourcePermissions={this.getReadPermission()}>
                    <FcButton
                      icon="pi pi-eye"
                      className="p-button-sm p-mr-2"
                      tooltip="Detalhes"
                      onClick={() => this.props.onDetail(rowData)}
                    />
                  </PermissionProxy>
                  <PermissionProxy resourcePermissions={this.getWritePermission()}>
                    <FcButton
                      icon="pi pi-pencil"
                      tooltip="Editar"
                      className="p-button-sm p-button-success p-mr-2"
                      disabled={rowData?.situacao !== 'VIGENTE' || rowData?.hasContratoAssociado}
                      onClick={() =>
                        this.pushUrlToHistory(
                          UrlRouter.cadastrosConsulta.credenciamento.credenciado.editar
                            .replace(':idCredenciamento', this.store.idCredenciamento)
                            .replace(':id', rowData.id)
                        )
                      }
                    />
                  </PermissionProxy>
                  <PermissionProxy resourcePermissions={this.getWritePermission()}>
                    <FcButton
                      icon="pi pi-trash"
                      tooltip="Remover"
                      className="p-button-sm p-button-danger"
                      disabled={rowData?.isSuspenso}
                      onClick={() => this._handleRemocao(rowData)}
                    />
                  </PermissionProxy>
                </div>
              )}
            </div>
          );
        },
      },
    ];

    const header = this.store.credenciamento && (
      <div className="table-header">
        <PermissionProxy resourcePermissions={this.getWritePermission()}>
          <FcButton
            className="p-button"
            label="Adicionar Credenciado"
            style={{ marginBottom: '5px', marginRight: '5px' }}
            icon={PrimeIcons.PLUS}
            onClick={() =>
              this.pushUrlToHistory(
                UrlRouter.cadastrosConsulta.credenciamento.credenciado.novo.replace(
                  ':idCredenciamento',
                  this.store.idCredenciamento
                )
              )
            }
            disabled={!this.store.permiteAdicao()}
            tooltip={!this.store.permiteAdicao() ? 'Vigência do credenciamento não permite novos credenciados' : ''}
          />
        </PermissionProxy>
      </div>
    );

    const { listKey, loading } = this.store;
    const { getDefaultTableProps } = this;

    const filterSuggest = [
      {
        id: '',
        field: 'credenciamento',
        operator: 'EQUAL_TO',
        value: this.store.idCredenciamento,
        formatted: '',
        fixed: true,
        invisible: true,
        completeParam: {
          field: 'credenciamento',
          label: 'Credenciamento',
          type: SearchTypes.TEXT,
        },
      },
      {
        id: '',
        field: 'status',
        operator: 'NOT_EQUAL_TO',
        value: 'REMOVIDA',
        formatted: '',
        fixed: true,
        invisible: true,
        completeParam: {
          field: 'status',
          label: 'Status',
          type: SearchTypes.ENUM,
          options: DadosEstaticosService.getStatusLicitacao(),
        },
      },
    ];

    return this.store.credenciamento ? (
      <>
        <AdvancedSearch
          searchParams={this.store.getAdvancedSearchParams()}
          store={this.store}
          searchFields={['licitante']}
          filterSuggest={filterSuggest}
        />
        <IndexDataTable
          columns={columns}
          value={listKey}
          header={header}
          loading={loading}
          {...getDefaultTableProps()}
        />
        {this._renderDialogRequisicaoRemocao()}
        {this.state.showSuspenderDialog && this.confirmSuspender(this.state.idRemove)}
      </>
    ) : (
      <div className="card page">
        <div className="p-d-inline p-d-flex align-items-center">
          <ProgressSpinner />
        </div>
      </div>
    );
  }
}

export default CredenciadoListagemPage;
