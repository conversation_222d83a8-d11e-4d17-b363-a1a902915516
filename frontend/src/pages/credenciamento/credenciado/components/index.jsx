import './style.scss';
import { observer } from 'mobx-react';
import React, { useState } from 'react';
import { Fieldset } from 'primereact/fieldset';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import LoteUnico from './loteUnico';
import Lote from './lote';
import { SelectButton } from 'primereact/selectbutton';
import { ConfirmDialog } from 'primereact/confirmdialog';
import PropTypes from 'prop-types';
import VencedorCredenciadoFormStore from '~/stores/credenciamento/credenciado/vencedorCredFormStore';
import Resumo from './resumo';

const VencedorCredenciado = observer((props) => {
  const store = props.store;
  const [tipoAdjudicacaoTemp, setTipoAdjudicacaoTemp] = useState(store.tipoAdjudicacao ?? 'ITEM');
  const [visibleConfirmDialogTipoAdjudicacao, setVisibleConfirmDialogTipoAdjudicacao] = useState(false);

  const renderLotesItens = () => {
    const { readOnly } = props;
    const decimalPlaces = store?.termoReferencia?.tresCasasDecimais ? 3 : 2;
    return (
      <>
        {store.termoReferencia && (
          <Fieldset className="p-col-12" legend={<b className="p-text mb-3">Fornecedores e respectivos itens</b>}>
            {!readOnly ? (
              <div className="flex w-full flex-column">
                <div className="p-col-12 flex flex-column">
                  {
                    <>
                      <b>
                        Adjudicado por: <span className="p-error"> *</span>
                      </b>
                      <SelectButton
                        options={DadosEstaticosService.getTipoAdjudicacao()}
                        optionLabel="text"
                        optionValue="value"
                        id="tipoAdjudicacao"
                        value={store.tipoAdjudicacao}
                        onChange={(e) => {
                          if (e?.target?.value && store.tipoAdjudicacao !== e.target.value) {
                            setTipoAdjudicacaoTemp(e.target.value);
                            if (store.vencedor != undefined) {
                              setVisibleConfirmDialogTipoAdjudicacao(true);
                            } else {
                              store.resetAll();
                              if (store.tipoAdjudicacao == 'LOTE') {
                                store.updateTipoAdjudicacao('ITEM');
                                setTipoAdjudicacaoTemp(null);
                              } else {
                                store.updateTipoAdjudicacao('LOTE');
                                setTipoAdjudicacaoTemp(null);
                                store.initializeLotesLicitante();
                              }
                            }
                          }
                        }}
                      />
                    </>
                  }
                  <ConfirmDialog
                    visible={visibleConfirmDialogTipoAdjudicacao}
                    onHide={() => {
                      setVisibleConfirmDialogTipoAdjudicacao(false);
                      setTipoAdjudicacaoTemp(null);
                    }}
                    message="Ao efetuar a substituição, o fornecedor e os lotes não selecionados serão perdidos. Você tem absoluta certeza disso?"
                    header="Confirmação"
                    icon="pi pi-exclamation-triangle"
                    accept={() => {
                      store.resetAll();
                      store.updateTipoAdjudicacao(tipoAdjudicacaoTemp);
                      setTipoAdjudicacaoTemp(null);
                      setVisibleConfirmDialogTipoAdjudicacao(false);

                      if (store.tipoAdjudicacao == 'LOTE') {
                        store.initializeLotesLicitante();
                      }
                    }}
                  />
                </div>
                <div className="p-col-12">
                  {store.tipoAdjudicacao === 'ITEM' ? (
                    <LoteUnico decimalPlaces={decimalPlaces} store={store} />
                  ) : (
                    store?.lotes?.map((lote, idx) => {
                      return (
                        <Lote lote={lote} collapsed={idx > 3} key={idx} decimalPlaces={decimalPlaces} store={store} />
                      );
                    })
                  )}
                </div>
              </div>
            ) : (
              renderResumo()
            )}
          </Fieldset>
        )}
      </>
    );
  };

  const renderResumo = () => {
    const { decimalPlaces } = props;
    return (
      <div className="flex flex-column">
        {store.itensGroupByVencedor?.map((v) => (
          <Resumo vencedor={v} decimalPlaces={decimalPlaces} />
        ))}
      </div>
    );
  };

  return <div className="p-grid">{renderLotesItens()}</div>;
});

VencedorCredenciado.propTypes = {
  readOnly: PropTypes.bool,
  store: PropTypes.objectOf(VencedorCredenciadoFormStore).isRequired,
};

export default VencedorCredenciado;
