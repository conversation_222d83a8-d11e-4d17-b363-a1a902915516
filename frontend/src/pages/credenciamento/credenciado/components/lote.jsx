import React, { useEffect, useState } from 'react';
import './style.scss';
import { observer } from 'mobx-react';
import classNames from 'classnames';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { InputText } from 'primereact/inputtext';
import FcButton from 'fc/components/FcButton';
import { Tag } from 'primereact/tag';
import { getValue, getValueMoney, isValueValid } from 'fc/utils/utils';
import Panel from './panel';
import Tooltip from 'fc/components/Tooltip';
import { Dialog } from 'primereact/dialog';
import { InputTextarea } from 'primereact/inputtextarea';
import PropTypes from 'prop-types';
import VencedorCredenciadoFormStore from '~/stores/credenciamento/credenciado/vencedorCredFormStore';
import { InputNumber } from 'primereact/inputnumber';

const Lote = observer((props) => {
  const [collapsed, setCollapsed] = useState(props.collapsed);
  const [editing, setEditing] = useState({});
  const [dialogVisible, setDialogVisible] = useState(false);

  const [obsItem, setObsItem] = useState('');
  const [itemSelected, setItemSelected] = useState({});
  const [itemError, setItemError] = useState({});
  const { store } = props;

  useEffect(() => {
    store.licitantes?.length > 0 && store.createOrUpdateVencedorLote(lote, store.licitantes[0]?.id);
  }, [store.licitantes]);

  const toggleLote = () => {
    setCollapsed(!collapsed);
  };

  const enableEdit = (item, field) => {
    if (!item.preenchido) {
      setEditing((oldState) => {
        if (!oldState[item.itemLote.id]) {
          oldState[item.itemLote.id] = {};
        }
        oldState[item.itemLote.id][field] = true;
        return { ...oldState };
      });
    }
  };

  const handleKey = (key, item, field) => {
    if (key === 'Enter') {
      setEditing((oldState) => {
        if (!oldState[item.itemLote.id]) {
          oldState[item.itemLote.id] = {};
        }
        oldState[item.itemLote.id][field] = true;
        return { ...oldState };
      });
    }
  };

  const _validateRequiredFields = (item) => {
    const requiredFields = ['marcaModelo', 'valorUnitario'];
    const itemId = item.itemLote.id;
    const result = { ...itemError };
    let hasError = false;
    requiredFields.forEach((f) => {
      if (!isValueValid(item[f])) {
        result[itemId] = { ...result[itemId] };
        result[itemId][f] = true;
        hasError = true;
      } else {
        result[itemId] = { ...result[itemId] };
        result[itemId][f] = false;
      }
    });
    setItemError(result);

    if (hasError) {
      const fieldsError = result[itemId];
      Object.keys(fieldsError).forEach((k) => {
        if (fieldsError[k]) {
          setEditing((oldState) => {
            if (!oldState[itemId]) {
              oldState[itemId] = {};
            }
            oldState[itemId][k] = true;
            return { ...oldState };
          });
        }
      });
    }
    return hasError;
  };

  const _renderDialogObs = () => {
    const { store } = props;
    return (
      <Dialog
        header="Observação"
        className="dialog-obs"
        visible={dialogVisible}
        onHide={() => setDialogVisible(false)}
        footer={
          <div>
            <FcButton
              label="Cancelar"
              icon="pi pi-times"
              className="p-button-danger"
              onClick={() => setDialogVisible(false)}
            />
            <FcButton
              label="Salvar"
              icon="pi pi-check"
              onClick={() => {
                store.updateAttributeItemLote(itemSelected.itemLote.id, 'observacao', obsItem);
                setDialogVisible(false);
              }}
            />
          </div>
        }
      >
        <div className="p-fluid p-formgrid p-grid">
          <InputTextarea
            onChange={(e) => setObsItem(e.target.value)}
            rows={4}
            value={obsItem}
            placeholder="Descreva a Observação"
          />
        </div>
      </Dialog>
    );
  };

  const _renderDataTable = () => {
    const { decimalPlaces, lote, store } = props;
    const loteCurrent = store.getLote(lote.id);
    const columns = [
      {
        header: 'Lote',
        style: { width: '15%' },
        body: (item) => store.getNomeLoteByitem(item),
      },
      {
        header: 'Código',
        body: (item) => getValue(item.itemLote?.materialDetalhamento?.codigo),
      },
      {
        header: 'Item',
        style: { width: '30%' },
        body: (item) =>
          `${item.itemLote.numero ? item.itemLote.numero + ' - ' : ''} ${getValue(
            item.itemLote.materialDetalhamento?.pdm?.nome
          )}`,
      },
      {
        header: 'Descrição Complementar',
        style: { width: '20%' },
        body: (item) => getValue(item.itemLote?.descricaoComplementar),
      },
      {
        header: 'Unidade de Medida',
        body: (item) => getValue(item.itemLote?.unidadeMedida?.textUnidadeMedida),
      },
      !loteCurrent.fracassado &&
        !loteCurrent.deserto && {
          header: (
            <span>
              Marca/Modelo<span className="p-error"> *</span>
            </span>
          ),
          body: (item) =>
            editing[item.itemLote.id]?.marcaModelo || !item.preenchido ? (
              <InputText
                className={itemError[item.itemLote.id]?.marcaModelo ? 'p-invalid p-error' : ''}
                onChange={(e) => store.updateAttributeItemLote(item.itemLote.id, 'marcaModelo', e)}
                placeholder="Informe a Marca/Modelo"
                value={store.getItem(item.itemLote.id).marcaModelo}
                onKeyDown={({ key }) => handleKey(key, item, 'marcaModelo')}
              />
            ) : (
              <span
                className={classNames({
                  pointer: !store.getItem(item.itemLote.id).preenchido,
                  'p-pr-5 p-pl-5': !store.getItem(item.itemLote.id).marcaModelo,
                })}
                onClick={() => !loteCurrent.fracassado && !loteCurrent.deserto && enableEdit(item, 'marcaModelo')}
              >
                {getValue(store.getItem(item.itemLote.id).marcaModelo)}
              </span>
            ),
        },
      !loteCurrent.fracassado &&
        !loteCurrent.deserto && {
          header: 'Valor Negociado',
          body: (item) =>
            !editing[item.itemLote.id]?.valorUnitario && !item.preenchido ? (
              <InputNumber
                value={store.getItem(item.itemLote.id).valorUnitario}
                onChange={(e) => handleInputValorNegociadoChange(e, item)}
                decimalPlaces={decimalPlaces}
                min={0}
                onKeyDown={({ key }) => handleKey(key, item, 'valorUnitario')}
                mode="currency"
                currency="BRL"
                locale="pt-BR"
              />
            ) : (
              <span
                className={classNames({ pointer: !store.getItem(item.itemLote.id).preenchido })}
                onClick={() => !loteCurrent.fracassado && !loteCurrent.deserto && enableEdit(item, 'valorUnitario')}
              >
                {getValueMoney(store.getItem(item.itemLote.id).valorUnitario, decimalPlaces)}
              </span>
            ),
        },
      {
        header: 'Status',
        body: (item) => {
          if (store.getItem(item.itemLote.id).preenchido) return <Tag severity="success" value="Preenchido" rounded />;
          else {
            return <Tag severity="danger" value="Pendente" rounded />;
          }
        },
      },
      !loteCurrent.fracassado &&
        !loteCurrent.deserto && {
          header: 'Ações',
          body: (item) => (
            <>
              {!store.getItem(item.itemLote.id).preenchido ? (
                <FcButton
                  type="button"
                  icon="pi pi-check"
                  className="p-button-text toggle-button"
                  onClick={() => {
                    if (!_validateRequiredFields(item)) {
                      setEditing({});
                      store.updateAttributeItemLote(item.itemLote.id, 'preenchido', true);
                      setItemError((oldState) => {
                        oldState[item.itemLote.id] = false;
                        return { ...oldState };
                      });
                    }
                  }}
                />
              ) : (
                <FcButton
                  type="button"
                  icon="pi pi-pencil"
                  className="p-button-text toggle-button"
                  onClick={() => {
                    setEditing((oldState) => {
                      oldState[item.itemLote.id] = {};
                      return { ...oldState };
                    });
                    store.updateAttributeItemLote(item.itemLote.id, 'preenchido', false);
                  }}
                />
              )}
              <FcButton
                type="button"
                icon="pi pi-comment"
                className="p-button-text toggle-button"
                onClick={() => {
                  setItemSelected(item);
                  setObsItem(item.observacao);
                  setEditing({});
                  setDialogVisible(true);
                }}
              />
            </>
          ),
        },
    ];

    return (
      <DataTable
        rowHover
        value={store.getItensByLote(lote)}
        emptyMessage="Nenhum item disponível"
        style={{ maxWidth: '100%' }}
        className="p-datatable-sm "
      >
        {columns.map((c, idx) => {
          return <Column key={`field-${idx}`} {...c} />;
        })}
      </DataTable>
    );
  };

  const handleInputValorNegociadoChange = (e, item) => {
    const value = e.value !== undefined && !isNaN(e.value) ? e.value : null;
    store.updateAttributeItem(item.itemLote?.id, item.licitante.id, 'valorUnitario', value);
  };

  const _renderHeader = () => {
    const { lote, store } = props;
    const loteCurrent = store.getLote(lote.id);
    return (
      <>
        <div className="flex-left pointer" onClick={() => toggleLote()}>
          <div className="info-lote">
            <span className="feedback">
              <i className={classNames('p-m-2', { 'pi pi-angle-right': collapsed, 'pi pi-angle-down': !collapsed })} />
            </span>
            <strong className="p-ml-2" onClick={() => toggleLote()}>
              {loteCurrent.nome}
            </strong>
          </div>
        </div>
        <div className="flex-right p-mr-2" style={{ alignItems: 'center' }}>
          <div className="feedback">
            {store.getStatusLote(lote) === 'check' && (
              <span className="circle check">
                <Tooltip value="Preenchido" sideOffset={10} delayDuration={100}>
                  <i className="pi pi-check" />
                </Tooltip>
              </span>
            )}
            {store.getStatusLote(lote) === 'warning' && (
              <span className="circle warning">
                <Tooltip value="Parcialmente Preenchido" sideOffset={10} delayDuration={100}>
                  <span className="default">!</span>
                </Tooltip>
              </span>
            )}
            {store.getStatusLote(lote) === 'error' && (
              <span className="circle error">
                <Tooltip value="Não Preenchido" sideOffset={10} delayDuration={100}>
                  <i className="pi pi-times" />
                </Tooltip>
              </span>
            )}
          </div>
        </div>
      </>
    );
  };

  const { lote } = props;
  let content = <>Erro ao renderizar o lote</>;
  if (lote) {
    content = (
      <div>
        <Panel
          className={`panel-${store.getStatusLote(lote)}`}
          header={_renderHeader()}
          content={_renderDataTable()}
          collapsed={collapsed}
        />
        {_renderDialogObs()}
      </div>
    );
  }
  return content;
});

Lote.propTypes = {
  lote: PropTypes.object,
  collapsed: PropTypes.bool,
  key: PropTypes.any,
  decimalPlaces: PropTypes.number,
  store: PropTypes.objectOf(VencedorCredenciadoFormStore).isRequired,
};

export default Lote;
