import UrlRouter from '~/constants/UrlRouter';
import AccessPermission from '~/constants/AccessPermission';
import { observer } from 'mobx-react';
import PropTypes from 'prop-types';
import GenericFormPage from 'fc/pages/GenericFormPage';
import CredenciadoFormStore from '~/stores/credenciamento/credenciado/formStore';
import AppBreadCrumb from 'fc/components/AppBreadCrumb';
import PermissionProxy from 'fc/components/PermissionProxy';
import FormField from 'fc/components/FormField';
import { showNotification } from 'fc/utils/utils';
import FcButton from 'fc/components/FcButton';
import AppStore from 'fc/stores/AppStore';
import { ProgressSpinner } from 'primereact/progressspinner';
import FcCalendar from 'fc/components/FcCalendar';
import SelectDialog from 'fc/components/SelectDialog';
import FornecedoresModal from '~/pages/dispensa/fornecedoresModal';
import VencedorCredenciado from './components';
import MultipleFileUploader from 'fc/components/MultipleFileUploader';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import moment from 'moment';

@observer
class CredenciadoFormPage extends GenericFormPage {
  constructor(props) {
    super(
      props,
      UrlRouter.cadastrosConsulta.credenciamento.credenciado.index.replace(':idCredenciamento', props.idCredenciamento),
      AccessPermission.credenciado
    );

    this.store = new CredenciadoFormStore();
  }

  componentDidMount() {
    const { id, idCredenciamento } = this.props;

    const initialize = () =>
      this.store.initialize(id, { status: 'PUBLICADA', tipoAdjudicacao: 'ITEM', isSuspenso: false }, () => {
        this.store.carregaCredenciamento(idCredenciamento, () => this.store.initializeVencedorStore());
        this.store.getVigenciasCredenciados(idCredenciamento);
      });

    if (id) {
      this.store.initializeArquivos(id, initialize);
    } else {
      initialize();
    }
  }

  renderActionButtons() {
    const hasWritePermission = !this.getWritePermission() || AppStore.hasPermission(this.getWritePermission());
    return (
      <div className="p-mt-10 form-actions">
        <div className="p-mt-2">
          <span className="p-mr-1 p-d-inline p-d-flex align-items-center">
            <FcButton
              label="Voltar"
              type="button"
              className="p-ml-auto p-button-secondary p-mr-2"
              onClick={() => this._goBack()}
              loading={this.store.loading}
            />
            {hasWritePermission && (
              <FcButton
                label="Salvar"
                type="button"
                loading={this.store.loading}
                onClick={this.submitFormData}
                disabled={!this.store.canSave}
              />
            )}
          </span>
        </div>
      </div>
    );
  }

  validarArquivos(callback) {
    if (this.store.validateSubmittedFiles(this.store.arquivos)) {
      this.store.validaArquivos(callback);
    } else {
      callback && callback();
    }
  }

  submitFormData() {
    const execution = () => {
      if (this.store.vencedorCredStore.hasItensNotFilleds) {
        this.canSave = false;
        showNotification('error', null, 'Itens selecionados precisam ser preenchidos.');
      } else if (!this.store.rules.hasError && this.store.validateSubmittedFiles(this.store.arquivos)) {
        this.store.validaArquivos(() => this.store.save(this._goBack, this.props.action));
      } else {
        showNotification('error', null, 'Verifique os campos do formulário!');
      }
    };

    if (this.state.submitted) {
      execution();
    } else {
      this.setState({ submitted: true }, execution);
    }
  }

  render() {
    const { getRule } = this.store;
    const { props, submitFormData } = this;
    const { submitted } = this.state;

    const breacrumbItems = [
      { label: 'Credenciamento', url: UrlRouter.cadastrosConsulta.credenciamento.index },
      {
        label: 'Credenciado',
        url: UrlRouter.cadastrosConsulta.credenciamento.credenciado.index.replace(
          ':idCredenciamento',
          props.idCredenciamento
        ),
      },
      { label: this.props.action === 'new' ? 'Novo' : 'Editar' },
    ];

    const columnsFornecedores = [
      {
        field: 'nome',
        header: 'Nome',
        sortable: true,
      },
      {
        field: 'endereco',
        header: 'Endereço',
        sortable: true,
      },
      {
        field: 'cpfCnpj',
        header: 'CPF/CNPJ',
        sortable: true,
      },
    ];

    let content;

    if (this.store.object) {
      content = (
        <>
          <AppBreadCrumb items={breacrumbItems} />
          <div className="card page form-action-buttons">
            <form onSubmit={submitFormData}>
              <div className="p-fluid p-formgrid p-grid">
                <FormField
                  columns={6}
                  attribute="licitante"
                  label="Fornecedor/Prestador Credenciado"
                  rule={getRule('licitante')}
                >
                  <SelectDialog
                    label="nome"
                    value={this.store.object.licitante}
                    indexStore={this.store.fornecedoresIndexStore}
                    headerDialog="Fornecedores"
                    emptyMessage="Selecione o Fornecedor"
                    nullMessage="Fornecedor sem Nome"
                    dialogColumns={columnsFornecedores}
                    searchFields={['nome', 'cpfCnpj']}
                    canCreate
                    radioMode
                    labelButtonCreate="Adicionar"
                    onChange={async (e) => {
                      this.store.updateAttribute('licitante', e);
                      this.store.vencedorCredStore.setLicitantes([e]);
                      await this.store.checkFornecedor();
                      this.forceUpdate();
                    }}
                    formPage={(props) => <FornecedoresModal action="new" closeMethod={props.closeMethod} />}
                  />
                </FormField>
                <FormField
                  columns={3}
                  attribute="vigenciaInicial"
                  label="Início do Credenciado"
                  rule={getRule('vigenciaInicial')}
                  submitted={submitted}
                >
                  <FcCalendar
                    value={this.getDateAttributeValue(this.store.object.vigenciaInicial)}
                    onChange={(e) => {
                      this.store.updateAttributeDate('vigenciaInicial', e);
                    }}
                    minDate={this.store.object?.vigenciaInicial ? moment(this.store.object?.vigenciaInicial)._d : null}
                    showIcon
                  />
                </FormField>

                <FormField
                  columns={3}
                  attribute="vigenciaFinal"
                  label="Final do Credenciado"
                  rule={getRule('vigenciaFinal')}
                  submitted={submitted}
                >
                  <FcCalendar
                    value={this.getDateAttributeValue(this.store.object.vigenciaFinal)}
                    onChange={(e) => {
                      this.store.updateAttributeDate('vigenciaFinal', e);
                      this.store.validateVigenciaFinal();
                    }}
                    minDate={this.store.object?.vigenciaInicial ? moment(this.store.object?.vigenciaInicial)._d : null}
                    maxDate={
                      this.store.credenciamento?.fimVigencia ? moment(this.store.credenciamento?.fimVigencia)._d : null
                    }
                    showIcon
                  />
                </FormField>
              </div>
              {this.renderActionButtons()}
            </form>
            {this.store.vencedorCredStore && (
              <div className="p-col-12">
                <VencedorCredenciado store={this.store.vencedorCredStore} />
              </div>
            )}
            <div className="p-col-12">
              <FormField
                columns={12}
                attribute="arquivos"
                label="Arquivos"
                submitted={submitted}
                rule={getRule('arquivos')}
              >
                <MultipleFileUploader
                  store={this.store.fileStore}
                  onChangeFiles={(files) => this.store.setArquivos(files)}
                  fileTypes={DadosEstaticosService.getTipoArquivoCredenciado()}
                />
              </FormField>
            </div>
          </div>
        </>
      );
    } else {
      content = (
        <div className="card page">
          <AppBreadCrumb items={breacrumbItems} />
          <div className="p-d-inline p-d-flex align-items-center">
            <ProgressSpinner />
          </div>
        </div>
      );
    }
    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        {content}
      </PermissionProxy>
    );
  }
}

CredenciadoFormPage.propTypes = {
  id: PropTypes.any,
  credenciamento: PropTypes.any,
  action: PropTypes.any,
  history: PropTypes.any,
};

export default CredenciadoFormPage;
