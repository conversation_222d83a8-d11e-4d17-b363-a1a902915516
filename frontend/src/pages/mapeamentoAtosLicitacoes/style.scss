.main-content {
  -webkit-line-clamp: 6;
  -webkit-box-orient: vertical;
}

.header-check {
  display: flex;
  width: 100%;
  padding: 10px;
  align-items: center;
  gap: 10px;
  flex-shrink: 0;
  border-bottom: 2px solid var(--green-500, #22c55e);
  background: var(--green-100, #caf1d8);
}

.header-warning {
  display: flex;
  width: 100%;
  padding: 10px;
  align-items: center;
  gap: 10px;
  flex-shrink: 0;
  border-bottom: 2px solid var(--yellow-500, #eab308);
  background: var(--yellow-100, #faedc4);
}

.header-error {
  display: flex;
  width: 100%;
  padding: 10px;
  align-items: center;
  gap: 10px;
  flex-shrink: 0;
  border-bottom: 2px solid var(--red-500, #ff3d32);
  background: var(--red-100, #ffd0ce);
}

.flex-right {
  display: flex;
  align-items: flex-end;
  justify-content: flex-end;
  align-self: center;
  margin-left: auto;

  div,
  strong {
    align-self: center;
  }
}

.processo-detail {
  display: flex;
  width: 100%;
  padding: 10px;
  flex-direction: column;
  align-items: flex-start;
  gap: 10px;
  border-radius: 6px;
  border: 1px solid var(--surface-300, #e0e0e0);
}

.feedback {
  margin-left: 10px;

  .circle {
    display: flex;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    justify-content: center;
    margin-right: 10px;

    i,
    span {
      align-self: center;
      color: white;
    }

    span {
      font-size: 20px;
    }
  }

  .check {
    background-color: #22c55e;
  }

  .warning {
    background-color: #eab308;
  }

  .error {
    background-color: #ff3d32;
  }
}

.default {
  cursor: default;
}

.button-arrow {
  cursor: pointer;
  border-radius: 6px;
  background: var(--blue-50, #f5f9ff);
}

.button-arrow:hover {
  background: var(--blue-500, #e5eefd);
}
