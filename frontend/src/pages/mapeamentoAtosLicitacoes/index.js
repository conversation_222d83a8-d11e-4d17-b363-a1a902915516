import React from 'react';
import { observer } from 'mobx-react';
import AppBreadCrumb from 'fc/components/AppBreadCrumb';
import GenericIndexPage from 'fc/pages/GenericIndexPage';
import PermissionProxy from 'fc/components/PermissionProxy';
import AccessPermission from '~/constants/AccessPermission';
import FcCloseableTabView from 'fc/components/FcCloseableTabView';
import MapeamentoAtosLicitacoesListagemPage from './listagem';
import MapeamentoAtosLicitacoesFormPage from './form';
import MapeamentoAtosLicitacoesIndexStore from '~/stores/mapeamentoAtosLicitacoes/indexStore';

@observer
class MapeamentoAtosLicitacoesIndexPage extends GenericIndexPage {
  constructor(props) {
    super(props, AccessPermission.mapeamentoAtosLicitacoes);
    this.store = new MapeamentoAtosLicitacoesIndexStore();

    this.onDetailAtoNotMapped = this.onDetailAtoNotMapped.bind(this);

    this.state = {
      data: [
        {
          id: 1,
          header: 'Atos',
          closeable: false,
          content: (
            <MapeamentoAtosLicitacoesListagemPage
              {...props}
              store={this.store}
              onDetail={(ato) => this.onDetail(ato)}
            />
          ),
        },
      ],
      count: 2,
      activeIndex: 0,
    };

    this.setActiveTabIndex = this.setActiveTabIndex.bind(this);
    this.onDetail = this.onDetail.bind(this);
    this.handleCloseTab = this.handleCloseTab.bind(this);
  }

  onDetail(ato) {
    const existingTab = this.state.data.find((tab) => tab.id === ato.id);
    if (existingTab) {
      this.setActiveTabIndex(existingTab);
    } else {
      const { id } = ato;
      const newAto = {
        id,
        header: `Ato de Licitação - ${id}`,
        closeable: true,
        content: (
          <MapeamentoAtosLicitacoesFormPage
            id={id}
            onDetailAtoNotMapped={this.onDetailAtoNotMapped}
            indexStore={this.store}
          />
        ),
      };
      this.setState({ data: [...this.state.data, newAto], count: this.state.count + 1 });
    }
  }

  onDetailAtoNotMapped(ato) {
    if (ato) {
      const existingTab = this.state.data.find((tab) => tab.id === ato.id);
      if (existingTab) {
        this.setActiveTabIndex(existingTab);
      } else {
        const { id } = ato;
        const newAto = {
          id,
          header: `Ato de Licitação - ${id}`,
          closeable: true,
          content: (
            <MapeamentoAtosLicitacoesFormPage
              id={id}
              onDetailAtoNotMapped={this.onDetailAtoNotMapped}
              indexStore={this.store}
            />
          ),
        };

        let atos = [...this.state.data];
        atos[this.state.activeTabIndex] = newAto;
        this.setState({ data: [...atos], count: this.state.count + 1 });
      }
    }
  }

  setActiveIndex(activeIndex) {
    this.setState({ activeIndex });
  }

  setActiveTabIndex(tab) {
    this.setState({ activeTabIndex: this.state.data.indexOf(tab) });
  }

  handleCloseTab(tab) {
    this.setState((prevState) => {
      return { data: prevState.data.filter((t) => t.id !== tab.id) };
    });
  }

  render() {
    const breacrumbItems = [{ label: 'Mapeamento de Atos de Licitações' }];
    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        <AppBreadCrumb items={breacrumbItems} />
        <div className="card page index-table">
          <FcCloseableTabView
            tabs={this.state.data}
            activeTabIndex={this.state.activeTabIndex}
            onChangeTab={this.setActiveTabIndex}
            onClose={this.handleCloseTab}
            fixedFirstTab
          />
        </div>
      </PermissionProxy>
    );
  }
}

MapeamentoAtosLicitacoesIndexPage.displayName = 'MapeamentoAtosLicitacoesIndexPage';

export default MapeamentoAtosLicitacoesIndexPage;
