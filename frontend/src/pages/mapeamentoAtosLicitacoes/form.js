import React from 'react';
import { observer } from 'mobx-react';
import PropTypes from 'prop-types';
import UrlRouter from '~/constants/UrlRouter';
import GenericFormPage from 'fc/pages/GenericFormPage';
import PermissionProxy from 'fc/components/PermissionProxy';
import AccessPermission from '~/constants/AccessPermission';
import MapeamentoAtosLicitacoesFormStore from '~/stores/mapeamentoAtosLicitacoes/formStore';
import { getValue, getValueDate, getValueMoney } from 'fc/utils/utils';
import {
  DATE_FORMAT,
  DATE_FORMAT_ONLY_YEAR,
  DATE_FORMAT_WITH_HOURS,
  DATE_PARSE_FORMAT,
  DATE_PARSE_FORMAT_WITH_HOURS,
} from 'fc/utils/date';
import { Divider } from 'primereact/divider';
import { InputText } from 'primereact/inputtext';
import './style.scss';
import SelectDialog from 'fc/components/SelectDialog';
import FcButton from 'fc/components/FcButton';
import AppStore from 'fc/stores/AppStore';
import { InputTextarea } from 'primereact/inputtextarea';
import { Link } from 'react-router-dom/cjs/react-router-dom.min';
import Tooltip from 'fc/components/Tooltip';
import { Skeleton } from 'primereact/skeleton';
import { ConfirmDialog } from 'primereact/confirmdialog';

@observer
class MapeamentoAtosLicitacoesFormPage extends GenericFormPage {
  constructor(props) {
    super(props, UrlRouter.auditoria.mapeamentoAtosLicitacoes.index, AccessPermission.mapeamentoAtosLicitacoes);
    this.store = new MapeamentoAtosLicitacoesFormStore();
    this.state = {
      editingFields: {
        ente: false,
        objeto: false,
        identificacao: false,
        orgao: false,
      },
      visibleWarningsDialog: false,
    };
  }

  submitFormData(e) {
    e && e.preventDefault();
    this.setState({ visibleWarningsDialog: false });
    const execution = () => {
      if (!this.store.rules.hasError) {
        const { id } = this.props;
        this.store.save(
          () => this.store.initialize(id),
          this.props.action,
          () => this.props.indexStore.load()
        );
      } else {
        showNotification('error', null, 'Verifique os campos do formulário!');
      }
    };

    if (this.state.submitted) {
      execution();
    } else {
      this.setState({ submitted: true }, execution);
    }
  }

  renderActionButtons() {
    const hasWritePermission = !this.getWritePermission() || AppStore.hasPermission(this.getWritePermission());
    return (
      <div className="p-mt-10 form-actions">
        <div className="p-mt-2">
          <span className="p-mr-1 p-d-inline p-d-flex align-items-center">
            {hasWritePermission && (
              <FcButton
                label="Confirmar"
                type="button"
                className="p-ml-auto p-mr-2"
                loading={this.store.loading}
                onClick={(e) => {
                  if (this.store.object?.matchDoeLicon?.licitacao) {
                    this.submitFormData(e);
                  } else {
                    this.setState({ visibleWarningsDialog: true });
                  }
                }}
              />
            )}
          </span>
        </div>
      </div>
    );
  }

  _renderWarningsDialog = () => {
    const hasWritePermission = !this.getWritePermission() || AppStore.hasPermission(this.getWritePermission());
    return (
      <ConfirmDialog
        blockScroll
        visible={this.state.visibleWarningsDialog}
        message="Deseja salvar as alterações sem a associação do processo?"
        header="Confirmação"
        icon="pi pi-exclamation-triangle"
        accept={(e) => {
          hasWritePermission && this.submitFormData(e);
        }}
        onHide={() => this.setState({ visibleWarningsDialog: false })}
      />
    );
  };

  _renderValue(
    label,
    value,
    col = 12,
    iconTitle = undefined,
    colorIconTitle = undefined,
    backgroundColorIconTitle = undefined,
    iconValue = undefined,
    colorIconValue = undefined,
    editable = false,
    field
  ) {
    return (
      <div className={`p-col-${col}`}>
        <div className="p-col-12 p-d-flex p-ai-baseline gap-2" style={{ fontWeight: 'bold' }}>
          {label}
          {iconTitle && this._renderIcon(iconTitle, colorIconTitle, backgroundColorIconTitle, editable, field)}
        </div>
        <div className="p-col-12 p-d-flex p-ai-baseline gap-2">
          {iconValue && this._renderIcon(iconValue, colorIconValue, editable)}
          {this._renderEditMode(field, value)}
        </div>
      </div>
    );
  }

  _getStatus() {
    let title = '';
    let status = '';
    const licitacaoUpdated =
      this.store.object?.matchDoeLicon?.licitacao?.id !== this.store.objectCopy?.matchDoeLicon?.licitacao?.id;

    if (licitacaoUpdated) {
      title = 'Ato mapeado pelo usuário, confirmação ainda necessária.';
      status = 'warning';
    } else if (this.store.object?.matchDoeLicon?.associacaoConfirmada) {
      title = 'Ato mapeado e confirmado.';
      status = 'check';
    } else if (this.store.object?.matchDoeLicon?.id) {
      title = 'Ato mapeado automaticamente pelo robô, confirmação necessária.';
      status = 'warning';
    } else {
      title = 'Ato não mapeado, associação necessária.';
      status = 'error';
    }

    return [status, title];
  }

  _renderConfirmEdit(field) {
    const handleClick = () => {
      const editingFields = { ...this.state.editingFields, ...{ [field]: !this.state.editingFields[field] } };
      this.setState({ editingFields });
      this.store.updateCopy(field);
    };

    return (
      <i
        className="pi pi-check"
        style={{ color: 'green', backgroundColor: '#E8F5E9', cursor: 'pointer', borderRadius: '50%', padding: '2px' }}
        onClick={handleClick}
      />
    );
  }

  _renderEditMode(field, value) {
    const { updateAttribute } = this.store;
    let content = <span style={{ width: '100%', wordWrap: 'break-word' }}>{value ?? '-'}</span>;
    if (this.state.editingFields[field]) {
      if (field === 'objeto') {
        content = (
          <>
            <InputTextarea
              placeholder={`Informe um valor para ${field}`}
              value={this.store.object[field]}
              onChange={(e) => updateAttribute(field, e)}
              autoResize
            />
            {this._renderConfirmEdit(field)}
          </>
        );
      } else {
        content = (
          <>
            <InputText
              onChange={(e) => updateAttribute(field, e)}
              placeholder={`Informe um valor para ${field}`}
              value={this.store.object[field]}
            />
            {this._renderConfirmEdit(field)}
          </>
        );
      }
    }
    return content;
  }

  _renderTitle(value, color = '#9E9E9E') {
    return <span style={{ color }}>{value}</span>;
  }

  _renderIcon(icon, color, backgroundColor, editable, field) {
    const handleClick = () => {
      const editingFields = { ...this.state.editingFields, ...{ [field]: !this.state.editingFields[field] } };
      if (this.state.editingFields[field]) {
        this.store.resetObject(field);
      }
      this.setState({ editingFields });
    };
    const editing = this.state.editingFields[field];
    return (
      <i
        className={!editing ? icon : 'pi pi-times'}
        style={
          editable
            ? {
                color: !editing ? color : '#FF3D32',
                backgroundColor: !editing ? backgroundColor : '#FFEBEE',
                cursor: 'pointer',
                borderRadius: '50%',
                padding: '2px',
              }
            : { color, backgroundColor }
        }
        onClick={handleClick}
      />
    );
  }

  _getTaggedValuesFromText(text) {
    return <span dangerouslySetInnerHTML={{ __html: text }} />;
  }

  _renderTitleLicitacao() {
    const licitacao = this.store.object?.matchDoeLicon?.licitacao;
    if (licitacao) {
      const title = `${licitacao.numero}/${licitacao.ano}`;
      return licitacao.entidade ? title + ` - ${licitacao.entidade.nome}` : title;
    }
  }

  _renderHeader(status, message) {
    return (
      <div className={`header-${status}`}>
        <div className="flex-left p-mr-2">
          <div className="feedback">
            {status?.includes('check') && (
              <span className={`circle check`}>
                <i className="pi pi-check" />
              </span>
            )}
            {status?.includes('warning') && (
              <span className="circle warning">
                <span className="default">!</span>
              </span>
            )}
            {status?.includes('error') && (
              <span className="circle error">
                <i className="pi pi-times" />
              </span>
            )}
          </div>
        </div>
        <div className="flex-center">
          <strong className="p-ml-2">{message}</strong>
        </div>
      </div>
    );
  }

  _renderValueDetail(label, value, col = 12, type = 'value', url = '#') {
    return (
      <div
        style={{ fontSize: '1.1rem' }}
        className={`details-set details-display p-col-12 p-md-${col} p-sm-${col >= 6 ? col : 6}`}
      >
        <div
          style={{ color: 'rgb(0 0 0 / 50%)', paddingLeft: 0 }}
          className="xl:col-2 lg:col-3 md:col-4 sm:col-4 details-attributte drawer-content-label"
        >
          {label}
        </div>

        {type == 'value' && (
          <div style={{ paddingLeft: 0 }} className="xl:col-10 lg:col-9 md:col-8 sm:col-8 details-value p-text-justify">
            {value ?? '-'}
          </div>
        )}

        {type == 'link' && (
          <div
            style={{ paddingLeft: 0 }}
            className={`xl:col-10 lg:col-9 md:col-8 sm:col-8 details-value p-text-justify`}
          >
            {
              <Link target="_blank" rel="noreferrer" style={{ color: '#3F51B5' }} to={url}>
                <Tooltip value="Abrir detalhes do processo" delayDuration={100}>
                  <u>{value}</u>
                </Tooltip>
              </Link>
            }
          </div>
        )}
      </div>
    );
  }

  _renderDetailsLicitacao() {
    const licitacao = this.store.object?.matchDoeLicon?.licitacao;
    if (licitacao) {
      return (
        <div className="p-fluid p-form">
          {this._renderValueDetail(
            'Título',
            this._renderTitleLicitacao(licitacao),
            12,
            'link',
            UrlRouter.cadastrosConsulta.licitacao.detalhe.replace(':id', licitacao.id)
          )}
          {this._renderValueDetail(
            'Valor Total Estimado',
            getValueMoney(licitacao.valorEstimado, licitacao?.termoReferencia?.tresCasasDecimais ? 3 : 2)
          )}
          {this._renderValueDetail('Objeto', licitacao.objeto)}
          {this._renderValueDetail(
            'Data de Abertura',
            getValueDate(licitacao.dataAbertura, DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS)
          )}
        </div>
      );
    }
  }

  render() {
    const { submitFormData } = this;

    const columnsLicitacao = [
      {
        field: 'numero',
        header: 'Número da Licitação',
        sortable: true,
      },
      {
        field: 'ano',
        header: 'Ano',
        body: ({ ano }) => getValueDate(ano, DATE_FORMAT_ONLY_YEAR),
        sortable: true,
      },
      {
        field: 'entidade',
        header: 'Entidade',
        body: ({ entidade }) => getValue(entidade?.nome),
        sortable: true,
      },
      {
        field: 'dataAbertura',
        header: 'Data de Abertura',
        sortable: true,
        body: ({ dataAbertura }) => getValueDate(dataAbertura, DATE_FORMAT, DATE_PARSE_FORMAT),
      },
      {
        field: 'valorEstimado',
        header: 'Valor Estimado',
        body: ({ valorEstimado }) => getValueMoney(valorEstimado),
      },
    ];

    let content;
    if (!this.store.loading && this.store.object) {
      const object = this.store.object;
      const status = this._getStatus();

      content = (
        <>
          <div className="form-action-buttons">
            <form onSubmit={submitFormData}>
              <div className="p-fluid p-formgrid p-grid gap-3">
                <div className="w-full flex justify-content-center">
                  {this._renderTitle('Navegar entre atos não mapeados')}
                </div>
                <div className="w-full flex justify-content-center gap-3">
                  <div
                    className="flex align-items-center justify-content-center px-2 gap-2 button-arrow"
                    onClick={() => this.store.getAtoNaoMapeado('anterior', this.props.onDetailAtoNotMapped)}
                  >
                    <i
                      className="pi pi-angle-left"
                      style={{
                        color: '#4655A7',
                        fontSize: '0.8em',
                      }}
                    />
                    <span>{this._renderTitle('Anterior', '#4655A7')}</span>
                  </div>
                  <div
                    className="flex align-items-center justify-content-center px-2 gap-2 button-arrow"
                    onClick={() => this.store.getAtoNaoMapeado('proximo', this.props.onDetailAtoNotMapped)}
                  >
                    <span>{this._renderTitle('Próximo', '#4655A7')}</span>
                    <i
                      className="pi pi-angle-right"
                      style={{
                        color: '#4655A7',
                        fontSize: '0.8em',
                      }}
                    />
                  </div>
                </div>
                <Divider align="left">
                  <b style={{ fontSize: '16px' }}>{`Ato de Licitação no DOE-AC - ${object.id}`}</b>
                </Divider>
                <div className="p-grid p-col-12">
                  {this._renderValue(
                    this._renderTitle('Data da Publicação'),
                    getValueDate(object.diarioOficial.dataPublicacao, DATE_FORMAT, DATE_PARSE_FORMAT),
                    3,
                    '',
                    '',
                    '',
                    'pi pi-calendar',
                    '#EAB308'
                  )}
                  {this._renderValue(
                    this._renderTitle('Órgão/Entidade'),
                    getValue(object.ente),
                    3,
                    'pi pi-pencil',
                    '#609AF8',
                    '#F5F9FF',
                    'pi pi-user',
                    '#14B8A6',
                    true,
                    'ente'
                  )}
                  {this._renderValue(
                    this._renderTitle('Identificação'),
                    getValue(object.identificacao),
                    3,
                    'pi pi-pencil',
                    '#609AF8',
                    '#F5F9FF',
                    'pi pi-tag',
                    '#4CD07D',
                    true,
                    'identificacao'
                  )}
                </div>
                <div className="p-grid p-col-12 mt-2">
                  {this._renderValue(
                    this._renderTitle('Objeto'),
                    getValue(object.objeto),
                    12,
                    'pi pi-pencil',
                    '#609AF8',
                    '#F5F9FF',
                    'pi pi-file',
                    '#326FD1',
                    true,
                    'objeto'
                  )}
                </div>
                <div className="p-grid p-col-12 p-mt-2 justify-content-end text-justify">
                  {this._renderValue(
                    this._renderTitle('Texto'),
                    this._getTaggedValuesFromText(this.store.formattedText),
                    12
                  )}
                </div>
                <Divider align="left">
                  <b style={{ fontSize: '16px' }}>Processo Mapeado</b>
                </Divider>
                {this._renderHeader(status[0], status[1])}
                <div className="w-full flex justify-content-end">
                  <SelectDialog
                    value={this.store.object?.matchDoeLicon?.licitacao}
                    indexStore={this.store.licitacaoIndexStore}
                    onChange={(e) => {
                      this.store.updateLicitacao(e);
                    }}
                    headerDialog="Nº da Licitação"
                    dialogColumns={columnsLicitacao}
                    searchFields={['numero', 'ano']}
                    disabledComponent={this.store.enableReqMod}
                    showInput={false}
                    iconButtonDialog="pi pi-pencil"
                    labelButtonDialog="Associar processo"
                  />
                </div>
              </div>
              {this._renderDetailsLicitacao()}
              {this.renderActionButtons()}
              {this._renderWarningsDialog()}
            </form>
          </div>
        </>
      );
    } else {
      content = (
        <div className="card page">
          <div className="col-12">
            <div className="flex gap-5 justify-content-center">
              <Skeleton width="100%" />
              <Skeleton width="100%" />
              <Skeleton width="100%" />
              <Skeleton width="100%" />
            </div>
            <Skeleton width="20rem" height="4rem" className="my-3" />
            <Skeleton width="100%" height="4rem" className="my-3" />
            <Skeleton width="100%" className="my-3" />
            <Skeleton width="100%" className="my-3" />
            <Skeleton width="100%" className="my-3" />
            <Skeleton width="100%" className="my-3" />
            <Skeleton width="100%" className="my-3" />
          </div>
        </div>
      );
    }
    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        {content}
      </PermissionProxy>
    );
  }
}
MapeamentoAtosLicitacoesFormPage.propTypes = {
  id: PropTypes.any,
  indexStore: PropTypes.any.isRequired,
};

export default MapeamentoAtosLicitacoesFormPage;
