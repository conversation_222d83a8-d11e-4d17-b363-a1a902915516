import React from 'react';
import { observer } from 'mobx-react';
import IndexDataTable from 'fc/components/IndexDataTable';
import FcButton from 'fc/components/FcButton';
import AppBreadCrumb from 'fc/components/AppBreadCrumb';
import GenericIndexPage from 'fc/pages/GenericIndexPage';
import AdvancedSearch from 'fc/components/AdvancedSearch';
import PermissionProxy from 'fc/components/PermissionProxy';
import AccessPermission from '~/constants/AccessPermission';
import HistoricoProcessosAuditoriaIndexStore from '~/stores/historicoProcessosAuditoria/indexStore';
import { Dialog } from 'primereact/dialog';
import { DataTable } from 'primereact/datatable';
import { DATE_FORMAT, DATE_PARSE_FORMAT } from 'fc/utils/date';
import { getValueDate, getValueElipsis, getValueMoney } from 'fc/utils/utils';

@observer
class HistoricoProcessosAuditoriaIndexPage extends GenericIndexPage {
  constructor(props) {
    super(props, AccessPermission.historicoProcessos);

    this.state = { visibleDialogHistoricoProcesso: false };
    this.store = new HistoricoProcessosAuditoriaIndexStore();
  }

  _toggleDialogHistoricoProcesso() {
    this.setState((oldState) => ({ visibleDialogHistoricoProcesso: !oldState.visibleDialogHistoricoProcesso }));
  }

  renderFooter() {
    return (
      <div>
        <FcButton
          label="Fechar"
          icon="pi pi-times"
          onClick={() => this._toggleDialogHistoricoProcesso()}
          className="p-button-text"
        />
      </div>
    );
  }

  renderDialogHistoricoAlteracaoProcesso() {
    const columnsProcesso = [
      {
        style: { width: '35%' },
        field: 'idAuditoria',
        header: 'Identificador',
      },
      {
        style: { width: '35%' },
        field: 'key',
        header: 'Versão',
      },
      {
        style: { width: '35%' },
        field: 'valorEstimado',
        header: 'Valor Estimado',
        body: ({ valorEstimado }) => getValueMoney(valorEstimado),
      },
    ];
    return (
      <Dialog
        header="Histórico de Alterações"
        visible={this.state.visibleDialogHistoricoProcesso}
        style={{ width: '50%' }}
        footer={this.renderFooter()}
        onHide={() => this._toggleDialogHistoricoProcesso()}
      >
        <div className="p-fluid">
          <DataTable
            rowHover
            value={this.store.listHistoricoProcessosComkey}
            emptyMessage="Nenhuma alteração de valor"
            paginator
            rows={10}
            loading={this.store.loading}
          >
            {this.renderColumns(columnsProcesso)}
          </DataTable>
        </div>
      </Dialog>
    );
  }

  render() {
    const columns = [
      {
        field: 'numero',
        header: 'Número',
        sortable: true,
      },
      {
        field: 'dataProcesso',
        header: 'Data do Processo',
        body: ({ dataProcesso }) => getValueDate(dataProcesso, DATE_FORMAT, DATE_PARSE_FORMAT),
        sortable: true,
      },
      {
        field: 'objeto',
        header: 'Objeto',
        sortable: true,
        body: (rowData) => {
          return getValueElipsis(rowData.objeto, 80);
        },
      },

      {
        style: { width: '110px' },
        body: (rowData) => {
          return (
            <div className="actions">
              <PermissionProxy resourcePermissions={this.getReadPermission()}>
                <FcButton
                  icon="pi pi-eye"
                  className="p-button-sm p-button-success p-mr-2"
                  onClick={() =>
                    this.store.loadHistoricoProcesso(rowData.id, rowData.tipoProcesso, () =>
                      this._toggleDialogHistoricoProcesso()
                    )
                  }
                />
              </PermissionProxy>
            </div>
          );
        },
      },
    ];

    const { listKey, loading } = this.store;
    const { getDefaultTableProps } = this;

    const breacrumbItems = [{ label: 'Histórico de Processos' }];

    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        <AppBreadCrumb items={breacrumbItems} />
        <div className="card page index-table">
          <AdvancedSearch
            searchParams={this.store.getAdvancedSearchParams()}
            store={this.store}
            searchFields={['nome']}
            filterSuggest={this.store.getFilterSuggest()}
          />
          <IndexDataTable columns={columns} value={listKey} loading={loading} {...getDefaultTableProps()} />
        </div>
        {this.state.visibleDialogHistoricoProcesso && this.renderDialogHistoricoAlteracaoProcesso()}
      </PermissionProxy>
    );
  }
}

HistoricoProcessosAuditoriaIndexPage.displayName = 'HistoricoProcessosAuditoriaIndexPage';

export default HistoricoProcessosAuditoriaIndexPage;
