import React from 'react';
import { observer } from 'mobx-react';
import GerenciamentoTermoIndexStore from '~/stores/gerenciamentoTermo/indexStore';
import AppBreadCrumb from 'fc/components/AppBreadCrumb';
import GenericIndexPage from 'fc/pages/GenericIndexPage';
import AccessPermission from '~/constants/AccessPermission';
import PermissionProxy from 'fc/components/PermissionProxy';
import AppStore from 'fc/stores/AppStore';
import TermoReferenciaDetails from './indexDetail';
import GerenciamentoTermoListagemPage from './listagem';
import { getValue } from 'fc/utils/utils';
import FcCloseableTabView from 'fc/components/FcCloseableTabView';

@observer
class GerenciamentoTermoIndexPage extends GenericIndexPage {
  constructor(props) {
    super(props, AccessPermission.gerenciamentoTermo);
    this.store = new GerenciamentoTermoIndexStore();
    this.store.loadSecoes(() => this.forceUpdate());

    this.state = {
      data: [
        {
          id: 1,
          header: 'Listagem',
          content: (
            <GerenciamentoTermoListagemPage {...props} onDetail={(termoReferencia) => this.onDetail(termoReferencia)} />
          ),
        },
      ],
      count: 2,
      activeTabIndex: 0,
    };

    this.setActiveTabIndex = this.setActiveTabIndex.bind(this);
    this.handleCloseTab = this.handleCloseTab.bind(this);
    this.onDetail = this.onDetail.bind(this);
  }

  componentDidMount() {
    !AppStore.getContextEntity() && this.store.load();
  }

  onDetail(termoReferencia) {
    const existingTab = this.state.data.find((tab) => tab.idTermo === termoReferencia.id);
    if (existingTab) {
      this.setActiveTabIndex(existingTab);
    } else {
      const newTermoReferencia = {
        id: this.state.count,
        idTermo: termoReferencia.id,
        header: getValue(termoReferencia?.identificadorProcesso || termoReferencia.id),
        closeable: true,
        content: (
          <TermoReferenciaDetails
            id={termoReferencia.id}
            store={this.store}
            formatList={this.formatList}
            history={this.props.history}
          />
        ),
      };
      this.setState({ data: [...this.state.data, newTermoReferencia], count: this.state.count + 1 });
    }
  }

  setActiveTabIndex(tab) {
    this.setState({ activeTabIndex: this.state.data.indexOf(tab) });
  }

  handleCloseTab(tab) {
    this.setState((prevState) => {
      return { data: prevState.data.filter((t) => t.id !== tab.id) };
    });
  }

  render() {
    const breacrumbItems = [{ label: 'Gerenciamento de Termos' }];
    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        <AppBreadCrumb items={breacrumbItems} />
        <div className="card page index-table">
          <FcCloseableTabView
            tabs={this.state.data}
            activeTabIndex={this.state.activeTabIndex}
            onChangeTab={this.setActiveTabIndex}
            onClose={this.handleCloseTab}
            fixedFirstTab
          />
        </div>
      </PermissionProxy>
    );
  }
}

GerenciamentoTermoIndexPage.displayName = 'GerenciamentoTermoIndexPage';

export default GerenciamentoTermoIndexPage;
