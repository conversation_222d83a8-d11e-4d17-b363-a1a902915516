import React from 'react';
import { observer } from 'mobx-react';
import PropTypes from 'prop-types';
import GerenciamentoTermoFormStore from '../../stores/gerenciamentoTermo/formStore';
import UrlRouter from '../../constants/UrlRouter';
import GenericFormPage from 'fc/pages/GenericFormPage';
import AppBreadCrumb from 'fc/components/AppBreadCrumb';
import { isValueValid, showNotification } from 'fc/utils/utils';
import AccessPermission from '~/constants/AccessPermission';
import PermissionProxy from 'fc/components/PermissionProxy';
import AppStore from 'fc/stores/AppStore';
import { Dialog } from 'primereact/dialog';
import FcButton from 'fc/components/FcButton';
import moment from 'moment';
import 'moment/locale/pt-br';
import { Steps } from 'primereact/steps';
import AbaSecoesFormPage from './abas/formAbaSecoes';
import AbaItensFormPage from './abas/formAbaItens';
import FormAbaDados from './abas/formAbaDados';
import RequisicaoModificacaoFormStore from '~/stores/requisicaoModificacao/formStore';
import FormField from 'fc/components/FormField';
import FcInputTextarea from 'fc/components/FcInputTextarea';
import { Message } from 'primereact/message';

@observer
class GerenciamentoTermoFormPage extends GenericFormPage {
  constructor(props) {
    super(props, UrlRouter.termoReferencia.gerenciamentoTermos.index, AccessPermission.gerenciamentoTermo);
    this.store = new GerenciamentoTermoFormStore();
    this.reqModificacaoStore = new RequisicaoModificacaoFormStore();

    this.state = {
      saveClock: 0,
      maxClock: 30,
      lastSave: undefined,
      showSaveDialog: false,
      showDialogSecao: false,
      activeIndexForm: 0,
      visibleDialogReqMod: false,
    };

    this._toggleDialogReqMod = this._toggleDialogReqMod.bind(this);
    this.hasWritePermission = this.hasWritePermission.bind(this);
    this.switchNewSecaoDialogVisibility = this.switchNewSecaoDialogVisibility.bind(this);
    this.updateLastSave = this.updateLastSave.bind(this);
    this.startAutomaticSave = this.startAutomaticSave.bind(this);
  }

  componentDidMount() {
    const { id } = this.props;
    this.store.initialize(
      id,
      {
        obraEngenharia: false,
        isFinalizado: false,
        lotes: [],
        secoesCriadas: [],
        srp: true,
        tresCasasDecimais: false,
        arquivosTemporarios: [],
        formaPreenchimentoSecao: 'IMPORTACAO_ARQUIVO',
        identificadorProcesso: this.props.identificadorTermo,
        modelo: false,
        tipo: 'ITENS',
      },
      () => {
        this.store.postInitialize(() => {
          this.props.step && this.setState({ activeIndexForm: this.props.step });
        });
        this.store.loadArvore();
        this.store.carregarArquivosObrigatorios();
      }
    );

    if (!id) {
      this.store.carregarArquivosObrigatorios();
    }
    this.store.setEntidadeContexto(AppStore.getContextEntity()?.id);
    moment.locale('pt-br');
  }

  _isReqMod() {
    return !!(
      this.store.loadedObject &&
      this.store.loadedObject.isFinalizado &&
      this.store.loadedObject.canCreateReqModificacao
    );
  }

  _toggleDialogReqMod() {
    this.setState((oldState) => ({ visibleDialogReqMod: !oldState.visibleDialogReqMod }));
  }

  switchNewSecaoDialogVisibility() {
    this.setState({ showDialogSecao: !this.state.showDialogSecao });
    this.store.setTituloSecao('', true);
  }

  hasWritePermission() {
    return !this.getWritePermission() || AppStore.hasPermission(this.getWritePermission());
  }

  startAutomaticSave() {
    if (this.hasWritePermission() && !this.store.isRequisicaoModificacao()) {
      let clock = this.state.saveClock;
      this.interval = setInterval(() => {
        clock += 1;

        if (clock >= this.state.maxClock) {
          if (
            this.store.houveModificacao &&
            this.store.validateSubmittedFiles(this.store.object?.arquivosTemporarios, false)
          ) {
            if (this.store.object.entidade) {
              this.store.automaticSave(() => this.setState({ lastSave: moment() }));
            } else {
              showNotification('info', 'Salvamento automático', 'A entidade deve ser preenchida!');
            }
          }

          clock = 0;
        }

        this.setState({
          saveClock: clock,
        });
      }, 1000);
    }
  }

  componentWillUnmount() {
    if (this.interval) {
      clearInterval(this.interval);
      this.interval = null;
    }
  }

  updateLastSave(currentDate) {
    this.setState({ lastSave: currentDate });
  }

  _toggleDialog() {
    this.setState((oldState) => ({ showSaveDialog: !oldState.showSaveDialog }));
  }

  renderDialogFooter() {
    return (
      <div>
        <FcButton label="Cancelar" icon="pi pi-times" onClick={() => this._toggleDialog()} className="p-button-text" />
        <FcButton
          label="Sim"
          icon="pi pi-check"
          onClick={() => {
            this._toggleDialog();
            if (this._isReqMod()) {
              this._toggleDialogReqMod();
            } else {
              super.submitFormData();
            }
          }}
          autoFocus
        />
      </div>
    );
  }

  renderSaveConfirmDialog() {
    const emptyMandatoryFields = this.store.validateMandatorySections();
    return (
      <Dialog
        visible={this.state.showSaveDialog}
        footer={this.renderDialogFooter()}
        header="Salvar Termo de Referência"
        closable={false}
        maximizable={false}
        draggable={false}
      >
        Este termo possui as seguintes seções obrigatórias vazias:
        <ul>
          {emptyMandatoryFields.map((field) => (
            <li>
              <b>{field}</b>
            </li>
          ))}
        </ul>
        Deseja realmente prosseguir?
      </Dialog>
    );
  }

  disabledAvancar(step) {
    const activeIndexForm = step ?? this.state.activeIndexForm;
    return (
      (activeIndexForm === 0 && !this.store.validaDadosBasicos()) ||
      (activeIndexForm === 1 &&
        this.store.object?.formaPreenchimentoSecao === 'IMPORTACAO_ARQUIVO' &&
        !(this.store.fileStore?.uploadedFiles?.length > 0)) ||
      (activeIndexForm === 2 && !this.store.validaDadosBasicos())
    );
  }

  isValidSections(e) {
    e && e.preventDefault();
    const { validateMandatorySections } = this.store;
    const emptyMandatoryFields = validateMandatorySections();
    if (emptyMandatoryFields.length > 0) {
      this._toggleDialog();
      return false;
    }
    return true;
  }

  renderActionButtons() {
    const hasWritePermission = !this.getWritePermission() || AppStore.hasPermission(this.getWritePermission());

    const messagesError = [
      'É necessário preencher todos os campos obrigatórios antes de avançar para a próxima etapa.',
      'É necessário adicionar um arquivo antes de avançar para a próxima etapa.',
    ];

    return (
      <div className="form-actions-termo-referencia">
        <div className="p-mt-2">
          <span className="p-d-inline p-d-flex align-items-center">
            <b className="p-ml-auto">
              {this.state.lastSave && `Informações salvas pela última vez às ${this.state.lastSave.format('LTS')}`}
            </b>
            {this.disabledAvancar() && (
              <Message className="p-ml-3 p-mr-2" severity="warn" text={messagesError[this.state.activeIndexForm]} />
            )}
            {this.state.activeIndexForm === 1 &&
              this.store.object?.formaPreenchimentoSecao === 'PREENCHIMENTO_MANUAL' && (
                <FcButton
                  className="p-ml-2"
                  label="Adicionar seção"
                  type="button"
                  onClick={() => this.switchNewSecaoDialogVisibility()}
                  loading={this.store.loading}
                />
              )}
            <FcButton
              label="Voltar"
              type="button"
              className="p-ml-2 p-button-secondary p-mr-2"
              onClick={() => this.onChangeStep(this.state.activeIndexForm - 1)}
              loading={this.store.loading}
            />

            {this.state.activeIndexForm < 2 && (
              <FcButton
                label="Avançar"
                type="button"
                disabled={this.disabledAvancar()}
                onClick={() => this.onChangeStep(this.state.activeIndexForm + 1)}
              />
            )}
            {hasWritePermission && this.state.activeIndexForm == 2 && (
              <>
                {this._isReqMod() ? (
                  <FcButton
                    label="Enviar Requisição"
                    type="button"
                    onClick={() => this._toggleDialogReqMod()}
                    loading={this.reqModificacaoStore.loading}
                  />
                ) : (
                  <FcButton label="Salvar" type="submit" loading={this.store.loading} />
                )}
              </>
            )}
          </span>
        </div>
      </div>
    );
  }

  renderDialogReqModFooter() {
    return (
      <div>
        <FcButton
          label="Cancelar"
          icon="pi pi-times"
          onClick={() => this._toggleDialogReqMod()}
          className="p-button-text"
        />
        <FcButton
          label="Enviar Requisição"
          icon="pi pi-check"
          loading={this.reqModificacaoStore.loading}
          onClick={() => {
            if (this.reqModificacaoStore.justificativaJurisdicionado) {
              this.setState({ errorDialogValue: false });
              this.submitFormData();
              this._toggleDialogReqMod();
            } else {
              !this.state.errorDialogValue && this.setState({ errorDialogValue: true });
            }
          }}
        />
      </div>
    );
  }

  _getFieldErrorMessage(rendered, message) {
    if (rendered) {
      return <small className="p-error">{message}</small>;
    }
    return null;
  }

  renderDialogRequisicaoModificacao() {
    const { getRule } = this.store;
    return (
      <Dialog
        header="Justificativa Requisição"
        visible={this.state.visibleDialogReqMod}
        style={{ width: '50vw' }}
        footer={this.renderDialogReqModFooter()}
        onHide={() => this._toggleDialogReqMod()}
      >
        <div className="p-fluid p-formgrid p-grid">
          <FormField columns={12} attribute="justificativa" rule={getRule('justificativa')} label="Justificativa">
            <FcInputTextarea
              className={this.state.errorDialogValue ? 'p-invalid p-error' : ''}
              value={this.reqModificacaoStore.justificativaJurisdicionado}
              onChange={(e) => {
                this.reqModificacaoStore.updateAttribute('jurisdicionado', e);
                isValueValid(this.reqModificacaoStore.justificativaJurisdicionado?.length) &&
                  this.setState({ errorDialogValue: false });
              }}
              placeholder="Descreva a justificativa"
            />
          </FormField>
          <div className="p-col-6">
            {this._getFieldErrorMessage(this.state.errorDialogValue, 'Por favor, preencha o campo')}
          </div>
        </div>
      </Dialog>
    );
  }

  onChangeStep(step) {
    if (step < 0) {
      this.store.toggleShowConfirmDialog();
    } else if (step === 0) {
      this.setState({ activeIndexForm: step });
    } else if (step === 1 && (!this.disabledAvancar() || this.state.activeIndexForm === 2)) {
      this.setState({ activeIndexForm: step });
    } else if (
      step === 2 &&
      !this.disabledAvancar() &&
      !this.disabledAvancar(1) &&
      this.store.validateSubmittedFiles(this.store.object?.arquivosTemporarios)
    ) {
      this.setState({ activeIndexForm: step });
    }
  }

  handleScrollToEnd() {
    this.state.activeIndexForm === 2 && window.scrollTo(0, document.body.scrollWidth);
  }

  submitFormData(e) {
    e && e.preventDefault();
    const execution = () => {
      if (!this.store.rules.hasError) {
        !this._isReqMod() && this.store.save(this._goBack, this.props.action);
        if (this._isReqMod() && this.reqModificacaoStore.justificativaJurisdicionado) {
          this.store.beforeSave();
          this.reqModificacaoStore.enviarRequisicaoTermoReferencia({ ...this.store.object }, this._goBack);
        }
      } else {
        showNotification('error', null, 'Verifique os campos do formulário!');
      }
    };

    if (this.state.submitted) {
      execution();
    } else {
      this.setState({ submitted: true }, execution);
    }
  }

  buildSecoesData() {
    const keyValue = {};
    const termo = this.store.object;
    termo?.secoes?.forEach((secaoData) => {
      keyValue[secaoData.secao.id] = secaoData;
    });
    return keyValue;
  }

  getFieldTextComponent(secao) {
    if (secao?.valor) {
      const text = secao.valor;
      return {
        label: (
          <span
            dangerouslySetInnerHTML={{
              __html: text,
            }}
          />
        ),
        labelStr: secao.valor,
      };
    }

    return '-';
  }

  getDataSecoes() {
    const secoesData = this.buildSecoesData();
    const data = [];
    const secoes = !this.store.object?.isFinalizado ? this.store.secoes : [];
    const secoesCriadas = this.store.object?.secoesCriadas ? this.store.object.secoesCriadas : [];
    [...secoes, ...secoesCriadas]?.forEach((secao) => {
      let child;
      const savedSection = secoesData && secoesData[secao.id];
      if (savedSection) {
        const value = this.getFieldTextComponent(savedSection);
        child = {
          key: savedSection?.id,
          label: value?.label,
          labelStr: value?.labelStr,
          data: savedSection,
          style: { color: '#000000' },
        };
      }

      data.push({
        key: secao?.id,
        label: secao?.titulo,
        data: secao,
        icon: 'pi pi-fw pi-briefcase',
        child,
        style: child ? { color: '#000000' } : { color: '#ff0000' },
        ordem:
          secao?.geradaTermo && !this.store.object?.isFinalizado
            ? this.store.secoes?.length + secao?.ordem + 1
            : secao?.ordem,
      });
    });

    data.sort((a, b) => a?.ordem - b?.ordem);
    return data;
  }

  getDataLotes() {
    const lotes = [{ nome: 'Itens sem lote', itens: [] }];
    let contLotes = 0;
    this.store.object?.lotes.forEach((lote) => {
      if (!lote.gerado) {
        contLotes++;
        lotes.push({
          nome: `Lote ${contLotes} - ${lote.nome}`,
          itens: lote.itens,
        });
      } else {
        lotes[0].itens.push(...lote.itens);
      }
    });

    if (lotes.length == 1) {
      lotes[0].nome = 'Lote Único';
    }

    return lotes;
  }

  render() {
    const { submitted } = this.state;
    const { submitFormData } = this;

    const breacrumbItems = [
      { label: 'Gerenciamento de Termos', url: UrlRouter.termoReferencia.gerenciamentoTermos.index },
      { label: this.props.action === 'new' ? 'Novo' : 'Editar' },
    ];

    let content;

    if (this.store.object) {
      const forms = [
        {
          label: 'Dados Básicos',
          step: 0,
          body: <FormAbaDados submitted={submitted} store={this.store} requisicaoModificao={this._isReqMod()} />,
        },
        {
          label: `${this.store.object?.formaPreenchimentoSecao === 'IMPORTACAO_ARQUIVO' ? 'Arquivo' : 'Seções'}`,
          step: 1,
          body: (
            <AbaSecoesFormPage
              store={this.store}
              showDialogSecao={this.state.showDialogSecao}
              switchNewSecaoDialogVisibility={this.switchNewSecaoDialogVisibility}
            />
          ),
        },
        {
          label: (
            <>
              <label className="p-overlay-badge" onClick={() => this.handleScrollToEnd()}>
                <span className="p-mr-3">Catálogo</span>
              </label>
            </>
          ),
          step: 2,
          body: (
            <AbaItensFormPage
              store={this.store}
              srp={this.store.object.srp}
              tresCasasDecimais={this.store.object.tresCasasDecimais}
              updateLastSave={this.updateLastSave}
              startAutomaticSave={this.startAutomaticSave}
            />
          ),
        },
      ];

      const manualFilling = this.store.object?.formaPreenchimentoSecao === 'PREENCHIMENTO_MANUAL';
      content = (
        <>
          <AppBreadCrumb items={breacrumbItems} />
          <div className="card page form-action-buttons">
            <form onSubmit={(e) => (this.isValidSections(e) || !manualFilling) && submitFormData(e)}>
              <Steps
                model={forms}
                activeIndex={this.state.activeIndexForm}
                onSelect={(e) => this.onChangeStep(e.index)}
                readOnly={false}
              />
              {forms.find((item) => item.step === this.state.activeIndexForm).body}
              {manualFilling && this.renderSaveConfirmDialog()}
              {this.renderDialogRequisicaoModificacao()}
              {this.renderActionButtons()}
              {this.store.isConfirmDialogVisible && this.confirmDiscardChanges()}
            </form>
          </div>
        </>
      );
    } else {
      content = (
        <div className="card page">
          <AppBreadCrumb items={breacrumbItems} />
          <i
            className="pi pi-spin pi-spinner"
            style={{
              marginTop: '20px',
              marginLeft: 'calc(50% - 20px)',
              fontSize: '2em',
            }}
          ></i>
        </div>
      );
    }
    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        {content}
      </PermissionProxy>
    );
  }
}

GerenciamentoTermoFormPage.propTypes = {
  id: PropTypes.any,
  action: PropTypes.any,
  history: PropTypes.any,
};

export default GerenciamentoTermoFormPage;
