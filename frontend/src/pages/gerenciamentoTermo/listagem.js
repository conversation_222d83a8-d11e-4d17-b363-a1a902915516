import React from 'react';
import { observer } from 'mobx-react';
import GerenciamentoTermoIndexStore from '~/stores/gerenciamentoTermo/indexStore';
import IndexDataTable from 'fc/components/IndexDataTable';
import FcButton from 'fc/components/FcButton';
import { PrimeIcons } from 'primereact/api';
import UrlRouter from '~/constants/UrlRouter';
import GenericIndexPage from 'fc/pages/GenericIndexPage';
import AdvancedSearch from 'fc/components/AdvancedSearch';
import { checkUserGroup, getValue, getValueDate } from 'fc/utils/utils';
import { DATE_FORMAT, DATE_PARSE_FORMAT } from 'fc/utils/date';
import AccessPermission from '~/constants/AccessPermission';
import PermissionProxy from 'fc/components/PermissionProxy';
import { Dialog } from 'primereact/dialog';
import { InputText } from 'primereact/inputtext';
import FormField from 'fc/components/FormField';
import FcCloseableTabView from 'fc/components/FcCloseableTabView';

@observer
class GerenciamentoTermoListagemPage extends GenericIndexPage {
  constructor(props) {
    super(props, AccessPermission.gerenciamentoTermo);
    this.state = {
      idRemove: null,
      selectedRow: null,
      dialogCloneVisibility: false,
      cloneRow: null,
      identificadorClone: '',
      cloneSubimitted: false,
      activeTabIndex: 0,
    };
    this.store = new GerenciamentoTermoIndexStore();
    this.store.loadSecoes(() => this.forceUpdate());

    this.setActiveTabindex = this.setActiveTabindex.bind(this);
    this.renderFooterClone = this.renderFooterClone.bind(this);
    this.renderDialogClone = this.renderDialogClone.bind(this);
  }

  componentDidMount() {
    const { idTermo } = this.props.match?.params || {};
    if (idTermo) {
      this.store.getTermoById(idTermo, (item) => {
        this._toggleDetails(item);
      });
    }
  }

  _toggleDetails(item, callback) {
    this.props.onDetail(item);
    callback && callback();
  }

  switchVisibilityClone(data) {
    this.setState({
      dialogCloneVisibility: !this.state.dialogCloneVisibility,
      cloneRow: data,
      identificadorClone: data ? (data.identificadorProcesso ? data.identificadorProcesso : '') + ' - Cópia' : '',
      cloneSubimitted: false,
    });
  }

  renderFooterClone() {
    return (
      <div>
        <FcButton
          className="p-confirm-dialog-reject p-button-text"
          label="Cancelar"
          icon="pi pi-times"
          onClick={() => this.switchVisibilityClone()}
        />
        <FcButton
          className="p-confirm-dialog-accept p-button-success"
          label="Clonar"
          icon="pi pi-check"
          onClick={() => {
            if (this.state.identificadorClone) {
              this.store.clone(this.state.cloneRow.id, this.state.identificadorClone, (url) => {
                this.pushUrlToHistory(url);
                this.switchVisibilityClone();
              });
            } else {
              this.setState({ cloneSubimitted: true });
            }
          }}
        />
      </div>
    );
  }

  renderDialogClone() {
    return (
      <Dialog
        header="Clonar Registro"
        footer={this.renderFooterClone}
        visible={this.state.dialogCloneVisibility}
        style={{ width: '30vw' }}
        modal
        onHide={() => this.switchVisibilityClone()}
      >
        <div className="p-fluid p-formgrid p-grid">
          <FormField attribute="identificadorClone" label="Identificador do Novo Termo de Referência (Clone)">
            <>
              <InputText
                className={this.state.cloneSubimitted && !this.state.identificadorClone ? 'p-invalid block' : ''}
                value={this.state.identificadorClone}
                onChange={(e) => this.setState({ identificadorClone: e.target.value })}
                aria-describedby="indentificador-obrigatorio"
              />
              {this.state.cloneSubimitted && (
                <small id="indentificador-obrigatorio" className="p-error block">
                  Identificador do termo obrigatório!
                </small>
              )}
            </>
          </FormField>
        </div>
      </Dialog>
    );
  }

  onPage(event, filter) {
    const page = (event.page ?? 0) + 1;
    let pagination = {};
    switch (filter) {
      case 'disponivel':
        pagination = JSON.parse(JSON.stringify(this.store.paginationDisponivel));
        break;
      case 'associado':
        pagination = JSON.parse(JSON.stringify(this.store.paginationAssociado));
        break;
      case 'modelo':
        pagination = JSON.parse(JSON.stringify(this.store.paginationModelo));
        break;
      default:
        pagination = JSON.parse(JSON.stringify(this.store.pagination));
        break;
    }
    pagination.page.index = page;
    this.store.setPagination(pagination, filter);
    this.store.load(pagination, filter);
  }

  onSort(event = {}, filter) {
    const { sortField, sortOrder } = event;
    if (sortField && sortOrder) {
      let params = {};
      switch (filter) {
        case 'disponivel':
          params = JSON.parse(JSON.stringify(this.store.paginationDisponivel));
          break;
        case 'associado':
          params = JSON.parse(JSON.stringify(this.store.paginationAssociado));
          break;
        case 'modelo':
          params = JSON.parse(JSON.stringify(this.store.paginationModelo));
          break;
        case 'todos':
          params = JSON.parse(JSON.stringify(this.store.pagination));
          break;
        default:
          params = JSON.parse(JSON.stringify(this.store.pagination));
          break;
      }
      params['sort'] = {
        by: sortField,
        order: sortOrder > 0 ? 'asc' : 'desc',
      };
      this.store.setPagination(params, filter);
      this.store.load(params, filter);
    }
  }

  getFirstFromPagination(filter) {
    let page = {};
    switch (filter) {
      case 'disponivel':
        page = this.store.paginationDisponivel.page;
        break;
      case 'associado':
        page = this.store.paginationAssociado.page;
        break;
      case 'modelo':
        page = this.store.paginationModelo.page;
        break;
      case 'todos':
        page = this.store.pagination.page;
        break;
      default:
        page = this.store.pagination.page;
        break;
    }
    const { index, size } = page;
    return size * (index - 1);
  }

  setActiveTabindex(tab) {
    this.setState({ activeTabIndex: tab.id });
    switch (tab.id) {
      case 0:
        this.store.setTabActive('todos');
        break;
      case 1:
        this.store.setTabActive('disponivel');
        break;
      case 2:
        this.store.setTabActive('associado');
        break;
      case 3:
        this.store.setTabActive('modelo');
        break;
    }
  }

  render() {
    const columns = [
      {
        field: 'identificadorProcesso',
        header: 'Identificador do Termo de Referência',
        sortable: true,
      },
      {
        field: 'dataCadastro',
        header: 'Data de Cadastro',
        sortable: true,
        style: { width: '15%' },
        body: ({ dataCadastro }) => getValueDate(dataCadastro, DATE_FORMAT, DATE_PARSE_FORMAT),
      },
      {
        field: 'usuario',
        header: 'Nome do Usuário',
        sortable: true,
        body: ({ usuario }) => getValue(usuario),
      },
      {
        style: { width: '260px' },
        body: (rowData) => {
          return (
            <div className="actions p-d-flex p-jc-end">
              <PermissionProxy resourcePermissions={this.getReadPermission()}>
                <FcButton
                  icon="pi pi-eye"
                  className="p-button-sm p-mr-2"
                  tooltip="Detalhes"
                  onClick={() => this.props.onDetail(rowData)}
                />
              </PermissionProxy>
              {!rowData.idRequisicaoModificacao && !rowData?.blockedInProcessReqMod ? (
                <>
                  <PermissionProxy resourcePermissions={this.getWritePermission()}>
                    <FcButton
                      icon="pi pi-clone"
                      tooltip="Clonar"
                      className="p-button-sm p-button-help p-mr-2"
                      onClick={() => this.switchVisibilityClone(rowData)}
                    />
                  </PermissionProxy>
                  <PermissionProxy resourcePermissions={this.getReadPermission()}>
                    <FcButton
                      icon="pi pi-pencil"
                      className="p-button-sm p-button-success p-mr-2"
                      tooltip={rowData.isFinalizado ? 'Criar Requisição de Modificação' : 'Editar'}
                      disabled={
                        (rowData.isFinalizado && !rowData.canCreateReqModificacao) ||
                        checkUserGroup('Auditor - Demais Inspetorias')
                      }
                      onClick={() =>
                        this.pushUrlToHistory(
                          rowData.isFinalizado && rowData.canCreateReqModificacao
                            ? UrlRouter.administracao.requisicaoModificacao.termoReferencia.requisitar.replace(
                                ':id',
                                rowData.id
                              )
                            : UrlRouter.termoReferencia.gerenciamentoTermos.editar.replace(':id', rowData.id)
                        )
                      }
                    />
                  </PermissionProxy>
                  <PermissionProxy resourcePermissions={this.getWritePermission()}>
                    <FcButton
                      icon="pi pi-trash"
                      tooltip="Remover"
                      className="p-button-sm p-button-danger p-mr-2"
                      onClick={() => {
                        this.setState({ idRemove: rowData.id });
                        this.store.toggleShowConfirmDialog();
                      }}
                      disabled={rowData.isFinalizado || !rowData.disponivel}
                    />
                  </PermissionProxy>
                </>
              ) : rowData.idRequisicaoModificacao ? (
                <PermissionProxy resourcePermissions={this.getReadPermission()}>
                  <FcButton
                    icon="pi pi-exclamation-triangle"
                    style={{ backgroundColor: '#c89800' }}
                    tooltip="Requisição de modificação pendente"
                    className="p-button-sm p-button-info p-mr-2"
                    onClick={() =>
                      this.pushUrlToHistory(
                        UrlRouter.administracao.requisicaoModificacao.indexDetail.replace(
                          ':id',
                          rowData.idRequisicaoModificacao
                        )
                      )
                    }
                  />
                </PermissionProxy>
              ) : (
                <PermissionProxy resourcePermissions={this.getReadPermission()}>
                  <FcButton
                    icon="pi pi-exclamation-circle"
                    style={{ backgroundColor: '#c89800' }}
                    tooltip="Pendente de Aprovação da Substituição"
                    className="p-button-sm p-button-info p-mr-2"
                    onClick={() => this.props.onDetail(rowData)}
                  />
                </PermissionProxy>
              )}
            </div>
          );
        },
      },
    ];

    const header = (
      <div className="table-header">
        <PermissionProxy resourcePermissions={this.getWritePermission()}>
          <FcButton
            className="p-button"
            label="Novo"
            style={{ marginBottom: '5px', marginRight: '5px' }}
            icon={PrimeIcons.PLUS}
            onClick={() => this.pushUrlToHistory(UrlRouter.termoReferencia.gerenciamentoTermos.novo)}
          />
        </PermissionProxy>
        {this.renderTableDataExport(columns, 'termoReferencia')}
      </div>
    );

    const { loading } = this.store;
    const { getDefaultTableProps } = this;

    const tabs = [
      {
        id: 0,
        header: 'Todos',
        badgeCount: this.store.pagination.total ?? 0,
        content: (
          <IndexDataTable
            setColumnsToExport={this.setColumnsToExport}
            columns={columns}
            value={this.store.listKey}
            header={header}
            loading={loading}
            {...getDefaultTableProps()}
            onSort={(e) => this.onSort(e, 'todos')}
            onPage={(e) => this.onPage(e, 'todos')}
            first={this.getFirstFromPagination('todos')}
          />
        ),
      },
      {
        id: 1,
        header: 'Disponível',
        badgeCount: this.store.paginationDisponivel.total ?? 0,
        content: (
          <IndexDataTable
            setColumnsToExport={this.setColumnsToExport}
            columns={columns}
            value={this.store.listDisponivel}
            loading={loading}
            {...getDefaultTableProps()}
            totalRecords={this.store.paginationDisponivel.total ?? 0}
            onSort={(e) => this.onSort(e, 'disponivel')}
            onPage={(e) => this.onPage(e, 'disponivel')}
            first={this.getFirstFromPagination('disponivel')}
            header={header}
          />
        ),
      },
      {
        id: 2,
        header: 'Associado',
        badgeCount: this.store.paginationAssociado.total ?? 0,
        content: (
          <IndexDataTable
            setColumnsToExport={this.setColumnsToExport}
            columns={columns}
            value={this.store.listAssociado}
            loading={loading}
            {...getDefaultTableProps()}
            totalRecords={this.store.paginationAssociado.total ?? 0}
            onSort={(e) => this.onSort(e, 'associado')}
            onPage={(e) => this.onPage(e, 'associado')}
            first={this.getFirstFromPagination('associado')}
            header={header}
          />
        ),
      },
      {
        id: 3,
        header: 'Modelo',
        badgeCount: this.store.paginationModelo.total ?? 0,
        content: (
          <IndexDataTable
            setColumnsToExport={this.setColumnsToExport}
            columns={columns}
            value={this.store.listModelo}
            loading={loading}
            {...getDefaultTableProps()}
            totalRecords={this.store.paginationModelo.total ?? 0}
            onSort={(e) => this.onSort(e, 'modelo')}
            onPage={(e) => this.onPage(e, 'modelo')}
            first={this.getFirstFromPagination('modelo')}
            header={header}
          />
        ),
      },
    ];

    return (
      <>
        <AdvancedSearch
          searchParams={this.store.getAdvancedSearchParams()}
          store={this.store}
          searchFields={['identificadorProcesso']}
          filterSuggest={this.store.getFilterSuggest()}
        />
        <FcCloseableTabView
          tabs={tabs}
          activeTabIndex={this.state.activeTabIndex}
          onChangeTab={this.setActiveTabindex}
          badgeSeverity={'success'}
        />
        {this.store.isConfirmDialogVisible && this.confirmRemove(this.state.idRemove)}
        {this.renderDialogClone()}
      </>
    );
  }
}

GerenciamentoTermoListagemPage.displayName = 'GerenciamentoTermoListagemPage';

export default GerenciamentoTermoListagemPage;
