import React from 'react';
import { observer } from 'mobx-react';
import { PropTypes } from 'prop-types';
import { Divider } from 'primereact/divider';
import { DataScroller } from 'primereact/datascroller';
import GenericIndexPage from 'fc/pages/GenericIndexPage';
import FcButton from 'fc/components/FcButton';
import { Dialog } from 'primereact/dialog';
import { Skeleton } from 'primereact/skeleton';
import <PERSON><PERSON>ield from 'fc/components/FormField';
import { InputText } from 'primereact/inputtext';
import { getNumberUnitThousands, getValue, getValueMoney, showNotification } from 'fc/utils/utils';
import { ConfirmDialog } from 'primereact/confirmdialog';
import InputMonetary from 'fc/components/InputMonetary';
import Highlighter from 'react-highlight-words';
import { Dropdown } from 'primereact/dropdown';
import { InputTextarea } from 'primereact/inputtextarea';
import FormMaterial from '../material';
import { Fieldset } from 'primereact/fieldset';
import { Tag } from 'primereact/tag';
import { Sidebar } from 'primereact/sidebar';
import { Accordion, AccordionTab } from 'primereact/accordion';
import moment from 'moment';
import ItemCarrinho from './ItemCarrinho';
import { Checkbox } from 'primereact/checkbox';

@observer
class AbaItensFormPage extends GenericIndexPage {
  storeForm;
  constructor(props) {
    super(props);
    this.storeForm = this.props.store;
    this.state = {
      itemRemove: null,
      loteRemove: null,
      isConfirmDialogRemoveItemVisible: false,
      isConfirmDialogRemoveLoteVisible: false,
      dialogVisibility: false,
      showDialogNewLote: false,
      showDialogEditLote: false,
      submitted: false,
      visibleRight: false,
      seeAllFeatures: false,
      buscaCarrinho: '',
      valorNovoLote: '',
    };

    this._renderRowDetails = this._renderRowDetails.bind(this);
    this.switchVisibility = this.switchVisibility.bind(this);
    this.setMaterial = this.setMaterial.bind(this);
    this.itemTemplate = this.itemTemplate.bind(this);
    this.calculaQuantidadeItens = this.calculaQuantidadeItens.bind(this);
    this.buscaItemCarrinho = this.buscaItemCarrinho.bind(this);
    this.orderItensLoteByDepreciadoSuspenso = this.orderItensLoteByDepreciadoSuspenso.bind(this);
  }

  componentDidMount() {
    if (!this.storeForm.object.tipo || this.storeForm.object.tipo === 'ITENS') {
      this.storeForm.initializeLoteUnico();
      this.props.startAutomaticSave();
    }
  }

  updateAttribute(attribute, event) {
    this.storeForm.updateAttributeMaterial(attribute, event);
    this.forceUpdate();
  }

  setMaterial(material, type = 'new') {
    this.storeForm.setMaterial(material, type);
    this.switchVisibility();
  }

  switchVisibility() {
    this.setState({
      dialogVisibility: !this.state.dialogVisibility,
      submitted: false,
    });
  }

  excluirItem() {
    return (
      <ConfirmDialog
        blockScroll
        visible={this.state.isConfirmDialogRemoveItemVisible}
        message="Você realmente deseja excluir o registro selecionado?"
        header="Excluir Registro"
        icon="pi pi-exclamation-triangle"
        acceptClassName="p-button-danger"
        accept={() => {
          this.storeForm.deleteItemCatalogo(this.state.itemRemove);
          this.forceUpdate();
          this.setState({ isConfirmDialogRemoveItemVisible: false, itemRemove: null });
        }}
        onHide={() => this.setState({ isConfirmDialogRemoveItemVisible: false, itemRemove: null })}
      />
    );
  }

  excluirTodosOsItens() {
    this.storeForm.excluiTodosOsItens();
    this.forceUpdate();
  }

  _renderValue(label, value, icon, col = 12) {
    return (
      <div className={`p-col-${col}`}>
        <div
          className="p-col-12 drawer-content-label"
          style={{ fontWeight: 'bold', color: '#9E9E9E', fontSize: '13px' }}
        >
          {label}
        </div>
        <div className="p-col-12 flex w-full align-items-center">
          {icon && (
            <span className="icon" style={{ marginRight: '0.5rem' }}>
              {icon}
            </span>
          )}
          {value ?? '-'}
        </div>
      </div>
    );
  }

  _renderRowDetails(row, name = 'card') {
    const { editMaterial } = this.storeForm;

    return (
      <div className={name}>
        <div className="p-grid ">
          <div className="p-col-12" style={{ fontSize: '18px', fontWeight: 'bold' }}>
            {getValue(row?.pdm?.nome)}
          </div>
          {this._renderValue('Código', row?.codigo, <i className="pi pi-tag" style={{ color: '#326FD1' }} />, 3)}
          {this._renderValue(
            'Classe',
            row?.pdm?.classe?.descricao,
            <i className="pi pi-file" style={{ color: '#22C55E' }} />,
            2
          )}
          {this._renderValue(
            'Subclasse',
            row?.pdm?.subClasse?.descricao,
            <i className="pi pi-palette" style={{ color: '#A855F7' }} />,
            3
          )}
          {this._renderValue(
            'Unidade de Medida',
            editMaterial?.unidadeMedida
              ? getValue(editMaterial.unidadeMedida.textUnidadeMedida)
              : getValue(
                  row?.pdm?.unidadesMedida
                    ?.filter((um) => um.descricao || um.nomeUnidadeFornecimento)
                    ?.map((um) => um.descricao ?? um.nomeUnidadeFornecimento)
                    ?.filter((item, pos, self) => self.indexOf(item) == pos)
                    ?.join(', ')
                ),
            <i className="pi pi-calculator" style={{ color: '#EAB308' }} />,
            3
          )}
          {
            <div className="p-formgrid p-grid p-d-flex p-jc-between-2 m-0">
              <div className="mt-2">
                {row?.caracteristicas.map((caracteristica, index) => (
                  <Tag
                    key={index}
                    style={{
                      color: '#609AF8',
                      backgroundColor: '#F5F9FF',
                      marginRight: '3px',
                      border: '1px solid #609AF8',
                    }}
                  >
                    {caracteristica.textCaracteristica}
                  </Tag>
                ))}
              </div>
            </div>
          }
        </div>
      </div>
    );
  }

  _renderDescricaoMaterial(descricao) {
    const sanitizeFunction = (value) => (value ? value.normalize('NFD').replace(/[\u0300-\u036f]/g, '') : '');

    return (
      <Highlighter
        highlightClassName="highlighted-text-material"
        searchWords={[]}
        autoEscape={true}
        sanitize={sanitizeFunction}
        textToHighlight={descricao}
        highlightStyle={{
          background: '#ffe694',
          padding: '0.15rem 0.2rem',
          fontFamily: 'Roboto, Helvetica Neue Light, Helvetica Neue, Helvetica, Arial, Lucida Grande, sans-serif',
        }}
      />
    );
  }

  renderFooter() {
    return (
      <div>
        <FcButton
          label="Cancelar"
          icon="pi pi-times"
          onClick={() => this.switchVisibility()}
          className="p-button-outlined p-button-brand-light"
        />
        <FcButton label="Adicionar" icon="pi pi-shopping-cart" onClick={() => this.submitItem()} />
      </div>
    );
  }

  submitItem() {
    const execution = () => {
      if (!this.storeForm.rulesItem.hasError) {
        this.storeForm.saveItemCatalogo(
          () => this.forceUpdate(),
          () => this.props.updateLastSave(moment())
        );
        this.switchVisibility();
        showNotification('info', 'Item adicionado', 'O item foi adicionado ao Termo de Referência!');
      } else {
        showNotification('error', null, 'Verifique os campos do formulário!');
      }
    };

    if (this.state.submitted) {
      execution();
    } else {
      this.setState({ submitted: true }, execution);
    }
  }

  renderDialog() {
    const { editMaterial, getRuleItem, keyedLotes, updateAttributeCheckboxEditMaterial } = this.storeForm;

    return (
      <Dialog
        header={
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <i
              className="pi pi-cart-plus"
              style={{
                color: '#326FD1',
                background: '#F5F9FF',
                borderRadius: '50%',
                padding: '13px',
                fontSize: '18px',
              }}
            ></i>
            <div style={{ marginLeft: '10px', marginTop: '10px' }}>
              <h2
                style={{
                  fontSize: '15px',
                  marginBottom: '0',
                  marginTop: '5px',
                  fontWeight: 'normal',
                  color: '#212121',
                }}
              >
                Adicionar Item
              </h2>
              <h2 style={{ fontSize: '12px', color: '#9E9E9E', marginTop: '0px', fontWeight: 'normal' }}>
                Detalhe o item para adicionar ao carrinho
              </h2>
            </div>
          </div>
        }
        visible={this.state.dialogVisibility}
        style={{ width: '50vw' }}
        footer={this.renderFooter()}
        onHide={() => this.switchVisibility()}
      >
        {editMaterial ? (
          <>
            {this._renderRowDetails(editMaterial.materialDetalhamento, '')}
            <Divider />
            <div className="p-fluid p-formgrid p-grid">
              <FormField
                columns={6}
                attribute="lote"
                label="Lote"
                submitted={this.state.submitted}
                rule={getRuleItem('lote')}
              >
                <Dropdown
                  emptyMessage="Nenhum Registro Encontrado"
                  emptyFilterMessage="Nenhum Registro Encontrado"
                  value={editMaterial.lote}
                  optionLabel="text"
                  optionValue="value"
                  options={keyedLotes}
                  onChange={(e) => this.updateAttribute('lote', e)}
                  placeholder="Informe o lote"
                  showClear
                />
              </FormField>
              <FcButton
                style={{ marginTop: '2.1rem', height: '2.8rem' }}
                tooltipOptions={{ position: 'top' }}
                tooltip="Adicionar Novo Lote"
                icon="pi pi-plus"
                onClick={() => this.switchDialogNewLoteVisibility()}
                disabled={this.storeForm.newLoteDisabled()}
              />
            </div>
            <div className="p-fluid p-formgrid p-grid">
              <FormField
                columns={12}
                attribute="fracionario"
                label="Quantidade Fracionária"
                checkbox
                submitted={this.state.submitted}
              >
                <Checkbox
                  inputId="fracionario"
                  checked={editMaterial.fracionario}
                  onChange={(e) => {
                    updateAttributeCheckboxEditMaterial('fracionario', e);
                    if (e.checked) {
                      this.updateAttribute('quantidade', parseFloat(editMaterial.quantidade));
                      this.updateAttribute('quantidadeConsumo', parseFloat(editMaterial.quantidadeConsumo));
                    } else {
                      this.updateAttribute('quantidade', parseInt(editMaterial.quantidade));
                      this.updateAttribute('quantidadeConsumo', parseInt(editMaterial.quantidadeConsumo));
                    }
                  }}
                  id="fracionario"
                />
              </FormField>
              <FormField
                columns={4}
                attribute="Quantidade"
                label={this.props.srp ? 'Quantidade para Registro' : 'Quantidade'}
                rule={getRuleItem('quantidade')}
                submitted={this.state.submitted}
              >
                {editMaterial.fracionario ? (
                  <InputMonetary
                    value={editMaterial.quantidade}
                    onChange={(e) => {
                      this.updateAttribute('quantidade', e);
                    }}
                    placeholder="Informe a quantidade"
                  />
                ) : (
                  <InputMonetary
                    value={editMaterial.quantidade}
                    onChange={(e) => this.updateAttribute('quantidade', e)}
                    placeholder="Informe a quantidade"
                    min={0}
                    allowEmpty={false}
                    thousandsPlaceMode
                  />
                )}
              </FormField>
              {this.props.srp && (
                <FormField
                  columns={4}
                  attribute="quantidadeConsumo"
                  label="Quantidade para Consumo"
                  rule={getRuleItem('quantidadeConsumo')}
                  submitted={this.state.submitted}
                >
                  {editMaterial.fracionario ? (
                    <InputMonetary
                      value={editMaterial.quantidadeConsumo}
                      onChange={(e) => {
                        this.updateAttribute('quantidadeConsumo', e);
                      }}
                      placeholder="Informe a quantidade para consumo"
                    />
                  ) : (
                    <InputMonetary
                      value={editMaterial.quantidadeConsumo}
                      onChange={(e) => this.updateAttribute('quantidadeConsumo', e)}
                      placeholder="Informe a quantidade para consumo"
                      thousandsPlaceMode
                    />
                  )}
                </FormField>
              )}

              <FormField
                columns={4}
                attribute="valorUnitarioEstimado"
                label="Valor Unitário Estimado (R$)"
                rule={getRuleItem('valorUnitarioEstimado')}
                submitted={this.state.submitted}
              >
                <InputMonetary
                  onChange={(e) => this.updateAttribute('valorUnitarioEstimado', e)}
                  placeholder="R$"
                  value={editMaterial.valorUnitarioEstimado}
                  decimalPlaces={this.props.tresCasasDecimais ? 3 : 2}
                />
              </FormField>
              {editMaterial?.materialDetalhamento?.pdm?.tipoMaterial === 'M' &&
                editMaterial?.materialDetalhamento?.pdm?.unidadesMedida?.length > 0 &&
                !this.props.srp && (
                  <FormField
                    columns={4}
                    attribute="unidadeMedida"
                    label="Unidade de Medida"
                    rule={getRuleItem('unidadeMedida')}
                    submitted={this.state.submitted}
                  >
                    <Dropdown
                      className="w-full"
                      options={editMaterial?.materialDetalhamento?.pdm?.unidadesMedida}
                      optionLabel="textUnidadeMedida"
                      optionValue="id"
                      value={editMaterial.unidadeMedida?.id}
                      onChange={(e) =>
                        this.updateAttribute(
                          'unidadeMedida',
                          editMaterial?.materialDetalhamento?.pdm?.unidadesMedida?.find(
                            (unidade) => unidade.id === e.value
                          )
                        )
                      }
                      placeholder="Selecione a unidade de medida utilizada"
                      showClear
                    />
                  </FormField>
                )}
            </div>
            <div className="p-grid">
              <div className="p-col-6 ">
                {this.props.srp ? 'Valor Total Estimado Para Registro' : 'Valor Total Estimado'}
              </div>
              <div className="p-col-6 ">{this.props.srp && 'Valor Total Estimado para Consumo'}</div>
              <div className="p-col-6 " style={{ color: '#606EC1', fontWeight: 'bold' }}>
                {getValueMoney((editMaterial.valorUnitarioEstimado ?? 0) * (editMaterial.quantidade ?? 0))}
              </div>
              <div className="p-col-6 " style={{ color: '#606EC1', fontWeight: 'bold' }}>
                {this.props.srp &&
                  getValueMoney((editMaterial.valorUnitarioEstimado ?? 0) * (editMaterial.quantidadeConsumo ?? 0))}
              </div>
            </div>
            {editMaterial?.materialDetalhamento?.pdm?.tipoMaterial === 'M' &&
              editMaterial?.materialDetalhamento?.pdm?.unidadesMedida?.length > 0 &&
              this.props.srp && (
                <FormField
                  columns={4}
                  attribute="unidadeMedida"
                  label="Unidade de Medida"
                  rule={getRuleItem('unidadeMedida')}
                  submitted={this.state.submitted}
                >
                  <Dropdown
                    className="w-full"
                    options={editMaterial?.materialDetalhamento?.pdm?.unidadesMedida}
                    optionLabel="textUnidadeMedida"
                    optionValue="id"
                    value={editMaterial.unidadeMedida?.id}
                    onChange={(e) =>
                      this.updateAttribute(
                        'unidadeMedida',
                        editMaterial?.materialDetalhamento?.pdm?.unidadesMedida?.find(
                          (unidade) => unidade.id === e.value
                        )
                      )
                    }
                    placeholder="Selecione a unidade de medida utilizada"
                    showClear
                  />
                </FormField>
              )}

            <div className="p-fluid p-formgrid p-grid">
              <FormField
                columns={12}
                attribute="descricaoComplementar"
                label="Descrição Complementar"
                rule={getRuleItem('descricaoComplementar')}
                submitted={this.state.submitted}
              >
                <div>
                  <InputTextarea
                    rows={5}
                    cols={30}
                    value={editMaterial.descricaoComplementar}
                    onChange={(e) => this.updateAttribute('descricaoComplementar', e)}
                    placeholder="Informe a descrição complementar"
                    maxlength="4000"
                  />
                  <p>
                    {editMaterial.descricaoComplementar
                      ? getNumberUnitThousands(4000 - editMaterial.descricaoComplementar.length)
                      : getNumberUnitThousands(4000)}{' '}
                    caracteres restantes
                  </p>
                </div>
              </FormField>
            </div>
          </>
        ) : (
          <Skeleton />
        )}
      </Dialog>
    );
  }

  renderItem(item) {
    return (
      <div style={{ display: 'flex', justifyContent: 'space-between', width: '100%' }}>
        <span>{item.nome}</span>
        <span style={{ width: 105 }}>
          <b>Quantidade: </b>
          {item.quantidade}
        </span>
      </div>
    );
  }

  itemTemplate(data) {
    return (
      <ItemCarrinho
        data={data}
        handleEdit={() => this.setMaterial(data.key, 'edit')}
        handleRemove={() => {
          this.setState({ isConfirmDialogRemoveItemVisible: true, itemRemove: data.key });
        }}
        title={this._renderDescricaoMaterial(data.materialDetalhamento.pdm.nome)}
        tresCasasDecimais={this.props.tresCasasDecimais}
        srp={this.props.srp}
      />
    );
  }

  buscaItemCarrinho(e) {
    const value = e?.target?.value;
    this.setState({ buscaCarrinho: value });
  }

  regulaExpressao(value = '') {
    return value
      .toLowerCase()
      .normalize('NFD')
      .replace(/\p{Diacritic}/gu, '');
  }

  orderItensLoteByDepreciadoSuspenso(lotes) {
    lotes.map((lote) => {
      lote.itens.sort((a, b) => {
        if (a.materialDetalhamento.status === 'D' || a.materialDetalhamento.itemSuspenso === 'S') {
          return -1;
        } else if (b.materialDetalhamento.itemSuspenso === 'S' || b.materialDetalhamento.status === 'D') {
          return 1;
        } else {
          return 0;
        }
      });
    });
  }

  renderDataTablesLotes() {
    const carrinhoVazio = this.storeForm.computedLoteValues.every((lote) => lote.itens.length === 0);

    const computedLoteValues = this.storeForm.computedLoteValues.map((lote) => ({
      ...lote,
      itens: lote.itens,
    }));
    const buscaCarrinhoFormatted = this.regulaExpressao(this.state.buscaCarrinho);
    const listaCarrinho = computedLoteValues
      .filter((lote) => {
        return (
          this.regulaExpressao(lote.nome).includes(buscaCarrinhoFormatted) ||
          lote.itens.some((item) =>
            this.regulaExpressao(item.materialDetalhamento?.pdm.nome).includes(buscaCarrinhoFormatted)
          )
        );
      })
      .map((lote) => {
        if (!this.regulaExpressao(lote.nome).includes(buscaCarrinhoFormatted)) {
          lote.itens = lote.itens.filter((item) =>
            this.regulaExpressao(item.materialDetalhamento?.pdm.nome).includes(buscaCarrinhoFormatted)
          );
        }
        return lote;
      });

    const listaCarrinhoFiltered = this.state.buscaCarrinho === '' ? computedLoteValues : listaCarrinho;

    this.orderItensLoteByDepreciadoSuspenso(listaCarrinho);

    const indices = listaCarrinhoFiltered.map((_, i) => i);

    return (
      <>
        <Sidebar
          className="p-sidebar-md carrinho"
          visible={this.state.visibleRight}
          position="right"
          onHide={() => this.setState({ visibleRight: false })}
        >
          <div className="flex lote max-w-full justify-content-between align-items-center">
            <div className="ml-2 mt-0 text-base font-bold">
              <i
                className="pi pi-cart-plus"
                style={{
                  color: '#326FD1',
                  background: '#F5F9FF',
                  borderRadius: '50%',
                  padding: '13px',
                  fontSize: '18px',
                  marginRight: '5px',
                }}
              ></i>
              Lotes/Itens Adicionados
            </div>
            <div>
              <FcButton
                style={{
                  color: '#ff0000',
                }}
                label="Excluir Todos"
                type="button"
                className="p-button-outlined"
                onClick={() => {
                  this.excluirTodosOsItens();
                }}
              />
            </div>
          </div>

          <div className="p-fluid p-formgrid p-grid p-3">
            <span className="p-field p-col-12">
              <span className="p-input-icon-left">
                <i className="pi pi-search" />
                <InputText value={this.state.buscaCarrinho} onChange={this.buscaItemCarrinho} placeholder="Buscar" />
              </span>
            </span>
          </div>

          <div className="lote-div px-0">
            {carrinhoVazio ? (
              <div className="text-center font-bold mt-4">Seu carrinho está vazio.</div>
            ) : (
              <Accordion multiple activeIndex={indices}>
                {listaCarrinhoFiltered.map((lote) => {
                  return (
                    <AccordionTab
                      className="lote mt-2"
                      header={<div className="font-bold text-base">{this.renderHeader(lote)}</div>}
                    >
                      <DataScroller
                        value={lote.itens}
                        emptyMessage="Nenhum item foi adicionado."
                        rows={lote.itens?.length ?? 0}
                        inline
                        itemTemplate={this.itemTemplate}
                      />
                      {this.state.isConfirmDialogRemoveItemVisible && this.excluirItem()}
                    </AccordionTab>
                  );
                })}
              </Accordion>
            )}
          </div>
        </Sidebar>
      </>
    );
  }

  renderHeader(lote) {
    return (
      <div className="flex lote max-w-full justify-content-between align-items-center">
        <div>{lote.nome}</div>
        <div>
          <FcButton
            icon="pi pi-pencil"
            tooltip="Editar"
            type="button"
            className="p-button-rounded p-button-text"
            onClick={() => {
              this.storeForm.editarLote = lote.key;
              this.switchDialogEditLote();
            }}
          />
          <FcButton
            icon="pi pi-trash"
            tooltip="Remover"
            type="button"
            style={{ color: '#FF3D32' }}
            className="p-button-rounded p-button-text"
            onClick={(e) => {
              e.stopPropagation();
              e.preventDefault();
              this.switchDialogRemoveLote(lote);
            }}
          />
        </div>
      </div>
    );
  }

  switchDialogRemoveLote(lote) {
    this.setState({
      isConfirmDialogRemoveLoteVisible: true,
      loteRemove: lote,
    });
  }

  renderRemoveLoteDialog() {
    return (
      <ConfirmDialog
        blockScroll
        visible={this.state.isConfirmDialogRemoveLoteVisible}
        message="Você realmente deseja excluir o lote selecionado?"
        header="Excluir Registro"
        icon="pi pi-exclamation-triangle"
        acceptClassName="p-button-danger"
        accept={() => {
          this.storeForm.removeLote(this.state.loteRemove);
          this.setState({ isConfirmDialogRemoveLoteVisible: false, loteRemove: null });
        }}
        onHide={() => this.setState({ isConfirmDialogRemoveLoteVisible: false, loteRemove: null })}
      />
    );
  }

  switchDialogNewLoteVisibility() {
    this.setState({ showDialogNewLote: !this.state.showDialogNewLote });
    this.storeForm.setNomeLote('', true);
  }

  switchDialogEditLote() {
    this.setState({ showDialogEditLote: !this.state.showDialogEditLote });
  }

  editLote(nomeLote) {
    this.setState({ showDialogNewLote: !this.state.showDialogNewLote });
    this.storeForm.editLote(nomeLote);
  }

  renderFooterLote() {
    return (
      <div>
        <FcButton
          label="Cancelar"
          icon="pi pi-times"
          onClick={() => {
            this.switchDialogNewLoteVisibility();
          }}
          className="p-button-text"
        />
        <FcButton
          label={this.storeForm.editingLote ? 'Salvar' : 'Adicionar'}
          icon="pi pi-check"
          onClick={() => {
            const { nomeLote, lotes, editingLote } = this.storeForm;
            if (this.storeForm.checkNomeLoteUnico(nomeLote) && lotes.size >= 1) {
              this.storeForm.showNotificacaoErroLoteUnico();
            } else {
              this.storeForm.criarLote();
              this.switchDialogNewLoteVisibility();
              showNotification(
                'info',
                `Lote ${editingLote ? 'editado' : 'criado'}`,
                `${editingLote ? 'Lote editado' : 'Novo lote criado'} no Termo de Referência!`
              );
            }
          }}
        />
      </div>
    );
  }

  renderFooterEditLote() {
    return (
      <div>
        <FcButton
          label="Cancelar"
          icon="pi pi-times"
          onClick={() => {
            this.setState({ valorNovoLote: '' });
            this.switchDialogEditLote();
          }}
          className="p-button-text"
        />
        <FcButton
          label={'Salvar'}
          icon="pi pi-check"
          onClick={() => {
            const { lotes } = this.storeForm;

            if (this.storeForm.checkNomeLoteUnico(this.state.valorNovoLote) && lotes.size > 1) {
              this.storeForm.showNotificacaoErroLoteUnico();
            } else {
              this.storeForm.editLote(this.state.valorNovoLote);
              this.storeForm.criarLote();
              this.storeForm.editarLote = '';
              this.switchDialogEditLote();
              showNotification('info', 'Lote editado', 'Lote editado no Termo de Referência!');
              this.setState({ valorNovoLote: '' });
            }
          }}
        />
      </div>
    );
  }

  renderNewLoteDialog() {
    return (
      <Dialog
        header={this.storeForm.editingLote ? 'Editar Lote' : 'Novo Lote'}
        visible={this.state.showDialogNewLote}
        style={{ width: '30vw' }}
        footer={this.renderFooterLote()}
        onHide={() => this.switchDialogNewLoteVisibility()}
      >
        <div className="p-fluid p-formgrid p-grid">
          <FormField
            columns={12}
            attribute="nome"
            label="Nome do Lote"
            infoTooltip="A palavra 'Lote' e a numeração serão inseridos automaticamente na exportação do termo."
          >
            <InputText
              value={this.storeForm.nomeLote}
              placeholder="Informe o nome do lote"
              onChange={(e) => {
                this.storeForm.setNomeLote(e.target.value);
                this.forceUpdate();
              }}
            />
          </FormField>
        </div>
      </Dialog>
    );
  }

  renderEditLoteDialog() {
    return (
      <Dialog
        header={'Editar Lote'}
        visible={this.state.showDialogEditLote}
        style={{ width: '30vw' }}
        footer={this.renderFooterEditLote()}
        onHide={() => this.switchDialogEditLote()}
      >
        <div className="p-fluid p-formgrid p-grid">
          <FormField
            columns={12}
            attribute="nome"
            label="Nome do Lote"
            infoTooltip="A palavra 'Lote' e a numeração serão inseridos automaticamente na exportação do termo."
          >
            <InputText
              placeholder="Informe o novo nome do lote"
              value={this.state.valorNovoLote}
              onChange={(e) => {
                this.setState({ valorNovoLote: e.target.value });
              }}
            />
          </FormField>
        </div>
      </Dialog>
    );
  }

  calculaQuantidadeItens() {
    let quantidade = 0;
    this.storeForm.lotes?.forEach((l) => (quantidade += l.itens?.length));
    return quantidade;
  }

  render() {
    return (
      <>
        {this.storeForm.isRequisicaoModificacao() && (
          <>
            <Fieldset legend="AVISO">
              <h6 style={{ color: '#dd0303' }}>
                A EDIÇÃO DESTE TERMO DE REFERÊNCIA ESTARÁ SUJEITA A RATIFICAÇÃO PELA EQUIPE DE AUDITORIA DO TCE
              </h6>
            </Fieldset>
            <Divider />
          </>
        )}
        <FormMaterial
          onSetMaterial={this.setMaterial}
          onClickCarrinho={() => this.setState({ visibleRight: true })}
          qtdItens={this.calculaQuantidadeItens()}
        />
        {this.renderDataTablesLotes()}
        {this.renderDialog()}
        {this.renderNewLoteDialog()}
        {this.renderEditLoteDialog()}
        {this.renderRemoveLoteDialog()}
      </>
    );
  }
}

AbaItensFormPage.propTypes = {
  store: PropTypes.any,
  srp: PropTypes.bool,
  tresCasasDecimais: PropTypes.bool,
  startAutomaticSave: PropTypes.any,
};

export default AbaItensFormPage;
