import React from 'react';
import { observer, PropTypes } from 'mobx-react';
import { Checkbox } from 'primereact/checkbox';
import { InputText } from 'primereact/inputtext';
import FormField from 'fc/components/FormField';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import { SelectButton } from 'primereact/selectbutton';
import { Message } from 'primereact/message';
import { Fieldset } from 'primereact/fieldset';
import { Divider } from 'primereact/divider';
import { ConfirmDialog } from 'primereact/confirmdialog';
import { RadioButton } from 'primereact/radiobutton';

@observer
class FormAbaDados extends React.Component {
  store;
  constructor(props) {
    super(props);
    this.store = this.props.store;
    this.state = {
      dialogVisibility: false,
      currentTipoValue: undefined,
      currentObraEngenharia: undefined,
      dialogVisibilityObraEngenharia: false,
    };
  }

  toggleDialogVisibility() {
    this.setState({ dialogVisibility: !this.state.dialogVisibility });
  }

  renderDialog() {
    return (
      <ConfirmDialog
        visible={this.state.dialogVisibility}
        message="O carrinho será esvaziado, tem certeza que deseja continuar?"
        header="Atualização do Tipo do Termo de Referência"
        onHide={() => this.toggleDialogVisibility()}
        accept={() => {
          this.store.updateTipo(this.state.currentTipoValue);
          this.store.excluiTodosOsItens();
        }}
        reject={() => this.toggleDialogVisibility()}
      />
    );
  }

  _toggleDialogVisibilityObraEngenharia() {
    this.setState({ dialogVisibilityObraEngenharia: !this.state.dialogVisibilityObraEngenharia });
  }

  _renderDialogObraEngenharia() {
    return (
      <ConfirmDialog
        visible={this.state.dialogVisibilityObraEngenharia}
        message={
          <div>
            Ao trocar a opção de Obra de Engenharia / Serviço Especial de Engenharia, os arquivos selecionados serão
            removidos do sistema.
            <br />
            Deseja continuar e aplicar a mudança, removendo os arquivos selecionados?
          </div>
        }
        header="Confirmação de Alteração"
        onHide={() => {
          this._toggleDialogVisibilityObraEngenharia();
        }}
        accept={() => {
          this.store.updateAttribute('arquivosTemporarios', []);
          this.store.fileStore.removeAllFiles();
          this.store.updateAttribute('obraEngenharia', this.state.currentObraEngenharia);
          this.store.carregarArquivosObrigatorios(this.state.currentLesgilacao);
          this.setState({ currentLesgilacao: undefined });
        }}
      />
    );
  }

  render() {
    if (this.store) {
      const { updateAttribute, getRule, updateAttributeCheckbox } = this.store;
      const { submitted } = this.props;
      return (
        <div className="p-fluid p-formgrid p-grid">
          {this.store.isRequisicaoModificacao() && (
            <>
              <Fieldset legend="AVISO" className="p-col-12">
                <h6 style={{ color: '#dd0303' }}>
                  A EDIÇÃO DESTE TERMO DE REFERÊNCIA ESTARÁ SUJEITA A RATIFICAÇÃO PELA EQUIPE DE AUDITORIA DO TCE
                </h6>
              </Fieldset>
              <Divider />
            </>
          )}
          <FormField
            columns={4}
            attribute="identificadorProcesso"
            label="Identificador do Termo de Referência"
            infoTooltip="Sugestão: resumo do objeto, número da licitação ou número do processo administrativo"
            rule={getRule('identificadorProcesso')}
            submitted={submitted}
          >
            <InputText
              onChange={(e) => updateAttribute('identificadorProcesso', e)}
              value={this.store.object.identificadorProcesso}
              placeholder="Informe o identificador do processo"
            />
          </FormField>
          <FormField columns={4} attribute="modelo" label="Modelo" rule={getRule('modelo')} submitted={submitted}>
            <SelectButton
              value={this.store.object.modelo}
              disabled={this.props.requisicaoModificao}
              onChange={(e) => {
                if (e.value !== null && e.value != this.store.object.modelo) {
                  updateAttribute('modelo', e);
                }
              }}
              options={DadosEstaticosService.getSimNao()}
              optionLabel="text"
              optionValue="value"
            />
          </FormField>
          {this.store.object?.modelo && (
            <Message
              severity="warn"
              text="Um Termo de Referência Modelo não poderá ser associado a nenhum processo licitatório, apenas utilizado para gerar novos termos."
              style={{ width: '100%', marginBottom: '10px' }}
            />
          )}
          {this.state.dialogVisibility && this.renderDialog()}
          <FormField
            columns={6}
            attribute="srp"
            label="Sistema de Registro de Preços (SRP)"
            rule={getRule('srp')}
            submitted={submitted}
          >
            <div className="p-field-radiobutton p-dir-row">
              <div className="p-field-radiobutton p-col">
                <RadioButton
                  inputId="srpSim"
                  name="srp"
                  value={true}
                  onChange={(e) => this.store.updateAttribute('srp', e.value)}
                  checked={this.store.object.srp === true}
                />
                <label htmlFor="srpSim">Sim</label>
              </div>
              <div className="p-field-radiobutton p-col">
                <RadioButton
                  inputId="srpNao"
                  name="srp"
                  value={false}
                  onChange={(e) => this.store.updateAttribute('srp', e.value)}
                  checked={this.store.object.srp === false}
                />
                <label htmlFor="srpNao">Não</label>
              </div>
            </div>
          </FormField>
          <div className="p-col-6"></div>
          <FormField checkbox columns={6} label="Valor de Itens com Três Casas Decimais" attribute="tresCasasDecimais">
            <Checkbox
              onChange={(e) => {
                updateAttributeCheckbox('tresCasasDecimais', e);
              }}
              checked={this.store.object.tresCasasDecimais}
            ></Checkbox>
          </FormField>
          {this.state.dialogVisibilityObraEngenharia && this._renderDialogObraEngenharia()}
          <FormField
            columns={6}
            attribute="obraEngenharia"
            label="Obra de Engenharia / Serviço Especial de Engenharia"
            checkbox={true}
            submitted={submitted}
          >
            <Checkbox
              inputId="obraEngenharia"
              checked={this.store.object.obraEngenharia}
              onChange={(e) => {
                if (this.store.object?.arquivosTemporarios?.length) {
                  this.setState({ currentObraEngenharia: e.checked }, () =>
                    this._toggleDialogVisibilityObraEngenharia()
                  );
                } else {
                  updateAttributeCheckbox('obraEngenharia', e);
                  this.store.carregarArquivosObrigatorios(e.checked);
                }
              }}
              id="obraEngenharia"
            />
          </FormField>
        </div>
      );
    } else {
      return <></>;
    }
  }
}

FormAbaDados.propTypes = {
  store: PropTypes.any,
  submitted: PropTypes.any,
  requisicaoModificao: PropTypes.bool,
};

export default FormAbaDados;
