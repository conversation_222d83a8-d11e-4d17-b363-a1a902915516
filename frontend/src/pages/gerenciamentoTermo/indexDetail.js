import './Style.scss';
import { observer } from 'mobx-react';
import { Column } from 'primereact/column';
import { DataTable } from 'primereact/datatable';
import { Component } from 'react';
import {
  getValueMoney,
  getValue,
  getValueByKey,
  getValueElipsis,
  getNumberUnitThousands,
  getLightenColor,
  somaValoresLotes,
} from 'fc/utils/utils';
import PropTypes from 'prop-types';
import TermoIndexStore from '~/stores/licitacao/termoIndex';
import { Fieldset } from 'primereact/fieldset';
import GerenciamentoTermoFormStore from '~/stores/gerenciamentoTermo/formStore';
import MultipleFileUploader from 'fc/components/MultipleFileUploader';
import { observable } from 'mobx';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import ScrollMenu from 'fc/components/ScrollMenu';
import { Divider } from 'primereact/divider';
import { ProgressSpinner } from 'primereact/progressspinner';
import { Link } from 'react-router-dom/cjs/react-router-dom.min';
import FcButton from 'fc/components/FcButton';
import FcCloseableTabView from 'fc/components/FcCloseableTabView';
import AppStore from 'fc/stores/AppStore';
import AccessPermission from '~/constants/AccessPermission';
import UrlRouter from '~/constants/UrlRouter';
import classNames from 'classnames';
import { Row } from 'primereact/row';
import { ColumnGroup } from 'primereact/columngroup';
import LicitacaoDetailPage from '../licitacao/boardLicitacoes/detalhes/IndexDetail';
import CaronaIndexDetailPage from '../carona/detalhes/indexDetail';
import InexigibilidadeIndexDetailPage from '../inexigibilidade/detalhes/indexDetail';
import DispensaIndexDetailPage from '../dispensa/detalhes/indexDetail';
import ExportPdfTermoButton from './exportPdfTermoButton';
import { Tag } from 'primereact/tag';

@observer
class TermoReferenciaDetails extends Component {
  @observable termoReferenciaStore;
  constructor(props) {
    super(props);
    this.termoReferenciaStore = new GerenciamentoTermoFormStore();
    this.state = {
      treeData: [],
      loadedFiles: false,
      step: 1,
      activeTabIndex: 0,
    };
  }

  componentDidMount() {
    const { id } = this.props;
    if (id) {
      this.termoReferenciaStore.initialize(id, {}, () => {
        this.termoReferenciaStore.recuperarArquivos(id, () => {
          this.setState({ loadedFiles: true });
        });
      });
    }
  }

  componentDidUpdate(prevProps) {
    const { id } = this.props;

    if (prevProps.id !== id) {
      this.termoReferenciaStore.initialize(id, {}, () => {
        this.termoReferenciaStore.recuperarArquivos(id, () => {
          this.setState({ loadedFiles: true });
        });
      });
    }
  }

  renderTabItens(lotes) {
    const data = this.termoReferenciaStore?.object;
    const { store, formatList } = this.props;
    const headerQuantidade = data?.srp ? 'Quantidade para Registro' : 'Quantidade';

    const somaQuantidades = (itens) => {
      let total = 0;
      for (let item of itens) {
        total += item.quantidade;
      }

      return getNumberUnitThousands(total);
    };

    const somaQuantidadesParaConsumo = (itens) => {
      const total = itens.reduce((total, item) => {
        const quantidade = isNaN(item.quantidadeConsumo) ? 0 : item.quantidadeConsumo;
        return total + quantidade;
      }, 0);

      return getNumberUnitThousands(total);
    };

    const somaQuantidadesParaRegistroLotes = (lotes) => {
      return getNumberUnitThousands(
        lotes.reduce((total, lote) => {
          return total + lote.itens.reduce((acc, item) => acc + item.quantidade, 0);
        }, 0)
      );
    };

    const somaQuantidadesParaConsumoLotes = (lotes) => {
      return getNumberUnitThousands(
        lotes.reduce((total, lote) => {
          return total + lote.itens.reduce((acc, item) => acc + item.quantidadeConsumo, 0);
        }, 0)
      );
    };

    const somaValores = (itens) => {
      let total = 0;
      for (let item of itens) {
        total += item.quantidade * item.valorUnitarioEstimado;
      }

      return getValueMoney(total);
    };

    const numerosItens = (lotes) => {
      const numeros = {};

      lotes.forEach((lote) => {
        let numero = 1;
        lote.itens.forEach((item) => {
          numeros[item.id] = numero++;
        });
      });

      return numeros;
    };

    const columnsItens = [
      {
        header: 'Nº',
        body: ({ id }) => numerosItens(lotes)[id],
        style: { width: '5%' },
      },
      {
        header: 'Código',
        body: ({ materialDetalhamento }) => getValue(materialDetalhamento?.codigo),
        sortable: true,
      },
      {
        header: 'Descrição',
        body: ({ materialDetalhamento }) => {
          const descricao = <span style={{ display: 'block' }}>{getValue(materialDetalhamento?.pdm?.nome)}</span>;

          const caracteristicas = materialDetalhamento?.caracteristicas.map(({ textCaracteristica }) => (
            <Tag style={{ color: '#3F51B5', backgroundColor: 'rgba(63, 81, 181, 0.2)', marginRight: '0.5rem' }}>
              <span>{textCaracteristica}</span>
            </Tag>
          ));

          const itemDepreciado = materialDetalhamento.status === 'D' && (
            <Tag
              style={{
                color: '#FF0000',
                backgroundColor: getLightenColor('#FA8072', 0.4),
                marginRight: '0.5rem',
                border: '1px solid #8B0000',
              }}
            >
              <span>Item Depreciado</span>
            </Tag>
          );

          return (
            <>
              {descricao}
              {itemDepreciado}
              {caracteristicas}
            </>
          );
        },
        sortable: true,
        style: { width: '15%' },
      },
      {
        field: 'descricaoComplementar',
        header: 'Descrição Complementar',
        body: (materialDetalhamento) => {
          return (
            <div className="flex align-items-right">
              <span className="tooltip-elipsis text-justify">
                {getValueElipsis(materialDetalhamento.descricaoComplementar, 'inf')}
              </span>
            </div>
          );
        },
        simpleBody: (materialDetalhamento) => getValue(materialDetalhamento.descricaoComplementar),
        sortable: true,
        style: { width: '15%' },
      },
      {
        header: 'Unidades de Medida',
        style: { width: '15%' },
        body: ({ unidadeMedida, materialDetalhamento }) =>
          getValue(
            unidadeMedida?.textUnidadeMedida ??
              materialDetalhamento?.pdm?.unidadesMedida?.map((um) => um.descricao).join(', ')
          ),
        sortable: true,
      },
      {
        field: 'quantidade',
        header: <td>{headerQuantidade}</td>,
        style: { textAlign: 'right', width: '15%' },
        body: (item) => getNumberUnitThousands(item.quantidade, item.fracionario),
        sortable: true,
        headerClassName: 'align-th-right',
      },
      {
        field: 'valorUnitarioEstimado',
        header: 'Valor Unitário',
        body: ({ valorUnitarioEstimado }) => getValueMoney(valorUnitarioEstimado, data?.tresCasasDecimais ? 3 : 2),
        sortable: true,
        style: { textAlign: 'right', width: '10%' },
        headerClassName: 'align-th-right',
      },
      {
        header: 'Valor Total',
        body: ({ valorUnitarioEstimado, quantidade }) =>
          getValueMoney(valorUnitarioEstimado * quantidade, data?.tresCasasDecimais ? 3 : 2),
        sortable: true,
        style: { textAlign: 'right', width: '10%' },
        headerClassName: 'align-th-right',
      },
    ];
    if (data?.srp) {
      columnsItens.splice(5, 0, {
        field: 'quantidadeConsumo',
        header: 'Quantidade para Consumo',
        style: { textAlign: 'right', width: '15%' },
        body: (item) => getNumberUnitThousands(item.quantidadeConsumo, item.fracionario),
        sortable: true,
        headerClassName: 'align-th-right',
      });
    }

    const rowItens = (itens, loteAtual, quantLotes) => {
      const tituloFooterTotalLote = quantLotes === 1 ? 'Total ' : `Total lote ${loteAtual}`;
      const tituloFooterRegistro = data?.srp ? 'Total para Registro: ' : 'Total: ';
      return (
        <ColumnGroup>
          <Row>
            <Column />
            <Column />
            <Column />
            <Column />
            <Column footer={`${tituloFooterTotalLote}:`} />
            <Column footer={somaQuantidadesParaConsumo(itens)} footerStyle={{ textAlign: 'right' }} />
            {data?.srp && <Column footer={somaQuantidades(itens)} footerStyle={{ textAlign: 'right' }} />}
            <Column />
            <Column footer={somaValores(itens)} footerStyle={{ textAlign: 'right' }} />
          </Row>
          {loteAtual === quantLotes && quantLotes > 1 && (
            <Row>
              <Column />
              <Column />
              <Column />
              <Column />
              <Column
                footer={`${tituloFooterRegistro}${somaQuantidadesParaRegistroLotes(lotes)}`}
                footerStyle={{ textAlign: 'right' }}
              />
              {data?.srp && (
                <Column
                  footer={` Total para Consumo: ${somaQuantidadesParaConsumoLotes(lotes)}`}
                  footerStyle={{ textAlign: 'right' }}
                />
              )}
              <Column />
              <Column footer={` Valor Total: ${somaValoresLotes(lotes)}`} footerStyle={{ textAlign: 'right' }} />
            </Row>
          )}
        </ColumnGroup>
      );
    };

    const allItens = [];
    lotes.forEach((lote) => allItens.push(...lote.itens));

    store?.setExportItens({
      columns: columnsItens.map((col) => ({ title: col.header, dataKey: col.field })),
      list: formatList(allItens, columnsItens),
    });

    let loteAtual = 0;

    if (lotes.length != 0) {
      return (
        <div className="lote-div">
          {lotes.map((lote) => {
            loteAtual += 1;
            return (
              <>
                <DataTable
                  rowHover
                  header={lote.nome}
                  value={lote.itens}
                  footerColumnGroup={rowItens(lote.itens, loteAtual, lotes.length)}
                  paginator
                  rows={5}
                >
                  {this.renderColumns(columnsItens)}
                </DataTable>
              </>
            );
          })}
        </div>
      );
    } else {
      return (
        <div className="lote-div">
          <DataTable rowHover emptyMessage="Nenhum item ou lote foi adicionado." paginator rows={5}>
            {this.renderColumns(columnsItens)}
          </DataTable>
        </div>
      );
    }
  }

  renderTabProcesso() {
    let content = <></>;
    let title = 'Processo Associado';
    let tipoProcesso = this.termoReferenciaStore.processoAssociado.tipoProcesso;
    let idProcesso = this.termoReferenciaStore.processoAssociado.idProcesso;
    if (tipoProcesso === 'L') {
      title += ' - Licitação';
      content = <LicitacaoDetailPage id={idProcesso} countDownloadRequest />;
    } else if (tipoProcesso === 'D') {
      title += ' - Dispensa';
      content = <DispensaIndexDetailPage id={idProcesso} />;
    } else if (tipoProcesso === 'I') {
      title += ' - Inexigibilidade';
      content = <InexigibilidadeIndexDetailPage id={idProcesso} />;
    } else if (tipoProcesso === 'C') {
      title += ' - Adesão/Carona';
      content = <CaronaIndexDetailPage id={idProcesso} countDownloadRequest />;
    }
    return { content: content, title: title };
  }

  renderColumns(columns) {
    return columns.map((col) => <Column key={`col-${col.field}`} {...col} />);
  }

  renderSections() {
    const idTermo = this.props.id;
    const { treeData } = this.state;
    const linksScrollMenu = treeData.map((section) => ({
      id: `${section.label}-${idTermo}`,
      label: section.label,
    }));

    return (
      <div>
        <div className="relative">
          <div className="scroll-menu">
            <ScrollMenu title="sumário" layoutPosition="left" offsetTopOnScroll={50} links={linksScrollMenu} />
          </div>
        </div>
        {treeData.map((section, idx) => {
          return (
            <div id={`${section.label}-${idTermo}`}>
              <Fieldset
                className="ck-content"
                legend={`${section.ordem} - ${section.label}`}
                toggleable={section.child}
                key={`${idx}-fieldset-element`}
                collapsed={this.props.collapsedSections && !idx == 0}
              >
                {section?.child?.label ?? (
                  <span style={{ color: '#ff0000' }}>Não foi adicionado conteúdo nesta seção.</span>
                )}
              </Fieldset>
            </div>
          );
        })}
      </div>
    );
  }

  getEntidadeTermo() {
    const data = this.termoReferenciaStore?.object;
    const identificador = data.identificadorProcesso && data.identificadorProcesso + '/';
    return `Termo de Referência - ${identificador ?? ''}${data.entidade.nome}`;
  }

  getLotes() {
    const data = this.termoReferenciaStore?.object;
    const lotes = [];
    const itens = [];
    let contLotes = 0;
    data?.lotes.forEach((lote) => {
      if (!lote.gerado) {
        contLotes++;
        lotes.push({
          nome: `Lote ${contLotes} - ${lote.nome}`,
          itens: lote.itens,
        });
      } else {
        itens.push(...lote.itens);
      }
    });

    if (itens.length > 0) {
      lotes.push({ nome: 'Itens', itens });
    }

    return lotes;
  }

  getTabs(lotes) {
    const data = this.termoReferenciaStore?.object;
    const { treeData } = this.state;
    const showSecoes = data?.formaPreenchimentoSecao === 'PREENCHIMENTO_MANUAL';
    const tabProcesso = this.renderTabProcesso();

    const tabs = [];
    tabs.push({
      id: 0,
      header: 'Catálogo',
      content: this.renderTabItens(lotes),
    });
    if (!showSecoes) {
      tabs.push({
        id: 1,
        header: 'Arquivo',
        content: (
          <MultipleFileUploader
            downloadOnly
            store={this.termoReferenciaStore?.fileStore}
            fileTypes={DadosEstaticosService.getTipoArquivoTermoReferencia()}
            key={`termo(${data.id})-files(${this.termoReferenciaStore.fileStore.uploadedFiles
              ?.map(({ idArquivo }) => idArquivo)
              .join(',')})`}
          />
        ),
      });
    } else {
      tabs.push({
        id: 1,
        header: 'Seções',
        content: (
          <>
            {!treeData.length && <>Não há seções cadastradas.</>}
            {treeData.length && !store.loadingArvore ? this.renderSections() : null}
          </>
        ),
      });
    }
    if (this.termoReferenciaStore.processoAssociado.tipoProcesso) {
      tabs.push({
        id: 2,
        header: tabProcesso.title,
        content: tabProcesso.content,
      });
    }
    return tabs;
  }

  _renderDivider(label) {
    return (
      <div style={{ flexBasis: 'auto', display: 'flex' }}>
        <h5
          style={{
            padding: '7px',
            whiteSpace: 'nowrap',
            width: 'auto',
            color: '#333333',
            alignContent: 'left',
            marginRight: '5px',
            marginTop: '11px',
          }}
        >
          {label}
        </h5>
        <Divider />
      </div>
    );
  }

  getValueStyle() {
    return { fontSize: AppStore.layout === 'extenso' ? '1.26rem' : '1.1rem' };
  }

  getValueLayoutClassNames() {
    const tipoLayout = AppStore.layout;
    return {
      'py-0': tipoLayout === 'compacto',
      'py-2': tipoLayout === 'default' || tipoLayout === 'extenso',
    };
  }

  _renderValue(label, value, col = 12, type = 'value', url = '#') {
    return (
      <div
        style={this.getValueStyle()}
        className={classNames(
          `details-set details-display flex align-items-center p-col-12 p-md-${col} p-sm-${col >= 6 ? col : 6}`,
          this.getValueLayoutClassNames()
        )}
      >
        <div
          style={{ color: 'rgb(0 0 0 / 50%)', paddingLeft: 0 }}
          className="xl:col-2 lg:col-3 md:col-4 sm:col-4 details-attributte drawer-content-label"
        >
          {label}
        </div>

        {type == 'value' && (
          <div style={{ paddingLeft: 0 }} className="xl:col-10 lg:col-9 md:col-8 sm:col-8 details-value p-text-justify">
            {value ?? '-'}
          </div>
        )}

        {type == 'list' && (
          <div style={{ display: 'block', paddingLeft: 0 }}>
            {value?.length ? value.map((value) => <div className={`details-value p-text-justify`}>{value}</div>) : '-'}
          </div>
        )}

        {type == 'link' && (
          <div style={{ paddingLeft: 0 }} className="xl:col-10 lg:col-9 md:col-8 sm:col-8 details-value p-text-justify">
            {
              <Link target="_blank" rel="noreferrer" style={{ color: '#3F51B5' }} to={url}>
                <u>{value}</u>
              </Link>
            }
          </div>
        )}
      </div>
    );
  }

  render() {
    const data = this.termoReferenciaStore?.object;
    const { history } = this.props;
    const { step, activeTabIndex } = this.state;
    const lotes = this.getLotes();
    if (this.state.loadedFiles && !this.termoReferenciaStore.loading) {
      return (
        <>
          {!data?.blockedInProcessReqMod ? (
            <div className="p-col-12" style={{ justifyContent: 'end', display: 'flex' }}>
              <ExportPdfTermoButton data={data} id={data.id} lotes={lotes} />
              {AppStore.hasPermission(AccessPermission.gerenciamentoTermo.writePermission) && (
                <FcButton
                  type="button"
                  label="Editar"
                  icon="pi pi-pencil"
                  tooltip={data?.isFinalizado ? 'Criar Requisição de Modificação' : 'Editar'}
                  disabled={data?.isFinalizado && !data?.canCreateReqModificacao}
                  onClick={() =>
                    history?.push({
                      pathname: data?.isFinalizado
                        ? UrlRouter.administracao.requisicaoModificacao.termoReferencia.requisitar.replace(
                            ':id',
                            data?.id
                          )
                        : UrlRouter.termoReferencia.gerenciamentoTermos.editar.replace(':id', data?.id),
                      state: { step: step },
                    })
                  }
                />
              )}
            </div>
          ) : (
            <Fieldset legend="AVISO">
              <h6 style={{ color: '#dd0303' }}>PENDENTE DE APROVAÇÃO DA SUBSTITUIÇÃO.</h6>
            </Fieldset>
          )}

          <div className="relative">
            <div className="scroll-menu">
              <ScrollMenu
                title="sumário"
                layoutPosition="left"
                offsetTopOnScroll={50}
                links={[{ id: `dadosBasicos-${data?.id}`, label: 'Dados Básicos' }]}
              />
            </div>
          </div>
          <div className="p-fluid p-form">
            <div id={`dadosBasicos-${data?.id}`}>{this._renderDivider('Dados Básicos')}</div>
            {this._renderValue('Identificador do Termo de Referência', data?.identificadorProcesso, 12)}
            {this._renderValue('Modelo', getValueByKey(data?.modelo, DadosEstaticosService.getSimNao()), 12)}
            {this._renderValue(
              'Sistema de Registro de Preços (SRP)',
              getValueByKey(data?.srp, DadosEstaticosService.getSimNao()),
              12
            )}
            {this._renderValue(
              'Obra de Engenharia / Serviço Especial de Engenharia',
              getValueByKey(data?.obraEngenharia, DadosEstaticosService.getSimNao()),
              12
            )}
            {this._renderValue(
              'Valor de Itens com Três Casas Decimais',
              getValueByKey(data?.tresCasasDecimais, DadosEstaticosService.getSimNao()),
              12
            )}
          </div>
          <Divider style={{ marginBottom: `1px` }} />
          <div className="p-col-12">
            <FcCloseableTabView
              tabs={this.getTabs(lotes)}
              activeTabIndex={activeTabIndex}
              onChangeTab={(tab) => this.setState({ activeTabIndex: tab.id })}
            />
          </div>
        </>
      );
    } else {
      return (
        <div className="p-d-inline p-d-flex align-items-right">
          <ProgressSpinner />
        </div>
      );
    }
  }
}

TermoReferenciaDetails.propTypes = {
  id: PropTypes.number.isRequired,
  store: PropTypes.objectOf(TermoIndexStore).isRequired,
  formatList: PropTypes.func.isRequired,
  collapsedSections: PropTypes.bool,
  history: PropTypes.any,
  disabledEditButton: PropTypes.bool,
};

export default TermoReferenciaDetails;
