import React from 'react';
import { observer } from 'mobx-react';
import PropTypes from 'prop-types';
import LinkConsultaFormStore from '../../stores/linkConsulta/formStore';
import UrlRouter from '../../constants/UrlRouter';
import GenericFormPage from 'fc/pages/GenericFormPage';
import FormField from 'fc/components/FormField';
import { Checkbox } from 'primereact/checkbox';
import { ColorPicker } from 'primereact/colorpicker';
import AsyncDropdown from 'fc/components/AsyncDropdown';
import { InputMask } from 'primereact/inputmask';
import AppBreadCrumb from 'fc/components/AppBreadCrumb';
import PermissionProxy from 'fc/components/PermissionProxy';
import AccessPermission from '~/constants/AccessPermission';
import FcInputTextarea from 'fc/components/FcInputTextarea';

@observer
class LinkConsultaFormPage extends GenericFormPage {
  constructor(props) {
    super(props, UrlRouter.administracao.linkConsulta.index, AccessPermission.geradorLinks);

    this.store = new LinkConsultaFormStore();
  }

  render() {
    const { submitted } = this.state;
    const { updateAttribute, getRule } = this.store;
    const { submitFormData } = this;

    const breacrumbItems = [
      { label: 'Gerador de Links', url: UrlRouter.administracao.linkConsulta.index },
      { label: this.props.action === 'new' ? 'Novo' : 'Editar' },
    ];

    let content;
    if (this.store.object) {
      content = (
        <>
          <AppBreadCrumb items={breacrumbItems} />
          <div className="card page form-action-buttons">
            <form onSubmit={submitFormData}>
              <div className="p-fluid p-formgrid p-grid">
                <FormField
                  columns={12}
                  attribute="entidade"
                  label="Entidade"
                  rule={getRule('entidade')}
                  submitted={submitted}
                >
                  <AsyncDropdown
                    onChange={updateAttribute}
                    value={this.store.object.entidade?.id ?? undefined}
                    placeholder="Selecione a entidade"
                    store={this.store.entidadeSelectStore}
                  />
                </FormField>

                <FormField
                  columns={5}
                  attribute="descricaoLink"
                  label="Descrição do Link"
                  rule={getRule('descricaoLink')}
                  submitted={submitted}
                >
                  <FcInputTextarea
                    rows={5}
                    cols={10}
                    onChange={(e) => updateAttribute('descricaoLink', e)}
                    placeholder="Informe a descrição do link"
                    maxLength="4000"
                    value={this.store.object.descricaoLink}
                    id="descricaoLink"
                  />
                </FormField>

                <FormField columns={3} attribute="anoExercicio" label="Ano" submitted={submitted}>
                  <InputMask
                    mask="9999"
                    onChange={(e) => updateAttribute('anoExercicio', e)}
                    placeholder="Informe o ano"
                    value={this.store.object.anoExercicio}
                    id="anoExercicio"
                  ></InputMask>
                </FormField>

                <FormField
                  columns={4}
                  attribute="fonteRecurso"
                  label="Fontes de Recurso"
                  rule={getRule('fonteRecurso')}
                  submitted={submitted}
                >
                  <AsyncDropdown
                    onChange={updateAttribute}
                    value={this.store.object.fonteRecurso?.id ?? undefined}
                    placeholder="Selecione a fonte de recurso"
                    store={this.store.fonteRecursoSelectStore}
                  />
                </FormField>

                <FormField
                  columns={6}
                  attribute="semTopo"
                  label="Sem Topo"
                  checkbox={true}
                  rule={getRule('semTopo')}
                  submitted={submitted}
                >
                  <Checkbox
                    inputId="semTopo"
                    checked={this.store.object.semTopo}
                    onChange={(e) => this.store.updateAttributeCheckbox('semTopo', e)}
                    id="semTopo"
                  />
                </FormField>
                <FormField
                  columns={6}
                  attribute="corTema"
                  label="Cor Tema "
                  rule={getRule('corTema')}
                  submitted={submitted}
                >
                  <ColorPicker
                    value={this.store.object.corTema}
                    onChange={(e) => updateAttribute('corTema', e)}
                    defaultColor="0066CC"
                    style={{ width: '45px', paddingLeft: '10px' }}
                    id="corTema"
                  />
                </FormField>
              </div>
              {this.renderActionButtons()}
            </form>
          </div>
        </>
      );
    } else {
      content = (
        <div className="card page">
          <AppBreadCrumb items={breacrumbItems} />
          <i
            className="pi pi-spin pi-spinner"
            style={{
              marginTop: '20px',
              marginLeft: 'calc(50% - 20px)',
              fontSize: '2em',
            }}
          ></i>
        </div>
      );
    }
    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        {content}
      </PermissionProxy>
    );
  }
}
LinkConsultaFormPage.propTypes = {
  id: PropTypes.any,
  action: PropTypes.any,
  history: PropTypes.any,
};

export default LinkConsultaFormPage;
