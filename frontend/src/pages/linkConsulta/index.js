import React from 'react';
import { observer } from 'mobx-react';
import LinkConsultaIndexStore from '../../stores/linkConsulta/indexStore';
import FcButton from 'fc/components/FcButton';
import { PrimeIcons } from 'primereact/api';
import UrlRouter from '~/constants/UrlRouter';
import GenericIndexPage from 'fc/pages/GenericIndexPage';
import AdvancedSearch from 'fc/components/AdvancedSearch';
import AppBreadCrumb from 'fc/components/AppBreadCrumb';
import IndexDataTable from 'fc/components/IndexDataTable';
import PermissionProxy from 'fc/components/PermissionProxy';
import AccessPermission from '~/constants/AccessPermission';

@observer
class LinkConsultaIndexPage extends GenericIndexPage {
  constructor(props) {
    super(props, AccessPermission.geradorLinks);
    this.store = new LinkConsultaIndexStore();
    this.state = {
      idRemove: null,
    };
  }

  detailsMessage(data) {
    return (
      <div>
        <strong>Entidade : </strong> <span>{data.entidade}</span> <br /> <br />
        <strong>Descrição : </strong> <span>{data.descricaoLink}</span> <br /> <br />
      </div>
    );
  }

  clickableLink(parametroLink) {
    return (
      <a target="_blank" rel="noreferrer" href={parametroLink}>
        {parametroLink}
      </a>
    );
  }

  render() {
    const columns = [
      {
        field: 'parametroLink',
        header: 'Link',
        body: ({ parametroLink }) => this.clickableLink(parametroLink),
        sortable: false,
      },
      {
        field: 'entidade.nome',
        header: 'Entidade',
        sortable: true,
      },
      {
        field: 'descricaoLink',
        header: 'Descrição',
        sortable: true,
      },
      {
        style: { width: '110px' },
        body: (rowData) => {
          return (
            <div className="actions p-d-flex p-jc-end">
              <PermissionProxy resourcePermissions={this.getReadPermission()}>
                <FcButton
                  icon="pi pi-pencil"
                  className="p-button-sm p-button-success p-mr-2"
                  onClick={() =>
                    this.pushUrlToHistory(UrlRouter.administracao.linkConsulta.editar.replace(':id', rowData.id))
                  }
                />
              </PermissionProxy>
              <PermissionProxy resourcePermissions={this.getWritePermission()}>
                <FcButton
                  icon="pi pi-trash"
                  className="p-button-sm p-button-danger"
                  onClick={() => {
                    this.setState({ idRemove: rowData.id });
                    this.store.toggleShowConfirmDialog();
                  }}
                />
              </PermissionProxy>
            </div>
          );
        },
      },
    ];

    const header = (
      <div className="table-header">
        <PermissionProxy resourcePermissions={this.getWritePermission()}>
          <FcButton
            className="p-button"
            label="Novo"
            style={{ marginBottom: '5px', marginRight: '5px' }}
            icon={PrimeIcons.PLUS}
            onClick={() => this.pushUrlToHistory(UrlRouter.administracao.linkConsulta.novo)}
          />
        </PermissionProxy>
      </div>
    );

    const { listKey, loading } = this.store;
    const { getDefaultTableProps } = this;

    const breacrumbItems = [{ label: 'Gerador de Links' }];

    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        <AppBreadCrumb items={breacrumbItems} />
        <div className="card page index-table">
          <AdvancedSearch
            searchParams={this.store.getAdvancedSearchParams()}
            store={this.store}
            searchFields={['entidade']}
            filterSuggest={this.store.getFilterSuggest()}
          />
          <IndexDataTable
            columns={columns}
            value={listKey}
            header={header}
            loading={loading}
            {...getDefaultTableProps()}
            style={{ wordWrap: 'break-word' }}
          />
          {this.store.isConfirmDialogVisible && this.confirmRemove(this.state.idRemove)}
        </div>
      </PermissionProxy>
    );
  }
}

LinkConsultaIndexPage.displayName = 'LinkConsultaIndexPage';

export default LinkConsultaIndexPage;
