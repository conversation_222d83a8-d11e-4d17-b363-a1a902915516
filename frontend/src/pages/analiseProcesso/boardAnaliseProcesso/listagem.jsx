import React from 'react';
import PropTypes from 'prop-types';
import './style.scss';
import { observer } from 'mobx-react';
import AdvancedSearch from 'fc/components/AdvancedSearch';
import LazyScroller from '~/pages/analiseProcesso/boardAnaliseProcesso/lazyScroller';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import AppStore from 'fc/stores/AppStore';
import { checkUserContextIsInspetor } from 'fc/utils/utils';

@observer
class BoardAnaliseProcessoListagemPage extends React.Component {
  constructor(props) {
    super(props);
    this.store = this.props.boardStore;
    this.store.setRefresh(() => this._refreshLazys());
    this.state = {
      reloadLazys: false,
      collapsedRays: {
        cadastrados: !checkUserContextIsInspetor(),
        auditoria: !checkUserContextIsInspetor(),
        em_analise: false,
        alertas: false,
        concluidos: false,
      },
    };

    this.toggleCollapseRay = this.toggleCollapseRay.bind(this);
  }

  componentDidMount() {
    this.store.initializeLimiarRisco();
    const userDetails = AppStore.getData('userDetails');
    const key = `${window.location.hash}-${userDetails.id}`;
    if (localStorage.getItem(key)) {
      this.setState({ collapsedRays: JSON.parse(localStorage.getItem(key)) });
    }
  }

  _refreshLazys() {
    this.setState({ reloadLazys: !this.state.reloadLazys });
  }

  _getLazyScrollerSortFields() {
    return [
      { label: 'Data de Cadastro', value: 'dataCadastro' },
      { label: 'Data de Abertura', value: 'dataProcesso' },
      { label: 'Número', value: 'numero' },
      { label: 'Valor', value: 'valor' },
      { label: 'Valor de Risco', value: 'valorRisco' },
    ];
  }

  toggleCollapseRay(phaseKey) {
    this.setState((oldState) => {
      const { collapsedRays } = oldState;
      collapsedRays[phaseKey] = !collapsedRays[phaseKey];
      const userDetails = AppStore.getData('userDetails');
      localStorage.setItem(`${window.location.hash}-${userDetails.id}`, JSON.stringify(collapsedRays));
      return { collapsedRays: collapsedRays };
    });
  }

  render() {
    const { collapsedRays } = this.state;

    return (
      <>
        <AdvancedSearch
          searchParams={this.store.getAdvancedSearchParams()}
          store={this.store}
          searchFields={['entidade', 'numero', 'tipoProcesso', 'objeto', 'valor']}
          filterSuggest={this.store.getFilterSuggest()}
          useOr
        />
        <div className="processos-page-container">
          <div className="proc-rays-container">
            {DadosEstaticosService.getLabelsAnaliseProcesso().map((ray, idx) => (
              <>
                <LazyScroller
                  key={idx}
                  phaseKey={ray}
                  refresh={this.state.reloadLazys}
                  service={this.store.service}
                  advancedSearchParams={this.store.advancedSearchParams}
                  sortOptions={this._getLazyScrollerSortFields()}
                  toggleDetails={this.props.onDetail}
                  limiarValorRisco={this.store.limiarValorRisco}
                  collapseRay={collapsedRays[ray.phaseKey]}
                  toggleCollapseRay={() => this.toggleCollapseRay(ray.phaseKey)}
                />
              </>
            ))}
          </div>
        </div>
      </>
    );
  }
}

BoardAnaliseProcessoListagemPage.propTypes = {
  history: PropTypes.any,
  match: PropTypes.any,
  boardStore: PropTypes.any,
  onDetail: PropTypes.func,
};

export default BoardAnaliseProcessoListagemPage;
