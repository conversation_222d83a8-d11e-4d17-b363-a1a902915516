import React from 'react';
import './style.scss';
import { observer, PropTypes } from 'mobx-react';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import FcButton from 'fc/components/FcButton';
import { checkUserContextIsInspetor, checkUserGroup, generateFullURL } from 'fc/utils/utils';
import InexigibilidadeIndexDetailPage from '~/pages/inexigibilidade/detalhes/indexDetail';
import DispensaDetailPage from '~/pages/dispensa/detalhes/indexDetail';
import CaronaIndexDetailPage from '~/pages/carona/detalhes/indexDetail';
import PermissionProxy from 'fc/components/PermissionProxy';
import UrlRouter from '~/constants/UrlRouter';
import { ProgressSpinner } from 'primereact/progressspinner';
import LicitacaoDetailPage from '~/pages/licitacao/boardLicitacoes/detalhes/IndexDetail';
import { Dialog } from 'primereact/dialog';
import FormField from 'fc/components/FormField';
import TdaFormPage from '~/pages/tda/form';
import AplicarChecklistFormPage from '~/pages/aplicarChecklist/form';
import { ConfirmDialog } from 'primereact/confirmdialog';
import EmitirAlertaFormPage from '~/pages/emitirAlerta/form';
import FcDropdown from 'fc/components/FcDropdown';
import LicitacaoFormStore from '~/stores/licitacao/formStore';
import CredenciamentoIndexDetailPage from '~/pages/credenciamento/detalhes/indexDetail';

@observer
class AnaliseProcessoIndexDetail extends React.Component {
  constructor(props) {
    super(props);
    this.store = props.store;
    this._disableEditMode = this._disableEditMode.bind(this);
    this.renderLicitacao = this.renderLicitacao.bind(this);
    this.renderCarona = this.renderCarona.bind(this);
    this.renderDispensa = this.renderDispensa.bind(this);
    this.renderInexigibilidade = this.renderInexigibilidade.bind(this);
    this.renderCredenciamento = this.renderCredenciamento.bind(this);
    this.state = {
      visibleDialogArquivamento: false,
      visibleDialogConfirmEdital: false,
      visibleDialogEdital: false,
      idRemove: null,
      editMod: false,
      phase: undefined,
      action: '',
      editalExists: false,
    };
  }

  componentDidMount() {
    if (this.props.item.tipoProcesso === 'L') {
      this.licitacaoStore = new LicitacaoFormStore();
    }

    if (this.props.item) {
      const { idProcesso, tipoProcesso } = this.props.item;
      this.store.getTdaProcesso(idProcesso, tipoProcesso);
    }
  }

  handleEditalExists = (exists) => {
    this.setState({ editalExists: exists });
  };

  _disableEditMode() {
    this.setState({ editMod: false });
  }

  renderCredenciamento() {
    const { item } = this.props;

    return (
      <>
        <div style={{ width: '100%', textAlign: 'left' }}>
          <h4 style={{ marginLeft: '5px' }}>
            Credenciamento - {item.numero} - {item.entidade?.nome}
          </h4>
        </div>

        <CredenciamentoIndexDetailPage id={item.idProcesso} />
      </>
    );
  }

  renderCarona() {
    const { item } = this.props;
    return (
      <>
        <div style={{ width: '100%', textAlign: 'left' }}>
          <h4 style={{ marginLeft: '5px' }}>
            Carona - {item.numero} / {item.entidade?.nome}
          </h4>
        </div>

        <CaronaIndexDetailPage id={item.idProcesso} countDownloadRequest />
      </>
    );
  }

  renderInexigibilidade() {
    const { item } = this.props;

    return (
      <>
        <div style={{ width: '100%', textAlign: 'left' }}>
          <h4 style={{ marginLeft: '5px' }}>
            Inexigibilidade - {item.numero} - {item.entidade?.nome}
          </h4>
        </div>

        <InexigibilidadeIndexDetailPage id={item.idProcesso} />
      </>
    );
  }

  renderDispensa() {
    const { item } = this.props;
    return (
      <>
        <div style={{ width: '100%', textAlign: 'left' }}>
          <h4 style={{ margin: '5px' }}>
            Dispensa - {item.numero} - {item.entidade?.nome}
          </h4>
        </div>

        <DispensaDetailPage id={item.idProcesso} />
      </>
    );
  }

  renderLicitacao() {
    const { item } = this.props;
    return (
      <>
        <div style={{ width: '100%', textAlign: 'left' }}>
          <h4 style={{ margin: '5px' }}>
            {item.numero} - {item.entidade?.nome}
          </h4>
        </div>

        <LicitacaoDetailPage
          id={item.idProcesso}
          countDownloadRequest
          onDetailContratoAssociado={this.props.onDetailContratoAssociado}
          onDetailEdital={this.props.onDetailEdital}
          tipoProcesso={item.tipoProcesso}
          onEditalExists={this.handleEditalExists}
          store={this.licitacaoStore}
          key={this.licitacaoStore?.statusEditalProcessamento}
        />
      </>
    );
  }

  pushUrlToHistory(url) {
    url && this.props.history.push(url);
  }

  _toggleShowConfirmDialogArquivamento() {
    this.setState({ visibleDialogArquivamento: !this.state.visibleDialogArquivamento });
  }

  _toggleShowConfirmProcessarEdital() {
    this.setState({ visibleDialogEdital: !this.state.visibleDialogEdital });
  }

  _renderFooter() {
    return (
      <div>
        <FcButton
          label="Cancelar"
          icon="pi pi-times"
          onClick={() => this._toggleShowConfirmProcessarEdital()}
          className="p-button-text"
        />
        <FcButton
          label="Processar Edital"
          icon="pi pi-check"
          loading={this.store.loading}
          type="button"
          onClick={() => {
            this.licitacaoStore.editalProcessement(this.props.item.idProcesso, () => {
              this.setState({ editalExists: true });
              this.licitacaoStore.initialize(this.props.item.idProcesso, {}, () =>
                this.licitacaoStore.getStatusEditalProcessamento(() => this.forceUpdate())
              );
            });
            this._toggleShowConfirmProcessarEdital();
          }}
        />
      </div>
    );
  }

  confirmArquivamento(idAlerta, idProcesso) {
    const { load } = this.store;
    const { item, onClose } = this.props;

    return (
      <Dialog
        blockScroll
        header="Arquivar"
        visible={this.state.visibleDialogArquivamento}
        className="sm:w-22rem lg:w-26rem xl:w-30rem"
        onHide={() => this._toggleShowConfirmDialogArquivamento()}
        draggable={false}
        resizable={false}
        footer={
          <div className="flex justify-content-end">
            <FcButton
              label="Cancelar"
              type="button"
              className="p-ml-auto p-button-secondary p-mr-2"
              onClick={() => this._toggleShowConfirmDialogArquivamento()}
              loading={this.store.loading}
            />
            <FcButton
              label="Arquivar"
              onClick={() => {
                this.store.arquivarAlertaAnalise(idAlerta, idProcesso, this.props.item.tipoProcesso, () => {
                  this._toggleShowConfirmDialogArquivamento();
                  onClose(item);
                  load();
                });
              }}
              loading={this.store.loading}
            />
          </div>
        }
      >
        <div className="p-fluid p-formgrid p-grid">
          <FormField columns={12} attribute="statusArquivamentoProcesso" label="Status">
            <FcDropdown
              inOrder
              style={{ marginTop: '5px', minWidth: '15rem' }}
              optionLabel="label"
              optionValue="value"
              options={DadosEstaticosService.getStatusArquivamento().filter(
                (s) => !['REJEITADO', 'ARQUIVADO_AUTOMATICAMENTE'].includes(s.value)
              )}
              placeholder="Selecione um status"
              value={this.store.statusArquivamento}
              onChange={(e) => {
                this.store.updateStatusArquivamento(e.value);
              }}
            />
          </FormField>
        </div>
      </Dialog>
    );
  }

  _redirectDetail(item) {
    window.open(generateFullURL(UrlRouter.alerta.editar.replace(':id', item.idAlertaAnalise)), '_blank');
  }

  renderButtons() {
    const { item, onClose } = this.props;
    let buttons = [];
    if (item.labels === 'CADASTRADO') {
      buttons.push({
        icon: 'pi pi-arrow-right',
        tooltip: 'Mover para Análise',
        style: { marginLeft: '3px' },
        disabled: item.labels !== 'CADASTRADO' || !(checkUserContextIsInspetor() || checkUserGroup('Administrador')),
        onClick: () => this.store.moverParaAnalise(item.idProcesso, item.tipoProcesso, true, () => onClose(item)),
      });
    } else if (item.labels === 'AUDITORIA') {
      buttons.push({
        icon: 'pi pi-plus',
        tooltip: 'Criar TDA',
        style: { marginLeft: '3px' },
        visible: item.labels === 'AUDITORIA',
        disabled: !(checkUserContextIsInspetor() || checkUserGroup('Administrador')),
        onClick: () => this.setState({ phase: 'criarTDA', editMod: true, action: 'new' }),
      });
      buttons.push({
        icon: 'pi pi-arrow-left',
        tooltip: 'Mover para Cadastrados',
        style: { marginLeft: '3px' },
        disabled: !(checkUserContextIsInspetor() || checkUserGroup('Administrador')),
        onClick: () => this.store.moverParaAnalise(item.idProcesso, item.tipoProcesso, false, () => onClose(item)),
      });
    } else if (item.labels?.includes('EM_ANALISE')) {
      const disabled = this.state.editalExists;
      buttons.push({
        icon: 'pi pi-file-import',
        tooltip: 'Processar Edital',
        disabled: disabled || !checkUserGroup(['Auditor', 'Administrador']) || !(item.tipoProcesso === 'L'),
        onClick: () => {
          this.setState({ phase: 'processarEdital', action: 'edit' });
          this._toggleShowConfirmProcessarEdital();
        },
      });
      buttons.push({
        icon: 'pi pi-pencil',
        tooltip: 'TDA',
        side: 'top',
        style: { marginLeft: '3px' },
        disabled: !(checkUserContextIsInspetor() || checkUserGroup('Administrador')),
        onClick: () => this.setState({ phase: 'editarTDA', editMod: true, action: 'edit' }),
      });
      buttons.push({
        icon: 'pi pi-list',
        tooltip: 'Aplicar Checklist',
        style: { marginLeft: '3px' },
        disabled: !checkUserGroup(['Auditor', 'Administrador']),
        onClick: () => this.setState({ phase: 'aplicarChecklist', editMod: true, action: 'edit' }),
      });
      if (this.store.tdaProcesso && (checkUserGroup(['Administrador']) || checkUserContextIsInspetor())) {
        buttons.push({
          icon: 'pi pi-exclamation-triangle',
          tooltip: item.labels?.includes('REJEITADO_DIRETOR_DAFO') ? 'Visualizar Alerta' : 'Emitir Alerta',
          style: { marginLeft: '3px' },
          disabled:
            !(item.checklistFinalizado && (checkUserContextIsInspetor() || checkUserGroup(['Administrador']))) ||
            item.checklistRejeitado,
          onClick: () => {
            if (item.idAlertaAnalise) {
              this.setState({ phase: 'visualizarAlerta', editMod: true, action: 'edit' });
            } else {
              this.setState({ phase: 'emitirAlerta', editMod: true, action: 'new' });
            }
          },
        });
        buttons.push({
          icon: 'pi pi-file',
          tooltip: 'Arquivar',
          className: 'p-button-danger',
          style: { marginLeft: '3px' },
          onClick: () => {
            this.setState({ idRemove: item.idAlertaAnalise, idProcessoArquivar: item.idProcesso });
            this._toggleShowConfirmDialogArquivamento();
          },
        });
      }
    } else if (
      ['ALERTA_RECEBIDO', 'ALERTA_RESPONDIDO', 'FINALIZADO', 'ALERTA_INSPETOR', 'ALERTA_REJEITADO_INSPETOR'].includes(
        item.labels
      )
    ) {
      item.statusProcessoArquivado === 'ARQUIVADO_AUTOMATICAMENTE' &&
        buttons.push({
          icon: 'pi pi-history',
          tooltip: 'Desarquivar Processo',
          style: { marginLeft: '3px' },
          onClick: () => this.store.desarquivarProcesso(item.idProcesso, item.tipoProcesso, () => onClose(item)),
        });

      item.idAlertaAnalise &&
        buttons.push({
          icon: 'pi pi-eye',
          tooltip: 'Visualizar Alerta',
          style: { marginLeft: '3px' },
          onClick: () => {
            this._redirectDetail(item);
          },
        });
      (checkUserContextIsInspetor() || checkUserGroup('Administrador')) &&
        buttons.push({
          icon: 'pi pi-pencil',
          tooltip: 'TDA',
          side: 'top',
          style: { marginLeft: '3px' },
          onClick: () => this.setState({ phase: 'editarTDA', editMod: true, action: 'edit' }),
        });

      item.passouDafo &&
        item.labels != 'FINALIZADO' &&
        (checkUserContextIsInspetor() || checkUserGroup(['Administrador'])) &&
        buttons.push({
          icon: 'pi pi-file',
          tooltip: 'Arquivar',
          className: 'p-button-danger',
          style: { marginLeft: '3px' },
          disabled: !(item.idAlertaAnalise && (checkUserContextIsInspetor() || checkUserGroup(['Administrador']))),
          onClick: () => {
            this.setState({ idRemove: item.idAlertaAnalise });
            this._toggleShowConfirmDialogArquivamento();
          },
        });
    }

    return (
      <div className="p-d-flex p-jc-end">
        {buttons.map((btn, idx) => {
          const button = (
            <FcButton style={{ marginLeft: '3px' }} tooltipOptions={{ position: 'top' }} {...btn} key={idx} />
          );
          if (btn.permissionsAttributes)
            return (
              <div>
                <PermissionProxy resourcePermissions={btn.permissionsAttributes}>{button}</PermissionProxy>
              </div>
            );
          else return <div>{button}</div>;
        })}
      </div>
    );
  }

  render() {
    const { loading } = this.store;
    const { item } = this.props;

    let content = <></>;
    if (loading) {
      content = (
        <div className="p-d-inline p-d-flex align-items-center">
          <ProgressSpinner />
        </div>
      );
    } else {
      const { tdaProcesso } = this.store;
      const renderProcesso = {
        L: this.renderLicitacao,
        C: this.renderCarona,
        D: this.renderDispensa,
        I: this.renderInexigibilidade,
        CR: this.renderCredenciamento,
      };

      const tipoToFullName = {
        L: 'licitacao',
        C: 'carona',
        D: 'dispensa',
        I: 'inexigibilidade',
        CR: 'credenciamento',
      };
      content = (
        <div>
          {!this.state.editMod && this.renderButtons()}
          {!this.state.editMod && renderProcesso[item.tipoProcesso]()}
          {this.state.editMod && this.state.phase === 'criarTDA' && (
            <div>
              <TdaFormPage
                idProcesso={item.idProcesso}
                tipoProcesso={tipoToFullName[item.tipoProcesso]}
                action={this.state.action}
                history={this.props.history}
                store={this.store}
                editMode
                disableEditMode={this._disableEditMode}
                reload={(idProcesso, tipoProcesso, idTab) => {
                  this.store.getTdaProcesso(this.props.item.idProcesso, this.props.item.tipoProcesso);
                  this.props.updateDataTable(idProcesso, tipoProcesso, idTab);
                }}
                idTab={this.props.idTab}
              />
            </div>
          )}
          {this.state.editMod && this.state.phase === 'editarTDA' && (
            <div>
              <TdaFormPage
                idProcesso={item.idProcesso}
                id={tdaProcesso?.id}
                tipoProcesso={tipoToFullName[item.tipoProcesso]}
                action={this.state.action}
                history={this.props.history}
                store={this.store}
                editMode
                disableEditMode={this._disableEditMode}
                reload={(idProcesso, tipoProcesso, idTab) => {
                  this.store.getTdaProcesso(this.props.item.idProcesso, this.props.item.tipoProcesso);
                  this.props.updateDataTable(idProcesso, tipoProcesso, idTab);
                }}
                idTab={this.props.idTab}
              />
            </div>
          )}
          {this.state.editMod && this.state.phase === 'aplicarChecklist' && (
            <div>
              <AplicarChecklistFormPage
                idProcesso={item.idProcesso}
                tipoProcesso={tipoToFullName[item.tipoProcesso]}
                action={this.state.action}
                history={this.props.history}
                store={this.store}
                editMode
                disableEditMode={this._disableEditMode}
                reload={this.props.updateDataTable}
                idTab={this.props.idTab}
              />
            </div>
          )}
          {this.state.phase === 'processarEdital' && (
            <div>
              <Dialog
                blockScroll
                header="Processar Edital"
                style={{ width: '28vw' }}
                footer={this._renderFooter()}
                visible={this.state.visibleDialogEdital}
                onHide={() => this._toggleShowConfirmProcessarEdital()}
                draggable={false}
                resizable={false}
              >
                O edital associado ao processo será encaminhado para processamento. Após ser finalizado, as informações
                do Edital estarão disponíveis para consulta. Deseja continuar?
                <ConfirmDialog />
              </Dialog>
            </div>
          )}
          {this.state.editMod && this.state.phase === 'emitirAlerta' && (
            <div>
              <EmitirAlertaFormPage
                idProcesso={item.idProcesso}
                tipoProcesso={tipoToFullName[item.tipoProcesso]}
                action={this.state.action}
                history={this.props.history}
                store={this.store}
                editMode
                disableEditMode={this._disableEditMode}
                reload={this.props.updateDataTable}
                idTab={this.props.idTab}
              />
            </div>
          )}
          {this.state.editMod && this.state.phase === 'visualizarAlerta' && (
            <div>
              <EmitirAlertaFormPage
                idAlerta={item.idAlertaAnalise}
                idProcesso={item.idProcesso}
                tipoProcesso={tipoToFullName[item.tipoProcesso]}
                action={this.state.action}
                history={this.props.history}
                store={this.store}
                editMode
                disableEditMode={this._disableEditMode}
                reload={this.props.updateDataTable}
                idTab={this.props.idTab}
              />
            </div>
          )}
          {this.state.visibleDialogArquivamento &&
            this.confirmArquivamento(this.state.idRemove, this.state.idProcessoArquivar)}
        </div>
      );
    }

    return content;
  }
}

AnaliseProcessoIndexDetail.propTypes = {
  item: PropTypes.any,
  store: PropTypes.any,
  show: PropTypes.bool,
  onClose: PropTypes.func,
  history: PropTypes.any,
  onDetailContratoAssociado: PropTypes.func,
  updateDataTable: PropTypes.func,
  idTab: PropTypes.number,
  onDetailEdital: PropTypes.func,
};

export default AnaliseProcessoIndexDetail;
