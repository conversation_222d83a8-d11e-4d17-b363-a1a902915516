import React from 'react';
import PropTypes from 'prop-types';
import './style.scss';
import { observer } from 'mobx-react';
import BoardAnaliseProcessoIndexStore from '~/stores/analiseProcesso/indexStore';
import PermissionProxy from 'fc/components/PermissionProxy';
import AccessPermission from '~/constants/AccessPermission';
import AppBreadCrumb from 'fc/components/AppBreadCrumb';
import BoardAnaliseProcessoListagemPage from './listagem';
import AnaliseProcessoIndexDetail from './indexDetail';
import FcCloseableTabView from 'fc/components/FcCloseableTabView';
import { getValue } from 'fc/utils/utils';
import ContratoDetailPage from '~/pages/contrato/indexDetail';
import AditivoDetailPage from '~/pages/contrato/aditivos/indexDetail';
import EmpenhoContratoIndexStore from '~/stores/contrato/empenhos/indexStore';
import EmpenhoDetailDialog from 'pages/contrato/empenhos/EmpenhoDetailDialog';
import SentencaEditalDetail from 'pages/analisarEditais/sentencaEdital/formDetail';

@observer
class BoardAnaliseProcessoIndexPage extends React.Component {
  constructor(props) {
    super(props);
    this.store = new BoardAnaliseProcessoIndexStore();
    this.empenhoStore = new EmpenhoContratoIndexStore();
    this.store.setRefresh(() => this._refreshLazys());
    this.state = {
      data: [
        {
          id: 1,
          header: 'Listagem',
          closable: false,
          content: (
            <BoardAnaliseProcessoListagemPage
              {...props}
              boardStore={this.store}
              onDetail={(processo) => this.onDetail(processo)}
            />
          ),
        },
      ],
      count: 2,
      activeTabIndex: 0,
      selectedRow: null,
      detalhesVisibility: false,
    };

    this.setActiveTabIndex = this.setActiveTabIndex.bind(this);
    this.handleCloseTab = this.handleCloseTab.bind(this);
    this.onDetail = this.onDetail.bind(this);
    this.onClose = this.onClose.bind(this);
    this.onDetailContratoAssociado = this.onDetailContratoAssociado.bind(this);
    this.onDetailAditivo = this.onDetailAditivo.bind(this);
    this.updateDatatable = this.updateDatatable.bind(this);
    this.updateHeaderTab = this.updateHeaderTab.bind(this);
  }

  updateDatatable(idProcesso, tipoProcesso, idTab) {
    this.store.reloadTableData();
    this.updateHeaderTab(idProcesso, tipoProcesso, idTab);
  }

  updateHeaderTab(idProcesso, tipoProcesso, idTab) {
    const fullNameToTipo = {
      licitacao: 'L',
      carona: 'C',
      dispensa: 'D',
      inexigibilidade: 'I',
      credenciamento: 'CR',
    };

    this.store.getProcessoByIdAndTipo(idProcesso, fullNameToTipo[tipoProcesso], (processo) => {
      const dataUpdate = this.state.data.map((tab) => {
        if (tab.id === idTab) {
          tab.header = getValue(processo?.numero);
          tab.content = (
            <AnaliseProcessoIndexDetail
              idTab={tab.id}
              item={processo}
              store={this.store}
              history={this.props.history}
              onClose={(processo) => this.onClose(processo)}
              onDetailContratoAssociado={this.onDetailContratoAssociado}
              updateDataTable={this.updateDatatable}
            />
          );
        }
        return tab;
      });

      this.setState({ data: dataUpdate });
    });
  }

  onDetail(processo) {
    const existingTab = this.state.data.find((tab) => tab.idProcesso === processo.id);
    if (existingTab) {
      this.setActiveTabIndex(existingTab);
    } else {
      const newProcesso = {
        id: this.state.count,
        idProcesso: processo.id,
        header: getValue(processo?.numero),
        closeable: true,
        content: (
          <AnaliseProcessoIndexDetail
            idTab={this.state.count}
            item={processo}
            store={this.store}
            history={this.props.history}
            onClose={(processo) => this.onClose(processo)}
            onDetailContratoAssociado={this.onDetailContratoAssociado}
            onDetailEdital={(edital) => this.onDetailEdital(edital)}
            updateDataTable={this.updateDatatable}
          />
        ),
      };
      this.setState({ data: [...this.state.data, newProcesso], count: this.state.count + 1 });
    }
  }

  onDetailEdital(edital) {
    const existingTab = this.state.data.find((tab) => tab.idEdital === edital.id);
    if (existingTab) {
      this.setActiveTabIndex(existingTab);
    } else {
      const newEditalDetail = {
        id: this.state.count,
        idEdital: edital.id,
        header: getValue(edital?.titulo),
        closeable: true,
        content: <SentencaEditalDetail idEdital={edital.id} />,
      };
      this.setState({ data: [...this.state.data, newEditalDetail], count: this.state.count + 1 });
    }
  }

  onDetailContratoAssociado(contrato) {
    const existingTab = this.state.data.find((tab) => tab.idContrato === contrato.id);
    if (existingTab) {
      this.setActiveTabIndex(existingTab);
    } else {
      const newContratoAssociado = {
        id: contrato.id,
        idContrato: contrato.id,
        header: `Contrato - ${contrato.numeroContrato}`,
        closeable: true,
        content: (
          <ContratoDetailPage
            idContrato={contrato.id}
            onDetailAditivo={(aditivo) => this.onDetailAditivo(aditivo)}
            onDetailEmpenho={(empenho) => {
              this.empenhoStore.initializeArquivos(empenho.id);
              this.setState({ detalhesVisibility: true, selectedRow: empenho });
            }}
          />
        ),
      };
      this.setState({ data: [...this.state.data, newContratoAssociado], count: this.state.count + 1 });
    }
  }

  onDetailAditivo(aditivo) {
    const existingTab = this.state.data.find((tab) => tab.idAditivo === aditivo.id);
    if (existingTab) {
      this.setActiveTabIndex(existingTab);
    } else {
      const newAditivo = {
        id: aditivo.id,
        idAditivo: aditivo.id,
        header: 'Aditivo - ' + aditivo.numero + ' - Contr. ' + aditivo.contrato.numero,
        closeable: true,
        content: <AditivoDetailPage aditivo={aditivo} />,
      };
      this.setState({ data: [...this.state.data, newAditivo], count: this.state.count + 1 });
    }
  }

  onClose(processo) {
    const existingTab = this.state.data.find((tab) => tab.idProcesso === processo.id);
    this.handleCloseTab(existingTab);
    this.store.setTdaProcesso(null);
    this.setActiveTabIndex(this.state.data[this.state.data.length - 1]);
  }

  setActiveTabIndex(tab) {
    this.setState({ activeTabIndex: this.state.data.indexOf(tab) });
  }

  handleCloseTab(tab) {
    this.setState((prevState) => {
      return { data: prevState.data.filter((elem) => elem.id !== tab.id) };
    });
  }

  render() {
    return (
      <>
        <PermissionProxy resourcePermissions={[AccessPermission.analiseProcessoView.readPermission]} blockOnFail>
          <AppBreadCrumb items={[{ label: 'Analisar Processos' }]} />
          <div className="card page index-table form-tab-actions-buttons">
            <FcCloseableTabView
              tabs={this.state.data}
              activeTabIndex={this.state.activeTabIndex}
              onChangeTab={this.setActiveTabIndex}
              onClose={this.handleCloseTab}
              fixedFirstTab
            />
          </div>
        </PermissionProxy>
        <EmpenhoDetailDialog
          fileStore={this.empenhoStore.fileStore}
          empenho={this.state.selectedRow}
          detalhesVisibility={this.state.detalhesVisibility}
          onClose={() => this.setState({ detalhesVisibility: false })}
        />
      </>
    );
  }
}

BoardAnaliseProcessoIndexPage.propTypes = {
  history: PropTypes.any,
  match: PropTypes.any,
};

export default BoardAnaliseProcessoIndexPage;
