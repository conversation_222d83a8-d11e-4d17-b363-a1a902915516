import React from 'react';
import '~/pages/analiseProcesso/boardAnaliseProcesso/style.scss';
import { observer } from 'mobx-react';
import PropTypes from 'prop-types';
import { Panel } from 'primereact/panel';
import classNames from 'classnames';
import { Skeleton } from 'primereact/skeleton';
import FcButton from 'fc/components/FcButton';
import { OverlayPanel } from 'primereact/overlaypanel';
import { SelectButton } from 'primereact/selectbutton';
import { Card } from 'primereact/card';
import LazyScrollerStore from '~/stores/analiseProcesso/lazyScroller/lazyScrollerStore';
import { Tag } from 'primereact/tag';
import {
  getFirstAndLastName,
  getLightenColor,
  getQtdDaysFromToday,
  getValueByKey,
  getValueDate,
  somaValoresLotes,
} from 'fc/utils/utils';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import Tooltip from 'fc/components/Tooltip';
import { getValueMoney } from 'fc/utils/utils';
import { Badge } from 'primereact/badge';
import { DATE_FORMAT, DATE_PARSE_FORMAT } from 'fc/utils/date';
import FcDropdown from 'fc/components/FcDropdown';

@observer
class LazyScroller extends React.Component {
  constructor(props) {
    super(props);
    this.store = new LazyScrollerStore(props.service, props.phaseKey.value, props.advancedSearchParams);

    this.state = {
      prevY: 0,
    };

    this._getItemTemplate = this._getItemTemplate.bind(this);
    this._renderRayContent = this._renderRayContent.bind(this);
    this._getItemTemplate = this._getItemTemplate.bind(this);
    this._getValorRiscoColor = this._getValorRiscoColor.bind(this);
  }

  componentDidMount() {
    var options = {
      root: null,
      rootMargin: '0px',
      threshold: 1.0,
    };

    this.observer = new IntersectionObserver(this._handleObserver.bind(this), options);
    if (!this.props.collapseRay) {
      this.observer.observe(this.loadingRef);
    }
  }

  componentDidUpdate(prevProps) {
    if (prevProps.refresh != this.props.refresh) {
      this.store.resetStates(this.props.advancedSearchParams);
      this.store.loadContent();
    }

    if (!this.props.collapseRay) {
      this.observer.observe(this.loadingRef);
    }
  }

  _getValorRiscoBackgroundColor(valorRisco) {
    const limiar = this.props.limiarValorRisco;
    let color = '#CAF1D8';
    if (valorRisco > limiar) {
      color = '#FFD0CE';
    } else if (valorRisco >= limiar / 2) {
      color = '#FAEDC4';
    }
    return color;
  }

  _getValorRiscoColor(valorRisco) {
    const limiar = this.props.limiarValorRisco;
    let color = '#1DA750';
    if (valorRisco > limiar) {
      color = '#FF3D32';
    } else if (valorRisco >= limiar / 2) {
      color = '#EAB308';
    }
    return color;
  }

  _renderRayContent(item) {
    const { numero, objeto, entidade, termoReferencia } = item;
    const titleEntityContext = `${entidade ? entidade.nome : ''}`;
    const subTitle = `${numero}`;
    const phaseKey = this.props.phaseKey.value;

    const hasValorAdjudicado = item?.valorAdjudicado;

    return (
      <>
        <div onClick={() => this.props.toggleDetails(item)}>
          <Card
            className="processo-content-card"
            key={item.id + '-' + item.tipoProcesso}
            header={
              item.valorRisco && (
                <Badge
                  key={'key' + item.id + '-' + 'value'}
                  value={
                    <Tooltip value="Risco">
                      <div>{item.valorRisco}</div>
                    </Tooltip>
                  }
                  style={{
                    backgroundColor: this._getValorRiscoBackgroundColor(item.valorRisco),
                    color: this._getValorRiscoColor(item.valorRisco),
                    float: 'right',
                    margin: '3.5px',
                    borderRadius: '50%',
                    width: '24px',
                    height: '24px',
                    fontSize: '12px',
                    padding: '0',
                    textAlign: 'center',
                    display: 'inline-block',
                    verticalAlign: 'middle',
                    lineHeight: '2',
                  }}
                />
              )
            }
          >
            <div className="card-header">
              <Tooltip value={titleEntityContext}>
                <div className="title">{titleEntityContext}</div>
              </Tooltip>
            </div>
            <div>
              <div
                className={classNames('subtitle p-d-flex p-flex-wrap p-ai-end', {
                  'p-jc-between': phaseKey === 'FINALIZADO',
                })}
              >
                <span
                  className="subtitle-text pt-2 text-base font-bold"
                  style={{
                    width:
                      phaseKey === 'FINALIZADO' && (item.statusProcessoArquivado || item.arquivado)
                        ? 'calc(100% - 1.1rem)'
                        : '100%',
                    paddingBottom: '0.7rem',
                  }}
                >
                  {subTitle}
                </span>
              </div>
            </div>
            <div className="desc">{objeto}</div>
            <div className="p-d-flex p-flex-wrap p-ai-end tags">
              <Tooltip value={hasValorAdjudicado ? 'Valor adjudicado' : 'Valor estimado'} sideOffset={0}>
                <div style={{ cursor: 'default' }}>
                  <Tag
                    icon="pi pi-money-bill"
                    className="p-d-flex"
                    style={
                      hasValorAdjudicado
                        ? {
                            backgroundColor: getLightenColor('#022B3A', 0.7),
                            color: '#022B3A',
                            border: `1px solid #022B3A`,
                          }
                        : {
                            backgroundColor: getLightenColor('#2F83DC', 0.7),
                            color: '#2F83DC',
                            border: `1px solid #2F83DC`,
                          }
                    }
                  >
                    {item.tipoProcesso === 'CR' ? (
                      <span>
                        {getValueMoney(
                          somaValoresLotes(termoReferencia.lotes),
                          termoReferencia?.tresCasasDecimais ? 3 : 2
                        )}
                      </span>
                    ) : (
                      <span>{getValueMoney(hasValorAdjudicado ? item?.valorAdjudicado : item?.valor)}</span>
                    )}
                  </Tag>
                </div>
              </Tooltip>
              <Tooltip value="Data de Abertura" sideOffset={0}>
                <div style={{ cursor: 'default' }}>
                  <Tag
                    icon="pi pi-calendar"
                    className="p-d-flex"
                    style={{
                      backgroundColor: getLightenColor('#4da73b', 0.7),
                      color: '#4da73b',
                      border: `1px solid #4da73b`,
                    }}
                  >
                    <span>{getValueDate(item.dataProcesso, DATE_FORMAT, DATE_PARSE_FORMAT)}</span>
                  </Tag>
                </div>
              </Tooltip>
              {phaseKey !== 'CADASTRADOS' && phaseKey !== 'AUDITORIA' && item?.responsavel?.nome && (
                <Tooltip value="Responsável" sideOffset={0}>
                  <div style={{ cursor: 'default' }}>
                    <Tag
                      value={getFirstAndLastName(item.responsavel.nome)}
                      icon="pi pi-user"
                      style={{
                        backgroundColor: '#fbf7ff',
                        color: '#8f48d2',
                        border: '1px solid #8f48d2',
                      }}
                    />
                  </div>
                </Tooltip>
              )}
              {(phaseKey === 'EM_ANALISE' || phaseKey === 'ALERTA') && (
                <>
                  <Tooltip value="Data final para análise" sideOffset={0}>
                    <div style={{ cursor: 'default' }}>
                      <Tag
                        value={getQtdDaysFromToday(item.dataFinalAnalise) + ' Dia(s)'}
                        icon="pi pi-calendar"
                        style={{
                          color: getQtdDaysFromToday(item.dataFinalAnalise) >= 0 ? 'green' : 'red',
                          backgroundColor: getQtdDaysFromToday(item.dataFinalAnalise) >= 0 ? '#f4fcf7' : '#fff5f5',
                          border:
                            getQtdDaysFromToday(item.dataFinalAnalise) >= 0 ? '1px solid #22c55e' : '1px solid #ff3d32',
                        }}
                      />
                    </div>
                  </Tooltip>
                  {item.labels?.includes('REJEITADO_DIRETOR_DAFO') && (
                    <Tooltip value="Alerta rejeitado pelo diretor da DAFO" sideOffset={0}>
                      <div style={{ cursor: 'default' }}>
                        <Tag
                          value="Rejeitado"
                          style={{
                            backgroundColor: '#fff5f5',
                            color: 'red',
                            border: '1px solid #ff3d32',
                          }}
                        />
                      </div>
                    </Tooltip>
                  )}
                </>
              )}
              {phaseKey === 'EM_ANALISE' && item.checklistFinalizado && (
                <div style={{ cursor: 'default' }}>
                  <Tag
                    value="Checklist Concluído - Inspetor"
                    style={{
                      backgroundColor: '#fefbf3',
                      color: '#422343',
                      border: '1px solid #422343',
                    }}
                  />
                </div>
              )}
              {phaseKey === 'EM_ANALISE' && item.checklistRejeitado && !item.checklistFinalizado && (
                <div style={{ cursor: 'default' }}>
                  <Tag
                    value="Checklist Rejeitado"
                    style={{
                      backgroundColor: '#fff5f5',
                      color: 'red',
                      border: '1px solid #ff3d32',
                    }}
                  />
                </div>
              )}

              {phaseKey === 'ALERTA' && (
                <Tooltip
                  value={getValueByKey(item.labels, DadosEstaticosService.getLabelByStatusAlerta(), 'value', 'tooltip')}
                  sideOffset={0}
                  key={item?.valor}
                >
                  <div style={{ cursor: 'default' }}>
                    <Tag
                      value={getValueByKey(item.labels, DadosEstaticosService.getLabelByStatusAlerta())}
                      style={{
                        backgroundColor: getValueByKey(
                          item.labels,
                          DadosEstaticosService.getLabelByStatusAlerta(),
                          'value',
                          'backgroundColor'
                        ),
                        color: getValueByKey(
                          item.labels,
                          DadosEstaticosService.getLabelByStatusAlerta(),
                          'value',
                          'color'
                        ),
                        border: getValueByKey(
                          item.labels,
                          DadosEstaticosService.getLabelByStatusAlerta(),
                          'value',
                          'border'
                        ),
                      }}
                    />
                  </div>
                </Tooltip>
              )}
              {phaseKey === 'FINALIZADO' && (item.statusProcessoArquivado || item.arquivado) && (
                <Tooltip
                  value={getValueByKey(
                    item.statusProcessoArquivado,
                    DadosEstaticosService.getStatusArquivamento(),
                    'value',
                    'label'
                  )}
                  sideOffset={0}
                >
                  <div style={{ cursor: 'default' }}>
                    <Tag
                      value={getValueByKey(
                        item.statusProcessoArquivado,
                        DadosEstaticosService.getStatusArquivamento(),
                        'value',
                        'label'
                      )}
                      style={{
                        backgroundColor: getValueByKey(
                          item.statusProcessoArquivado,
                          DadosEstaticosService.getStatusArquivamento(),
                          'value',
                          'backgroundColor'
                        ),
                        color: getValueByKey(
                          item.statusProcessoArquivado,
                          DadosEstaticosService.getStatusArquivamento(),
                          'value',
                          'color'
                        ),
                        border: getValueByKey(
                          item.statusProcessoArquivado,
                          DadosEstaticosService.getStatusArquivamento(),
                          'value',
                          'border'
                        ),
                      }}
                    />
                  </div>
                </Tooltip>
              )}
            </div>
          </Card>
        </div>
      </>
    );
  }

  _renderHeader(ray, collapsed = false) {
    return (
      <div className="p-d-flex p-jc-between p-ai-center">
        {!collapsed && <Tooltip target={`.ray-name-${ray.value}`} />}
        <div className="overflow-hidden text-overflow-ellipsis">
          <FcButton
            icon={`pi ${collapsed ? 'pi-angle-down' : 'pi-angle-right'}`}
            className="p-button-text toggle-button"
            onClick={() => this.props.toggleCollapseRay()}
          />
          <span className={`ray-name-${ray.value}`}>{ray.text}</span>
        </div>
        {!collapsed && (
          <FcButton
            icon={'pi pi-sort-alt'}
            style={{ backgroundColor: 'rgba(0, 0, 0, 0)' }}
            onClick={(e) => this.op.toggle(e)}
          />
        )}

        <OverlayPanel style={{ width: '400px' }} ref={(el) => (this.op = el)} showCloseIcon>
          <div className="p-d-flex p-jc-between p-ai-center">
            <div>
              <b style={{ display: 'block' }}>Campo</b>
              <FcDropdown
                inOrder
                style={{ marginTop: '5px', minWidth: '15rem' }}
                optionLabel="label"
                optionValue="value"
                options={this.props.sortOptions}
                placeholder="Selecione um campo"
                onChange={(e) => {
                  this.store.column = e.value;
                }}
                value={this.store.column}
              />
            </div>
            <div style={{ marginLeft: '5px' }}>
              <b>Ordem</b>
              <SelectButton
                style={{ marginTop: '5px' }}
                options={[
                  { icon: 'pi pi-sort-amount-down', value: 'desc' },
                  { icon: 'pi pi-sort-amount-up', value: 'asc' },
                ]}
                value={this.store.orderBy}
                onChange={(e) => {
                  this.store.orderBy = e.value;
                }}
                itemTemplate={this._getItemTemplate}
              />
            </div>
          </div>
          <FcButton
            className="p-ml-auto p-button-primary p-mr-2"
            label="Aplicar"
            optionLabel="value"
            style={{ marginTop: '5px' }}
            disabled={!this.store.column || !this.store.orderBy}
            onClick={(e) => {
              this.store.resetStates(this.props.advancedSearchParams, true);
              this.store.loadContent();
              this.op.hide(e);
            }}
          />
        </OverlayPanel>
      </div>
    );
  }

  _getItemTemplate(option) {
    return <i style={{ marginLeft: '33%' }} className={option.icon} />;
  }

  _handleObserver(entities) {
    const y = entities[0].boundingClientRect.y;
    if (this.state.prevY > y) {
      if (this.store.page > 1 && !this.store.blockRequests) {
        this.store.loadContent();
      }
    }

    this.setState({ prevY: y });
  }

  render() {
    return (
      <>
        {this.props.collapseRay ? (
          <div className={classNames('proc-ray-collapsed', `ray-${this.props.phaseKey.value}-collapsed`)}>
            <span className="p-panel-content p-component">{this._renderHeader(this.props.phaseKey, true)}</span>
          </div>
        ) : (
          <Panel
            className={classNames('proc-ray', `ray-${this.props.phaseKey.value}`)}
            header={this._renderHeader(this.props.phaseKey)}
            collapsed={this.props.collapseRay}
          >
            {this.store.data.map((item) => {
              return this._renderRayContent(item);
            })}
            <div
              ref={(loadingRef) => (this.loadingRef = loadingRef)}
              className="p-d-flex p-ai-center p-p-2"
              style={{ height: '20px' }}
            >
              {this.store.loading && <Skeleton width={'100%'} height="1rem" />}
            </div>
          </Panel>
        )}
      </>
    );
  }
}

LazyScroller.propTypes = {
  phaseKey: PropTypes.object,
  refresh: PropTypes.bool,
  service: PropTypes.any,
  advancedSearchParams: PropTypes.any,
  sortOptions: PropTypes.array,
  toggleDetails: PropTypes.func,
  limiarValorRisco: PropTypes.any,
  collapseRay: PropTypes.bool,
  toggleCollapseRay: PropTypes.func,
};

export default LazyScroller;
