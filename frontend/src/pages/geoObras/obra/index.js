import AdvancedSearch from 'fc/components/AdvancedSearch';
import AppBreadCrumb from 'fc/components/AppBreadCrumb';
import FcButton from 'fc/components/FcButton';
import IndexDataTable from 'fc/components/IndexDataTable';
import PermissionProxy from 'fc/components/PermissionProxy';
import GenericIndexPage from 'fc/pages/GenericIndexPage';
import { observer } from 'mobx-react';
import { PrimeIcons } from 'primereact/api';
import AccessPermission from '~/constants/AccessPermission';
import UrlRouter from '~/constants/UrlRouter';
import { getValueElipsis, getValueMoney, getValueDate, getValueByKey } from 'fc/utils/utils';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import ObraIndexStore from '~/stores/geoObras/obra/indexStore';

@observer
class ObraIndexPage extends GenericIndexPage {
  constructor(props) {
    super(props, AccessPermission.geoObras.obra);
    this.store = new ObraIndexStore();
    this.state = {
      idRemove: null,
    };
  }

  pushUrlToHistory(url) {
    url && this.props.history.push(url);
  }

  render() {
    const columns = [
      {
        field: 'numero',
        header: 'N° da Obra',
        sortable: true,
      },
      {
        style: { width: '80%' },
        field: 'descricao',
        header: 'Descrição',
        body: ({ descricao }) => getValueElipsis(descricao, 150),
        sortable: true,
      },
      {
        field: 'valor',
        header: 'Valor',
        body: ({ valor }) => getValueMoney(valor),
        sortable: true,
      },
      {
        field: 'dataPrevistaInicio',
        header: 'Início',
        body: ({ dataPrevistaInicio }) => getValueDate(dataPrevistaInicio),
        sortable: true,
      },
      {
        field: 'dataPrevistaConclusao',
        header: 'Fim',
        body: ({ dataPrevistaConclusao }) => getValueDate(dataPrevistaConclusao),
        sortable: true,
      },
      {
        field: 'status',
        header: 'Situação',
        body: ({ status }) => getValueByKey(status, DadosEstaticosService.getStatusObra()),
        sortable: true,
      },
      {
        style: { width: '110px' },
        body: (rowData) => {
          return (
            <div className="actions p-d-flex p-jc-end">
              <PermissionProxy resourcePermissions={this.getReadPermission()}>
                <FcButton
                  icon="pi pi-pencil"
                  className="p-button-sm p-button-success p-mr-2"
                  onClick={() => this.pushUrlToHistory(UrlRouter.obra.cadastro.editar.replace(':id', rowData.id))}
                />
              </PermissionProxy>
              <PermissionProxy resourcePermissions={this.getWritePermission()}>
                <FcButton
                  icon="pi pi-trash"
                  className="p-button-sm p-button-danger"
                  onClick={() => {
                    this.setState({ idRemove: rowData.id });
                    this.store.toggleShowConfirmDialog();
                  }}
                />
              </PermissionProxy>
            </div>
          );
        },
      },
    ];

    const header = (
      <div className="table-header">
        <PermissionProxy resourcePermissions={this.getWritePermission()}>
          <FcButton
            className="p-button"
            label="Novo"
            style={{ marginBottom: '5px', marginRight: '5px' }}
            icon={PrimeIcons.PLUS}
            onClick={() => this.pushUrlToHistory(UrlRouter.obra.cadastro.novo)}
          />
        </PermissionProxy>
      </div>
    );

    const { listKey, loading } = this.store;
    const { getDefaultTableProps } = this;

    const breacrumbItems = [{ label: 'Cadastro de Obras' }];

    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        <AppBreadCrumb items={breacrumbItems} />
        <div className="card page index-table">
          <AdvancedSearch
            searchParams={this.store.getAdvancedSearchParams()}
            store={this.store}
            searchFields={['numero']}
            filterSuggest={this.store.getFilterSuggest()}
          />
          <IndexDataTable
            columns={columns}
            value={listKey}
            header={header}
            loading={loading}
            {...getDefaultTableProps()}
          />
          {this.store.isConfirmDialogVisible && this.confirmRemove(this.state.idRemove)}
        </div>
      </PermissionProxy>
    );
  }
}

ObraIndexPage.displayName = 'ObraIndexPage';

export default ObraIndexPage;
