import React from 'react';
import { observer } from 'mobx-react';
import PropTypes from 'prop-types';
import Form<PERSON>ield from 'fc/components/FormField';
import { Divider } from 'primereact/divider';
import { RadioButton } from 'primereact/radiobutton';
import ConvenioDataTable from '~/pages/geoObras/obra/components/convenioDataTable';
import ConvenioDialogForm from '~/pages/geoObras/obra/components/convenioDialogForm';
import { ConfirmDialog, confirmDialog } from 'primereact/confirmdialog';

import './style.scss';

@observer
class TabConvenios extends React.Component {
  constructor(props) {
    super(props);
    this.store = props.store;

    this.state = {
      visibleConvenioDialogForm: false,
      convenioEditMode: false,
      selectedConvenio: null,
    };
  }

  resetConvenioDialogForm() {
    this.setState({ visibleConvenioDialogForm: false, convenioEditMode: false, selectedConvenio: null });
  }

  setConvenioDialogFormEditMode(convenio) {
    this.setState({ visibleConvenioDialogForm: true, convenioEditMode: true, selectedConvenio: convenio });
  }

  _renderDivider(label) {
    return (
      <div style={{ flexBasis: 'auto', display: 'flex' }}>
        <h5
          style={{
            padding: '0px',
            whiteSpace: 'nowrap',
            width: 'auto',
            color: '#333333',
            alignContent: 'left',
            marginRight: '5px',
            marginTop: '11px',
          }}
        >
          {label}
        </h5>
        <Divider />
      </div>
    );
  }

  confirmDangerDialog({ message, header, onAccept }) {
    confirmDialog({
      message,
      header,
      icon: 'pi pi-exclamation-triangle',
      acceptClassName: 'p-button-danger',
      accept: () => onAccept(),
    });
  }

  render() {
    const { submitted } = this.props;
    const { getRule } = this.store;

    return (
      <>
        <div className="p-col-12">
          {this._renderDivider('Convênios')}
          <form onSubmit={(e) => e.preventDefault()}>
            <div className="p-fluid p-formgrid p-grid">
              <FormField
                columns={4}
                attribute="possuiConveniosAssociados"
                label="A obra possui convênios associados?"
                rule={getRule('possuiConveniosAssociados')}
                submitted={submitted}
              >
                <div className="p-field-radiobutton p-dir-row">
                  <div className="p-field-radiobutton p-col">
                    <RadioButton
                      inputId="sim"
                      name="sim"
                      value={true}
                      onChange={(e) => this.store.updateAttribute('possuiConveniosAssociados', e.value)}
                      checked={this.store.object.possuiConveniosAssociados === true}
                    />
                    <label htmlFor="Sim">Sim</label>
                  </div>
                  <div className="p-field-radiobutton p-col">
                    <RadioButton
                      inputId="nao"
                      name="nao"
                      value={false}
                      onChange={(e) => {
                        if (this.store.object.convenios.length > 0) {
                          this.confirmDangerDialog({
                            message:
                              'Você possui convênios cadastrados. Esta operação irá remover todos os registros. Deseja continuar?',
                            header: 'Confirmar Remoção dos Convênios',
                            onAccept: () => {
                              this.store.updateAttribute('possuiConveniosAssociados', false);
                              this.store.updateAttribute('convenios', []);
                            },
                          });
                        } else {
                          this.store.updateAttribute('possuiConveniosAssociados', e.value);
                        }
                      }}
                      checked={this.store.object.possuiConveniosAssociados === false}
                    />
                    <label htmlFor="Não">Não</label>
                  </div>
                </div>
              </FormField>
              <div className="p-col-12">
                {this.store.object.possuiConveniosAssociados && (
                  <ConvenioDataTable
                    convenios={this.store.object.convenios}
                    loading={this.store.loading}
                    onClickNew={() => {
                      this.resetConvenioDialogForm();
                      this.setState({ visibleConvenioDialogForm: true });
                    }}
                    onClickEdit={(convenio) => this.setConvenioDialogFormEditMode(convenio)}
                    onClickRemove={(convenio) => {
                      this.confirmDangerDialog({
                        message: 'Você realmente deseja remover este convênio?',
                        header: 'Remover Convênio',
                        onAccept: () => {
                          this.store.removeConvenio(convenio);
                        },
                      });
                    }}
                  />
                )}
              </div>
            </div>
          </form>
        </div>
        {this.store.object.possuiConveniosAssociados && (
          <ConvenioDialogForm
            convenio={this.state.selectedConvenio}
            visible={this.state.visibleConvenioDialogForm}
            editMode={this.state.convenioEditMode}
            onHide={() => this.resetConvenioDialogForm()}
            onCancel={() => this.resetConvenioDialogForm()}
            onSave={(convenio) => this.store.addConvenio(convenio)}
            onEdit={(convenio, convenioEdit) => this.store.updateConvenio(convenio, convenioEdit)}
            onEditOrSaveSuccessful={() => this.resetConvenioDialogForm()}
          />
        )}
        <ConfirmDialog />
      </>
    );
  }
}

TabConvenios.propTypes = {
  store: PropTypes.object,
  submitted: PropTypes.bool,
};

export default TabConvenios;
