import React from 'react';
import { observer } from 'mobx-react';
import PropTypes from 'prop-types';
import MultipleFileUploader from 'fc/components/MultipleFileUploader';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import { getValueDate } from 'fc/utils/utils';
import { DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS } from 'fc/utils/date';
import FormField from 'fc/components/FormField';

import './style.scss';

@observer
class TabArquivos extends React.Component {
  constructor(props) {
    super(props);
    this.store = props.store;
    this.state = {
      idRemove: null,
    };
  }

  render() {
    const { submitted } = this.props;
    const { getRule } = this.store;

    const columnsArquivoObra = [
      {
        style: { width: '30%' },
        field: 'arquivo',
        header: 'Arquivo',
      },
      {
        style: { width: '15%' },
        field: 'tipo',
        header: (
          <div>
            Tipo <span className="p-error"> *</span>
          </div>
        ),
      },
      {
        style: { width: '25%' },
        field: 'descricao',
        header: 'Descrição',
      },
      {
        style: { width: '15%' },
        field: 'dataEnvio',
        header: 'Data de Envio',
        body: ({ dataEnvio }) => getValueDate(dataEnvio, DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS),
        editor: ({ dataEnvio }) => getValueDate(dataEnvio, DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS),
      },
    ];

    return (
      <>
        <div className="p-col-12">
          <form onSubmit={(e) => e.preventDefault()}>
            <div className="p-fluid p-formgrid p-grid">
              <FormField
                columns={12}
                attribute="arquivos"
                label="Arquivos"
                submitted={submitted}
                rule={getRule('arquivos')}
              >
                <MultipleFileUploader
                  store={this.store.fileStore}
                  tableColumns={columnsArquivoObra}
                  onChangeFiles={(files) => this.store.setArquivos(files)}
                  fileTypes={DadosEstaticosService.getTipoArquivoObra()}
                  filterTypes={{
                    included: this.store.getTypeFilesByPhase('CADASTRAL'),
                    filter: {
                      column: 'fase',
                      values: ['CADASTRAL'],
                    },
                  }}
                />
              </FormField>
            </div>
          </form>
        </div>
      </>
    );
  }
}

TabArquivos.propTypes = {
  store: PropTypes.object,
  submitted: PropTypes.bool,
};

export default TabArquivos;
