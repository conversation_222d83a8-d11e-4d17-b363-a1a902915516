import AsyncDropdown from 'fc/components/AsyncDropdown';
import FcDropdown from 'fc/components/FcDropdown';
import <PERSON><PERSON>ield from 'fc/components/FormField';
import InputMonetary from 'fc/components/InputMonetary';
import SelectDialog from 'fc/components/SelectDialog';
import { getNumberUnitThousands, getValueByKey, isValueValid } from 'fc/utils/utils';
import { observer } from 'mobx-react';
import moment from 'moment';
import { Calendar } from 'primereact/calendar';
import { Divider } from 'primereact/divider';
import { InputMask } from 'primereact/inputmask';
import { InputNumber } from 'primereact/inputnumber';
import { InputSwitch } from 'primereact/inputswitch';
import { InputText } from 'primereact/inputtext';
import { InputTextarea } from 'primereact/inputtextarea';
import { SelectButton } from 'primereact/selectbutton';
import PropTypes from 'prop-types';
import React from 'react';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import TipoObraIndexStore from '~/stores/geoObras/tipoObra/indexStore';
import './style.scss';

@observer
class TabDadosGerais extends React.Component {
  constructor(props) {
    super(props);
    this.store = props.store;

    this.state = {
      idRemove: null,
    };
  }

  getDateAttributeValue(value) {
    return value ? moment(value).toDate() : value;
  }

  _renderDivider(label) {
    return (
      <div style={{ flexBasis: 'auto', display: 'flex' }}>
        <h5
          style={{
            padding: '0px',
            whiteSpace: 'nowrap',
            width: 'auto',
            color: '#333333',
            alignContent: 'left',
            marginRight: '5px',
            marginTop: '11px',
          }}
        >
          {label}
        </h5>
        <Divider />
      </div>
    );
  }

  render() {
    const { submitted } = this.props;
    const { updateAttribute, updateAttributeDate, getRule } = this.store;

    const selectTipoObjetoObraItemTemplate = ({ icon, label, description }) => (
      <div style={{ display: 'flex', flexDirection: 'row' }}>
        <div style={{ flex: 1, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
          <i className={`pi ${icon}`} style={{ fontSize: '3rem' }}></i>
        </div>
        <div style={{ flex: 3, textAlign: 'left' }}>
          <div style={{ fontWeight: 'bold', marginBottom: '10px' }}>{label}</div>
          <div>{description}</div>
        </div>
      </div>
    );

    const columnsTipoObra = [
      {
        field: 'nome',
        header: 'Nome',
        sortable: true,
      },
      {
        field: 'descricao',
        header: 'Descrição',
        sortable: true,
      },
      {
        field: 'tipoDimensao',
        header: 'Tipo de Dimensão',
        body: ({ tipoDimensao }) => getValueByKey(tipoDimensao, DadosEstaticosService.getTipoDimensaoObra()),
        sortable: true,
      },
    ];

    return (
      <>
        <div className="p-col-12">
          {this._renderDivider('Cadastro de Obra')}
          <form onSubmit={(e) => e.preventDefault()}>
            <div className="p-fluid p-formgrid p-grid">
              <FormField
                columns={3}
                attribute="numero"
                label="Número da Obra"
                rule={getRule('numero')}
                submitted={submitted}
              >
                <InputText
                  onChange={(e) => updateAttribute('numero', e)}
                  placeholder="Informe o numero da obra"
                  value={this.store.object.numero}
                  id="numero"
                />
              </FormField>
              <FormField columns={4} attribute="tipo" label="Tipo de Obra" rule={getRule('tipo')} submitted={submitted}>
                <SelectDialog
                  value={this.store.object.tipo}
                  label="nome"
                  indexStore={new TipoObraIndexStore()}
                  onChange={(e) => {
                    this.store.updateAttribute('tipo', e);
                    this.forceUpdate();
                  }}
                  headerDialog="Tipo de Obra"
                  emptyMessage="Selecione o tipo de obra"
                  nullMessage="Tipo de obra sem nome"
                  dialogColumns={columnsTipoObra}
                  searchFields={['nome']}
                  canCreate
                  disabledCreate
                />
              </FormField>
              <FormField
                columns={3}
                attribute="dimensoes"
                label="Dimensões"
                rule={getRule('dimensoes')}
                submitted={submitted}
              >
                <div className="p-inputgroup flex-1">
                  <InputNumber
                    onChange={(e) =>
                      updateAttribute(
                        'dimensoes',
                        isValueValid(e.value) && Number(e.value) >= 0 ? Number(e.value) : null
                      )
                    }
                    placeholder="Informe as dimensões"
                    value={this.store.object?.dimensoes}
                    mode="decimal"
                    locale="pt-BR"
                    minFractionDigits={2}
                    id="dimensoes"
                    disabled={!this.store.object?.tipo}
                  />
                  <span className="p-inputgroup-addon" style={{ backgroundColor: '#d9d9d9', fontWeight: 'bold' }}>
                    {getValueByKey(this.store.object?.tipo?.tipoDimensao, DadosEstaticosService.getTipoDimensaoObra())}
                  </span>
                </div>
              </FormField>
              <FormField columns={2} attribute="valor" label="Valor" rule={getRule('valor')} submitted={submitted}>
                <InputMonetary
                  placeholder="R$"
                  mode="currency"
                  onChange={(e) => updateAttribute('valor', e)}
                  value={this.store.object?.valor}
                />
              </FormField>
              <FormField
                columns={5}
                attribute="endereco"
                label="Endereço"
                rule={getRule('endereco')}
                submitted={submitted}
              >
                <InputText
                  onChange={(e) => updateAttribute('endereco', e)}
                  placeholder="Informe o endereço"
                  value={this.store.object.endereco}
                  id="endereco"
                />
              </FormField>
              <FormField columns={2} attribute="cep" label="CEP" rule={getRule('cep')} submitted={submitted}>
                <InputMask
                  mask={'99999-999'}
                  onChange={(e) => updateAttribute('cep', e)}
                  placeholder="00000-000"
                  value={this.store.object.cep}
                  id="cep"
                />
              </FormField>
              <FormField
                columns={5}
                attribute="municipio"
                label="Município/UF"
                rule={getRule('municipio')}
                submitted={submitted}
              >
                <AsyncDropdown
                  onChange={updateAttribute}
                  value={this.store.object.municipio?.id}
                  placeholder="Selecione o Município"
                  store={this.store.municipioSelectStore}
                />
              </FormField>
              <FormField
                columns={12}
                attribute="descricao"
                label="Descrição da Obra"
                rows={5}
                rule={getRule('descricao')}
                submitted={submitted}
              >
                <div>
                  <InputTextarea
                    rows={5}
                    cols={30}
                    autoResize={true}
                    value={this.store.object.descricao}
                    onChange={(e) => updateAttribute('descricao', e)}
                    placeholder="Informe a descrição da obra"
                    maxlength="4000"
                  />
                  <p>
                    {this.store.object.descricao
                      ? getNumberUnitThousands(4000 - this.store.object.descricao.length)
                      : getNumberUnitThousands(4000)}{' '}
                    caracteres restantes
                  </p>
                </div>
              </FormField>
              <FormField
                columns={12}
                attribute="incorporavelPatrimonio"
                label="A obra é incorporável ao patrimônio."
                rule={getRule('incorporavelPatrimonio')}
                submitted={submitted}
                checkbox
              >
                <InputSwitch
                  onChange={(e) => updateAttribute('incorporavelPatrimonio', e.value)}
                  checked={this.store.object.incorporavelPatrimonio}
                />
              </FormField>
              <FormField
                columns={12}
                attribute="tipoObjeto"
                label="Tipo do Objeto da Obra"
                rule={getRule('tipoObjeto')}
                submitted={submitted}
              >
                <SelectButton
                  value={this.store.object.tipoObjeto}
                  onChange={(e) => {
                    if (e.value !== null && e.value != this.store.object.tipoObjeto) {
                      updateAttribute('tipoObjeto', e);
                      updateAttribute('subtipoObjeto', null);
                    }
                  }}
                  options={DadosEstaticosService.getObjetoObra()}
                  optionLabel="label"
                  optionValue="type"
                  itemTemplate={selectTipoObjetoObraItemTemplate}
                />
              </FormField>
              {this.store.object.tipoObjeto && this.store.object.tipoObjeto !== 'SERVICO' && (
                <FormField
                  columns={4}
                  attribute="subtipoObjeto"
                  label="Subtipo do Objeto da Obra"
                  rule={getRule('subtipoObjeto')}
                  submitted={submitted}
                >
                  <FcDropdown
                    inOrder
                    id="subtipoObjeto"
                    options={
                      DadosEstaticosService.getObjetoObra().find((item) => item.type === this.store.object.tipoObjeto)
                        ?.subtypes || []
                    }
                    optionLabel="label"
                    optionValue="type"
                    value={this.store.object.subtipoObjeto}
                    onChange={(e) => updateAttribute('subtipoObjeto', e)}
                    placeholder="Selecione o subtipo do objeto da obra"
                    showClear
                    showFilterClear
                    filter
                    emptyMessage="Nenhum Registro Encontrado"
                    emptyFilterMessage="Nenhum Registro Encontrado"
                  />
                </FormField>
              )}
            </div>
            <div className="p-fluid p-formgrid p-grid">
              <FormField
                columns={4}
                attribute="dataPrevistaInicio"
                label="Data Prevista de Início"
                rule={getRule('dataPrevistaInicio')}
                submitted={submitted}
              >
                <Calendar
                  value={this.getDateAttributeValue(this.store.object.dataPrevistaInicio)}
                  onChange={(e) => updateAttributeDate('dataPrevistaInicio', e)}
                  showIcon
                  mask="99/99/9999"
                  maxDate={this.getDateAttributeValue(this.store.object.dataPrevistaConclusao)}
                />
              </FormField>
              <FormField
                columns={4}
                attribute="dataPrevistaConclusao"
                label="Data Prevista de Conclusão"
                rule={getRule('dataPrevistaConclusao')}
                submitted={submitted}
              >
                <Calendar
                  value={this.getDateAttributeValue(this.store.object.dataPrevistaConclusao)}
                  onChange={(e) => updateAttributeDate('dataPrevistaConclusao', e)}
                  showIcon
                  mask="99/99/9999"
                  minDate={this.getDateAttributeValue(this.store.object.dataPrevistaInicio)}
                />
              </FormField>
            </div>
          </form>
        </div>
      </>
    );
  }
}

TabDadosGerais.propTypes = {
  store: PropTypes.object,
  submitted: PropTypes.bool,
};

export default TabDadosGerais;
