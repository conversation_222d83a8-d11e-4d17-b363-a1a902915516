import React from 'react';
import { observer } from 'mobx-react';
import PropTypes from 'prop-types';
import Form<PERSON>ield from 'fc/components/FormField';
import { Divider } from 'primereact/divider';
import { RadioButton } from 'primereact/radiobutton';
import './style.scss';
import { confirmDialog } from 'primereact/confirmdialog';
import RecursoProprioDataTable from '../components/recursoProprioDataTable';

@observer
class TabRecursosProprios extends React.Component {
  constructor(props) {
    super(props);
    this.store = props.store;
  }

  _renderDivider(label) {
    return (
      <div style={{ flexBasis: 'auto', display: 'flex' }}>
        <h5
          style={{
            padding: '0px',
            whiteSpace: 'nowrap',
            width: 'auto',
            color: '#333333',
            alignContent: 'left',
            marginRight: '5px',
            marginTop: '11px',
          }}
        >
          {label}
        </h5>
        <Divider />
      </div>
    );
  }

  confirmDangerDialog({ message, header, onAccept }) {
    confirmDialog({
      message,
      header,
      icon: 'pi pi-exclamation-triangle',
      acceptClassName: 'p-button-danger',
      accept: () => onAccept(),
    });
  }

  render() {
    const { submitted } = this.props;
    const { getRule, updateAttribute } = this.store;

    return (
      <div className="p-col-12">
        {this._renderDivider('Recursos Próprios')}
        <form onSubmit={(e) => e.preventDefault()}>
          <div className="p-fluid p-formgrid p-grid">
            <FormField
              columns={4}
              attribute="possuiRecursosProprios"
              label="A obra possui recursos próprios?"
              rule={getRule('possuiRecursosProprios')}
              submitted={submitted}
            >
              <div className="p-field-radiobutton p-dir-row">
                <div className="p-field-radiobutton p-col">
                  <RadioButton
                    inputId="sim"
                    name="sim"
                    value={true}
                    onChange={(e) => updateAttribute('possuiRecursosProprios', e.value)}
                    checked={this.store.object.possuiRecursosProprios === true}
                  />
                  <label htmlFor="Sim">Sim</label>
                </div>
                <div className="p-field-radiobutton p-col">
                  <RadioButton
                    inputId="nao"
                    name="nao"
                    value={false}
                    onChange={(e) => {
                      if (this.store.object.recursosProprios.length > 0) {
                        this.confirmDangerDialog({
                          message:
                            'Você possui recursos próprios cadastrados. Esta operação irá remover todos os registros. Deseja continuar?',
                          header: 'Confirmar Remoção dos Recursos Próprios',
                          onAccept: () => {
                            updateAttribute('possuiRecursosProprios', false);
                            updateAttribute('recursosProprios', []);
                          },
                        });
                      } else {
                        updateAttribute('possuiRecursosProprios', e.value);
                      }
                    }}
                    checked={this.store.object.possuiRecursosProprios === false}
                  />
                  <label htmlFor="Não">Não</label>
                </div>
              </div>
            </FormField>
            <div className="p-col-12">
              {this.store.object.possuiRecursosProprios && (
                <RecursoProprioDataTable
                  recursosProprios={this.store.object.recursosProprios}
                  loading={this.store.loading}
                  onAdd={(recursoProprio) => this.store.addRecursoProprio(recursoProprio)}
                  onRemove={(recursoProprio) =>
                    this.confirmDangerDialog({
                      message: 'Você realmente deseja remover este recurso próprio?',
                      header: 'Remover Recurso Próprio',
                      onAccept: () => this.store.removeRecursoProprio(recursoProprio),
                    })
                  }
                />
              )}
            </div>
          </div>
        </form>
      </div>
    );
  }
}

TabRecursosProprios.propTypes = {
  store: PropTypes.object,
  submitted: PropTypes.bool,
};

export default TabRecursosProprios;
