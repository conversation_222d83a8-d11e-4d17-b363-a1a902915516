import AdvancedSearch from 'fc/components/AdvancedSearch';
import FcButton from 'fc/components/FcButton';
import IndexDataTable from 'fc/components/IndexDataTable';
import GenericIndexPage from 'fc/pages/GenericIndexPage';
import { getValue, getValue<PERSON>y<PERSON><PERSON>, getValueElipsis, getValueMoney } from 'fc/utils/utils';
import { observer } from 'mobx-react';
import { Dialog } from 'primereact/dialog';
import AccessPermission from '~/constants/AccessPermission';
import ContratoDetailPage from '~/pages/contrato/indexDetail';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import ContratoObraIndexStore from '~/stores/geoObras/contrato/indexStore';
import './style.scss';

@observer
class TabContrato extends GenericIndexPage {
  constructor(props) {
    super(props, AccessPermission.contrato);
    this.store = new ContratoObraIndexStore();

    this.state = {
      detalhesVisibility: false,
      selectedRow: null,
    };
  }

  renderDetalhesDialog() {
    const header = (
      <div style={{ width: '100%', textAlign: 'left' }}>
        <h4 style={{ margin: 0 }}>Detalhes</h4>
      </div>
    );

    return (
      <Dialog
        header={header}
        visible={this.state.detalhesVisibility}
        style={{ width: '80vw' }}
        onHide={() => this.setState({ detalhesVisibility: false })}
        draggable={false}
        resizable={false}
        dismissableMask
      >
        <ContratoDetailPage idContrato={this.state.selectedRow.id} />
      </Dialog>
    );
  }

  render() {
    const columns = [
      {
        field: 'numeroContrato',
        header: 'Número',
        sortable: true,
      },
      {
        field: 'licitante',
        header: 'Contratado(a)',
        body: ({ licitante }) => getValue(licitante?.nome),
        body: ({ licitante }) => getValue(licitante?.nome),
        sortable: true,
      },
      {
        field: 'objeto',
        header: 'Objeto',
        body: ({ objeto }) => getValueElipsis(objeto, 150),
        sortable: true,
      },
      {
        field: 'tipo',
        header: 'Origem',
        sortable: true,
        body: ({ tipo }) => getValueByKey(tipo, DadosEstaticosService.getTipoProcesso()),
        body: ({ tipo }) => getValueByKey(tipo, DadosEstaticosService.getTipoProcesso()),
      },
      {
        field: 'valorGlobal',
        header: 'Valor',
        sortable: true,
        body: (contrato) => getValueMoney(contrato?.valorGlobal, contrato?.termoReferencia?.tresCasasDecimais ? 3 : 2),
      },
      {
        body: (rowData) => {
          return (
            <div className="actions p-d-flex p-jc-end">
              <FcButton
                icon="pi pi-list"
                tooltip="Visualizar"
                className="p-button-bg p-button-info p-mr-2"
                onClick={() => this.setState({ detalhesVisibility: true, selectedRow: rowData })}
              />
              <FcButton
                icon="pi pi-arrow-circle-right"
                tooltip="Selecionar"
                className="p-button-bg p-button-success p-mr-2"
                onClick={() => this.props.onSelect(rowData)}
              />
            </div>
          );
        },
      },
    ];
    const { listKey, loading } = this.store;
    const { getDefaultTableProps } = this;

    return (
      <div className="card page index-table">
        <AdvancedSearch
          searchParams={this.store.getAdvancedSearchParams()}
          store={this.store}
          searchFields={['numeroContrato']}
          filterSuggest={this.store.getFilterSuggest()}
        />
        <IndexDataTable columns={columns} value={listKey} loading={loading} {...getDefaultTableProps()} />
        {this.state.detalhesVisibility && this.renderDetalhesDialog()}
      </div>
    );
  }
}

TabContrato.displayName = 'ListagemContrato';

export default TabContrato;
