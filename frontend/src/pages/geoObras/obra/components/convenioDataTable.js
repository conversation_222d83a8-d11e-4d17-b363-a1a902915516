import { Column } from 'primereact/column';
import { DataTable } from 'primereact/datatable';
import React from 'react';
import PropTypes from 'prop-types';
import FcButton from 'fc/components/FcButton';
import { getValueDate, getValueMoney } from 'fc/utils/utils';
import { FilterMatchMode, PrimeIcons } from 'primereact/api';
import { InputText } from 'primereact/inputtext';
import { observer } from 'mobx-react';

@observer
class ConvenioDataTable extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      filters: {
        global: { value: null, matchMode: FilterMatchMode.CONTAINS },
      },
      filterValue: null,
    };
  }

  _renderColumns(columns) {
    return columns.map((col) => <Column key={`col-${col.field}`} {...col} />);
  }

  onGlobalFilterChange(e) {
    const value = e.target.value;
    let filters = { ...this.state.filters };
    filters['global'].value = value;

    this.setState({ filters, filterValue: value });
  }

  render() {
    const { convenios, loading, onClickNew, onClickEdit, onClickRemove } = this.props;

    const header = (
      <>
        <div className="flex justify-content-between">
          <div style={{ width: '12rem' }}>
            <FcButton
              type="button"
              className="p-button"
              label="Adicionar Convênio"
              style={{ marginBottom: '5px', marginRight: '5px' }}
              icon={PrimeIcons.PLUS}
              onClick={() => onClickNew()}
            />
          </div>
          <span className="p-input-icon-left" style={{ width: '20rem' }}>
            <i className="pi pi-search" />
            <InputText
              value={this.state.filterValue}
              onChange={(e) => this.onGlobalFilterChange(e)}
              placeholder="Filtre por algum valor"
            />
          </span>
        </div>
      </>
    );

    const columns = [
      {
        field: 'numero',
        header: 'Número',
        sortable: true,
      },
      {
        field: 'convenente',
        header: 'Convenente',
        sortable: true,
      },
      {
        field: 'cnpjConvenente',
        header: 'CNPJ do Convenente',
        sortable: true,
      },
      {
        field: 'dataAssinatura',
        header: 'Assinatura',
        body: ({ dataAssinatura }) => getValueDate(dataAssinatura),
        sortable: true,
      },
      {
        field: 'dataTermino',
        header: 'Término',
        body: ({ dataTermino }) => getValueDate(dataTermino),
        sortable: true,
      },
      {
        field: 'valor',
        header: 'Valor do Convênio',
        body: ({ valor }) => getValueMoney(valor),
        sortable: true,
      },
      {
        style: { width: '10%', textAlign: 'center' },
        body: (convenio) => (
          <div className="actions p-d-flex p-jc-end">
            <FcButton
              type="button"
              icon="pi pi-pencil"
              className="p-button-sm p-button-success p-mr-2"
              onClick={() => onClickEdit(convenio)}
            />
            <FcButton
              type="button"
              icon="pi pi-trash"
              className="p-button-sm p-button-danger"
              onClick={() => onClickRemove(convenio)}
            />
          </div>
        ),
      },
    ];

    return (
      <DataTable
        rowHover
        filters={this.state.filters}
        filterDisplay="menu"
        globalFilterFields={['numero', 'convenente', 'cnpjConvenente', 'dataAssinatura', 'dataTermino', 'valor']}
        editMode="cell"
        dataKey="id"
        responsiveLayout="scroll"
        stripedRows
        emptyMessage="Nenhum convênio adicionado"
        value={convenios}
        header={header}
        loading={loading}
      >
        {this._renderColumns(columns)}
      </DataTable>
    );
  }
}

ConvenioDataTable.propTypes = {
  convenios: PropTypes.array,
  loading: PropTypes.bool,
  onClickNew: PropTypes.func,
  onClickEdit: PropTypes.func,
  onClickRemove: PropTypes.func,
};

export default ConvenioDataTable;
