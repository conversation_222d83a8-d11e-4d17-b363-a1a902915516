import { Column } from 'primereact/column';
import { DataTable } from 'primereact/datatable';
import React from 'react';
import PropTypes from 'prop-types';
import FcButton from 'fc/components/FcButton';
import { FilterMatchMode } from 'primereact/api';
import { InputText } from 'primereact/inputtext';
import RecursoProprioDialogSelect from './recursoProprioDialogSelect';
import { getValue, getValue<PERSON>y<PERSON>ey, getValueElipsis, getValueMoney } from 'fc/utils/utils';
import DadosEstaticosService from '~/services/DadosEstaticosService';

const { observer } = require('mobx-react');

@observer
class RecursoProprioDataTable extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      filters: {
        global: { value: null, matchMode: FilterMatchMode.CONTAINS },
      },
      filterValue: null,
    };
  }

  _renderColumns(columns) {
    return columns.map((col) => <Column key={`col-${col.field}`} {...col} />);
  }

  onGlobalFilterChange(e) {
    const value = e.target.value;
    let filters = { ...this.state.filters };
    filters['global'].value = value;

    this.setState({ filters, filterValue: value });
  }

  render() {
    const { recursosProprios, loading, onAdd, onRemove } = this.props;

    const header = (
      <div className="flex justify-content-between">
        <div>
          <RecursoProprioDialogSelect
            onSelect={(recursoProprio) => {
              onAdd(recursoProprio);
              this.forceUpdate();
            }}
          />
        </div>
        <span className="p-input-icon-left" style={{ width: '20rem' }}>
          <i className="pi pi-search" />
          <InputText
            value={this.state.filterValue}
            onChange={(e) => this.onGlobalFilterChange(e)}
            placeholder="Filtre por algum valor"
          />
        </span>
      </div>
    );

    const columns = [
      {
        field: 'contrato.numero',
        header: 'Número',
        body: ({ contrato }) => getValue(contrato.numeroContrato),
        sortable: true,
      },
      {
        field: 'contrato.contratoLicitante.licitante',
        header: 'Contratado(a)',
        body: ({ contrato }) => getValue(contrato.licitante?.nome),
        sortable: true,
      },
      {
        field: 'contrato.objeto',
        header: 'Objeto',
        body: ({ contrato }) => getValueElipsis(contrato.objeto, 150),
        sortable: true,
      },
      {
        field: 'tipo',
        header: 'Origem',
        sortable: true,
        body: ({ contrato }) => getValueByKey(contrato.tipo, DadosEstaticosService.getTipoProcesso()),
      },
      {
        field: 'contrato.valorGlobal',
        header: 'Valor',
        sortable: true,
        body: (contrato) =>
          getValueMoney(contrato.contrato?.valorGlobal, contrato.contrato?.termoReferencia?.tresCasasDecimais ? 3 : 2),
      },
      {
        field: 'valorUtilizado',
        header: 'Valor Utilizado',
        sortable: true,
        body: (recursoProprio) =>
          getValueMoney(
            recursoProprio.valorUtilizado,
            recursoProprio.contrato?.termoReferencia?.tresCasasDecimais ? 3 : 2
          ),
      },
      {
        style: { width: '10%', textAlign: 'center' },
        body: (recursoProprio) => (
          <div className="actions p-d-flex p-jc-end">
            <FcButton
              type="button"
              icon="pi pi-trash"
              className="p-button-sm p-button-danger"
              onClick={() => {
                onRemove(recursoProprio);
                this.forceUpdate();
              }}
            />
          </div>
        ),
      },
    ];

    return (
      <DataTable
        rowHover
        filters={this.state.filters}
        filterDisplay="menu"
        globalFilterFields={['numero', 'convenente', 'cnpjConvenente', 'dataAssinatura', 'dataTermino', 'valor']}
        editMode="cell"
        dataKey="id"
        responsiveLayout="scroll"
        stripedRows
        emptyMessage="Nenhum recurso próprio adicionado"
        value={recursosProprios}
        header={header}
        loading={loading}
      >
        {this._renderColumns(columns)}
      </DataTable>
    );
  }
}

RecursoProprioDataTable.propTypes = {
  recursosProprios: PropTypes.array,
  loading: PropTypes.bool,
  onAdd: PropTypes.func,
  onRemove: PropTypes.func,
};

export default RecursoProprioDataTable;
