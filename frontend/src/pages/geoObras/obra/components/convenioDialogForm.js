import { Dialog } from 'primereact/dialog';
import { InputText } from 'primereact/inputtext';
import React from 'react';
import PropTypes from 'prop-types';
import FcButton from 'fc/components/FcButton';
import <PERSON><PERSON>ield from 'fc/components/FormField';
import moment from 'moment';
import InputMonetary from 'fc/components/InputMonetary';
import { InputMask } from 'primereact/inputmask';
import { Calendar } from 'primereact/calendar';
import ConvenioDialogFormStore from '~/stores/geoObras/obra/convenioDialogFormStore';
import { observer } from 'mobx-react';

@observer
class ConvenioDialogForm extends React.Component {
  constructor(props) {
    super(props);
    this.store = new ConvenioDialogFormStore();

    this.store.initialize(null, {});

    this.state = {
      submitted: false,
      isObjectChanged: false,
    };
  }

  reset() {
    this.store.cleanObjeto();
    this.setState({ submitted: false, isObjectChanged: false });
  }

  getDateAttributeValue(value) {
    return value ? moment(value).toDate() : value;
  }

  render() {
    const { convenio, visible, editMode, onHide, onCancel, onSave, onEdit, onEditOrSaveSuccessful } = this.props;
    const { updateAttribute, updateAttributeDate, getRule } = this.store;

    if (convenio && editMode && !this.state.isObjectChanged) {
      this.store.initialize(null, convenio);
      this.setState({ isObjectChanged: true });
    }

    return (
      <Dialog
        blockScroll
        header="Convênio"
        visible={visible}
        style={{ width: '50vw' }}
        onHide={() => {
          this.reset();
          onHide();
        }}
        draggable={false}
        resizable={false}
        footer={
          <div className="p-mt-2">
            <span className="p-mr-1 p-d-inline p-d-flex align-items-center">
              <FcButton
                label="Cancelar"
                type="button"
                className="p-ml-auto p-button-secondary p-mr-2"
                onClick={() => {
                  this.reset();
                  onCancel();
                }}
              />
              <FcButton
                label="Salvar"
                onClick={() => {
                  this.setState({ submitted: true });
                  if (!this.store.rules.hasError) {
                    const success = editMode ? onEdit(convenio, this.store.object) : onSave(this.store.object);
                    if (success) {
                      this.reset();
                      onEditOrSaveSuccessful();
                    }
                  }
                }}
              />
            </span>
          </div>
        }
      >
        <form>
          <div className="p-fluid p-formgrid p-grid">
            <FormField
              columns={6}
              attribute="numero"
              label="Número do Convênio"
              rule={getRule('numero')}
              submitted={this.state.submitted}
            >
              <InputText
                id="numero"
                placeholder="Informe o número"
                value={this.store.object.numero}
                onChange={(e) => {
                  updateAttribute('numero', e);
                }}
              />
            </FormField>
            <FormField
              columns={6}
              attribute="convenente"
              label="Nome do Convenente"
              rule={getRule('convenente')}
              submitted={this.state.submitted}
            >
              <InputText
                id="convenente"
                placeholder="Informe o convenente"
                value={this.store.object.convenente}
                onChange={(e) => {
                  updateAttribute('convenente', e);
                }}
              />
            </FormField>
            <FormField
              columns={6}
              attribute="cnpjConvenente"
              label="CNPJ do Convenente"
              rule={getRule('cnpjConvenente')}
              submitted={this.state.submitted}
            >
              <InputMask
                id="cnpjConvenente"
                mask={'99.999.999/9999-99'}
                placeholder="Informe o CNPJ do Convenente"
                value={this.store.object.cnpjConvenente}
                onChange={(e) => {
                  updateAttribute('cnpjConvenente', e);
                }}
              />
            </FormField>
            <FormField
              columns={3}
              attribute="dataAssinatura"
              label="Data de Assinatura"
              rule={getRule('dataAssinatura')}
              submitted={this.state.submitted}
            >
              <Calendar
                value={this.getDateAttributeValue(this.store.object.dataAssinatura)}
                onChange={(e) => updateAttributeDate('dataAssinatura', e)}
                showIcon
                mask="99/99/9999"
              />
            </FormField>
            <FormField
              columns={3}
              attribute="dataTermino"
              label="Data de Término"
              rule={getRule('dataTermino')}
              submitted={this.state.submitted}
            >
              <Calendar
                value={this.getDateAttributeValue(this.store.object.dataTermino)}
                onChange={(e) => updateAttributeDate('dataTermino', e)}
                showIcon
                mask="99/99/9999"
              />
            </FormField>
            <FormField
              columns={4}
              attribute="valor"
              label="Valor"
              rule={getRule('valor')}
              submitted={this.state.submitted}
            >
              <InputMonetary
                placeholder="R$"
                mode="currency"
                onChange={(e) => updateAttribute('valor', e)}
                value={this.store.object.valor}
              />
            </FormField>
          </div>
        </form>
      </Dialog>
    );
  }
}

ConvenioDialogForm.propTypes = {
  convenio: PropTypes.object,
  visible: PropTypes.bool,
  editMode: PropTypes.bool,
  onHide: PropTypes.func,
  onCancel: PropTypes.func,
  onSave: PropTypes.func,
  onEdit: PropTypes.func,
  onEditOrSaveSuccessful: PropTypes.func,
};

ConvenioDialogForm.defaultProps = {
  visible: false,
  editMode: false,
};

export default ConvenioDialogForm;
