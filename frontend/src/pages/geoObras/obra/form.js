import React from 'react';
import { observer } from 'mobx-react';
import PropTypes from 'prop-types';
import UrlRouter from '~/constants/UrlRouter';
import GenericFormPage from 'fc/pages/GenericFormPage';
import AppBreadCrumb from 'fc/components/AppBreadCrumb';
import AccessPermission from '~/constants/AccessPermission';
import PermissionProxy from 'fc/components/PermissionProxy';
import ObraFormStore from '~/stores/geoObras/obra/formStore';
import { Steps } from 'primereact/steps';
import TabContrato from './tabs/tabContrato';
import { Message } from 'primereact/message';
import FcButton from 'fc/components/FcButton';
import TabDadosGerais from './tabs/tabDadosGerais';
import AppStore from 'fc/stores/AppStore';
import { classNames } from 'primereact/utils';
import { ProgressSpinner } from 'primereact/progressspinner';
import TabArquivos from './tabs/tabArquivos';
import { showNotification } from 'fc/utils/utils';
import TabConvenios from './tabs/tabConvenios';
import TabRecursosProprios from './tabs/tabRecursosProprios';

@observer
class ObraFormPage extends GenericFormPage {
  constructor(props) {
    super(props, UrlRouter.obra.cadastro.index, AccessPermission.geoObras.obra);

    this.store = new ObraFormStore();

    this.formIndexes = Object.freeze({
      contrato: 0,
      dadosGerais: 1,
      convenios: 2,
      recursosProprios: 3,
      arquivos: 4,
    });

    this.state = {
      activeIndexForm: this.formIndexes.contrato,
    };
  }

  componentDidMount() {
    const { id, action } = this.props;

    if (action === 'edit') {
      this.setState({ activeIndexForm: this.formIndexes.dadosGerais });
    }

    const initialize = () =>
      this.store.initialize(id, {
        incorporavelPatrimonio: false,
        convenios: [],
        possuiConveniosAssociados: null,
        possuiRecursosProprios: null,
        recursosProprios: [],
      });

    if (id) {
      this.store.initializeArquivos(id, initialize);
    } else {
      initialize();
    }
  }

  onBack = () => {
    this.setState({ activeIndexForm: this.state.activeIndexForm - 1 });
  };

  onNext = () => {
    this.setState({ activeIndexForm: this.state.activeIndexForm + 1 });
  };

  isDisabledAvancar() {
    const { activeIndexForm } = this.state;

    const errorsMessages = {
      message: '',
      disabled: false,
    };

    const formValidations = {
      [this.formIndexes.contrato]: [
        {
          validate: () => this.store.object.contrato,
          message: 'É necessário selecionar um contrato antes de avançar para a próxima etapa.',
        },
      ],
      [this.formIndexes.dadosGerais]: [
        {
          validate: () => this.store.validaDadosGerais(),
          message: 'É necessário preencher todos os campos obrigatórios antes de avançar para a próxima etapa.',
        },
      ],
      [this.formIndexes.convenios]: [
        {
          validate: () => this.store.object.possuiConveniosAssociados !== null,
          message: 'É necessário preencher o campo "A obra possui convênios associados?" antes de avançar.',
        },
        {
          validate: () => !this.store.object.possuiConveniosAssociados || this.store.object.convenios.length > 0,
          message: 'É necessário adicionar ao menos um convênio antes de avançar para a próxima etapa.',
        },
      ],
      [this.formIndexes.recursosProprios]: [
        {
          validate: () => this.store.object.possuiRecursosProprios !== null,
          message: 'É necessário preencher o campo "A obra possui recursos próprios?" antes de avançar.',
        },
        {
          validate: () => !this.store.object.possuiRecursosProprios || this.store.object.recursosProprios.length > 0,
          message: 'É necessário adicionar ao menos um recurso próprio antes de avançar para a próxima etapa.',
        },
      ],
    };

    const validation = formValidations[activeIndexForm];
    if (validation) {
      const invalidValidator = validation.find(({ validate }) => !validate());
      if (invalidValidator) {
        errorsMessages.message = invalidValidator.message;
        errorsMessages.disabled = true;
      }
    }

    return errorsMessages;
  }

  onChangeStep(step, action = 'avancar', mode = 'button') {
    if (
      step < this.formIndexes.contrato ||
      (this.props.action === 'edit' && step === this.formIndexes.contrato && action === 'voltar' && mode === 'button')
    ) {
      this.store.toggleShowConfirmDialog();
    } else if (step === this.formIndexes.contrato && action === 'voltar' && this.props.action === 'edit') {
      this.setState({ activeIndexForm: this.state.activeIndexForm });
    } else {
      (action !== 'avancar' || !this.isDisabledAvancar().disabled) && this.setState({ activeIndexForm: step });
    }
  }

  validarArquivos(callback) {
    if (this.store.validateSubmittedFiles(this.store.arquivos)) {
      if (this.state.activeIndexForm === this.formIndexes.arquivos) {
        this.store.validaArquivos(callback);
      } else {
        callback && callback();
      }
    }
  }

  renderActionButtons() {
    const { activeIndexForm } = this.state;
    const hasWritePermission = !this.getWritePermission() || AppStore.hasPermission(this.getWritePermission());

    const disabledAvancar = this.isDisabledAvancar();
    return (
      <div className="p-mt-10 form-actions">
        <div className="p-mt-2">
          <span className="p-mr-1 p-d-inline p-d-flex align-items-center justify-content-end">
            {disabledAvancar.disabled && (
              <Message className="p-ml-auto p-mr-2" severity="warn" text={disabledAvancar.message} />
            )}
            <FcButton
              label="Voltar"
              type="button"
              className={classNames('p-button-secondary p-mr-2', { 'p-ml-auto': !disabledAvancar.disabled })}
              onClick={() => this.onChangeStep(activeIndexForm - 1, 'voltar', 'button')}
              loading={this.store.loading}
            />
            {activeIndexForm < this.formIndexes.arquivos && (
              <FcButton
                label="Avançar"
                type="button"
                onClick={() => this.onChangeStep(activeIndexForm + 1, 'avancar', 'button')}
                disabled={disabledAvancar.disabled}
              />
            )}
            {hasWritePermission && activeIndexForm === this.formIndexes.arquivos && (
              <FcButton
                label="Salvar"
                onClick={(e) => this.submitFormData(e)}
                loading={this.store.loading}
                disabled={disabledAvancar.disabled}
              />
            )}
          </span>
        </div>
      </div>
    );
  }

  handleSelectContrato(contrato) {
    this.store.setContrato(contrato);
    this.onNext();
  }

  submitFormData(e) {
    e && e.preventDefault();
    const execution = () => {
      if (!this.store.rules.hasError && this.store.validateSubmittedFiles(this.store.arquivos)) {
        this.store.validaArquivos(() => this.store.save(this._goBack, this.props.action));
      } else {
        showNotification('error', null, 'Verifique os campos dos formulários!');
      }
    };

    if (this.state.submitted) {
      execution();
    } else {
      this.setState({ submitted: true }, execution);
    }
  }

  render() {
    const { submitted } = this.state;

    const breacrumbItems = [
      { label: 'Cadastro de Obras', url: UrlRouter.obra.cadastro.index },
      { label: this.props.action === 'new' ? 'Novo' : 'Editar' },
    ];

    if (this.store.object) {
      let forms = [
        {
          label: 'Seleção do Contrato',
          step: this.formIndexes.contrato,
          body: (
            <TabContrato
              key="tabContrato"
              onSelect={(contrato) => this.handleSelectContrato(contrato)}
              store={this.store}
            />
          ),
        },
        {
          label: 'Dados Gerais',
          step: this.formIndexes.dadosGerais,
          body: <TabDadosGerais key="dadosGerais" store={this.store} submitted={submitted} />,
        },
        {
          label: 'Convênios',
          step: this.formIndexes.convenios,
          body: <TabConvenios key="convenios" store={this.store} submitted={submitted} />,
        },
        {
          label: 'Recursos Próprios',
          step: this.formIndexes.recursosProprios,
          body: <TabRecursosProprios key="recursosProprios" store={this.store} submitted={submitted} />,
        },
        {
          label: 'Arquivos',
          step: this.formIndexes.arquivos,
          body: <TabArquivos key="arquivos" store={this.store} submitted={submitted} />,
        },
      ];

      return (
        <PermissionProxy resourcePermissions={this.getWritePermission()} blockOnFail>
          <AppBreadCrumb items={breacrumbItems} />
          <div className="card page form-action-buttons">
            <Steps
              model={forms}
              activeIndex={this.state.activeIndexForm}
              onSelect={(e) =>
                this.onChangeStep(e.index, e.index < this.state.activeIndexForm ? 'voltar' : 'avancar', 'select')
              }
              readOnly={false}
            />
            {forms.find((item) => item.step === this.state.activeIndexForm).body}
            {this.renderActionButtons()}
            {this.store.isConfirmDialogVisible && this.confirmDiscardChanges()}
          </div>
        </PermissionProxy>
      );
    } else {
      return (
        <div className="card page">
          <AppBreadCrumb items={breacrumbItems} />
          <div className="p-d-inline p-d-flex align-items-center">
            <ProgressSpinner />
          </div>
        </div>
      );
    }
  }
}

ObraFormPage.propTypes = {
  id: PropTypes.any,
  action: PropTypes.any,
  history: PropTypes.any,
};

export default ObraFormPage;
