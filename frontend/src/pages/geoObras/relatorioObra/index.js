import React from 'react';
import { observer } from 'mobx-react';
import GenericIndexPage from 'fc/pages/GenericIndexPage';
import {
  getOriginUrl,
  getValue,
  getValueDate,
  getFormatMonthYear,
  getValueMoney,
  isValueValid,
  showNotification,
} from 'fc/utils/utils';
import { DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS } from 'fc/utils/date';
import { Dialog } from 'primereact/dialog';
import FcButton from 'fc/components/FcButton';
import { InputText } from 'primereact/inputtext';
import InputMonetary from 'fc/components/InputMonetary';
import MultipleFileUploader from 'fc/components/MultipleFileUploader';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import UrlRouter from '~/constants/UrlRouter';
import { PrimeIcons } from 'primereact/api';
import AdvancedSearch from 'fc/components/AdvancedSearch';
import IndexDataTable from 'fc/components/IndexDataTable';
import SicroSinapiIndexStore from '~/stores/geoObras/sicroSinapi/indexStore';
import PermissionProxy from 'fc/components/PermissionProxy';
import AppBreadCrumb from 'fc/components/AppBreadCrumb';
import AccessPermission from '~/constants/AccessPermission';
import DialogItemsMatching from './abas/components/dialogItemsMatching';
import { InputMask } from 'primereact/inputmask';

@observer
class RelatorioObraIndexPage extends GenericIndexPage {
  constructor(props) {
    super(props, AccessPermission.relatorioObra);
    this.store = new SicroSinapiIndexStore();

    this.state = {
      selectedReportId: null,
      visibiliyDialogImport: false,
      dialogItemsMatchingVisible: false,
    };
  }

  componentDidMount() {
    this.store.carregarArquivosObrigatorios();
  }

  toggleDialogItemsMatching() {
    this.setState({
      dialogItemsMatchingVisible: !this.state.dialogItemsMatchingVisible,
    });
  }

  handleImport() {
    if (!this.store.arquivos?.length) {
      showNotification('error', null, 'É necessário adicionar um arquivo!');
    } else if (!isValueValid(this.store.newRelatorio.titulo)) {
      showNotification('error', null, 'Verifique os campos do formulário!');
    } else {
      this.setState({ loadingImport: true }, () =>
        this.store.importarRelatorioObra(
          () => {
            this.setState({ visibiliyDialogImport: false, loadingImport: false }, () => {
              this.store.clear();
            });
          },
          () => this.setState({ visibiliyDialogImport: false, loadingImport: false })
        )
      );
    }
  }

  getDateAttributeValue(value) {
    const parts = value.split('/');
    const day = parseInt(parts[0], 10);
    const month = parseInt(parts[1], 10) - 1;
    const year = parseInt(parts[2], 10);
    return new Date(year, month, day);
  }

  _renderDialog() {
    const columnsArquivo = [
      {
        style: { width: '30%' },
        field: 'arquivo',
        header: 'Arquivo',
      },
      {
        field: 'tipo',
        header: (
          <div>
            Tipo <span className="p-error"> *</span>
          </div>
        ),
      },
      {
        style: { width: '25%' },
        field: 'descricao',
        header: 'Descrição',
      },
      {
        style: { width: '155px' },
        field: 'dataEnvio',
        header: 'Data de Envio',
        body: ({ dataEnvio }) => getValueDate(dataEnvio, DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS),
        editor: ({ value }) => getValueDate(value, DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS),
      },
    ];

    return (
      <Dialog
        header="Importar Relatório"
        visible={this.state.visibiliyDialogImport}
        style={{ width: '40vw' }}
        onHide={() => {
          this.store.clear();
          this.setState({ visibiliyDialogImport: false });
        }}
        footer={
          <div className="p-mt-2">
            <span className="p-mr-1 p-d-inline p-d-flex align-items-center">
              <FcButton
                label="Cancelar"
                type="button"
                className="p-ml-auto p-button-secondary p-mr-2"
                onClick={() => {
                  this.setState({ visibiliyDialogImport: false, loadingImport: false });
                  this.store.clear();
                }}
                disabled={this.state.loadingImport}
              />
              <FcButton label="Importar" onClick={() => this.handleImport()} loading={this.state.loadingImport} />
            </span>
          </div>
        }
      >
        <div className="p-mb-3">
          <a href={getOriginUrl() + '/assets/files/templatePlanilha.xlsx'} download="templatePlanilha.xlsx">
            <FcButton
              label="Template da Planilha"
              type="button"
              className="p-ml-auto p-button-secondary p-mr-2"
              icon="pi pi-download"
            />
          </a>
        </div>
        <div className="p-fluid p-formgrid p-grid">
          <div className="field col-6">
            <label>
              Titulo <span className="p-error"> *</span>
            </label>
            <InputText
              placeholder="Digite o título do relatório"
              onChange={(e) => this.store.updateAttribute('titulo', e)}
            />
          </div>
          <div className="field col-6">
            <label>
              Valor da Licitação <span className="p-error"> *</span>
            </label>
            <InputMonetary
              onChange={(e) => this.store.updateAttribute('valorLicitacao', e)}
              placeholder="Informe o valor da licitação"
            />
          </div>
          <div className="field col-6">
            <label>
              Data Desejavel <span className="p-error"> *</span>
            </label>
            <InputMask
              mask={'99/9999'}
              placeholder="Data Desejavel"
              onChange={(e) =>
                this.store.updateAttribute('dataDesejavel', this.getDateAttributeValue('01/' + e.target.value))
              }
              id="dataDesejavel"
            />
          </div>
          <div className="field col-12">
            <label>
              Arquivos <span className="p-error"> *</span>
            </label>
            <MultipleFileUploader
              store={this.store.fileStore}
              tableColumns={columnsArquivo}
              onChangeFiles={(files) => this.store.setArquivos(files)}
              fileTypes={DadosEstaticosService.getTipoArquivoRelatorioObra()}
              accept=".xlsx"
              multi={false}
            />
          </div>
        </div>
      </Dialog>
    );
  }

  render() {
    const columns = [
      {
        field: 'titulo',
        header: 'Título',
        sortable: true,
      },
      {
        field: 'autor',
        header: 'Autor',
        body: ({ usuario }) => getValue(usuario.nome),
      },
      {
        field: 'valorLicitacao',
        header: 'Valor da Licitação',
        sortable: true,
        body: ({ valorLicitacao }) => getValueMoney(valorLicitacao),
      },
      {
        field: 'dataCadastro',
        header: 'Data de Cadastro',
        sortable: true,
        body: ({ dataCadastro }) => getValueDate(dataCadastro, DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS),
      },
      {
        field: 'dataDesejavel',
        header: 'Data Desejavel',
        sortable: true,
        body: ({ dataDesejavel }) => getFormatMonthYear(dataDesejavel),
      },
      {
        style: { width: '110px' },
        body: (rowData) => (
          <div className="actions p-d-flex p-jc-end">
            {!rowData.correspondenciaRealizada && rowData.importado ? (
              <FcButton
                icon="pi pi-arrow-right-arrow-left"
                className="p-button-sm p-button-info p-mr-2"
                onClick={() => {
                  this.setState({ selectedReportId: rowData.id });
                  this.toggleDialogItemsMatching();
                }}
              />
            ) : (
              <FcButton
                icon="pi pi-pencil"
                className="p-button-sm p-button-success p-mr-2"
                onClick={() => this.pushUrlToHistory(UrlRouter.obra.relatorioObra.editar.replace(':id', rowData.id))}
              />
            )}
            <FcButton
              icon="pi pi-trash"
              className="p-button-sm p-button-danger p-mr-2"
              onClick={() => {
                this.setState({ selectedReportId: rowData.id });
                this.store.toggleShowConfirmDialog();
              }}
            />
          </div>
        ),
      },
    ];

    const header = (
      <div className="table-header">
        <FcButton
          className="p-button"
          label="Novo"
          style={{ marginBottom: '5px', marginRight: '5px' }}
          icon={PrimeIcons.PLUS}
          onClick={() => this.pushUrlToHistory(UrlRouter.obra.relatorioObra.novo)}
        />
        <FcButton
          className="p-button-secondary"
          label="Importar Relatório"
          style={{ marginBottom: '5px', marginRight: '5px' }}
          icon={PrimeIcons.FILE_EXCEL}
          onClick={() => this.setState({ visibiliyDialogImport: true })}
        />
      </div>
    );

    const { listKey, loading } = this.store;
    const { getDefaultTableProps } = this;

    const breacrumbItems = [{ label: 'SICRO/SINAPI' }];

    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        <AppBreadCrumb items={breacrumbItems} />
        <div className="card page index-table">
          <AdvancedSearch
            searchParams={this.store.getAdvancedSearchParams()}
            store={this.store}
            searchFields={['detalhes']}
            filterSuggest={this.store.getFilterSuggest()}
          />
          <IndexDataTable
            setColumnsToExport={this.setColumnsToExport}
            columns={columns}
            value={listKey}
            header={header}
            loading={loading}
            {...getDefaultTableProps()}
          />
          {this.state.selectedReportId && this.state.dialogItemsMatchingVisible && (
            <DialogItemsMatching
              reportId={this.state.selectedReportId}
              visible={this.state.dialogItemsMatchingVisible}
              onCancel={() => this.toggleDialogItemsMatching()}
              onSuccessfulSave={() => {
                this.toggleDialogItemsMatching();
                this.store.reloadTableData();
              }}
            />
          )}

          {this.store.isConfirmDialogVisible && this.confirmRemove(this.state.selectedReportId)}
          {this.state.visibiliyDialogImport && this._renderDialog()}
        </div>
      </PermissionProxy>
    );
  }
}

RelatorioObraIndexPage.displayName = 'RelatorioObraIndexPage';

export default RelatorioObraIndexPage;
