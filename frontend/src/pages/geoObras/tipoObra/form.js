import AppBreadCrumb from 'fc/components/AppBreadCrumb';
import FcInputTextarea from 'fc/components/FcInputTextarea';
import FormField from 'fc/components/FormField';
import PermissionProxy from 'fc/components/PermissionProxy';
import GenericFormPage from 'fc/pages/GenericFormPage';
import { observer } from 'mobx-react';
import { InputText } from 'primereact/inputtext';
import PropTypes from 'prop-types';
import AccessPermission from '~/constants/AccessPermission';
import UrlRouter from '~/constants/UrlRouter';
import FcDropdown from 'fc/components/FcDropdown';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import TipoObraFormStore from '~/stores/geoObras/tipoObra/formStore';

@observer
class TipoObraFormPage extends GenericFormPage {
  constructor(props) {
    super(props, UrlRouter.obra.tipoObra.index, AccessPermission.geoObras.tipoObra);

    this.store = new TipoObraFormStore();
  }

  componentDidMount() {
    const { id } = this.props;
    this.store.initialize(id);
  }

  _goBack(tipoObra) {
    const { closeMethod, history } = this.props;
    closeMethod ? closeMethod(tipoObra) : history.push(this.goBackUrl);
  }

  render() {
    const { submitted } = this.state;
    const { getRule } = this.store;
    const { submitFormData } = this;

    const breacrumbItems = [
      { label: 'Tipos de Obra', url: UrlRouter.obra.tipoObra.index },
      { label: this.props.action === 'new' ? 'Novo' : 'Editar' },
    ];

    let content;

    if (this.store.object) {
      content = (
        <>
          <AppBreadCrumb items={breacrumbItems} />
          <div className="card page form-action-buttons">
            <form onSubmit={submitFormData}>
              <div className="p-fluid p-formgrid p-grid">
                <FormField columns={6} attribute="nome" label="Nome" rule={getRule('nome')} submitted={submitted}>
                  <InputText
                    onChange={(e) => this.store.updateAttribute('nome', e)}
                    placeholder="Informe o nome"
                    value={this.store.object.nome}
                    id="nome"
                  />
                </FormField>

                <FormField
                  columns={6}
                  attribute="tipoDimensao"
                  label="Tipo de Dimensão"
                  rule={getRule('tipoDimensao')}
                  submitted={submitted}
                >
                  <FcDropdown
                    inOrder
                    id="tipoDimensao"
                    options={DadosEstaticosService.getTipoDimensaoObra()}
                    optionLabel="text"
                    optionValue="value"
                    value={this.store.object.tipoDimensao}
                    onChange={(e) => this.store.updateAttribute('tipoDimensao', e)}
                    placeholder="Selecione o tipo de dimensão da obra"
                    showClear
                    showFilterClear
                    filter
                    emptyMessage="Nenhum Registro Encontrado"
                    emptyFilterMessage="Nenhum Registro Encontrado"
                  />
                </FormField>

                <FormField
                  columns={12}
                  attribute="descricao"
                  label="Descrição"
                  rule={getRule('descricao')}
                  submitted={submitted}
                >
                  <FcInputTextarea
                    rows={6}
                    cols={30}
                    value={this.store.object.descricao}
                    onChange={(e) => this.store.updateAttribute('descricao', e)}
                  />
                </FormField>
              </div>
              {this.renderActionButtons()}
            </form>
          </div>
        </>
      );
    } else {
      content = (
        <div className="card page">
          <AppBreadCrumb items={breacrumbItems} />
          <i
            className="pi pi-spin pi-spinner"
            style={{
              marginTop: '20px',
              marginLeft: 'calc(50% - 20px)',
              fontSize: '2em',
            }}
          ></i>
        </div>
      );
    }
    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        {content}
      </PermissionProxy>
    );
  }
}

TipoObraFormPage.propTypes = {
  id: PropTypes.any,
  action: PropTypes.any,
  history: PropTypes.any,
};

export default TipoObraFormPage;
