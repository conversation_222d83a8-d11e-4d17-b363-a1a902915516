import AdvancedSearch from 'fc/components/AdvancedSearch';
import AppBreadCrumb from 'fc/components/AppBreadCrumb';
import FcButton from 'fc/components/FcButton';
import IndexDataTable from 'fc/components/IndexDataTable';
import PermissionProxy from 'fc/components/PermissionProxy';
import GenericIndexPage from 'fc/pages/GenericIndexPage';
import { observer } from 'mobx-react';
import { PrimeIcons } from 'primereact/api';
import AccessPermission from '~/constants/AccessPermission';
import UrlRouter from '~/constants/UrlRouter';
import { getValueByKey } from 'fc/utils/utils';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import TipoObraIndexStore from '~/stores/geoObras/tipoObra/indexStore';

@observer
class TipoObraIndexPage extends GenericIndexPage {
  constructor(props) {
    super(props, AccessPermission.geoObras.tipoObra);
    this.store = new TipoObraIndexStore();
    this.state = {
      idRemove: null,
    };
  }

  render() {
    const columns = [
      {
        field: 'nome',
        header: 'Nome',
        sortable: true,
      },
      {
        field: 'tipoDimensao',
        header: 'Tipo de Dimensão',
        body: ({ tipoDimensao }) => getValueByKey(tipoDimensao, DadosEstaticosService.getTipoDimensaoObra()),
        sortable: true,
      },
      {
        field: 'descricao',
        header: 'Descrição',
        sortable: true,
      },
      {
        style: { width: '110px' },
        body: (rowData) => {
          return (
            <div className="actions p-d-flex p-jc-end">
              <PermissionProxy resourcePermissions={this.getReadPermission()}>
                <FcButton
                  icon="pi pi-pencil"
                  className="p-button-sm p-button-success p-mr-2"
                  onClick={() => this.pushUrlToHistory(UrlRouter.obra.tipoObra.editar.replace(':id', rowData.id))}
                />
              </PermissionProxy>
              <PermissionProxy resourcePermissions={this.getWritePermission()}>
                <FcButton
                  icon="pi pi-trash"
                  className="p-button-sm p-button-danger"
                  onClick={() => {
                    this.setState({ idRemove: rowData.id });
                    this.store.toggleShowConfirmDialog();
                  }}
                />
              </PermissionProxy>
            </div>
          );
        },
      },
    ];

    const header = (
      <div className="table-header">
        <PermissionProxy resourcePermissions={this.getWritePermission()}>
          <FcButton
            className="p-button"
            label="Novo"
            style={{ marginBottom: '5px', marginRight: '5px' }}
            icon={PrimeIcons.PLUS}
            onClick={() => this.pushUrlToHistory(UrlRouter.obra.tipoObra.novo)}
          />
        </PermissionProxy>
        {this.renderTableDataExport(columns, 'tiposDeObras')}
      </div>
    );

    const { listKey, loading } = this.store;
    const { getDefaultTableProps } = this;

    const breacrumbItems = [{ label: 'Tipos de Obra' }];

    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        <AppBreadCrumb items={breacrumbItems} />
        <div className="card page index-table">
          <AdvancedSearch
            searchParams={this.store.getAdvancedSearchParams()}
            store={this.store}
            searchFields={['nome', 'descricao']}
          />
          <IndexDataTable
            setColumnsToExport={this.setColumnsToExport}
            columns={columns}
            value={listKey}
            header={header}
            loading={loading}
            {...getDefaultTableProps()}
          />
          {this.store.isConfirmDialogVisible && this.confirmRemove(this.state.idRemove)}
        </div>
      </PermissionProxy>
    );
  }
}

TipoObraIndexPage.displayName = 'TipoObraIndexPage';

export default TipoObraIndexPage;
