import classNames from 'classnames';
import SearchOperators from 'fc/components/AdvancedSearch/SearchOperators';
import CardList from 'fc/components/CardList';
import Carousel from 'fc/components/Carousel';
import ModalImages from 'fc/components/Carousel/ModalImages';
import FcButton from 'fc/components/FcButton';
import AppStore from 'fc/stores/AppStore';
import { DATE_FORMAT } from 'fc/utils/date';
import { getNumberFractionDigits, getValue, getValueDate, getValueMoney } from 'fc/utils/utils';
import { observer } from 'mobx-react';
import { Dialog } from 'primereact/dialog';
import { Divider } from 'primereact/divider';
import { ProgressSpinner } from 'primereact/progressspinner';
import { Sidebar } from 'primereact/sidebar';
import { TabPanel, TabView } from 'primereact/tabview';
import { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import ObraUpload from '~/pages/geoObras/acompanhamento/obraUpload';
import DadosEstaticosService from '~/services/DadosEstaticosService';

const ObraDetalhe = observer((props) => {
  const { idObra, onHide, obraStore, medicaoIndexStore, medicaoFormStore, googleStreetViewUrl } = props;
  const [obraDetalheVisible, setObraDetalheVisible] = useState(false);
  const [activeIndex, setActiveIndex] = useState(0);

  const [fotosInicio, setFotosInicio] = useState([]);
  const [fotosFinal, setFotosFinal] = useState([]);
  const [fotosMedicao, setFotosMedicao] = useState([]);

  const [modalFotosMedicaoVisible, setModalFotosMedicaoVisible] = useState(false);
  const [modalArquivosMedicaoVisible, setModalArquivosMedicaoVisible] = useState(false);
  const [loadingMedicao, setLoadingMedicao] = useState({});

  const obra = obraStore.object;

  useEffect(() => {
    if (idObra) {
      obraStore.initialize(idObra, {}, () =>
        obraStore.initializeArquivos(idObra, () => {
          const filtro = {
            page: { index: 1, size: 999 },
            sort: {
              by: 'dataCadastro',
              order: 'asc',
            },
            andParameters: [
              {
                field: 'obra',
                operator: SearchOperators.EQUAL_TO.value,
                value: idObra,
              },
            ],
          };
          medicaoIndexStore.load(filtro);
          obraStore
            .downloadFotos(obraStore.arquivos?.filter((f) => f.fase === 'INICIAL' && f.tipo === 'FOTO'))
            .then((images) => setFotosInicio(images));
          obraStore
            .downloadFotos(obraStore.arquivos?.filter((f) => f.fase === 'FINALIZACAO' && f.tipo === 'FOTO'))
            .then((images) => setFotosFinal(images));

          setObraDetalheVisible(true);
        })
      );
    } else {
      setObraDetalheVisible(false);
    }
  }, [idObra]);

  const getValueStyle = () => {
    return { fontSize: AppStore.layout === 'extenso' ? '1.26rem' : '1.1rem' };
  };

  const getValueLayoutClassNames = () => {
    const tipoLayout = AppStore.layout;
    return { 'py-0': tipoLayout === 'compacto', 'py-2': tipoLayout === 'default' || tipoLayout === 'extenso' };
  };

  const renderValue = (label, value, col = 12, type = 'value', url = '#') => {
    return (
      <div
        style={getValueStyle()}
        className={classNames(
          `details-set details-display flex align-items-center p-col-12 p-md-${col} p-sm-${col >= 6 ? col : 6}`,
          getValueLayoutClassNames()
        )}
      >
        <div
          style={{ color: 'rgb(0 0 0 / 50%)', paddingLeft: 0 }}
          className="xl:col-4 lg:col-4 md:col-2 sm:col-2 details-attributte drawer-content-label"
        >
          {label}
        </div>

        {type == 'value' && (
          <div
            style={{ paddingLeft: 0 }}
            className={`xl:col-8 lg:col-8 md:col-10 sm:col-10 details-value p-text-justify pb-1`}
          >
            {value ?? '-'}
          </div>
        )}

        {type == 'link' && (
          <div
            style={{ paddingLeft: 0 }}
            className={`xl:col-10 lg:col-9 md:col-8 sm:col-8 details-value p-text-justify`}
          >
            {
              <Link
                style={{ color: '#3F51B5' }}
                onClick={(event) => {
                  event.preventDefault();
                  window.open(url, '_blank');
                }}
              >
                <u>{value}</u>
              </Link>
            }
          </div>
        )}
      </div>
    );
  };

  const renderDivider = (label) => {
    return (
      <div style={{ flexBasis: 'auto', display: 'flex' }}>
        <h5
          style={{
            padding: '7px',
            whiteSpace: 'nowrap',
            width: 'auto',
            color: '#333333',
            alignContent: 'left',
            marginRight: '5px',
            marginTop: '11px',
          }}
        >
          {label}
        </h5>
        <Divider />
      </div>
    );
  };

  const renderPanelDadosObra = (obra) => {
    return (
      <div className="p-fluid p-formgrid">
        {renderValue('Número', obra.numero)}
        {renderValue('Data de Cadastro', getValueDate(obra.dataCadastro, DATE_FORMAT))}
        {renderValue('Valor', getValueMoney(obra.valor))}
        {renderValue('Dimensões', `${getNumberFractionDigits(obra.dimensoes)} m²`)}
        {renderValue('Endereço', obra.endereco)}
        {renderValue('Street View ', 'Google Maps', 12, 'link', googleStreetViewUrl)}
        {renderValue('Descrição', obra.descricao)}
        <ObraUpload
          arquivos={obraStore.arquivos.filter((f) => f.fase === 'CADASTRAL')}
          tipos={DadosEstaticosService.getTipoArquivoObra().filter((t) => t.fase === 'CADASTRAL')}
          readOnly={true}
          store={obraStore.fileStore}
        />
      </div>
    );
  };

  const carregaFotosMedicao = (obra, idMedicao, callback) => {
    const newLoadingMedicao = { ...loadingMedicao };
    newLoadingMedicao[idMedicao] = true;
    setLoadingMedicao(newLoadingMedicao);
    medicaoFormStore.initializeArquivos(obra, idMedicao, () => {
      const fotos = medicaoFormStore.arquivos?.filter((arquivo) => arquivo.tipo === 'FOTO');
      medicaoFormStore
        .downloadFotos(fotos)
        .then((images) => {
          callback && callback(images);
        })
        .finally(() => {
          const newLoadingMedicao = { ...loadingMedicao };
          newLoadingMedicao[idMedicao] = false;
          setLoadingMedicao(newLoadingMedicao);
        });
    });
  };

  const carregaArquivosMedicao = (obra, idMedicao, callback) => {
    const newLoadingMedicao = { ...loadingMedicao };
    newLoadingMedicao[idMedicao] = true;
    setLoadingMedicao(newLoadingMedicao);
    medicaoFormStore.initializeArquivos(obra, idMedicao, () => {
      callback && callback();
      const newLoadingMedicao = { ...loadingMedicao };
      newLoadingMedicao[idMedicao] = false;
      setLoadingMedicao(newLoadingMedicao);
    });
  };

  const getCardEllipsisOptions = (obra, medicao) => {
    const items = [
      {
        label: 'Fotos',
        icon: 'pi pi-images',
        command: () =>
          carregaFotosMedicao(obra, medicao.id, (images) => {
            setFotosMedicao(images);
            setModalFotosMedicaoVisible(true);
          }),
      },
      {
        label: 'Documentos',
        icon: 'pi pi-file',
        command: () => carregaArquivosMedicao(obra, medicao.id, () => setModalArquivosMedicaoVisible(true)),
      },
    ];

    return items;
  };

  const renderMedicoesListInstance = () => {
    const fields = [
      {
        label: 'Medição',
        field: 'dataCadastro',
        value: 'title',
        body: ({ dataCadastro }) => `Medição - ${getValueDate(dataCadastro, DATE_FORMAT)}`,
      },
      {
        label: 'Número Empenho',
        field: 'numeroEmpenho',
        value: 'subtitle',
        body: ({ empenho }) => `Empenho Nº: ${getValue(empenho?.numeroEmpenho)}`,
      },

      {
        field: 'objeto',
        label: 'Objeto',
        value: 'mainContent',
        body: ({ id }) =>
          loadingMedicao[id] ? (
            <div className="p-d-inline p-d-flex align-items-center">
              <ProgressSpinner style={{ width: '50px', height: 'auto' }} />
            </div>
          ) : (
            ''
          ),
      },
      {
        field: 'intervalo',
        label: 'Intervalo da Medição',
        value: 'iconLabel',
        color: '#38AAAD',
        icon: 'pi pi-calendar',
        body: ({ dataInicio, dataFim }) =>
          `${getValueDate(dataInicio, DATE_FORMAT)} a ${getValueDate(dataFim, DATE_FORMAT)}`,
      },
      {
        field: 'valor',
        label: 'Valor da Medição',
        value: 'iconLabel',
        color: '#d080fa',
        icon: 'pi pi-money-bill',
        body: ({ valor }) => getValueMoney(valor),
      },
      {
        field: 'percentual',
        label: 'Percentual de Conclusão',
        value: 'iconLabel',
        color: '#46ad38',
        icon: 'pi pi-hourglass',
        body: ({ percentualConclusao }) => `${getValueMoney(percentualConclusao)} %`,
      },
      {
        label: 'Ações',
        value: 'ellipsisItems',
        body: (medicao) => getCardEllipsisOptions(obra, medicao),
      },
    ];
    return (
      <div>
        <CardList
          fields={fields}
          store={medicaoIndexStore}
          emptyMessage="Nenhuma medição cadastrada para a obra."
          footer={() => ''}
        />
      </div>
    );
  };

  const renderPanelCicloVida = (obra) => {
    return (
      <>
        <div className="p-fluid p-formgrid">
          {obra.dataInicio && (
            <>
              {renderDivider('Fase Inicial')}
              {renderValue('Data de Início', getValueDate(obra.dataInicio, DATE_FORMAT))}
              <Carousel images={fotosInicio} />
            </>
          )}

          {renderDivider('Fase Medição')}
          {renderMedicoesListInstance()}

          {obra.dataFim && (
            <>
              {renderDivider('Fase Final')}
              {renderValue('Data Final', getValueDate(obra.dataFim, DATE_FORMAT))}
              <Carousel images={fotosFinal} />
            </>
          )}
          {obra.dataConclusao && (
            <>
              {renderDivider('Fase Entrega')}
              {renderValue('Data Conclusão', getValueDate(obra.dataConclusao, DATE_FORMAT))}
              {renderValue('Data Recebimento', getValueDate(obra.dataRecebimento, DATE_FORMAT))}
              {renderValue('Tipo Encerramento', obra.tipoEncerramento)}
              <ObraUpload
                arquivos={obraStore.arquivos.filter((f) => f.fase === 'ENTREGA')}
                tipos={DadosEstaticosService.getTipoArquivoObra().filter((t) => t.fase === 'ENTREGA')}
                readOnly={true}
                store={obraStore.fileStore}
              />
            </>
          )}
        </div>
        <ModalImages
          images={fotosMedicao}
          visible={modalFotosMedicaoVisible}
          onHide={() => setModalFotosMedicaoVisible(false)}
        />
        <Dialog
          header="Arquivo da Medição"
          style={{ width: '50vw' }}
          draggable={false}
          visible={modalArquivosMedicaoVisible}
          onHide={() => setModalArquivosMedicaoVisible(false)}
        >
          <ObraUpload
            arquivos={medicaoFormStore.arquivos?.filter(
              (arquivo) => !['FOTO', 'GEORREFERENCIAMENTO'].includes(arquivo.tipo)
            )}
            tipos={DadosEstaticosService.getTipoArquivoMedicaoObra()}
            readOnly={true}
            store={medicaoFormStore.fileStore}
          />
        </Dialog>
      </>
    );
  };

  return (
    <Sidebar
      position="right"
      dismissable={false}
      modal={false}
      d
      style={{
        width: '530px',
        display: 'flex',
        flexDirection: 'column',
        paddingTop: '1rem',
      }}
      visible={obraDetalheVisible}
    >
      <div id="header" className="flex justify-content-between align-items-center mt-2 text-xl font-bold">
        <div className="flex gap-2 align-items-center">
          <span>Detalhes da Obra</span>
        </div>
        <FcButton
          icon="pi pi-times"
          className="p-button-rounded p-button-text"
          onClick={() => onHide()}
          style={{ color: 'black' }}
        />
      </div>
      <Divider className="mt-3" />
      {obraStore.loading ? (
        <div className="p-d-inline p-d-flex align-items-center">
          <ProgressSpinner />
        </div>
      ) : (
        <TabView activeIndex={activeIndex} onTabChange={(e) => setActiveIndex(e.index)}>
          <TabPanel header="Dados da Obra">{obra && renderPanelDadosObra(obra)}</TabPanel>
          <TabPanel header="Ciclo de Vida">{obra && renderPanelCicloVida(obra)}</TabPanel>
        </TabView>
      )}
    </Sidebar>
  );
});

export default ObraDetalhe;
