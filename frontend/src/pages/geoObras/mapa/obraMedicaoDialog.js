import React from 'react';
import { observer } from 'mobx-react';
import PropTypes from 'prop-types';
import GenericFormPage from 'fc/pages/GenericFormPage';
import FormField from 'fc/components/FormField';
import AccessPermission from '~/constants/AccessPermission';
import { Dialog } from 'primereact/dialog';
import ObraMedicaoFormStore from '~/stores/obraMedicao/formStore';
import MultipleFileUploader from 'fc/components/MultipleFileUploader';
import { getValueDate, isValueValid, showNotification } from 'fc/utils/utils';
import { DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS } from 'fc/utils/date';
import AppStore from 'fc/stores/AppStore';
import FcButton from 'fc/components/FcButton';
import { InputTextarea } from 'primereact/inputtextarea';
import { Calendar } from 'primereact/calendar';
import InputMonetary from 'fc/components/InputMonetary';
import { Slider } from 'primereact/slider';
import { InputText } from 'primereact/inputtext';
import { Dropdown } from 'primereact/dropdown';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import { Fieldset } from 'primereact/fieldset';
import RequisicaoModificacaoFormStore from '~/stores/requisicaoModificacao/formStore';
import FcInputTextarea from 'fc/components/FcInputTextarea';

@observer
class ObraMedicaoDialog extends GenericFormPage {
  constructor(props) {
    super(props, AccessPermission.obraMedicao);

    this.store = new ObraMedicaoFormStore();
    this.reqModificacaoStore = new RequisicaoModificacaoFormStore();
    this.state = { readFiles: false, visibleDialogReqMod: false, errorDialogValue: false };

    this.validarForm = this.validarForm.bind(this);
  }

  componentDidMount() {
    const { obra, idObraMedicaoReqMod } = this.props;
    this.store.initialize(
      obra.id,
      {
        obra: obra,
        valorEmpenhado: 0.0,
      },
      idObraMedicaoReqMod
    );
  }

  submitFormData(e) {
    e && e.preventDefault();

    const callback = () => {
      this.props.closeDialog();
      this.props.onSave && this.props.onSave();
    };

    const execution = () => {
      if (!this.store.rules.hasError) {
        if (this.props.idObraMedicaoReqMod) {
          const obraMedicaoDTO = {
            obraMedicao: { ...this.store.object },
            arquivosObraMedicao: this.store.arquivos,
          };

          this.reqModificacaoStore.justificativaJurisdicionado &&
            this.reqModificacaoStore.enviarRequisicaoObraMedicao(obraMedicaoDTO, callback);
        } else {
          this.store.save(callback, 'new');
        }
      } else {
        showNotification('error', null, 'Verifique os campos do formulário!');
      }
    };

    if (this.state.submitted) {
      execution();
    } else {
      this.setState({ submitted: true }, execution);
    }
  }

  renderActionButtons() {
    const hasWritePermission = !this.getWritePermission() || AppStore.hasPermission(this.getWritePermission());
    return (
      <div className="p-mt-2">
        <span className="p-mr-1 p-d-inline p-d-flex align-items-center">
          <FcButton
            label="Cancelar"
            type="button"
            className="p-ml-auto p-button-secondary p-mr-2"
            onClick={() => this.props.closeDialog()}
            loading={this.store.loading}
          />
          {hasWritePermission && !this.props.idObraMedicaoReqMod && (
            <FcButton
              label="Salvar"
              type="button"
              loading={this.store.loading}
              onClick={(e) => this.validarForm(() => this.submitFormData(e))}
            />
          )}
          {hasWritePermission && this.props.idObraMedicaoReqMod && (
            <FcButton
              label="Enviar Requisição"
              type="button"
              onClick={() => this._toggleDialogReqMod()}
              loading={this.reqModificacaoStore.loading}
            />
          )}
        </span>
      </div>
    );
  }

  _toggleDialogReqMod() {
    this.setState((oldState) => ({ visibleDialogReqMod: !oldState.visibleDialogReqMod }));
  }

  renderDialogReqModFooter() {
    return (
      <div>
        <FcButton
          label="Cancelar"
          icon="pi pi-times"
          onClick={() => this._toggleDialogReqMod()}
          className="p-button-text"
        />
        <FcButton
          label="Enviar Requisição"
          icon="pi pi-check"
          loading={this.reqModificacaoStore.loading}
          onClick={(e) => {
            if (this.reqModificacaoStore.justificativaJurisdicionado) {
              this.validarForm(() => this.submitFormData(e));
              this._toggleDialogReqMod();
            } else {
              !this.state.errorDialogValue && this.setState({ errorDialogValue: true });
            }
          }}
        />
      </div>
    );
  }

  _getFieldErrorMessage(rendered, message) {
    if (rendered) {
      return <small className="p-error">{message}</small>;
    }
    return null;
  }

  renderDialogRequisicaoModificacao() {
    const { getRule } = this.store;
    return (
      <Dialog
        header="Justificativa Requisição"
        visible={this.state.visibleDialogReqMod}
        style={{ width: '50vw' }}
        footer={this.renderDialogReqModFooter()}
        onHide={() => this._toggleDialogReqMod()}
      >
        <div className="p-fluid p-formgrid p-grid">
          <FormField columns={12} attribute="justificativa" rule={getRule('justificativa')} label="Justificativa">
            <FcInputTextarea
              className={this.state.errorDialogValue ? 'p-invalid p-error' : ''}
              value={this.reqModificacaoStore.justificativaJurisdicionado}
              onChange={(e) => {
                this.reqModificacaoStore.updateAttribute('jurisdicionado', e);
                isValueValid(this.reqModificacaoStore.justificativaJurisdicionado?.length) &&
                  this.setState({ errorDialogValue: false });
              }}
              placeholder="Descreva a justificativa"
            />
          </FormField>
          <div className="p-col-6">
            {this._getFieldErrorMessage(this.state.errorDialogValue, 'Por favor, preencha o campo')}
          </div>
        </div>
      </Dialog>
    );
  }

  validarForm(callback) {
    if (this.store.validateSubmittedFiles(this.store.arquivos)) {
      this.store.validaArquivos(callback);
    }
  }

  render() {
    const { submitted } = this.state;
    const { visible, closeDialog } = this.props;
    const { getRule } = this.store;
    const { submitFormData, store } = this;

    const columnsArquivosMedicao = [
      {
        style: { width: '30%' },
        field: 'arquivo',
        header: 'Arquivo',
      },
      {
        style: { width: '20%' },
        field: 'tipo',
        header: (
          <div>
            Tipo <span className="p-error"> *</span>
          </div>
        ),
      },
      {
        style: { width: '25%' },
        field: 'descricao',
        header: 'Descrição',
      },
      {
        style: { width: '25%' },
        field: 'dataEnvio',
        header: 'Data de Envio',
        body: ({ dataEnvio }) => getValueDate(dataEnvio, DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS),
        editor: ({ value }) => getValueDate(value, DATE_FORMAT_WITH_HOURS, DATE_PARSE_FORMAT_WITH_HOURS),
      },
    ];

    return (
      <div>
        <Dialog
          header="Medição de Obra"
          visible={visible}
          modal
          style={{ minWidth: '60vw', maxWidth: '600px' }}
          onHide={closeDialog}
          draggable={false}
          resizable={false}
          dismissableMask
        >
          {store.object && (
            <div>
              {!this.props.idObraMedicaoReqMod && <h5 style={{ marginBottom: '10px' }}>Cadastrar Nova Medição</h5>}
              <form onSubmit={submitFormData}>
                <div className="p-fluid p-formgrid p-grid">
                  {this.props.idObraMedicaoReqMod && (
                    <div className="p-col-12 mb-2">
                      <Fieldset legend="AVISO">
                        <h6 style={{ color: '#dd0303' }}>
                          A EDIÇÃO DESTA MEDIÇÃO ESTARÁ SUJEITA A RATIFICAÇÃO PELA EQUIPE DE AUDITORIA DO TCE
                        </h6>
                      </Fieldset>
                    </div>
                  )}
                  <FormField
                    columns={4}
                    attribute="percConclusao"
                    label="Percentual de Conclusão"
                    submitted={submitted}
                    rule={getRule('percConclusao')}
                  >
                    <Slider
                      value={
                        store.object.percConclusao ??
                        (store.medicoesObra.length
                          ? store.medicoesObra[store.medicoesObra.length - 1]?.obraMedicao?.percConclusao
                          : 0)
                      }
                      onChange={(e) => store.updateAttribute('percConclusao', e.value)}
                      style={{ marginTop: '12px' }}
                      min={
                        store.medicoesObra.length
                          ? store.medicoesObra[store.medicoesObra.length - 1]?.obraMedicao?.percConclusao + 1
                          : 1
                      }
                      max={this.store.maxPercConclusao}
                      step={1}
                    />
                  </FormField>
                  <FormField columns={2} attribute="percConclusao" submitted={submitted}>
                    <InputText
                      id="percConclusao"
                      style={{ marginTop: '26px' }}
                      value={`${
                        store.object.percConclusao ??
                        (store.medicoesObra.length
                          ? store.medicoesObra[store.medicoesObra.length - 1]?.obraMedicao?.percConclusao
                          : 0)
                      }%`}
                      disabled
                    />
                  </FormField>
                  <FormField
                    columns={3}
                    attribute="dataMedicao"
                    label="Data da Medição"
                    submitted={submitted}
                    rule={getRule('dataMedicao')}
                  >
                    <Calendar
                      value={this.getDateAttributeValue(store.object.dataMedicao)}
                      onChange={(e) => store.updateAttributeDateWithHours('dataMedicao', e)}
                      showTime
                      hourFormat="24"
                      showIcon
                    />
                  </FormField>
                  <FormField
                    columns={3}
                    attribute="valorEmpenhado"
                    label="Valor Pago"
                    submitted={submitted}
                    rule={getRule('valorEmpenhado')}
                  >
                    <InputMonetary
                      onChange={(e) => store.updateAttribute('valorEmpenhado', e)}
                      placeholder="R$"
                      value={store.object.valorEmpenhado}
                    />
                  </FormField>
                  <FormField
                    columns={3}
                    attribute="numEmpenho"
                    label="Número Empenho"
                    submitted={submitted}
                    rule={getRule('numEmpenho')}
                  >
                    <Dropdown
                      onChange={(e) => store.updateAttribute('numEmpenho', e)}
                      placeholder={'Selecione um número de empenho'}
                      value={store.object?.numEmpenho}
                      id="numEmpenho"
                      optionLabel="numeroEmpenho"
                      optionValue="numeroEmpenho"
                      emptyMessage="Nenhum empenho encontrado"
                      options={this.props?.obra?.contrato?.empenhos}
                    />
                  </FormField>
                  <FormField
                    columns={9}
                    attribute="descricao"
                    label="Descrição"
                    submitted={submitted}
                    rule={getRule('descricao')}
                  >
                    <InputTextarea
                      autoResize
                      onChange={(e) => store.updateAttribute('descricao', e)}
                      placeholder="Informe uma descrição"
                      value={store.object.descricao}
                    />
                  </FormField>
                  <FormField columns={12}>
                    <MultipleFileUploader
                      store={store.fileStore}
                      tableColumns={columnsArquivosMedicao}
                      onChangeFiles={(files) => store.setFileList(files)}
                      fileTypes={DadosEstaticosService.getTipoArquivoObraMedicao()}
                      accept=""
                    />
                  </FormField>
                </div>
                {this.renderActionButtons()}
              </form>
            </div>
          )}
        </Dialog>
        {this.store.object && this.renderDialogRequisicaoModificacao()}
      </div>
    );
  }
}

ObraMedicaoDialog.propTypes = {
  closeDialog: PropTypes.func.isRequired,
  visible: PropTypes.bool.isRequired,
  obra: PropTypes.object,
  idObraMedicaoReqMod: PropTypes.number,
  onSave: PropTypes.func,
};

export default ObraMedicaoDialog;
