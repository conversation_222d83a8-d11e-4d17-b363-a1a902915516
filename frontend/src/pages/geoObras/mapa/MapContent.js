import 'leaflet.awesome-markers/dist/leaflet.awesome-markers.css';
import 'leaflet.awesome-markers/dist/leaflet.awesome-markers.js';
import 'leaflet/dist/leaflet.css';
import 'leaflet.markercluster/dist/leaflet.markercluster.js';
import 'leaflet.markercluster/dist/MarkerCluster.css';
import 'leaflet.markercluster/dist/MarkerCluster.Default.css';
import L from 'leaflet';
import { observer } from 'mobx-react';
import { useEffect, useMemo, useState } from 'react';
import { GeoJSON, useMap } from 'react-leaflet';
import MarkerClusterGroup from '@changey/react-leaflet-markercluster';

const MunicipiosGeometry = require('~/constants/municipios_ac.json');

const WMS_API_URL = window._env_.WMS_API_URL;
const WMS_LAYER = window._env_.WMS_LAYER;

const MAX_ZOOM_TO_RENDER_GEOMETRY = 13;

const MapContent = observer(({ ente, store }) => {
  const { geoSelected, setGeoSelected, loadingMascara, mascara } = store;
  const obras = store.listKey;
  const map = useMap();
  const [geoHovered, setGeoHovered] = useState(new Map());
  const [zoom, setZoom] = useState(0);

  map.on('zoomend', () => {
    setZoom(map.getZoom());
  });

  useEffect(() => {
    if (geoSelected) {
      const obra = obras.find((o) => o.obra.id === geoSelected);
      if (obra) {
        const point = [...obra.centroid.coordinates];
        map.flyTo(point.reverse(), 15, { animate: true });
      }
    }
  }, [geoSelected]);

  useEffect(() => {
    try {
      const enteNome = ente && ente.nome ? ente.nome : 'Estado do Acre';
      const enteGeoJson = fetchEnteGeometryFromNome(enteNome);
      addEnteBoundsToMap(enteGeoJson);
    } catch (e) {
      console.error(e);
    }
  }, []);

  useEffect(() => {
    if (!loadingMascara && WMS_API_URL && WMS_LAYER && mascara) {
      const maskLayer = L.TileLayer.boundaryCanvas('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        boundary: mascara,
        zIndex: 999,
      });
      map.addLayer(maskLayer);
    }
  }, [loadingMascara, mascara]);

  const createFeature = ({ fase, obra, geom }) => ({
    type: 'Feature',
    properties: { idObra: obra.id, fase: fase },
    geometry: geom,
  });

  const createFeatureColletion = (obras) => {
    let obrasGeojson = null;
    if (obras && obras.length > 0) {
      const obrasFases = obras.map((obra) => {
        const inicial = obras.filter((o) => o.fase === 'INICIAL' && o.obra.id === obra.obra.id);
        const medicao = obras.filter((o) => o.fase === 'MEDICAO' && o.obra.id === obra.obra.id);
        const finalizacao = obras.filter((o) => o.fase === 'FINALIZACAO' && o.obra.id === obra.obra.id);
        return finalizacao.length > 0 ? finalizacao : inicial.concat(medicao);
      });

      obrasGeojson = {
        type: 'FeatureCollection',
        features: obrasFases.flat().map(createFeature),
      };
    }
    return obrasGeojson;
  };

  const createCentroidGeojson = (obras) => {
    let centroidGeojson = [];
    if (obras && obras.length > 0) {
      const result = Object.groupBy(obras, ({ obra }) => obra.id);
      const fases = Object.keys(result).map((k) => result[k].pop());
      if (fases?.length > 0) {
        centroidGeojson = fases.map((fase) => fase.centroid);
      }
    }
    return centroidGeojson;
  };

  const geoJsonData = useMemo(() => createFeatureColletion(obras), [obras]);
  const centroidGeoJsonData = useMemo(() => createCentroidGeojson(obras), [obras]);

  const styleFeature = (feature) => {
    const fase = feature?.properties?.fase;
    const idObra = feature?.properties?.idObra;
    const isSelected = geoSelected === idObra || geoHovered.get(idObra);
    const colors = {
      INICIAL: { color: '#ffa726' },
      MEDICAO: { color: '#66bb6a' },
      FINALIZACAO: { color: '#66bb6a' },
    };

    return {
      color: colors[fase]?.color,
      weight: isSelected ? 10 : 5,
    };
  };

  const onClickFeature = (layer) => {
    const { feature } = layer.target;
    const idObra = feature.properties?.idObra;

    if (idObra) {
      setGeoSelected(idObra);
    }
  };

  const highlightFeature = ({ target }) => {
    const { feature } = target;
    const idObra = feature.properties?.idObra;
    if (idObra) {
      const newGeoHovered = new Map(geoHovered);
      newGeoHovered.set(idObra, true);
      setGeoHovered(newGeoHovered);
    }
  };

  const resetFeatureHighlight = ({ target }) => {
    const { feature } = target;
    const idObra = feature.properties?.idObra;
    if (idObra) {
      const newGeoSelected = new Map(geoHovered);
      newGeoSelected.set(idObra, false);
      setGeoHovered(newGeoSelected);
    }
  };

  const defineLayerListeners = (layer) => {
    const events = {
      mouseover: highlightFeature,
      mouseout: resetFeatureHighlight,
      click: onClickFeature,
    };
    layer.on(events);
  };

  const addEnteBoundsToMap = (enteGeoJson) => {
    const mapGeom = L.geoJSON(enteGeoJson);
    const geometryBounds = mapGeom.getBounds();
    map.setZoom(ente?.localizacaoZoom || 13);
    map.fitBounds(geometryBounds);
  };

  const fetchEnteGeometryFromNome = (nome) => {
    const enteGeoJson = MunicipiosGeometry.features;
    const upperNome = nome?.toUpperCase();
    const filtered = enteGeoJson?.filter(({ properties }) => {
      return properties['NOME']?.toUpperCase() === upperNome;
    });
    return filtered?.length > 0 ? filtered[0] : null;
  };

  const enteGeoJSON = useMemo(() => {
    const enteNome = ente && ente.nome ? ente.nome : 'Estado do Acre';
    return fetchEnteGeometryFromNome(enteNome);
  }, [ente]);

  return (
    <div id={`map-obra-${geoHovered ?? ''}`}>
      <GeoJSON data={enteGeoJSON} style={{ fillOpacity: 0 }} />
      {zoom < MAX_ZOOM_TO_RENDER_GEOMETRY ? (
        <MarkerClusterGroup showCoverageOnHover={false} maxClusterRadius={30}>
          {centroidGeoJsonData.length > 0 && <GeoJSON data={centroidGeoJsonData} />}
        </MarkerClusterGroup>
      ) : (
        <GeoJSON data={geoJsonData} style={styleFeature} onEachFeature={(_, layer) => defineLayerListeners(layer)} />
      )}
    </div>
  );
});

export default MapContent;
