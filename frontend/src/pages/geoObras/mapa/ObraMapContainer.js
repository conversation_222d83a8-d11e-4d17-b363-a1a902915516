import 'leaflet-draw/dist/leaflet.draw.css';
import 'leaflet.awesome-markers/dist/leaflet.awesome-markers.css';
import 'leaflet.awesome-markers/dist/leaflet.awesome-markers.js';
import 'leaflet/dist/leaflet.css';
import 'leaflet-boundary-canvas';
import { observer } from 'mobx-react';
import { LayersControl, MapContainer, TileLayer, WMSTileLayer } from 'react-leaflet';
import MapContent from './MapContent';

const { Overlay } = LayersControl;
const WMS_API_URL = window._env_.WMS_API_URL;
const WMS_LAYER = window._env_.WMS_LAYER;

const ObraMapContainer = observer(({ ente, store }) => {
  return (
    <div>
      <MapContainer zoom={3} preferCanvas={true} style={{ height: '90vh', width: '110wh', zIndex: 0 }}>
        <TileLayer
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
          url="https://tile.openstreetmap.org/{z}/{x}/{y}.png"
        />
        <LayersControl collapsed position="topleft">
          <Overlay name="Satélite" key="Satélite" checked>
            <WMSTileLayer layers={WMS_LAYER} url={WMS_API_URL} transparent format="image/png" version="1.1.1" />
          </Overlay>
        </LayersControl>
        <MapContent ente={ente} store={store} />
      </MapContainer>
    </div>
  );
});

export default ObraMapContainer;
