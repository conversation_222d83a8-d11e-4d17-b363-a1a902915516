import AdvancedSearch from 'fc/components/AdvancedSearch';
import SearchOperators from 'fc/components/AdvancedSearch/SearchOperators';
import AppBreadCrumb from 'fc/components/AppBreadCrumb';
import CardList from 'fc/components/CardList';
import FcButton from 'fc/components/FcButton';
import PermissionProxy from 'fc/components/PermissionProxy';
import GenericIndexPage from 'fc/pages/GenericIndexPage';
import AppStore from 'fc/stores/AppStore';
import { getValueByKey, getValueElipsis, getValueMoney } from 'fc/utils/utils';
import { observer } from 'mobx-react';
import { ProgressSpinner } from 'primereact/progressspinner';
import { Sidebar } from 'primereact/sidebar';
import '~/assets/leaflet.mask';
import AccessPermission from '~/constants/AccessPermission';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import MedicaoFormStore from '~/stores/geoObras/acompanhamento/medicao/formStore';
import MedicaoIndexStore from '~/stores/geoObras/acompanhamento/medicao/indexStore';
import MapaObraIndexStore from '~/stores/geoObras/mapa/indexStore';
import ObraFormStore from '~/stores/geoObras/obra/formStore';
import ObraIndexStore from '~/stores/geoObras/obra/indexStore';
import ObraDetalhe from './ObraDetalhe';
import ObraMapContainer from './ObraMapContainer';

@observer
class MapaObrasIndexPage extends GenericIndexPage {
  constructor(props) {
    super(props, AccessPermission.geoObras.obra);
    this.store = new ObraIndexStore();
    this.obraFormStore = new ObraFormStore();
    this.mapaStore = new MapaObraIndexStore();
    this.medicaoIndexStore = new MedicaoIndexStore();
    this.medicaoFormStore = new MedicaoFormStore();

    this.state = {
      showSideBarPanel: true,
      showSideBarObraDetalhe: false,
    };

    const filtro = {
      page: { index: 1, size: 999 },
      sort: {
        by: 'id',
        order: 'desc',
      },
      andParameters: [
        {
          field: 'entidade',
          operator: SearchOperators.EQUAL_TO.value,
          value: AppStore.getContextEntity()?.id,
        },
      ],
    };

    this._showSideBarPanel = this.showSideBarPanel.bind(this);
    this.mapaStore.load(filtro);
  }

  showSideBarPanel() {
    if (this.mapaStore?.geoSelected) {
      this.setState({
        showSideBarPanel: false,
      });
      this.mapaStore.setGeoSelected('');
    } else {
      this.setState({
        showSideBarPanel: !this.state.showSideBarPanel,
      });
    }
  }

  showSideBarObraDetalhe() {
    this.setState({
      showSideBarObraDetalhe: !this.state.showSideBarObraDetalhe,
    });
  }

  getCardEllipsisOptions(obra) {
    const items = [];
    items.push({
      label: 'Detalhes',
      icon: 'pi pi-eye',
      command: () => {
        this.mapaStore.setGeoSelected(obra.id);
        this.showSideBarObraDetalhe();
      },
    });
    return items;
  }

  renderObraListInstance() {
    const fields = [
      {
        label: 'Número',
        field: 'numero',
        value: 'title',
        body: ({ numero, fase }) => `${numero} - ${getValueByKey(fase, DadosEstaticosService.getFaseObra())}`,
      },
      {
        label: 'Endereço',
        field: 'endereco',
        value: 'subtitle',
      },
      {
        label: 'Descrição',
        field: 'descricao',
        value: 'mainContent',
        body: ({ descricao }) => getValueElipsis(descricao, 50),
      },
      {
        field: 'status',
        label: 'Status',
        value: 'iconLabel',
        color: '#38AAAD',
        icon: 'pi pi-history',
        body: ({ status }) => getValueByKey(status, DadosEstaticosService.getStatusObra()),
      },
      {
        field: 'tipoObjeto',
        label: 'Tipo Objeto',
        value: 'iconLabel',
        color: '#d080fa',
        icon: 'pi pi-building',
        body: ({ tipoObjeto }) => getValueByKey(tipoObjeto, DadosEstaticosService.getObjetoObra(), 'type', 'label'),
      },
      {
        field: 'valor',
        label: 'Valor',
        value: 'iconLabel',
        color: '#46ad38',
        icon: 'pi pi-money-bill',
        body: ({ valor }) => getValueMoney(valor),
      },
      {
        label: 'Ações',
        value: 'ellipsisItems',
        body: (obra) => this.getCardEllipsisOptions(obra),
      },
      {
        field: 'subtipoObjeto',
        label: 'Subtipo Objeto',
        value: 'iconLabel',
        color: '#ad3844',
        icon: 'pi pi-tag',
        body: ({ tipoObjeto, subtipoObjeto }) =>
          getValueByKey(
            subtipoObjeto,
            getValueByKey(tipoObjeto, DadosEstaticosService.getObjetoObra(), 'type', 'subtypes'),
            'type',
            'label'
          ),
      },
    ];
    return (
      <div>
        <CardList fields={fields} store={this.store} />
      </div>
    );
  }

  renderSideBarObras() {
    const { store, state } = this;
    const { showSideBarPanel } = state;
    return (
      <Sidebar
        position="right"
        dismissable={false}
        modal={false}
        style={{
          width: '530px',
          display: 'flex',
          flexDirection: 'column',
          paddingTop: '1rem',
        }}
        visible={showSideBarPanel}
      >
        <div>
          <div className="obras-list-advanced-search">
            <AdvancedSearch
              searchParams={store.getAdvancedSearchParams()}
              store={store}
              searchFields={['nome']}
              filterSuggest={store.getFilterSuggest()}
              alwaysDrawer
            />
          </div>
          <div> {this.renderObraListInstance()}</div>
        </div>
      </Sidebar>
    );
  }

  getGoogleStreetViewUrl() {
    let url = '#';
    const idObra = this.mapaStore.geoSelected;
    if (idObra) {
      const obra = this.mapaStore.listKey?.find((o) => o.obra.id == idObra && o.fase === 'INICIAL');
      if (obra) {
        const point = [...obra.startPoint.coordinates];
        url = `http://www.google.com/maps/@?api=1&map_action=pano&viewpoint=${point.reverse().join(',')}`;
      }
    }
    return url;
  }

  render() {
    const { showSideBarPanel } = this.state;
    const breacrumbItems = [{ label: 'Mapa de Obras' }];
    const entidade = AppStore.getContextEntity();
    const geoSelected = this.mapaStore?.geoSelected;
    let content = (
      <div>
        <AppBreadCrumb items={breacrumbItems} />

        {this.mapaStore?.loading ? (
          <div className="p-d-inline p-d-flex align-items-center">
            <ProgressSpinner />
          </div>
        ) : (
          <>
            <FcButton
              icon={showSideBarPanel || geoSelected ? 'pi pi-chevron-right' : 'pi pi-chevron-left'}
              className={' p-button-lg'}
              style={{
                width: '50px',
                height: '50px',
                zIndex: 1,
                position: 'absolute',
                right: 0,
                marginTop: '1px',
                marginRight: showSideBarPanel || geoSelected ? '531px' : '0px',
                transitionDuration: '.5s',
                transitionProperty: 'margin-right',
              }}
              onClick={() => this.showSideBarPanel()}
            />
            {this.renderSideBarObras()}
            <ObraDetalhe
              idObra={this.mapaStore.geoSelected}
              obraStore={this.obraFormStore}
              googleStreetViewUrl={this.getGoogleStreetViewUrl()}
              medicaoIndexStore={this.medicaoIndexStore}
              medicaoFormStore={this.medicaoFormStore}
              onHide={() => this.mapaStore.setGeoSelected('')}
            />
            <ObraMapContainer ente={entidade?.ente} store={this.mapaStore} />
          </>
        )}
      </div>
    );

    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        {content}
      </PermissionProxy>
    );
  }
}

MapaObrasIndexPage.displayName = 'MapaObrasIndexPage';

export default MapaObrasIndexPage;
