import AdvancedSearch from 'fc/components/AdvancedSearch';
import AppBreadCrumb from 'fc/components/AppBreadCrumb';
import IndexDataTable from 'fc/components/IndexDataTable';
import PermissionProxy from 'fc/components/PermissionProxy';
import GenericIndexPage from 'fc/pages/GenericIndexPage';
import { getValueElipsis, getValueMoney } from 'fc/utils/utils';
import { observer } from 'mobx-react';
import { ConfirmDialog } from 'primereact/confirmdialog';
import { Dialog } from 'primereact/dialog';
import AccessPermission from '~/constants/AccessPermission';
import AcompanhamentoIndexStore from '~/stores/geoObras/acompanhamento/indexStore';
import CicloObra from './cicloObra';
import DiarioObraIndexPage from './diarioObra';
import BoletimObraIndexPage from './boletimObra';
import FormEntrega from './formEntrega/form';
import FormFim from './formInicioFim/formFim';
import FormInicio from './formInicioFim/formInicio';
import FormInterrupcao from './formInterrupcao/form';
import FormMedicao from './formMedicao/form';
import Opcoes from './opcoes';

@observer
class AcompanhamentoIndexPage extends GenericIndexPage {
  constructor(props) {
    super(props, AccessPermission.geoObras.acompanhamento);
    this.store = new AcompanhamentoIndexStore();

    this.state = {
      inicioDialogVisible: false,
      medicoesDialogVisible: false,
      fimDialogVisible: false,
      entregaDialogVisible: false,
      interrupcaoDialogVisible: false,
      confirmDialogParalisacaoVisible: false,
      diarioDialogVisible: false,
      boletimDialogVisible: false,
    };
  }

  getOpcoes = (obra) => {
    const { status, fase, percentualConclusao } = obra;
    const itens = [];
    if (fase === 'CADASTRAL' && status === 'NAO_INICIADA') {
      itens.push({
        label: 'Iniciar Obra',
        icon: 'pi pi-arrow-up-right',
        command: () => this.toggleInicioDialog(obra),
      });
    } else if (['MEDICAO', 'INICIAL'].includes(fase) && status === 'EM_ANDAMENTO') {
      if (percentualConclusao < 100) {
        itens.push({
          label: 'Adicionar Medição',
          icon: 'pi pi-plus',
          command: () => this.toggleMedicoesDialog(obra),
        });
      }
      itens.push({
        label: 'Diários de Obras',
        icon: 'pi pi-calendar-plus',
        command: () => this.toggleDiariosDialog(obra),
      });
      itens.push({
        label: 'Boletins de Obras',
        icon: 'pi pi-copy',
        command: () => this.toggleBoletimDialog(obra),
      });
      if (percentualConclusao > 0 && 'MEDICAO' === fase && status === 'EM_ANDAMENTO') {
        itens.push({
          label: 'Finalizar Obra',
          icon: 'pi pi-check',
          command: () => this.toggleFimDialog(obra),
        });
      }
    } else if (fase === 'FINALIZACAO') {
      itens.push({
        label: 'Entregar Obra',
        icon: 'pi pi-briefcase',
        command: () => this.toggleEntregaDialog(obra),
      });
    }

    if (fase === 'INTERRUPCAO') {
      itens.push({
        label: 'Visualizar Interrumpção',
        icon: 'pi pi-eye',
        command: () => this.toggleInterrupcaoDialog(obra),
      });
    } else {
      if (status === 'PARALISADA') {
        itens.push({
          label: 'Continuar Obra',
          icon: 'pi pi-play',
          command: () => this.toggleParalisacaoConfirmDialog(obra),
        });
      } else {
        itens.push({
          label: 'Paralisar Obra',
          icon: 'pi pi-pause',
          command: () => this.toggleParalisacaoConfirmDialog(obra, false),
        });
      }
      itens.push({
        label: 'Interromper Obra',
        icon: 'pi pi-ban',
        command: () => this.toggleInterrupcaoDialog(obra),
      });
    }

    return itens;
  };

  toggleMedicoesDialog(obra) {
    this.store.storeMedicao.initialize(null, obra, () => {
      this.setState({ medicoesDialogVisible: !this.state.medicoesDialogVisible });
    });
  }

  toggleInicioDialog({ id }) {
    this.store.storeObraInicio.initializeArquivos(id, () =>
      this.store.storeObraInicio.initialize(id, {}, () => {
        this.setState({ inicioDialogVisible: !this.state.inicioDialogVisible });
      })
    );
  }

  toggleFimDialog(obra) {
    this.store.storeObraFim.initializeArquivos(obra.id, () =>
      this.store.storeObraFim.initialize(obra.id, obra, () => {
        this.setState({ fimDialogVisible: !this.state.fimDialogVisible });
      })
    );
  }

  toggleEntregaDialog(obra) {
    this.store.storeObraEntrega.initializeArquivos(obra.id, () =>
      this.store.storeObraEntrega.initialize(obra.id, obra, () => {
        this.setState({ entregaDialogVisible: !this.state.entregaDialogVisible });
      })
    );
  }

  toggleInterrupcaoDialog({ id }) {
    this.store.storeObraInterrupcao.initializeArquivos(id, () =>
      this.store.storeObraInterrupcao.initialize(id, {}, () => {
        this.setState({ interrupcaoDialogVisible: !this.state.interrupcaoDialogVisible });
      })
    );
  }

  toggleParalisacaoConfirmDialog({ id }) {
    this.store.storeObra.initialize(id, {}, () => {
      this.setState({
        confirmDialogParalisacaoVisible: !this.state.confirmDialogParalisacaoVisible,
      });
    });
  }

  toggleDiariosDialog({ id }) {
    this.store.storeObra.initialize(id, {}, () => {
      this.setState({
        diarioDialogVisible: !this.state.diarioDialogVisible,
      });
    });
  }

  toggleBoletimDialog({ id }) {
    this.store.storeObra.initialize(id, {}, () => {
      this.setState({
        boletimDialogVisible: !this.state.boletimDialogVisible,
      });
    });
  }

  onSuccessDialogParalisacao() {
    this.setState({ confirmDialogParalisacaoVisible: false }, () => this.store.load());
  }

  onSuccessDialogMedicao() {
    this.setState({ medicoesDialogVisible: false }, () => this.store.load());
  }

  onSuccessDialogInicio() {
    this.setState({ inicioDialogVisible: false }, () => this.store.load());
  }

  onSuccessDialogFim() {
    this.setState({ fimDialogVisible: false }, () => this.store.load());
  }

  onSuccessDialogEntrega() {
    this.setState({ entregaDialogVisible: false }, () => this.store.load());
  }

  onSuccessDialogInterrupcao() {
    this.setState({ interrupcaoDialogVisible: false }, () => this.store.load());
  }

  renderDialogInicio() {
    const onHide = () => this.setState({ inicioDialogVisible: false });
    return (
      <Dialog
        header="Cadastro da Fase Inicial"
        visible={this.state.inicioDialogVisible}
        style={{ width: '50vw' }}
        onHide={onHide}
      >
        <FormInicio
          onSuccess={() => this.onSuccessDialogInicio()}
          onCancel={onHide}
          store={this.store.storeObraInicio}
        />
      </Dialog>
    );
  }

  renderDialogMedicao() {
    const onHide = () => this.setState({ medicoesDialogVisible: false });
    return (
      <Dialog
        header="Adicionar Medição"
        visible={this.state.medicoesDialogVisible}
        style={{ width: '50vw' }}
        onHide={onHide}
      >
        <FormMedicao
          store={this.store.storeMedicao}
          onSuccess={() => this.onSuccessDialogMedicao()}
          onCancel={onHide}
        />
      </Dialog>
    );
  }

  renderDialogFim() {
    const onHide = () => this.setState({ fimDialogVisible: false });
    return (
      <Dialog
        header="Cadastro da Fase Final"
        visible={this.state.fimDialogVisible}
        style={{ width: '50vw' }}
        onHide={onHide}
      >
        <FormFim onSuccess={() => this.onSuccessDialogFim()} onCancel={onHide} store={this.store.storeObraFim} />
      </Dialog>
    );
  }

  renderDialogEntrega() {
    const onHide = () => this.setState({ entregaDialogVisible: false });
    return (
      <Dialog
        header="Cadastro de Entrega da Obra"
        visible={this.state.entregaDialogVisible}
        style={{ width: '50vw' }}
        onHide={onHide}
      >
        <FormEntrega
          onSuccess={() => this.onSuccessDialogEntrega()}
          onCancel={onHide}
          store={this.store.storeObraEntrega}
        />
      </Dialog>
    );
  }

  renderDialogInterrupcao() {
    const onHide = () => this.setState({ interrupcaoDialogVisible: false });
    return (
      <Dialog
        header="Interrupção da Obra"
        visible={this.state.interrupcaoDialogVisible}
        style={{ width: '50vw' }}
        onHide={onHide}
      >
        <FormInterrupcao
          onSuccess={() => this.onSuccessDialogInterrupcao()}
          onCancel={onHide}
          store={this.store.storeObraInterrupcao}
        />
      </Dialog>
    );
  }

  renderConfirmDialog() {
    let content = <></>;
    if (this.store?.storeObra?.object?.id) {
      const { numero, status } = this.store.storeObra.object;
      const paralisar = status !== 'PARALISADA';
      content = (
        <ConfirmDialog
          blockScroll
          visible={this.state.confirmDialogParalisacaoVisible}
          message={`Deseja ${paralisar ? 'paralisar' : 'continuar'} a obra de número ${numero}?`}
          header={`Confirmar ${paralisar ? 'paralisação' : 'continuação'}`}
          acceptClassName="p-button-danger"
          accept={() => this.store.storeObra.confirmParalisacao(() => this.onSuccessDialogParalisacao())}
          onHide={() => this.setState({ confirmDialogParalisacaoVisible: false })}
        />
      );
    }
    return content;
  }

  renderDiarioDialog() {
    let content = <></>;
    const onHide = () => this.setState({ diarioDialogVisible: false });
    if (this.store?.storeObra?.object?.id) {
      content = (
        <Dialog
          header="Diários da Obras"
          visible={this.state.diarioDialogVisible}
          style={{ width: '50vw' }}
          onHide={onHide}
        >
          <DiarioObraIndexPage obraId={this.store?.storeObra?.object?.id} />
        </Dialog>
      );
    }
    return content;
  }

  renderBoletimDialog() {
    let content = <></>;
    const onHide = () => this.setState({ boletimDialogVisible: false });
    if (this.store?.storeObra?.object?.id) {
      content = (
        <Dialog
          header="Boletins de Obras"
          visible={this.state.boletimDialogVisible}
          style={{ width: '50vw' }}
          onHide={onHide}
        >
          <BoletimObraIndexPage obraId={this.store?.storeObra?.object?.id} />
        </Dialog>
      );
    }
    return content;
  }

  getFaseAnterior(obra) {
    let faseAnterior = 'ENTREGA';
    const { dataInicio, dataFim, ultimaMedicao, dataConclusao } = obra;
    if (!dataInicio) {
      faseAnterior = 'CADASTRAL';
    } else if (!ultimaMedicao) {
      faseAnterior = 'INICIAL';
    } else if (!dataFim) {
      faseAnterior = 'MEDICAO';
    } else if (!dataConclusao) {
      faseAnterior = 'FINALIZACAO';
    }
    return faseAnterior;
  }

  render() {
    const columns = [
      {
        field: 'numero',
        header: 'N° da Obra',
        sortable: true,
        style: { width: '10%' },
      },
      {
        field: 'descricao',
        header: 'Descrição',
        style: { width: '20%' },
        body: ({ descricao }) => getValueElipsis(descricao, 100),
      },
      {
        field: 'valor',
        header: 'Valor',
        sortable: true,
        body: ({ valor }) => getValueMoney(valor),
        style: { width: '15%' },
      },
      {
        field: 'ciclo',
        header: 'Ciclo de Vida',
        style: { width: '45%' },
        body: (obra) => (
          <CicloObra faseAtual={obra.fase} faseAnterior={this.getFaseAnterior(obra)} status={obra.status} />
        ),
      },
      {
        style: { width: '10px' },
        body: (obra) => {
          return (
            <div className="actions p-d-flex p-jc-end">
              <Opcoes itens={this.getOpcoes(obra)} />
            </div>
          );
        },
      },
    ];

    const { listKey, loading } = this.store;
    const { getDefaultTableProps } = this;

    const breacrumbItems = [{ label: 'Acompanhamento' }];

    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        <AppBreadCrumb items={breacrumbItems} />
        <div className="card page index-table">
          <AdvancedSearch
            searchParams={this.store.getAdvancedSearchParams()}
            store={this.store}
            searchFields={['numero', 'descricao']}
            filterSuggest={this.store.getFilterSuggest()}
          />
          <IndexDataTable
            setColumnsToExport={this.setColumnsToExport}
            columns={columns}
            value={listKey}
            loading={loading}
            {...getDefaultTableProps()}
          />
          {this.store.isConfirmDialogVisible && this.confirmRemove(this.state.idRemove)}
          {this.renderDialogInicio()}
          {this.renderDialogMedicao()}
          {this.renderDialogFim()}
          {this.renderDialogEntrega()}
          {this.renderDialogInterrupcao()}
          {this.renderConfirmDialog()}
          {this.renderDiarioDialog()}
          {this.renderBoletimDialog()}
        </div>
      </PermissionProxy>
    );
  }
}

AcompanhamentoIndexPage.displayName = 'AcompanhamentoIndexPage';

export default AcompanhamentoIndexPage;
