import FcButton from 'fc/components/FcButton';
import IndexDataTable from 'fc/components/IndexDataTable';
import PermissionProxy from 'fc/components/PermissionProxy';
import GenericIndexPage from 'fc/pages/GenericIndexPage';
import { DATE_FORMAT, DATE_PARSE_FORMAT } from 'fc/utils/date';
import { getValueDate, getValueElipsis } from 'fc/utils/utils';
import { observer } from 'mobx-react';
import { PrimeIcons } from 'primereact/api';
import { ConfirmDialog } from 'primereact/confirmdialog';
import { Dialog } from 'primereact/dialog';
import AccessPermission from '~/constants/AccessPermission';
import DiarioObraIndexStore from '~/stores/geoObras/diario/indexStore';
import DiarioObraFormPage from './form';

@observer
class DiarioObraIndexPage extends GenericIndexPage {
  constructor(props) {
    super(props, AccessPermission.geoObras.obra);
    this.store = new DiarioObraIndexStore(props.obraId);
    this.state = {
      idRemove: null,
      formDialogVisible: false,
      diarioObraId: '',
      readOnly: false,
    };
    this.store.load();
  }

  renderFormDialog() {
    const onHide = () => this.store.load(() => this.setState({ formDialogVisible: false }));

    const isEdit = !!this.state.diarioObraId;
    return (
      <Dialog
        visible={this.state.formDialogVisible}
        header={`${isEdit ? 'Editar' : 'Adicionar'} Diário`}
        style={{ width: '50vw' }}
        onHide={onHide}
      >
        <DiarioObraFormPage
          onHide={onHide}
          obraId={this.props.obraId}
          id={this.state.diarioObraId}
          readOnly={this.state.readOnly}
        />
      </Dialog>
    );
  }

  confirmRemove(id) {
    const { deleteRow } = this.store;

    return (
      <ConfirmDialog
        blockScroll
        visible={this.store.isConfirmDialogVisible}
        message="Você realmente deseja excluir o registro selecionado?"
        header="Excluir registro"
        icon="pi pi-exclamation-triangle"
        acceptClassName="p-button-danger"
        accept={() => deleteRow(id, () => this.store.load())}
        onHide={() => this.store.toggleShowConfirmDialog()}
      />
    );
  }

  enableForm({ formDialogVisible = false, diarioObraId = '', readOnly = false }) {
    this.setState({ formDialogVisible, diarioObraId, readOnly });
  }

  render() {
    const columns = [
      {
        field: 'dataDiario',
        header: 'Data do Diário',
        style: { width: '135px' },
        body: ({ dataDiario }) => getValueDate(dataDiario, DATE_FORMAT, DATE_PARSE_FORMAT),
      },
      {
        field: 'observacoes',
        header: 'Observações',
        body: ({ observacoes }) => getValueElipsis(observacoes, 50),
      },
      {
        style: { width: '110px' },
        body: (rowData) => {
          return (
            <div className="actions p-d-flex p-jc-end">
              <PermissionProxy resourcePermissions={this.getReadPermission()}>
                <FcButton
                  icon="pi pi-eye"
                  className="p-button-sm p-mr-2"
                  onClick={() => this.enableForm({ formDialogVisible: true, diarioObraId: rowData.id, readOnly: true })}
                />
              </PermissionProxy>
              <PermissionProxy resourcePermissions={this.getWritePermission()}>
                <FcButton
                  icon="pi pi-pencil"
                  className="p-button-sm p-button-success p-mr-2"
                  onClick={() => this.enableForm({ formDialogVisible: true, diarioObraId: rowData.id })}
                />
              </PermissionProxy>
              <PermissionProxy resourcePermissions={this.getWritePermission()}>
                <FcButton
                  icon="pi pi-trash"
                  className="p-button-sm p-button-danger"
                  onClick={() => {
                    this.setState({ idRemove: rowData.id });
                    this.store.toggleShowConfirmDialog();
                  }}
                />
              </PermissionProxy>
            </div>
          );
        },
      },
    ];

    const header = (
      <div className="table-header">
        <PermissionProxy resourcePermissions={this.getWritePermission()}>
          <FcButton
            className="p-button"
            label="Novo"
            style={{ marginBottom: '5px', marginRight: '5px' }}
            icon={PrimeIcons.PLUS}
            onClick={() => this.enableForm({ formDialogVisible: true })}
          />
        </PermissionProxy>
      </div>
    );

    const { listKey, loading } = this.store;
    const { getDefaultTableProps } = this;

    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        <IndexDataTable
          columns={columns}
          value={listKey}
          header={header}
          loading={loading}
          disableColumnToggle
          {...getDefaultTableProps()}
        />
        {this.store.isConfirmDialogVisible && this.confirmRemove(this.state.idRemove)}
        {this.renderFormDialog()}
      </PermissionProxy>
    );
  }
}

DiarioObraIndexPage.displayName = 'DiarioObraIndexPage';

export default DiarioObraIndexPage;
