import FcButton from 'fc/components/FcButton';
import FcDropdown from 'fc/components/FcDropdown';
import FcInputTextarea from 'fc/components/FcInputTextarea';
import FormField from 'fc/components/FormField';
import { showNotification } from 'fc/utils/utils';
import { observer } from 'mobx-react';
import { Message } from 'primereact/message';
import PropTypes from 'prop-types';
import { useEffect, useMemo, useState } from 'react';
import ObraUpload from '~/pages/geoObras/acompanhamento/obraUpload';
import DadosEstaticosService from '~/services/DadosEstaticosService';

const FormInterrupcao = observer((props) => {
  const { store, onCancel, onSuccess } = props;
  const [submitted, setSubmitted] = useState(false);

  const readOnly = useMemo(() => store.object.fase === 'INTERRUPCAO', [store.object]);

  useEffect(() => {
    if (submitted) execution();
  }, [submitted]);

  const execution = () => {
    if (!store.rules.hasError) {
      store.validaArquivos(() => store.interromperObra(onSuccess));
    } else {
      showNotification('error', null, 'Verifique os campos do formulário!');
    }
  };

  const submitFormData = (e) => {
    e?.preventDefault && e.preventDefault();
    if (submitted) {
      execution();
    } else {
      setSubmitted(true);
    }
  };

  const renderForm = () => {
    return (
      <div key="dadosBasicosInterrupcao" className="p-fluid p-formgrid p-grid">
        <FormField
          columns={6}
          attribute="motivoInterrupcao"
          label="Motivo da Interrupção da Obra"
          rule={store.getRule('motivoInterrupcao')}
          submitted={submitted}
        >
          <FcDropdown
            inOrder
            id="motivoInterrupcao"
            options={DadosEstaticosService.getMotivoInterrupcaoObra()}
            optionLabel="text"
            optionValue="value"
            value={store.object?.motivoInterrupcao}
            onChange={(e) => store.updateAttribute('motivoInterrupcao', e)}
            placeholder="Selecione um Motivo"
            showClear
            showFilterClear
            filter
            emptyMessage="Nenhum Motivo Encontrado"
            emptyFilterMessage="Nenhum Motivo Encontrado"
            disabled={readOnly}
          />
        </FormField>

        <FormField
          columns={12}
          attribute="descricaoInterrupcao"
          label="Descrição da Interrupção"
          rows={5}
          rule={store.getRule('descricaoInterrupcao')}
          submitted={submitted}
        >
          <div>
            <FcInputTextarea
              rows={5}
              cols={30}
              autoResize={true}
              rules={store.getRule('descricaoInterrupcao')}
              value={store.object.descricaoInterrupcao}
              onChange={(e) => store.updateAttribute('descricaoInterrupcao', e)}
              placeholder="Informe a descrição da interrupção"
              disabled={readOnly}
            />
          </div>
        </FormField>

        <div className="p-grid p-col-12 p-p-0 mt-3">
          <ObraUpload
            chooseLabel="Adicionar Documentos"
            accept=".pdf"
            tipos={store?.fileStore.tipoArquivoEnum?.filter(
              (tipo) => tipo.fase === 'INTERRUPCAO' && tipo.value === 'OUTROS_DOCUMENTOS'
            )}
            onChangeFiles={(arquivos) => store.setArquivos(arquivos)}
            arquivos={store?.arquivos?.filter((arquivo) => arquivo.fase === 'INTERRUPCAO')}
            store={store?.fileStore}
            updateAttributeFile={(key, attribute, value) => store.updateAttributeFile(key, attribute, value)}
            emptyMenssage="Documentos não adicionados!"
            fase={store.fase}
            readOnly={readOnly}
          />
        </div>
      </div>
    );
  };

  const isDisabledInterromper = () => {
    const errorsMessages = {
      message: '',
      disabled: false,
    };
    if (!store.validaDadosBasicos()) {
      errorsMessages.message = 'É necessário preencher todos os campos obrigatórios antes de interromper a obra.';
    } else {
      errorsMessages.disabled = false;
    }
    return errorsMessages;
  };

  const renderActionButtons = () => {
    const disabledEntregar = isDisabledInterromper();
    return (
      <div className="flex">
        {!readOnly && (
          <span className="p-ml-auto">
            {disabledEntregar.disabled && (
              <Message className="p-ml-auto mr-2 mt-2" severity="warn" text={disabledEntregar.message} />
            )}
            <FcButton
              type="button"
              label="Cancelar"
              className="p-button-secondary mr-2 mt-2"
              onClick={() => onCancel && onCancel()}
            />
            <FcButton
              className="mt-2"
              type="button"
              disabled={!store.validaDadosBasicos() || readOnly}
              label="Interromper"
              loading={store.loading}
              onClick={() => submitFormData()}
            />
          </span>
        )}
      </div>
    );
  };

  return (
    <form onSubmit={submitFormData}>
      {renderForm()}
      {renderActionButtons()}
    </form>
  );
});

FormInterrupcao.propTypes = {
  store: PropTypes.any,
  onCancel: PropTypes.func,
  onSuccess: PropTypes.func,
};

export default FormInterrupcao;
