import moment from 'moment';
import { ConfirmDialog } from 'primereact/confirmdialog';
import { FileUpload } from 'primereact/fileupload';
import { Message } from 'primereact/message';
import PropTypes from 'prop-types';
import { useState } from 'react';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import ItemObraUpload from './itemObraUpload';
import './style.scss';

const ObraUpload = (props) => {
  const {
    chooseLabel,
    max = Number.MAX_SAFE_INTEGER,
    accept,
    tipos,
    onChangeFiles,
    store,
    updateAttributeFile,
    emptyMenssage,
    fase,
    arquivos,
    readOnly,
  } = props;
  const [visibleDialogRemove, setVisibleDialogRemove] = useState(false);
  const [arquivoToRemove, setArquivoToRemove] = useState(null);

  const _onChangeFiles = (fileList) => {
    onChangeFiles(
      fileList?.map((file) => {
        if (!file.fase) {
          file.fase = fase;
        }
        return file;
      })
    );
  };

  const _onUploadFile = (arquivo) => {
    const callback = (fileList) => {
      _onChangeFiles(fileList);
    };

    store.uploadFile(
      {
        arquivo: arquivo.file,
        descricao: arquivo.descricao,
        tipoArquivo: arquivo.tipo,
        key: arquivo.key,
        fase: arquivo.fase,
      },
      callback
    );
  };

  const _uploadHandler = (event) => {
    Object.keys(event.files)
      .slice(0, max)
      .forEach((key) => {
        const arq = {
          key: moment().toISOString() + key,
          arquivo: event.files[key]?.name,
          dataEnvio: moment(),
          file: event.files[key],
          tipo: tipos?.length === 1 ? tipos[0].value : '',
          descricao: '',
          fase: fase,
        };

        _onUploadFile(arq);
      });

    event.options?.clear();
  };

  const _removeFile = (files) => {
    let newArquivos = [...files];
    newArquivos = newArquivos.filter((f) =>
      arquivoToRemove.key ? f.key !== arquivoToRemove.key : f.idArquivo !== arquivoToRemove.idArquivo
    );
    _onChangeFiles(newArquivos);
    setArquivoToRemove(null);
  };

  const _renderRemoveFileDialog = () => {
    const message = `${
      arquivoToRemove?.idArquivo ? 'Este arquivo está persistido e será removido permanentemente. ' : ''
    }Você reamente deseja remover este arquivo?`;

    return (
      <ConfirmDialog
        visible={visibleDialogRemove}
        message={message}
        header="Excluir Arquivo"
        icon="pi pi-info-circle"
        acceptClassName="p-button-danger"
        accept={() => {
          store.removeFile(arquivoToRemove, (fileList) => _removeFile(fileList));
          setVisibleDialogRemove(false);
        }}
        onHide={() => setVisibleDialogRemove(false)}
      />
    );
  };

  return (
    <div className="p-grid p-col-12 p-m-0">
      <div className="p-col-12 p-m-0">
        {!readOnly && (
          <FileUpload
            name={`file-uploader-${new Date().toString()}`}
            chooseLabel={chooseLabel}
            disabled={!(arquivos?.length < max) || !!readOnly}
            auto
            multiple
            customUpload
            mode="basic"
            uploadHandler={(event) => _uploadHandler(event)}
            accept={accept}
            maxFileSize={30000000}
            emptyTemplate={<p className="m-0">Solte o arquivo para realizar upload.</p>}
          />
        )}
        {arquivos.length > 0 ? (
          arquivos.map((arq, idx) => (
            <div key={`item-upload-${idx}`}>
              <ItemObraUpload
                arquivo={arq}
                tipos={tipos}
                onRemoveFile={() => {
                  setArquivoToRemove(arq);
                  setVisibleDialogRemove(true);
                }}
                updateAttributeFile={updateAttributeFile}
                onDownload={(arq) => store.downloadFile(arq.arquivo)}
                readOnly={readOnly}
              />
            </div>
          ))
        ) : (
          <Message className="mt-2" severity="info" text={emptyMenssage} />
        )}

        {_renderRemoveFileDialog()}
      </div>
    </div>
  );
};

ObraUpload.defaultProps = {
  accept: '.pdf',
  chooseLabel: 'Adicionar',
  max: Number.MAX_SAFE_INTEGER,
  tipos: DadosEstaticosService.getTipoArquivoMedicaoObra(),
  arquivos: [],
  emptyMenssage: 'Nenhum documento adicionado!',
  readOnly: false,
};

ObraUpload.propTypes = {
  accept: PropTypes.string,
  chooseLabel: PropTypes.string,
  max: PropTypes.number,
  tipos: PropTypes.array,
  onChangeFiles: PropTypes.func,
  arquivos: PropTypes.array,
  store: PropTypes.any,
  updateAttributeFile: PropTypes.func,
  emptyMenssage: PropTypes.string,
  fase: PropTypes.string,
  readOnly: PropTypes.bool,
};

export default ObraUpload;
