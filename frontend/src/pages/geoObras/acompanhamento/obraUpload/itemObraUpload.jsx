import FcButton from 'fc/components/FcButton';
import FcDropdown from 'fc/components/FcDropdown';
import { getValueByKey } from 'fc/utils/utils';
import { InputTextarea } from 'primereact/inputtextarea';
import PropTypes from 'prop-types';
import { useEffect, useState } from 'react';
import './style.scss';

const ItemObraUpload = ({ arquivo, tipos, onRemoveFile, updateAttributeFile, onDownload, readOnly }) => {
  const [tipo, setTipo] = useState(arquivo.tipo || 0);
  const fileName = arquivo?.arquivo?.nomeOriginal ? arquivo?.arquivo.nomeOriginal : arquivo?.arquivo;
  const [descricao, setDescricao] = useState(arquivo?.descricao ?? '');
  const [editDescricao, setEditDescricao] = useState(false);

  const iconItem = (arq) => {
    const icons = {
      pdf: 'pi pi-file-pdf',
      xlsx: 'pi pi-file-excel',
      jpg: 'pi pi-images',
      png: 'pi pi-images',
      kml: 'pi pi-map',
    };
    const filename = arq?.toLocaleLowerCase()?.split('.');

    const extension = filename[filename.length - 1] || 'pi pi-file';
    return icons[extension];
  };

  useEffect(() => {
    !readOnly && updateAttributeFile && updateAttributeFile(arquivo.key, 'tipo', tipo);
  }, [tipo]);

  useEffect(() => {
    !readOnly && updateAttributeFile && updateAttributeFile(arquivo.key, 'descricao', descricao);
  }, [descricao]);

  return (
    <div className="flex align-items-center flex-wrap mt-3 item-upload">
      <div className="flex align-items-center m-3">
        <icon className={`${iconItem(fileName)} mx-2 fs-xxlarge`}></icon>
        <div className="flex flex-column text-left ml-3">
          <strong>{fileName}</strong>
          {!editDescricao && (
            <small className="pointer long-text" onClick={() => !readOnly && setEditDescricao(true)}>
              {descricao.length > 0 ? descricao : 'Descrição não informada(opcional)'}
            </small>
          )}
          {editDescricao && (
            <InputTextarea
              autoResize
              maxlength={255}
              className="fc-text-area-default"
              disabled={!!readOnly}
              onChange={(e) => {
                setDescricao(e.target.value);
              }}
              value={descricao}
            />
          )}

          <div className="flex flex-wrap align-items-center ">
            <span className="mr-2">Tipo de Documento: </span>
            {!readOnly ? (
              <FcDropdown
                inOrder
                disabled={tipos?.length === 1 || !!readOnly}
                style={{ marginTop: '5px', minWidth: '15rem' }}
                optionLabel="text"
                optionValue="value"
                options={tipos}
                placeholder="Selecione um tipo"
                value={tipo}
                onChange={(e) => {
                  setTipo(e.value);
                }}
              />
            ) : (
              <span>{getValueByKey(tipo, tipos)}</span>
            )}
          </div>
        </div>
      </div>
      <div className="ml-auto mr-2">
        {!readOnly ? (
          <>
            {!editDescricao ? (
              <FcButton
                type="button"
                text
                icon="pi pi-comment"
                className="p-button-text"
                onClick={() => setEditDescricao(true)}
              />
            ) : (
              <FcButton
                type="button"
                text
                icon="pi pi-check"
                className="p-button-text"
                onClick={() => setEditDescricao(false)}
              />
            )}
            <FcButton
              type="button"
              text
              icon="pi pi-download"
              className="p-button-text"
              onClick={() => onDownload(arquivo)}
            />
            <FcButton
              type="button"
              text
              icon="pi pi-trash"
              className="p-button-danger p-button-text"
              onClick={() => onRemoveFile && onRemoveFile(arquivo)}
            />
          </>
        ) : (
          <FcButton
            type="button"
            text
            icon="pi pi-download"
            className="p-button-text"
            onClick={() => onDownload(arquivo)}
          />
        )}
      </div>
    </div>
  );
};

ItemObraUpload.defaultProps = {
  readOnly: false,
};

ItemObraUpload.propTypes = {
  arquivo: PropTypes.object,
  tipos: PropTypes.array,
  onRemoveFile: PropTypes.func,
  updateAttributeFile: PropTypes.func,
  onDownload: PropTypes.func,
  readOnly: PropTypes.bool,
};

export default ItemObraUpload;
