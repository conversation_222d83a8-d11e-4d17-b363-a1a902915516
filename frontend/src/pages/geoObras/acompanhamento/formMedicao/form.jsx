import AsyncDropdown from 'fc/components/AsyncDropdown';
import FcButton from 'fc/components/FcButton';
import FcCalendar from 'fc/components/FcCalendar';
import FormField from 'fc/components/FormField';
import InputMonetary from 'fc/components/InputMonetary';
import { DATE_FORMAT } from 'fc/utils/date';
import { getNumberFractionDigits, showNotification } from 'fc/utils/utils';
import { observer } from 'mobx-react';
import moment from 'moment';
import { Message } from 'primereact/message';
import PropTypes from 'prop-types';
import { useEffect, useState } from 'react';
import ObraUpload from '~/pages/geoObras/acompanhamento/obraUpload';

const FormMedicao = observer((props) => {
  const { store, onCancel, onSuccess } = props;
  const [submitted, setSubmitted] = useState(false);
  const [activeIndexForm, setActiveIndexForm] = useState(0);

  useEffect(() => {
    if (submitted) execution();
  }, [submitted]);

  const execution = () => {
    if (!store.rules.hasError && store.validaTiposArquivos()) {
      store.validaArquivos(() => store.criarMedicao(onSuccess));
    } else {
      showNotification('error', null, 'Verifique os campos do formulário!');
      setActiveIndexForm(0);
    }
  };

  const submitFormData = (e) => {
    e?.preventDefault && e.preventDefault();
    if (submitted) {
      execution();
    } else {
      setSubmitted(true);
    }
  };

  const minDateInicio = () => {
    let minDate = null;
    if (store.obra?.ultimaMedicao) {
      minDate = moment(store.obra.ultimaMedicao);
    } else if (store.obra?.dataInicio) {
      minDate = moment(store.obra.dataInicio);
    }
    return minDate;
  };

  const renderDadosBasicos = () => {
    const minDate = minDateInicio();
    return (
      <div key="dadosBasicos" className="p-fluid p-formgrid p-grid">
        <FormField
          columns={6}
          attribute="dataInicio"
          label="Data Início"
          rule={store.getRule('dataInicio')}
          submitted={submitted}
        >
          <>
            <FcCalendar
              value={store.object.dataInicio ? moment(store.object.dataInicio)._d : null}
              placeholder="Selecione a data inicial"
              onChange={(e) => store.updateAttributeDate('dataInicio', e)}
              maxDate={store.object.dataFim ? moment(store.object.dataFim)._d : null}
              minDate={minDate?._d}
            />
            {store.obra?.ultimaMedicao ? (
              <small>{`A data final da última medição foi ${minDate.format(DATE_FORMAT)}.`}</small>
            ) : (
              store.obra?.dataInicio && <small>{`A data inicial da obra foi ${minDate.format(DATE_FORMAT)}.`}</small>
            )}
          </>
        </FormField>
        <FormField
          columns={6}
          attribute="dataFim"
          label="Data Fim"
          rule={store.getRule('dataFim')}
          submitted={submitted}
        >
          <FcCalendar
            value={store.object.dataFim ? moment(store.object.dataFim)._d : null}
            placeholder="Selecione a data final"
            onChange={(e) => store.updateAttributeDate('dataFim', e)}
            minDate={store.object.dataInicio ? moment(store.object.dataInicio)._d : null}
            disabled={!store.object.dataInicio}
          />
        </FormField>
        <FormField
          columns={6}
          attribute="valor"
          label="Valor (R$) da Medição"
          rule={store.getRule('valor')}
          submitted={submitted}
        >
          <InputMonetary
            onChange={(e) => store.updateAttribute('valor', e)}
            placeholder="Informe o valor da medição"
            value={store.object?.valor}
          />
        </FormField>
        <FormField
          columns={6}
          attribute="percentualConclusao"
          label="Percentual (%) de Conclusão"
          rule={store.getRule('percentualConclusao')}
          submitted={submitted}
        >
          <>
            <InputMonetary
              onChange={(e) => store.updateAttribute('percentualConclusao', e)}
              placeholder="Informe o percetual de conclusao"
              value={store.object?.percentualConclusao ? store.object?.percentualConclusao : 0}
              max={100}
            />
            {store.obra?.percentualConclusao ? (
              <small>
                {`O percentual da última medição foi de ${getNumberFractionDigits(store.obra.percentualConclusao)}%.`}
              </small>
            ) : (
              ''
            )}
          </>
        </FormField>
        <FormField
          columns={6}
          attribute="empenho"
          label="Empenho"
          rule={store.getRule('empenho')}
          submitted={submitted}
        >
          <AsyncDropdown
            id="empenho"
            onChange={(e, v) => {
              store.updateAttribute(e, v);
            }}
            value={store.object.empenho?.id ?? undefined}
            placeholder="Selecione o Empenho"
            store={store.storeEmpenho}
          />
        </FormField>
        <div className="p-grid p-col-12 p-p-0 mt-3">
          <ObraUpload
            chooseLabel="Adicionar Nota Fiscal"
            max={1}
            tipos={store?.fileStore.tipoArquivoEnum.filter((t) => t.value === 'NOTA_FISCAL')}
            onChangeFiles={(arquivos) => store.setArquivoMedicaoList(arquivos)}
            arquivos={store?.arquivoMedicaoList?.filter((f) => f.tipo === 'NOTA_FISCAL')}
            store={store?.fileStore}
            updateAttributeFile={(key, attribute, value) => store.updateAttributeFile(key, attribute, value)}
            emptyMenssage="Nota fiscal não adicionada!"
            fase={store.fase}
          />
        </div>
        <div className="p-grid p-col-12 p-p-0 mt-3">
          <ObraUpload
            chooseLabel="Adicionar Georreferenciamento"
            accept=".kml"
            max={1}
            tipos={store?.fileStore.tipoArquivoEnum.filter((t) => t.value === 'GEORREFERENCIAMENTO')}
            onChangeFiles={(arquivos) => store.setArquivoMedicaoList(arquivos)}
            arquivos={store?.arquivoMedicaoList?.filter((f) => f.tipo === 'GEORREFERENCIAMENTO')}
            store={store?.fileStore}
            updateAttributeFile={(key, attribute, value) => store.updateAttributeFile(key, attribute, value)}
            emptyMenssage="Georreferenciamento não adicionado!"
            fase={store.fase}
          />
        </div>
      </div>
    );
  };

  const renderFotos = () => {
    return (
      <div key="fotos" className="p-fluid p-formgrid p-grid mt-3">
        <div className="p-grid p-col-12 ">
          <ObraUpload
            chooseLabel="Adicionar Fotos"
            accept="image/*"
            tipos={store?.fileStore.tipoArquivoEnum.filter((t) => t.value === 'FOTO')}
            onChangeFiles={(arquivos) => store.setArquivoMedicaoList(arquivos)}
            arquivos={store?.arquivoMedicaoList?.filter((f) => f.tipo === 'FOTO')}
            store={store?.fileStore}
            updateAttributeFile={(key, attribute, value) => store.updateAttributeFile(key, attribute, value)}
            emptyMenssage="Nenhuma imagem adicionada!"
            fase={store.fase}
          />
        </div>
      </div>
    );
  };

  const renderDocumentos = () => {
    return (
      <div key="documentos" className="p-fluid p-formgrid p-grid mt-3">
        <div className="p-grid p-col-12 p-p-0">
          <ObraUpload
            chooseLabel="Adicionar Documentos"
            accept=".pdf, .xlsx"
            tipos={store?.fileStore.tipoArquivoEnum.filter((t) =>
              ['RELATORIO_TECNICO', 'OUTROS_DOCUMENTOS', 'PLANILHA_MEDICAO'].includes(t.value)
            )}
            onChangeFiles={(arquivos) => store.setArquivoMedicaoList(arquivos)}
            arquivos={store?.arquivoMedicaoList?.filter(
              (f) => !['NOTA_FISCAL', 'GEORREFERENCIAMENTO', 'FOTO'].includes(f.tipo)
            )}
            store={store?.fileStore}
            updateAttributeFile={(key, attribute, value) => store.updateAttributeFile(key, attribute, value)}
            fase={store.fase}
          />
        </div>
      </div>
    );
  };

  const onBack = () => {
    setActiveIndexForm(activeIndexForm - 1);
  };

  const onNext = () => {
    setActiveIndexForm(activeIndexForm + 1);
  };

  const isDisabledAvancar = () => {
    const errorsMessages = {
      message: '',
      disabled: true,
    };
    if (activeIndexForm === 0 && !store.validaDadosBasicos()) {
      errorsMessages.message =
        'É necessário preencher todos os campos obrigatórios antes de avançar para a próxima etapa.';
    } else if (activeIndexForm === 1 && !store.validaFotos()) {
      errorsMessages.message = 'É necessário adicionar fotos antes de avançar para a próxima etapa.';
    } else if (activeIndexForm === 2 && !store.validaDocumentos()) {
      errorsMessages.message = 'É necessário adicionar documentos antes de avançar para a próxima etapa.';
    } else {
      errorsMessages.disabled = false;
    }
    return errorsMessages;
  };

  const isDisabledVoltar = () => {
    const errorsMessages = {
      message: '',
      disabled: true,
    };
    if (activeIndexForm == 2 && !store.validaTiposArquivos()) {
      errorsMessages.message = 'É necessário preencher os tipos dos arquivo.';
    } else {
      errorsMessages.disabled = false;
    }
    return errorsMessages;
  };

  const renderActionButtons = () => {
    const disabledAvancar = isDisabledAvancar();
    const disabledVoltar = isDisabledVoltar();
    return (
      <div className="flex">
        <span className="p-ml-auto">
          {disabledAvancar.disabled ? (
            <Message className="p-ml-auto mr-2 mt-2" severity="warn" text={disabledAvancar.message} />
          ) : (
            disabledVoltar.disabled && (
              <Message className="p-ml-auto mr-2 mt-2" severity="warn" text={disabledVoltar.message} />
            )
          )}
          {activeIndexForm === 0 && (
            <FcButton
              type="button"
              label="Cancelar"
              className="p-button-secondary mr-2 mt-2"
              loading={store.loading}
              onClick={() => onCancel && onCancel()}
            />
          )}
          {activeIndexForm > 0 && (
            <FcButton
              className="p-button-outlined mt-2 mr-2"
              disabled={disabledVoltar.disabled}
              type="button"
              label="Voltar"
              onClick={() => onBack()}
            />
          )}
          {activeIndexForm < 2 && (
            <FcButton
              className="mt-2"
              type="button"
              label="Avançar"
              disabled={disabledAvancar.disabled}
              onClick={() => onNext()}
            />
          )}
          {activeIndexForm === 2 && (
            <FcButton
              className="mt-2"
              type="button"
              disabled={!store.validaMedicao()}
              label="Adicionar"
              loading={store.loading}
              onClick={() => submitFormData()}
            />
          )}
        </span>
      </div>
    );
  };

  const forms = [
    {
      label: 'Dados Básicos',
      step: 0,
      body: renderDadosBasicos(),
    },
    {
      label: 'Fotos',
      step: 1,
      body: renderFotos(),
    },
    {
      label: 'Documentos',
      step: 2,
      body: renderDocumentos(),
    },
  ];

  return (
    <form onSubmit={submitFormData}>
      {forms.find((item) => item.step === activeIndexForm).body}
      {renderActionButtons()}
    </form>
  );
});

FormMedicao.propTypes = {
  store: PropTypes.any,
  onCancel: PropTypes.func,
  onSuccess: PropTypes.func,
};

export default FormMedicao;
