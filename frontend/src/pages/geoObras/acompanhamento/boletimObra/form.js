import FcCalendar from 'fc/components/FcCalendar';
import FcInputTextarea from 'fc/components/FcInputTextarea';
import FormField from 'fc/components/FormField';
import PermissionProxy from 'fc/components/PermissionProxy';
import GenericFormPage from 'fc/pages/GenericFormPage';
import { observer } from 'mobx-react';
import PropTypes from 'prop-types';
import AccessPermission from '~/constants/AccessPermission';
import FcButton from 'fc/components/FcButton';
import MultipleFileUploader from 'fc/components/MultipleFileUploader';
import AppStore from 'fc/stores/AppStore';
import { showNotification } from 'fc/utils/utils';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import BoletimFormStore from '~/stores/geoObras/boletim/formStore';

@observer
class BoletimObraFormPage extends GenericFormPage {
  constructor(props) {
    super(props, '', AccessPermission.geoObras.obra);

    this.store = new BoletimFormStore(props.obraId);
  }

  _onHide() {
    const { onHide } = this.props;
    onHide && onHide();
  }

  componentDidMount() {
    const { id } = this.props;
    this.store.initialize(id, {}, () => {
      if (id) {
        this.store.recuperarArquivos(id);
      }
    });
  }

  submitFormData(e) {
    e?.preventDefault && e.preventDefault();
    const execution = () => {
      if (!this.store.rules.hasError) {
        this.store.validaArquivos(() => this.store.save(() => this._onHide(), this.props.action));
      } else {
        showNotification('error', null, 'Verifique os campos do formulário!');
      }
    };

    if (this.state.submitted) {
      execution();
    } else {
      this.setState({ submitted: true }, execution);
    }
  }

  renderActionButtons() {
    const hasWritePermission = !this.getWritePermission() || AppStore.hasPermission(this.getWritePermission());
    return (
      <div className="p-mt-10">
        <div className="p-mt-2">
          <span className="p-mr-1 p-d-inline p-d-flex align-items-center">
            <FcButton
              label="Cancelar"
              type="button"
              className="p-ml-auto p-button-secondary p-mr-2"
              onClick={() => this._onHide()}
              loading={this.store.loading}
            />
            {hasWritePermission && (
              <FcButton
                label="Salvar"
                type="submit"
                loading={this.store.loading}
                disabled={this.store.rules.hasError || !this.store.arquivosValidos}
              />
            )}
          </span>
        </div>
      </div>
    );
  }

  render() {
    const { submitted } = this.state;
    const { readOnly } = this.props;
    const { updateAttribute, getRule, updateAttributeDate } = this.store;
    const { submitFormData } = this;

    let content;

    if (this.store.object) {
      content = (
        <div className="page form-action-buttons">
          <form onSubmit={submitFormData}>
            <div className="p-fluid p-formgrid p-grid">
              <FormField
                columns={6}
                attribute="dataBoletim"
                label="Data do Boletim"
                rule={getRule('dataBoletim')}
                submitted={submitted}
              >
                <>
                  <FcCalendar
                    value={this.getDateAttributeValue(this.store.object.dataBoletim)}
                    placeholder="Selecione a data do boletim"
                    onChange={(e) => updateAttributeDate('dataBoletim', e)}
                    disabled={readOnly}
                  />
                </>
              </FormField>

              <FormField
                attribute="observacoes"
                label="Observações"
                rule={getRule('observacoes')}
                submitted={submitted}
              >
                <FcInputTextarea
                  rows={5}
                  cols={30}
                  value={this.store.object.observacoes}
                  onChange={(e) => updateAttribute('observacoes', e)}
                  placeholder="Informe observações do boletim"
                  disabled={readOnly}
                />
              </FormField>

              <FormField columns={12} attribute="arquivos" label="Arquivos" submitted={submitted}>
                <MultipleFileUploader
                  store={this.store.fileStore}
                  fileTypes={DadosEstaticosService.getTipoArquivoBoletimObra()}
                  onChangeFiles={(files) => this.store.setArquivoList(files)}
                  downloadOnly={readOnly}
                />
              </FormField>
            </div>
            {!readOnly && this.renderActionButtons()}
          </form>
        </div>
      );
    } else {
      content = (
        <div className="card page">
          <i
            className="pi pi-spin pi-spinner"
            style={{
              marginTop: '20px',
              marginLeft: 'calc(50% - 20px)',
              fontSize: '2em',
            }}
          ></i>
        </div>
      );
    }
    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        {content}
      </PermissionProxy>
    );
  }
}

BoletimObraFormPage.propTypes = {
  id: PropTypes.number,
  obraId: PropTypes.number,
  onHide: PropTypes.func,
  readOnly: PropTypes.bool,
};

export default BoletimObraFormPage;
