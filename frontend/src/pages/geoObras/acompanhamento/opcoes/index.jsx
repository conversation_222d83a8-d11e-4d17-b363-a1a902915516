import FcButton from 'fc/components/FcButton';
import { Menu } from 'primereact/menu';
import PropTypes from 'prop-types';
import { useRef } from 'react';
import AccessPermission from '~/constants/AccessPermission';
import PermissionProxy from 'fc/components/PermissionProxy';

const Opcoes = ({ itens }) => {
  const menuRef = useRef(null);
  return (
    <>
      <Menu model={itens} popup ref={menuRef} id="popup_menu_opcoes" />
      <PermissionProxy resourcePermissions={[AccessPermission.geoObras.obra.writePermission]}>
        <FcButton
          icon="pi pi-ellipsis-v"
          className="p-button-text ellipsis-button"
          disabled={itens.length === 0}
          onClick={(event) => {
            event.stopPropagation();
            menuRef.current.toggle(event);
          }}
        />
      </PermissionProxy>
    </>
  );
};

Opcoes.propTypes = {
  itens: PropTypes.array,
};

export default Opcoes;
