import FcButton from 'fc/components/FcButton';
import FcCalendar from 'fc/components/FcCalendar';
import FormField from 'fc/components/FormField';
import { DATE_FORMAT } from 'fc/utils/date';
import { showNotification } from 'fc/utils/utils';
import { observer } from 'mobx-react';
import moment from 'moment/moment';
import { InputText } from 'primereact/inputtext';
import { Message } from 'primereact/message';
import PropTypes from 'prop-types';
import { useEffect, useState } from 'react';
import ObraUpload from '~/pages/geoObras/acompanhamento/obraUpload';

const FormEntrega = observer((props) => {
  const { store, onCancel, onSuccess } = props;
  const [submitted, setSubmitted] = useState(false);

  useEffect(() => {
    if (submitted) execution();
  }, [submitted]);

  const execution = () => {
    if (!store.rules.hasError) {
      store.validaArquivos(() => store.entregarObra(onSuccess));
    } else {
      showNotification('error', null, 'Verifique os campos do formulário!');
    }
  };

  const submitFormData = (e) => {
    e?.preventDefault && e.preventDefault();
    if (submitted) {
      execution();
    } else {
      setSubmitted(true);
    }
  };

  const renderForm = () => {
    return (
      <div key="dadosBasicosEntrega" className="p-fluid p-formgrid p-grid">
        <FormField
          columns={6}
          attribute="tipoEncerramento"
          label="Tipo Encerramento"
          rule={store.getRule('tipoEncerramento')}
          submitted={submitted}
        >
          <InputText
            onChange={(e) => store.updateAttribute('tipoEncerramento', e)}
            placeholder="Informe o tipo de encerramento"
            value={store.object?.tipoEncerramento}
          />
        </FormField>
        <FormField
          columns={6}
          attribute="dataConclusao"
          label="Data de Conclusão"
          rule={store.getRule('dataConclusao')}
          submitted={submitted}
        >
          <>
            <FcCalendar
              value={store.object.dataConclusao ? moment(store.object.dataConclusao)._d : null}
              placeholder="Selecione a data de conclusão"
              onChange={(e) => store.updateAttributeDate('dataConclusao', e)}
              minDate={store.obra?.dataFim ? moment(store.obra.dataFim)._d : null}
            />
            {store.obra?.dataFim && (
              <small>{`A data de finalização da obra foi ${moment(store.obra.dataFim)?.format(DATE_FORMAT)}.`}</small>
            )}
          </>
        </FormField>

        <FormField
          columns={6}
          attribute="dataRecebimento"
          label="Data de Recebimento"
          rule={store.getRule('dataRecebimento')}
          submitted={submitted}
        >
          <>
            <FcCalendar
              value={store.object.dataRecebimento ? moment(store.object.dataRecebimento)._d : null}
              placeholder="Selecione a data de recebimento"
              onChange={(e) => store.updateAttributeDate('dataRecebimento', e)}
            />
          </>
        </FormField>

        <div className="p-grid p-col-12 p-p-0 mt-3">
          <ObraUpload
            chooseLabel="Adicionar Documentos"
            accept=".pdf,.kml,.png,.jpg"
            tipos={store?.fileStore.tipoArquivoEnum?.filter(
              (tipo) => tipo.fase === 'ENTREGA' && tipo.value === 'OUTROS_DOCUMENTOS'
            )}
            onChangeFiles={(arquivos) => store.setArquivos(arquivos)}
            arquivos={store?.arquivos?.filter((arquivo) => arquivo.fase === 'ENTREGA')}
            store={store?.fileStore}
            updateAttributeFile={(key, attribute, value) => store.updateAttributeFile(key, attribute, value)}
            emptyMenssage="Documentos não adicionados!"
            fase={store.fase}
          />
        </div>
      </div>
    );
  };

  const isDisabledEntregar = () => {
    const errorsMessages = {
      message: '',
      disabled: false,
    };
    if (!store.validaForm()) {
      errorsMessages.message =
        'É necessário preencher todos os campos obrigatórios antes de avançar para a próxima etapa.';
    } else {
      errorsMessages.disabled = false;
    }
    return errorsMessages;
  };

  const renderActionButtons = () => {
    const disabledEntregar = isDisabledEntregar();
    return (
      <div className="flex">
        <span className="p-ml-auto">
          {disabledEntregar.disabled && (
            <Message className="p-ml-auto mr-2 mt-2" severity="warn" text={disabledEntregar.message} />
          )}
          <FcButton
            type="button"
            label="Cancelar"
            className="p-button-secondary mr-2 mt-2"
            onClick={() => onCancel && onCancel()}
          />
          <FcButton
            className="mt-2"
            type="button"
            disabled={!store.validaForm()}
            label="Entregar"
            loading={store.loading}
            onClick={() => submitFormData()}
          />
        </span>
      </div>
    );
  };

  return (
    <form onSubmit={submitFormData}>
      {renderForm()}
      {renderActionButtons()}
    </form>
  );
});

FormEntrega.propTypes = {
  store: PropTypes.any,
  onCancel: PropTypes.func,
  onSuccess: PropTypes.func,
};

export default FormEntrega;
