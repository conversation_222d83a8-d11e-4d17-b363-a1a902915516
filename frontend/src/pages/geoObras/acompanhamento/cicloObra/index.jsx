import PropTypes from 'prop-types';
import React from 'react';
import './style.scss';
import DadosEstaticosService from '~/services/DadosEstaticosService';

const CicloObra = (props) => {
  const { faseAtual, faseAnterior, status } = props;

  const classeStatus = (fase) => {
    let classe = '';

    const faseBase = faseAtual === 'INTERRUPCAO' ? faseAnterior : faseAtual;

    switch (fase) {
      case 'INICIO':
        if ('CADASTRAL' === faseBase) {
          classe = 'ativa';
        } else {
          classe = 'concluida';
        }
        break;
      case 'MEDICAO':
        if (['MEDICAO', 'INICIAL'].includes(faseBase)) {
          classe = 'ativa';
        } else if (['FINALIZACAO', 'ENTREGA'].includes(faseBase)) {
          classe = 'concluida';
        }
        break;
      case 'FIM':
        if (['FINALIZACAO', 'ENTREGA'].includes(faseBase)) {
          classe = 'concluida';
        }
        break;
      case 'ENTREGA':
        if ('FINALIZACAO' === faseBase) {
          classe = 'ativa';
        } else if ('ENTREGA' === faseBase) {
          classe = 'concluida';
        }
        break;
    }

    return classe;
  };

  const getFasesOBra = () => {
    let fases = DadosEstaticosService.getCicloObra();

    if (faseAtual === 'INTERRUPCAO' || status === 'PARALISADA') {
      let maxIndex = 0;
      if (faseAnterior === 'INICIAL' || faseAnterior === 'CADASTRAL') {
        maxIndex = 1;
      } else if (faseAnterior === 'MEDICAO') {
        maxIndex = 2;
      } else if (faseAnterior === 'FINALIZACAO' && faseAtual !== 'FINALIZACAO') {
        maxIndex = 3;
      } else if (faseAnterior === 'FINALIZACAO' && faseAtual === 'FINALIZACAO') {
        maxIndex = 4;
      }
      fases = fases.slice(0, maxIndex);
    }

    return fases;
  };

  let fasesObra = getFasesOBra();

  return (
    <div className="container">
      <div className="ciclo-vida">
        <div className="fases">
          {fasesObra.map((fase, index) => {
            const classe = classeStatus(fase.value);
            return (
              <React.Fragment key={index}>
                <div className={`fase ${classe}`}>
                  <span>{fase.text}</span>
                </div>
                {(fase.value !== 'ENTREGA' || (faseAtual === 'FINALIZACAO' && status === 'PARALISADA')) && (
                  <span className={`conector ${classe}`}>
                    <i className="pi pi-angle-right" />
                  </span>
                )}
              </React.Fragment>
            );
          })}
          {faseAtual === 'INTERRUPCAO' && (
            <React.Fragment>
              <div className={`fase interrompida`}>
                <span>Interrompida</span>
              </div>
            </React.Fragment>
          )}
          {status === 'PARALISADA' && (
            <React.Fragment>
              <div className={`fase paralisada`}>
                <span>Paralisada</span>
              </div>
            </React.Fragment>
          )}
        </div>
      </div>
    </div>
  );
};

CicloObra.propTypes = {
  fase: PropTypes.string,
  faseAtual: PropTypes.string,
  faseAnterior: PropTypes.string,
  status: PropTypes.string,
};

export default CicloObra;
