.ciclo-vida {
  text-align: center;
  border-radius: 50px;
  border: 2px solid #000000;
}

.fases {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 2px;
}

.fase {
  position: relative;
  padding: 6px;
  border-radius: 50px;
  background-color: #ccc;
  cursor: default;
}

.fase.ativa {
  background-color: #618af2;

  > span {
    color: #fff;
  }
}

.fase.concluida {
  background-color: #9aedb8;
}

.fase.interrompida {
  background-color: #c51616;

  > span {
    color: #fff;
  }
}

.fase.paralisada {
  background-color: #d1ce13;
}

.conector > i {
  font-size: 20px;
  color: #ccc;
}

.conector.ativa > i {
  color: #618af2;
}

.conector.concluida > i {
  color: #9aedb8;
}

.container {
  display: flex;
  align-items: start;
}
