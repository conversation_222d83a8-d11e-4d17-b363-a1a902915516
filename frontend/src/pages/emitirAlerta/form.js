import { observer } from 'mobx-react';
import AccessPermission from '~/constants/AccessPermission';
import UrlRouter from '~/constants/UrlRouter';
import GenericFormPage from 'fc/pages/GenericFormPage';
import EmitirAlertaFormStore from '~/stores/emitirAlerta/formStore';
import PropTypes from 'prop-types';
import PermissionProxy from 'fc/components/PermissionProxy';
import FormField from 'fc/components/FormField';
import { InputText } from 'primereact/inputtext';
import { InputTextarea } from 'primereact/inputtextarea';
import InputMonetary from 'fc/components/InputMonetary';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import '@ckeditor/ckeditor5-build-classic/build/translations/pt-br';
import { CKEditor } from '@ckeditor/ckeditor5-react';
import TermoEditor from 'fc/editors/termoReferencia';
import { Fieldset } from 'primereact/fieldset';
import { checkUserContextIsInspetor, checkUserGroup, getValueBy<PERSON>ey, somaValoresLotes } from 'fc/utils/utils';
import moment from 'moment';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { Message } from 'primereact/message';
import FcButton from 'fc/components/FcButton';
import AppStore from 'fc/stores/AppStore';
import FcCalendar from 'fc/components/FcCalendar';
import { Dialog } from 'primereact/dialog';
import { Dropdown } from 'primereact/dropdown';

@observer
class EmitirAlertaFormPage extends GenericFormPage {
  constructor(props) {
    super(props, UrlRouter.auditoria.analiseProcesso.index, AccessPermission.alerta);
    this.store = new EmitirAlertaFormStore(this.props.idProcesso, this.props.tipoProcesso);

    this.state = {
      visibleDialogAlerta: false,
      visibleDialogRejeitarAlerta: false,
      submitted: false,
      loadingAlerta: false,
    };

    this._toggleDialogAlerta = this._toggleDialogAlerta.bind(this);
    this._toggleDialogRejeitarAlerta = this._toggleDialogRejeitarAlerta.bind(this);
  }

  _toggleDialogAlerta() {
    this.setState((oldState) => ({ visibleDialogAlerta: !oldState.visibleDialogAlerta }));
  }

  componentDidMount() {
    const tipoProcesso = getValueByKey(
      this.props.tipoProcesso,
      DadosEstaticosService.getTiposProcesso(),
      'lowerText',
      'key'
    );

    this.store.initialize(this.props.idAlerta, {
      destinatarios: [],
      status: 'ENCAMINHADO',
      passouPorAuditor: true,
      tipo: tipoProcesso,
      motivoChecklistRejeitado: '',
    });
    this.store.getDiretoresDafo();
    this.store.initializeProcesso(() => this.forceUpdate());
  }

  getValuePregoeiroByTipoProcesso(tipoProcesso) {
    if (this.store.processo) {
      const columnPregoeiroResponsavel =
        DadosEstaticosService.getColumnPregoeiroResponsavelByTipoProcesso(tipoProcesso);
      if (tipoProcesso === 'carona' || tipoProcesso === 'dispensa') {
        return this.store.processo[columnPregoeiroResponsavel];
      } else if (tipoProcesso === 'inexigibilidade') {
        const responsavelHomologacao = this.store.processo[columnPregoeiroResponsavel.responsavelHomologacao];
        return responsavelHomologacao != ''
          ? responsavelHomologacao
          : this.store.processo[columnPregoeiroResponsavel.responsavelRatificacao];
      } else {
        return this.store.processo[columnPregoeiroResponsavel]?.nome;
      }
    }
  }

  _renderColumnsComissao(columns) {
    return columns.map((col) => <Column key={`col-${col.field}`} {...col} />);
  }

  render() {
    const { submitted } = this.state;

    const columnsComissao = [
      {
        style: { width: '25%' },
        field: 'nome',
        header: 'Nome',
      },
    ];

    const { submitFormData } = this;
    const { getRule, updateAttributeDate, updateAttribute } = this.store;
    const { tipoProcesso } = this.props;

    let content;
    if (this.store.processo) {
      content = (
        <>
          <div className="card " style={{ marginBottom: '65px' }}>
            <form onSubmit={submitFormData}>
              <div className="p-fluid p-formgrid p-grid">
                <Fieldset
                  className="p-col-12"
                  legend={<span style={{ color: 'rgba(0, 0, 0, 0.87)' }}>Dados do Processo</span>}
                >
                  <div className="p-fluid p-formgrid p-grid">
                    <FormField columns={4} label="Órgão">
                      <InputText placeholder="Órgão" value={this.store.processo.entidade.nome} disabled />
                    </FormField>
                    <FormField columns={8} label="Objeto">
                      <InputTextarea placeholder="Objeto" value={this.store.processo.objeto} disabled autoResize />
                    </FormField>
                    <FormField columns={3} label="Data da Abertura">
                      <FcCalendar
                        placeholder="Data da abertura"
                        value={this.getDateAttributeValue(
                          this.store.processo[DadosEstaticosService.getColumnDataAberturaByTipoProcesso(tipoProcesso)]
                        )}
                        disabled
                      />
                    </FormField>
                    <FormField columns={3} label="Pregoeiro/Responsável">
                      <InputText
                        placeholder="Pregoeiro/Responsável"
                        value={this.store.processo && this.getValuePregoeiroByTipoProcesso(tipoProcesso)}
                        disabled
                      />
                    </FormField>
                    <FormField columns={3} label="Valor Total Estimado">
                      <InputMonetary
                        placeholder="R$"
                        value={
                          tipoProcesso === 'credenciamento'
                            ? somaValoresLotes(this.store.processo.termoReferencia.lotes)
                            : this.store.processo[
                                DadosEstaticosService.getColumnValorEstimadoByTipoProcesso(tipoProcesso)
                              ]
                        }
                        disabled
                      />
                    </FormField>
                    <FormField columns={3} label="Número do Processo">
                      <InputText
                        placeholder="Número do processo"
                        value={
                          this.store.processo[
                            DadosEstaticosService.getColumnNumeroProcessoByTipoProcesso(this.store.tipoProcesso)
                          ]
                        }
                        disabled
                      />
                    </FormField>
                  </div>
                </Fieldset>
                {this.props.tipoProcesso === 'licitacao' && (
                  <Fieldset
                    className="p-col-12"
                    legend={<span style={{ color: 'rgba(0, 0, 0, 0.87)' }}>Destinatários/Responsáveis</span>}
                  >
                    <DataTable
                      rowHover
                      value={this.store.comissao}
                      emptyMessage="Nenhum destinatário/responsável"
                      paginator
                      rows={5}
                    >
                      {this._renderColumnsComissao(columnsComissao)}
                    </DataTable>
                  </Fieldset>
                )}
                {this.store.alertaRejeitado && (
                  <Fieldset className="p-col-12" legend={<Message severity="error" text="Alerta Rejeitado" />}>
                    <FormField
                      columns={12}
                      attribute="respostaRejeicao"
                      submitted={submitted}
                      label="Mensagem do Diretor da DAFO"
                    >
                      <InputTextarea value={this.store.object.respostaRejeicao} rows={5} cols={30} disabled />
                    </FormField>
                  </Fieldset>
                )}
                <Fieldset className="p-col-12" legend={<span style={{ color: 'rgba(0, 0, 0, 0.87)' }}>Mensagem</span>}>
                  <FormField
                    columns={12}
                    attribute="mensagem"
                    label="Texto"
                    rule={getRule('mensagem')}
                    submitted={submitted}
                  >
                    <CKEditor
                      editor={TermoEditor}
                      data={this.store.object.mensagem}
                      onChange={(_, editor) => this.store.updateMensagem(editor)}
                    />
                  </FormField>
                </Fieldset>
                <Fieldset
                  className="p-col-12"
                  legend={<span style={{ color: 'rgba(0, 0, 0, 0.87)' }}>Observações</span>}
                >
                  <div className="p-fluid p-formgrid p-grid">
                    <FormField
                      columns={3}
                      attribute="prazoResposta"
                      label="Prazo para resposta"
                      rule={getRule('prazoResposta')}
                      submitted={submitted}
                    >
                      <FcCalendar
                        value={this.store.object.dataTda}
                        onChange={(e) => updateAttributeDate('prazoResposta', e)}
                        minDate={moment().toDate()}
                        showIcon
                        mask="99/99/9999"
                      />
                    </FormField>
                  </div>
                </Fieldset>
                <Fieldset
                  className="p-col-12"
                  legend={<span style={{ color: 'rgba(0, 0, 0, 0.87)' }}>Enviar para</span>}
                >
                  <div className="p-fluid p-formgrid p-grid">
                    <FormField
                      columns={4}
                      label="Diretor(a) da DAFO"
                      rule={getRule('diretorDafo')}
                      submitted={submitted}
                    >
                      <Dropdown
                        onChange={(e) => updateAttribute('diretorDafo', e.value)}
                        value={this.store.object.diretorDafo}
                        placeholder="Selecione o(a) Diretor(a) da DAFO"
                        options={this.store.diretoresDafo}
                        optionLabel="nome"
                      />
                    </FormField>
                  </div>
                </Fieldset>
              </div>
              {this.renderActionButtons()}
            </form>
          </div>
        </>
      );
    } else {
      content = (
        <div className="card page">
          <i className="pi pi-spin pi-spinner" style={{ fontSize: '2em' }}></i>
        </div>
      );
    }
    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        {content}
        {this.state.visibleDialogRejeitarAlerta && this.renderDialogRejeitarAlerta()}
      </PermissionProxy>
    );
  }
  renderActionButtons() {
    const hasWritePermission = !this.getWritePermission() || AppStore.hasPermission(this.getWritePermission());
    return (
      <div className="p-mt-10 form-actions">
        <div className="p-mt-2">
          <span className="p-mr-1 p-d-inline p-d-flex align-items-center">
            <FcButton
              label="Voltar"
              type="button"
              className="p-ml-auto p-button-secondary p-mr-2"
              onClick={() => this._goBack()}
              loading={this.store.loading}
            />
            <FcButton
              label="Rejeitar Alerta"
              icon="pi pi-times"
              type="button"
              className="p-button-danger p-mr-2"
              loading={this.store.loading}
              disabled={!(checkUserGroup(['Administrador']) || checkUserContextIsInspetor())}
              onClick={() => this._toggleDialogRejeitarAlerta()}
            />
            {hasWritePermission && (
              <FcButton
                label={this.props.action === 'new' ? 'Emitir' : 'Reenviar'}
                type="submit"
                loading={this.store.loading}
              />
            )}
          </span>
        </div>
      </div>
    );
  }

  _toggleDialogRejeitarAlerta() {
    this.setState((oldState) => ({ visibleDialogRejeitarAlerta: !oldState.visibleDialogRejeitarAlerta }));
  }

  renderDialogRejeitarAlertaFooter() {
    return (
      <div>
        <FcButton
          label="Cancelar"
          icon="pi pi-times"
          onClick={() => this._toggleDialogRejeitarAlerta()}
          className="p-button-text"
          loading={this.state.loadingAlerta}
        />
        <FcButton
          label="Rejeitar Alerta"
          icon="pi pi-times"
          className="p-button-danger"
          loading={this.state.loadingAlerta}
          onClick={() => {
            this.setState({ loadingAlerta: true }, () => {
              this.store.rejeitarCheckInspetor(
                () =>
                  this.setState({
                    loadingAlerta: false,
                    visibleDialogAlerta: false,
                    visibleDialogRejeitarAlerta: false,
                  }),
                this._goBack
              );
            });
          }}
        />
      </div>
    );
  }

  renderDialogRejeitarAlerta() {
    const { submitted } = this.state;

    return (
      <Dialog
        header="Rejeitar Alerta"
        visible={this.state.visibleDialogRejeitarAlerta}
        style={{ width: '40%' }}
        footer={this.renderDialogRejeitarAlertaFooter()}
        onHide={() => this._toggleDialogRejeitarAlerta()}
      >
        <div className="p-fluid p-formgrid p-grid">
          <FormField attribute="motivoChecklistRejeitado" submitted={submitted} columns={12} label="Motivo Rejeição">
            <InputTextarea
              placeholder="Motivo rejeição"
              value={this.store.motivoChecklistRejeitado}
              onChange={(e) => {
                this.store.updateRespostaRejeicaoInspetor(e);
              }}
              autoResize
            />
          </FormField>
        </div>
      </Dialog>
    );
  }

  _goBack() {
    if (this.props.editMode) {
      this.props.disableEditMode();
      this.props.reload && this.props.reload(this.props.idProcesso, this.props.tipoProcesso, this.props.idTab);
    } else {
      this.props.history.push(this.goBackUrl);
    }
  }
}

EmitirAlertaFormPage.propTypes = {
  id: PropTypes.any,
  action: PropTypes.any,
  history: PropTypes.any,
  index: PropTypes.any,
  disableEditMode: PropTypes.func,
};

export default EmitirAlertaFormPage;
